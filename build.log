
> PromptHQ@0.1.0 build
> next build

   ▲ Next.js 15.2.4
   - Environments: .env.local

   Creating an optimized production build ...
 ✓ Compiled successfully
   Skipping linting
   Checking validity of types ...
   Collecting page data ...
   Generating static pages (0/24) ...
   Generating static pages (6/24) 
   Generating static pages (12/24) 
   Generating static pages (18/24) 
 ✓ Generating static pages (24/24)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                                                               Size  First Load JS
┌ ○ /                                                                  1.38 kB         232 kB
├ ○ /_not-found                                                          985 B         103 kB
├ ƒ /api-test                                                          12.5 kB         169 kB
├ ƒ /api/categories                                                      195 B         103 kB
├ ƒ /api/prompts/[shortId]                                               195 B         103 kB
├ ƒ /api/refresh-cache                                                   195 B         103 kB
├ ƒ /api/test-voting                                                     195 B         103 kB
├ ƒ /auth/callback                                                       195 B         103 kB
├ ○ /auth/set-username                                                 4.52 kB         153 kB
├ ƒ /category/[slug]                                                     181 B         246 kB
├ ○ /collections                                                       6.37 kB         173 kB
├ ƒ /collections/[id]                                                  2.03 kB         105 kB
├ ○ /debug-prompts                                                     4.35 kB         153 kB
├ ○ /explore                                                           7.29 kB         202 kB
├ ○ /notifications                                                     14.5 kB         176 kB
├ ƒ /profile/[username]/c/[collectionId]                               4.62 kB         223 kB
├ ƒ /prompt/[categorySlug]/[toolSlug]/[tagSlug]/[titleSlug]/[shortId]    36 kB         266 kB
├ ƒ /prompt/edit/[shortId]                                             8.28 kB         211 kB
├ ƒ /prompt/remix/[shortId]                                            8.24 kB         211 kB
├ ○ /prompt/remix/success                                              7.47 kB         204 kB
├ ○ /prompt/submit                                                     9.13 kB         212 kB
├ ○ /prompt/templates                                                    195 B         103 kB
├ ○ /redesigned-page                                                   2.25 kB         233 kB
├ ○ /saved                                                             14.5 kB         245 kB
├ ○ /search                                                            3.87 kB         251 kB
├ ○ /settings                                                          11.9 kB         170 kB
├ ○ /sign-in                                                           4.29 kB         156 kB
├ ○ /sign-up                                                           4.12 kB         156 kB
├ ƒ /tag/[slug]                                                          181 B         246 kB
├ ƒ /tool/[slug]                                                         181 B         246 kB
└ ƒ /user/[username]                                                   6.99 kB         226 kB
+ First Load JS shared by all                                           102 kB
  ├ chunks/11eacf67-5e0952662d24cda0.js                                53.2 kB
  ├ chunks/954-3b1fdf1477baa477.js                                     46.2 kB
  └ other shared chunks (total)                                        3.01 kB


ƒ Middleware                                                           64.9 kB

○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

