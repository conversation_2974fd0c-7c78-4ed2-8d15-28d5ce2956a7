import { supabase } from "../supabase/client";

/**
 * Normalizes an AI model input string by:
 * - Converting to lowercase
 * - Removing extra spaces
 * - Standardizing common variations
 */
export function normalizeModelInput(input: string): string {
  if (!input) return '';
  
  // Convert to lowercase and trim
  let normalized = input.toLowerCase().trim();
  
  // Standardize common variations
  normalized = normalized
    // Remove extra spaces
    .replace(/\s+/g, ' ')
    // Standardize version numbers (e.g., "gpt 4" -> "gpt-4")
    .replace(/(\w+)\s+(\d+)/g, '$1-$2')
    // Standardize OpenAI models
    .replace(/gpt(\d)/, 'gpt-$1')
    .replace(/gpt-(\d)o/, 'gpt-$1o')
    .replace(/gpt-(\d)\.(\d)/, 'gpt-$1.$2')
    // Standardize Claude models
    .replace(/claude(\d)/, 'claude $1')
    .replace(/claude\s+(\d+)\.(\d+)/, 'claude $1.$2');
    
  return normalized;
}

/**
 * Attempts to find an exact match for the normalized model input
 * @returns The AI model ID if found, null otherwise
 */
export async function findExactModelMatch(normalizedInput: string): Promise<number | null> {
  try {
    // Try to match by normalized tool_name
    const { data, error } = await supabase
      .from('ai_models')
      .select('id')
      .or(`tool_name::text ILIKE '%${normalizedInput}%',provider::text || ' ' || tool_name::text ILIKE '%${normalizedInput}%'`);
      
    if (error || !data || data.length === 0) {
      return null;
    }
    
    return Number(data[0].id) || null;
  } catch (error) {
    console.error('Error finding exact model match:', error);
    return null;
  }
}

/**
 * Attempts to find a fuzzy match for the normalized model input
 * This is a simplified implementation - in a production environment,
 * you might want to use a more sophisticated fuzzy matching algorithm
 * @returns The AI model ID if found, null otherwise
 */
interface AIModelRecord {
  id: number;
  provider: string;
  tool_name: string;
}

export async function findFuzzyModelMatch(normalizedInput: string): Promise<number | null> {
  try {
    // Get all models for fuzzy matching
    const { data, error } = await supabase
      .from('ai_models')
      .select('id, provider, tool_name');
      
    if (error || !data || data.length === 0) {
      return null;
    }
    
    // Type assertion to help TypeScript understand the structure
    const typedData = data as AIModelRecord[];
    
    // Check for partial matches
    for (const model of typedData) {
      const modelName = String(model.tool_name).toLowerCase();
      const providerName = String(model.provider).toLowerCase();
      const combinedName = `${providerName} ${modelName}`;
      
      // Check if input is a substring of model name or provider + model name
      if (modelName.includes(normalizedInput) || 
          normalizedInput.includes(modelName) ||
          combinedName.includes(normalizedInput) ||
          normalizedInput.includes(combinedName)) {
        return Number(model.id) || null;
      }
      
      // Check if model name is a substring of input
      if (normalizedInput.includes(modelName) && modelName.length > 3) {
        return Number(model.id) || null;
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error finding fuzzy model match:', error);
    return null;
  }
}

/**
 * Standardizes and matches user input to a known AI model
 * @param rawModelInput User's raw model input
 * @returns The matched AI model ID or null if no match found
 */
export async function standardizeAndMatchAIModel(
  rawModelInput: string | null | undefined
): Promise<number | null> {
  if (!rawModelInput?.trim()) return null;
  
  // 1. Basic normalization
  const normalized = normalizeModelInput(rawModelInput);
  
  // 2. Try exact match first
  const exactMatch = await findExactModelMatch(normalized);
  if (exactMatch) return exactMatch;
  
  // 3. Try fuzzy matching algorithms
  const fuzzyMatch = await findFuzzyModelMatch(normalized);
  if (fuzzyMatch) return fuzzyMatch;
  
  // 4. If no match found, return null
  return null;
}
