"use client";

import { useState, useEffect } from "react";
import { createBrowserClient } from "@supabase/ssr";
import type { User } from "@supabase/supabase-js";
import type { Profile } from "@/lib/types";

type UserWithProfile = User & {
  profile?: Profile | null;
};

export function useUser() {
  const [user, setUser] = useState<UserWithProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  useEffect(() => {
    // Function to fetch user and profile
    const fetchUserAndProfile = async () => {
      try {
        setIsLoading(true);
        
        // Get current session instead of user directly
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError || !session?.user) {
          setUser(null);
          return;
        }
        
        const currentUser = session.user;
        
        // Get user profile
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", currentUser.id)
          .single();
          
        if (profileError) {
          console.error("Error fetching user profile:", profileError);
        }
        
        // Combine user and profile
        const userWithProfile = {
          ...currentUser,
          profile: profileData || null
        };
        
        setUser(userWithProfile);
      } catch (error) {
        console.error("Error in useUser hook:", error);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    // Initial fetch
    fetchUserAndProfile();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          fetchUserAndProfile();
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
      }
    );

    // Cleanup subscription
    return () => {
      subscription.unsubscribe();
    };
  }, [supabase]);

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
  };
}
