import { createServerSupabaseClient } from "./supabase/server"
import type { Prompt, Category, Tool, Tag, Profile } from "./types"

export async function getPrompts({
  categoryId,
  toolId,
  tagId,
  userId,
  limit = 20,
  offset = 0,
  sortBy = "created_at",
  sortOrder = "desc",
}: {
  categoryId?: number
  toolId?: number
  tagId?: number
  userId?: string
  limit?: number
  offset?: number
  sortBy?: string
  sortOrder?: "asc" | "desc"
}): Promise<Prompt[]> {
  try {
    const supabase = await createServerSupabaseClient()

    let query = supabase
      .from("prompts")
      .select(`
        *,
        category:categories(*),
        tool:tools(*),
        user:user_id (username, avatar_url),
        tags:prompt_tags(tag:tags(*))
      `)
      .eq("is_public", true)
      .order(sortBy, { ascending: sortOrder === "asc" })
      .range(offset, offset + limit - 1)

    if (categoryId) {
      query = query.eq("category_id", categoryId)
    }

    if (toolId) {
      query = query.eq("tool_id", toolId)
    }

    if (tagId) {
      query = query.eq("prompt_tags.tag_id", tagId)
    }

    if (userId) {
      query = query.eq("user_id", userId)
    }

    const { data, error } = await query

    if (error) {
      console.error("Error fetching prompts:", error)
      throw new Error(`Error fetching prompts: ${error.message}`)
    }

    // Transform the nested data structure
    return data.map((prompt) => ({
      ...prompt,
      category: prompt.category,
      tool: prompt.tool,
      user: prompt.user,
      tags: prompt.tags.map((tagRelation: any) => tagRelation.tag),
    }))
  } catch (error) {
    console.error("Error in getPrompts:", error)
    throw error
  }
}

export async function getPromptById(id: string): Promise<Prompt | null> {
  try {
    const supabase = await createServerSupabaseClient()

    const { data, error } = await supabase
      .from("prompts")
      .select(`
        *,
        category:categories(*),
        tool:tools(*),
        user:user_id (username, avatar_url),
        tags:prompt_tags(tag:tags(*))
      `)
      .eq("id", id)
      .single()

    if (error) {
      console.error("Error fetching prompt:", error)
      return null
    }

    if (!data) {
      return null
    }

    // Transform the nested data structure
    return {
      ...data,
      category: data.category,
      tool: data.tool,
      user: data.user,
      tags: data.tags.map((tagRelation: any) => tagRelation.tag),
    }
  } catch (error) {
    console.error("Error in getPromptById:", error)
    return null
  }
}

export async function getCategories(): Promise<Category[]> {
  try {
    const supabase = await createServerSupabaseClient()

    const { data, error } = await supabase.from("categories").select("*").order("name")

    if (error) {
      console.error("Error fetching categories:", error)
      return []
    }

    return data || []
  } catch (error) {
    console.error("Error in getCategories:", error)
    return []
  }
}

export async function getTools(): Promise<Tool[]> {
  try {
    const supabase = await createServerSupabaseClient()

    const { data, error } = await supabase.from("tools").select("*").order("name")

    if (error) {
      console.error("Error fetching tools:", error)
      return []
    }

    return data || []
  } catch (error) {
    console.error("Error in getTools:", error)
    return []
  }
}

export async function getTags(): Promise<Tag[]> {
  try {
    const supabase = await createServerSupabaseClient()

    const { data, error } = await supabase.from("tags").select("*").order("name")

    if (error) {
      console.error("Error fetching tags:", error)
      return []
    }

    return data || []
  } catch (error) {
    console.error("Error in getTags:", error)
    return []
  }
}

export async function getProfile(userId: string): Promise<Profile | null> {
  try {
    const supabase = await createServerSupabaseClient()

    const { data, error } = await supabase.from("profiles").select("*").eq("id", userId).single()

    if (error) {
      console.error("Error fetching profile:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error in getProfile:", error)
    return null
  }
}
