import { supabase } from "../supabase/client";

/**
 * Updates a prompt's collection memberships by adding to and/or removing from collections
 * This function handles both adding and removing in a single operation
 */
export async function updatePromptCollections(
  userId: string,
  promptId: string,
  { 
    addToCollectionIds = [], 
    removeFromCollectionIds = [] 
  }: { 
    addToCollectionIds?: string[],
    removeFromCollectionIds?: string[]
  }
): Promise<{ success: boolean; error?: any }> {
  try {
    console.log(`[updatePromptCollections] Updating collections for prompt ${promptId}`);
    console.log(`[updatePromptCollections] Adding to: ${addToCollectionIds.length} collections`);
    console.log(`[updatePromptCollections] Removing from: ${removeFromCollectionIds.length} collections`);
    
    // If there's nothing to do, return success
    if (addToCollectionIds.length === 0 && removeFromCollectionIds.length === 0) {
      console.log("[updatePromptCollections] No changes requested");
      return { success: true };
    }

    // Call the update_prompt_collections RPC function to handle both operations in a single transaction
    const { data, error } = await supabase.rpc(
      "update_prompt_collections",
      {
        p_user_id: userId,
        p_prompt_id: promptId,
        p_add_collection_ids: addToCollectionIds.length > 0 ? addToCollectionIds : null,
        p_remove_collection_ids: removeFromCollectionIds.length > 0 ? removeFromCollectionIds : null
      }
    );

    if (error) {
      console.error("[updatePromptCollections] Error updating collections:", error);
      return { success: false, error };
    }

    if (data === false) { // RPC returns boolean
      console.warn("[updatePromptCollections] RPC returned false");
      return { success: false, error: "Failed to update collections (RPC)." };
    }

    return { success: true };
  } catch (e) {
    console.error("[updatePromptCollections] Unexpected error:", e);
    return { success: false, error: e };
  }
}
