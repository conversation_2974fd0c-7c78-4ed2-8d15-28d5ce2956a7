import { supabase } from "../supabase/client";
import { Collection } from "../types";
import { transformCollection } from "../transformers";

/**
 * Get collections for a user with optional filtering for dialog purpose
 */
export async function getUserCollections(
  userId: string,
  options: {
    includePrivate?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: "created_at" | "prompt_count" | "name";
    sortOrder?: "asc" | "desc";
    purpose?: "dialog" | "general";
  } = {}
): Promise<Collection[]> {
  const {
    includePrivate = false,
    limit = 10,
    offset = 0,
    sortBy = "created_at",
    sortOrder = "desc",
    purpose = "general",
  } = options;

  try {
    let query = supabase
      .from("collections")
      .select(`
        id,
        user_id,
        name,
        description,
        icon,
        is_public,
        is_default,
        default_type,
        prompt_count,
        view_count,
        created_at,
        updated_at,
        user:profiles!collections_user_id_fkey (username, avatar_url)
      `)
      .eq("user_id", userId);
    
    // For dialog purpose, exclude "My Prompts" collection and prioritize "Saved Prompts"
    if (purpose === "dialog") {
      query = query.neq("default_type", "my_prompts"); // Exclude "My Prompts"
      // Add specific ordering for "Saved Prompts" first
      query = query.order("is_default", { ascending: false }) // Puts true (default) first
                   .order("default_type", { ascending: true }); // 'saved_prompts' first
    } else {
      // Use the standard ordering for general purpose
      query = query.order(sortBy, { ascending: sortOrder === "asc" });
    }
    
    // Apply range for pagination
    query = query.range(offset, offset + limit - 1);

    if (!includePrivate) {
      query = query.eq("is_public", true);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching user collections:", error.message);
      throw error;
    }

    return (data || []).map(transformCollection);
  } catch (error) {
    console.error("Error in getUserCollections:", error);
    return [];
  }
}
