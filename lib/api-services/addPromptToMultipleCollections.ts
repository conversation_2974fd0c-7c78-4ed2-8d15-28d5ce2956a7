import { supabase } from "../supabase/client";

/**
 * Adds a prompt to multiple collections at once
 * Uses the add_prompt_to_multiple_collections RPC function
 */
export async function addPromptToMultipleCollections(
  userId: string,
  promptId: string,
  collectionIds: string[]
): Promise<{ success: boolean; error?: any }> {
  try {
    const { data, error } = await supabase.rpc("add_prompt_to_multiple_collections", {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_collection_ids: collectionIds,
    });

    if (error) {
      console.error("Error adding prompt to multiple collections:", error);
      return { success: false, error };
    }
    if (data === false) { // RPC returns boolean
      console.warn("add_prompt_to_multiple_collections RPC returned false.");
      return { success: false, error: "Failed to add prompt to one or more collections (RPC)." };
    }
    return { success: true };
  } catch (e) {
    console.error("Unexpected error in addPromptToMultipleCollections service:", e);
    return { success: false, error: e };
  }
}
