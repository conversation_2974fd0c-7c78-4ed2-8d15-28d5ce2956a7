import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with the service role key for server-side operations
// This bypasses RLS policies
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

/**
 * Vote on a prompt (server-side function that bypasses RLS)
 * @param userId The ID of the user casting the vote
 * @param promptId The ID of the prompt being voted on
 * @param voteType The type of vote: 1 for upvote, -1 for downvote, 0 to remove vote
 * @returns Object with success flag and updated vote count
 */
export async function voteOnPromptServer(
  userId: string, 
  promptId: string, 
  voteType: 1 | -1 | 0
): Promise<{ success: boolean; updatedVoteCount?: number; error?: string }> {
  try {
    if (!userId) {
      return { success: false, error: 'User must be logged in to vote' };
    }

    // Call the database function to record the vote
    const { data, error } = await supabaseAdmin
      .rpc('vote_on_prompt_smallint', {
        p_user_id: userId,
        p_prompt_id: promptId,
        p_vote_type: voteType
      });

    if (error) {
      console.error('Error voting on prompt:', error);
      return { success: false, error: error.message };
    }

    // After voting, fetch the updated vote count
    const { data: updatedStats, error: statsError } = await supabaseAdmin
      .from('prompt_statistics')
      .select('rating')
      .eq('id', promptId)
      .single();

    if (statsError) {
      console.error('Error fetching updated vote count:', statsError);
      return { success: true }; // Vote was successful even though we couldn't get the new count
    }

    // Ensure we're returning a number for the vote count
    const rating = updatedStats?.rating;
    return { 
      success: true, 
      updatedVoteCount: typeof rating === 'number' ? rating : 0 
    };
  } catch (e) {
    console.error(`Unexpected error voting on prompt:`, e);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get the user's current vote on a prompt (server-side function that bypasses RLS)
 * @param userId The ID of the user
 * @param promptId The ID of the prompt
 * @returns Vote type: 1 for upvote, -1 for downvote, null if no vote
 */
export async function getUserVoteOnPromptServer(
  userId: string, 
  promptId: string
): Promise<{ voteType: 1 | -1 | null; error?: string }> {
  try {
    if (!userId) {
      return { voteType: null, error: 'User not logged in' };
    }

    const { data, error } = await supabaseAdmin
      .from('prompt_votes')
      .select('vote_type')
      .eq('user_id', userId)
      .eq('prompt_id', promptId)
      .single();

    if (error) {
      // If error is 'No rows found', user hasn't voted
      if (error.code === 'PGRST116') {
        return { voteType: null };
      }
      console.error('Error fetching user vote:', error);
      return { voteType: null, error: error.message };
    }

    return { voteType: data?.vote_type as 1 | -1 | null };
  } catch (e) {
    console.error(`Unexpected error fetching user vote:`, e);
    return { voteType: null, error: 'An unexpected error occurred' };
  }
}
