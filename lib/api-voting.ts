import { supabase } from "./supabase/client"

/**
 * Vote on a prompt
 * @param userId The ID of the user casting the vote
 * @param promptId The ID of the prompt being voted on
 * @param voteType The type of vote: 1 for upvote, -1 for downvote, 0 to remove vote
 * @returns Object with success flag and updated vote count
 */
export async function voteOnPrompt(
  userId: string, 
  promptId: string, 
  voteType: 1 | -1 | 0
): Promise<{ success: boolean; updatedVoteCount?: number; error?: string }> {
  console.log(`[VOTE] Attempting to cast vote: userId=${userId}, promptId=${promptId}, voteType=${voteType}`);
  
  try {
    if (!userId) {
      console.log('[VOTE] Error: User not logged in');
      return { success: false, error: 'User must be logged in to vote' };
    }
    
    // Step 1: Get current vote if it exists (to know if we're changing or adding new)
    console.log('[VOTE] Checking for existing vote');
    let previousVoteType: number | null = null;
    let voteExists = false;
    
    try {
      // First check if a vote exists
      const { count, error: countError } = await supabase
        .from('prompt_votes')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('prompt_id', promptId);
      
      if (countError) {
        console.error('[VOTE] Error checking vote count:', countError);
      } else {
        voteExists = count ? count > 0 : false;
        console.log(`[VOTE] Vote exists: ${voteExists}`);
      }
      
      // If vote exists, get the current vote type
      if (voteExists) {
        const { data, error } = await supabase
          .from('prompt_votes')
          .select('vote_type')
          .eq('user_id', userId)
          .eq('prompt_id', promptId);
        
        if (error) {
          console.error('[VOTE] Error getting vote type:', error);
        } else if (data && data.length > 0) {
          // Explicitly cast to number to fix TypeScript error
          previousVoteType = Number(data[0].vote_type) as 1 | -1 | null;
          console.log(`[VOTE] Current vote type: ${previousVoteType}`);
        }
      }
    } catch (e) {
      console.error('[VOTE] Exception checking for existing vote:', e);
    }
    
    // Step 2: Handle the vote operation (insert, update, or delete)
    let voteOpSuccess = true;
    let voteOpError = null;
    
    // Use raw SQL approach to avoid PostgreSQL errors
    if (voteType === 0 && voteExists) {
      // Remove vote
      console.log('[VOTE] Attempting to delete vote');
      const { error } = await supabase
        .from('prompt_votes')
        .delete()
        .eq('user_id', userId)
        .eq('prompt_id', promptId);
      
      if (error) {
        console.error('[VOTE] Error deleting vote:', error);
        voteOpSuccess = false;
        voteOpError = error.message;
      }
    } else if (voteType !== 0) {
      if (voteExists) {
        // Update existing vote
        console.log('[VOTE] Updating existing vote');
        const { error } = await supabase
          .from('prompt_votes')
          .update({ vote_type: voteType })
          .eq('user_id', userId)
          .eq('prompt_id', promptId);
        
        if (error) {
          console.error('[VOTE] Error updating vote:', error);
          voteOpSuccess = false;
          voteOpError = error.message;
        }
      } else {
        // Insert new vote
        console.log('[VOTE] Inserting new vote');
        const { error } = await supabase
          .from('prompt_votes')
          .insert([
            { user_id: userId, prompt_id: promptId, vote_type: voteType }
          ]);
        
        if (error) {
          console.error('[VOTE] Error inserting vote:', error);
          voteOpSuccess = false;
          voteOpError = error.message;
        }
      }
    }
    
    if (!voteOpSuccess) {
      return { 
        success: false, 
        error: voteOpError || 'Failed to update vote'
      };
    }
    
    console.log('[VOTE] Vote operation succeeded');
    
    // Step 3: Update statistics manually
    try {
      console.log('[VOTE] Updating prompt statistics');
      
      // Get current statistics
      const { data: stats, error: statsError } = await supabase
        .from('prompt_statistics')
        .select('rating, upvotes, downvotes')
        .eq('id', promptId)
        .single();
      
      if (statsError) {
        console.error('[VOTE] Error fetching statistics:', statsError);
        return { 
          success: true,
          error: 'Vote recorded but could not update statistics'
        };
      }
      
      // Calculate new statistics
      let newRating = Number(stats?.rating || 0);
      let newUpvotes = Number(stats?.upvotes || 0);
      let newDownvotes = Number(stats?.downvotes || 0);
      
      console.log('[VOTE] Current statistics:', { 
        rating: newRating, 
        upvotes: newUpvotes, 
        downvotes: newDownvotes 
      });
      
      // Update based on vote change
      if (voteType === 0 && previousVoteType === 1) {
        // Removing upvote
        newRating -= 1;
        newUpvotes -= 1;
        console.log('[VOTE] Removing upvote from statistics');
      } else if (voteType === 0 && previousVoteType === -1) {
        // Removing downvote
        newRating += 1;
        newDownvotes -= 1;
        console.log('[VOTE] Removing downvote from statistics');
      } else if (voteType === 1 && previousVoteType === -1) {
        // Changing downvote to upvote
        newRating += 2;
        newUpvotes += 1;
        newDownvotes -= 1;
        console.log('[VOTE] Changing downvote to upvote in statistics');
      } else if (voteType === 1 && previousVoteType === null) {
        // New upvote
        newRating += 1;
        newUpvotes += 1;
        console.log('[VOTE] Adding new upvote to statistics');
      } else if (voteType === -1 && previousVoteType === 1) {
        // Changing upvote to downvote
        newRating -= 2;
        newUpvotes -= 1;
        newDownvotes += 1;
        console.log('[VOTE] Changing upvote to downvote in statistics');
      } else if (voteType === -1 && previousVoteType === null) {
        // New downvote
        newRating -= 1;
        newDownvotes += 1;
        console.log('[VOTE] Adding new downvote to statistics');
      }
      
      // Update statistics in database
      const { error: updateError } = await supabase
        .from('prompt_statistics')
        .update({
          rating: newRating,
          upvotes: newUpvotes,
          downvotes: newDownvotes
        })
        .eq('id', promptId);
      
      if (updateError) {
        console.error('[VOTE] Error updating statistics:', updateError);
        return { 
          success: true,
          updatedVoteCount: newRating,
          error: 'Vote recorded but statistics update failed'
        };
      }
      
      console.log(`[VOTE] Statistics updated successfully. New rating: ${newRating}`);
      return { success: true, updatedVoteCount: newRating };
    } catch (statsError) {
      console.error('[VOTE] Error in statistics update:', statsError);
      return { 
        success: true,
        error: 'Vote recorded but statistics update encountered an error'
      };
    }
  } catch (e) {
    console.error('[VOTE] Unexpected error voting on prompt:', e);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * Get the current vote of a user on a prompt
 * @param userId The ID of the user
 * @param promptId The ID of the prompt
 * @returns The vote type (1 for upvote, -1 for downvote, null if no vote) and any error
 */
export async function getUserVoteOnPrompt(
  userId: string, 
  promptId: string
): Promise<{ voteType: 1 | -1 | null; error?: string }> {
  console.log(`[GET_VOTE] Fetching vote for userId=${userId}, promptId=${promptId}`);

  try {
    if (!userId) {
      console.log('[GET_VOTE] Error: User not logged in');
      return { voteType: null, error: 'User not logged in' };
    }

    console.log('[GET_VOTE] Querying prompt_votes table');
    // Use a regular query instead of .single() to avoid 406 errors
    const { data, error } = await supabase
      .from('prompt_votes')
      .select('vote_type')
      .eq('user_id', userId)
      .eq('prompt_id', promptId);

    if (error) {
      console.error('[GET_VOTE] Error fetching user vote:', error);
      return { voteType: null, error: error.message };
    }

    // If no data or empty array, user hasn't voted
    if (!data || data.length === 0) {
      console.log('[GET_VOTE] No vote found for this user/prompt');
      return { voteType: null };
    }

    console.log(`[GET_VOTE] Current vote type: ${data[0]?.vote_type}`);
    return { voteType: data[0]?.vote_type as 1 | -1 | null };
  } catch (e) {
    console.error(`[GET_VOTE] Unexpected error fetching user vote:`, e);
    return { voteType: null, error: 'An unexpected error occurred' };
  }
}
