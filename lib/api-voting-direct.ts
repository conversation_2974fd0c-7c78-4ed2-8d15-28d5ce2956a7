import { supabase } from "./supabase/client";

/**
 * Vote on a prompt using an RPC call to a dedicated SQL function.
 * This function updates the prompt_votes table, and then fetches the
 * new aggregated rating from the prompt_statistics view.
 * @param userId The ID of the user casting the vote
 * @param promptId The ID of the prompt being voted on
 * @param voteType The type of vote: 1 for upvote, -1 for downvote, 0 to remove vote
 * @returns Object with success flag and updated vote count (rating)
 */
export async function voteOnPromptDirect(
  userId: string,
  promptId: string,
  voteType: 1 | -1 | 0
): Promise<{ success: boolean; updatedVoteCount?: number; error?: string }> {
  console.log(`[VOTE_DIRECT] Attempting to cast vote: userId=${userId}, promptId=${promptId}, voteType=${voteType}`);

  try {
    if (!userId) {
      console.log('[VOTE_DIRECT] Error: User not logged in');
      return { success: false, error: 'User must be logged in to vote' };
    }

    // Call the existing SQL function 'vote_on_prompt'
    // This function handles the logic for inserting, updating, or deleting a vote in prompt_votes.
    // It returns true on success, false on failure (e.g. invalid vote_type or non-public prompt).
    console.log('[VOTE_DIRECT] Calling vote_on_prompt RPC function');
    const { data: rpcSuccess, error: rpcError } = await supabase.rpc('vote_on_prompt', {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_vote_type: voteType,
    });

    if (rpcError) {
      console.error('[VOTE_DIRECT] Error calling vote_on_prompt RPC:', rpcError);
      return { success: false, error: rpcError.message };
    }

    // The SQL function `vote_on_prompt` returns a boolean.
    // If it returns false, it means the operation within the function failed (e.g., prompt not public).
    if (rpcSuccess === false) {
      console.warn('[VOTE_DIRECT] vote_on_prompt RPC returned false, indicating an issue within the SQL function (e.g., prompt not public or invalid vote type).');
      return { success: false, error: 'Vote operation failed in database (e.g., prompt not public or invalid vote type).' };
    }

    console.log('[VOTE_DIRECT] Vote operation via RPC succeeded.');

    // After the vote is successfully recorded/updated/deleted in prompt_votes,
    // fetch the updated rating from the prompt_statistics view.
    // The prompt_statistics view automatically reflects changes in prompt_votes.
    console.log('[VOTE_DIRECT] Fetching updated rating from prompt_statistics');
    const { data: updatedStats, error: statsError } = await supabase
      .from('prompt_statistics')
      .select('rating') // 'rating' is the net score (upvotes - downvotes)
      .eq('id', promptId)
      .single();

    if (statsError) {
      console.error('[VOTE_DIRECT] Error fetching updated statistics:', statsError);
      // Vote was successful, but fetching new count failed.
      // Return success true, but no updatedVoteCount or an error message for this part.
      return { success: true, error: 'Vote recorded, but failed to fetch updated count.' };
    }

    const updatedRating = Number(updatedStats?.rating ?? 0);
    console.log(`[VOTE_DIRECT] Updated rating from prompt_statistics: ${updatedRating}`);

    return { success: true, updatedVoteCount: updatedRating };

  } catch (e) {
    console.error('[VOTE_DIRECT] Unexpected error voting on prompt:', e);
    const errorMessage = e instanceof Error ? e.message : 'An unexpected error occurred';
    return { success: false, error: errorMessage };
  }
}

/**
 * Get the current vote of a user on a prompt using direct SQL
 * @param userId The ID of the user
 * @param promptId The ID of the prompt
 * @returns The vote type (1 for upvote, -1 for downvote, null if no vote) and any error
 */
export async function getUserVoteOnPromptDirect(
  userId: string,
  promptId: string
): Promise<{ voteType: 1 | -1 | null; error?: string }> {
  console.log(`[GET_VOTE_DIRECT] Fetching vote for userId=${userId}, promptId=${promptId}`);

  try {
    if (!userId) {
      console.log('[GET_VOTE_DIRECT] Error: User not logged in');
      return { voteType: null, error: 'User not logged in' };
    }

    // This RPC maps to a SQL function that returns the user's vote type for a specific prompt
    console.log('[GET_VOTE_DIRECT] Querying vote type via RPC get_vote_type');
    const { data, error } = await supabase.rpc(
      'get_vote_type',
      { user_id_param: userId, prompt_id_param: promptId }
    );

    if (error) {
      console.error('[GET_VOTE_DIRECT] Error fetching user vote via RPC:', error);
      return { voteType: null, error: error.message };
    }

    // If data is null (no vote found by the RPC), it's a valid scenario.
    if (data === null) {
      console.log('[GET_VOTE_DIRECT] No vote found for this user/prompt via RPC.');
      return { voteType: null };
    }

    // Ensure we return the correct type
    const voteType = typeof data === 'number' ? (data as 1 | -1) : null;
    console.log(`[GET_VOTE_DIRECT] Current vote type from RPC: ${voteType}`);
    return { voteType };
  } catch (e) {
    console.error(`[GET_VOTE_DIRECT] Unexpected error fetching user vote:`, e);
    const errorMessage = e instanceof Error ? e.message : 'An unexpected error occurred';
    return { voteType: null, error: errorMessage };
  }
}
