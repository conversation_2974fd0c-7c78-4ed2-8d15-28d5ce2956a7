import { z } from "zod"

// Common validation patterns
const emailValidation = z
  .string()
  .min(1, "Email is required")
  .email("Please enter a valid email address")
  .max(255, "Email is too long")

const passwordValidation = z
  .string()
  .min(8, "Password must be at least 8 characters long")
  .max(128, "Password is too long")
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    "Password must contain at least one lowercase letter, one uppercase letter, and one number"
  )

const usernameValidation = z
  .string()
  .min(3, "Username must be at least 3 characters long")
  .max(20, "Username must be no more than 20 characters long")
  .regex(
    /^[a-zA-Z0-9_-]+$/,
    "Username can only contain letters, numbers, underscores, and hyphens"
  )
  .transform((val) => val.trim())

// Base URL validation with protocol check
const createUrlValidation = (urlMessage = "Please enter a valid URL", protocolMessage = "URL must use HTTP or HTTPS protocol") =>
  z
    .string()
    .url(urlMessage)
    .refine(
      (url) => {
        try {
          const parsed = new URL(url)
          return ['http:', 'https:'].includes(parsed.protocol)
        } catch {
          return false
        }
      },
      protocolMessage
    )

const urlValidation = createUrlValidation()
  .optional()
  .or(z.literal(""))

const avatarUrlValidation = createUrlValidation(
  "Avatar URL must be a valid URL",
  "Avatar URL must use HTTP or HTTPS protocol"
)
  .optional()
  .nullable()
  .or(z.literal(""))

// User registration schema
export const signUpSchema = z.object({
  email: emailValidation,
  password: passwordValidation,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// User sign-in schema
export const signInSchema = z.object({
  email: emailValidation,
  password: z.string().min(1, "Password is required")
})

// Email validation schema for forgot password
export const forgotPasswordSchema = z.object({
  email: emailValidation
})

// Password reset schema
export const resetPasswordSchema = z.object({
  password: passwordValidation,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// Username setup/update schema
export const usernameSchema = z.object({
  username: usernameValidation
})

// Profile update schema - matches Profile interface from lib/types.ts
export const profileUpdateSchema = z.object({
  username: usernameValidation,
  bio: z.string().max(500, "Bio must be no more than 500 characters").optional().nullable(),
  avatar_url: avatarUrlValidation,
  website_url: urlValidation,
  github_url: urlValidation,
  x_url: urlValidation,
  youtube_url: urlValidation,
  // Notification preferences
  email_on_comment: z.boolean().optional(),
  email_on_reply: z.boolean().optional(),
  email_on_like: z.boolean().optional(),
  email_product_updates: z.boolean().optional()
})

// Partial profile update schema for settings page
export const profilePartialUpdateSchema = profileUpdateSchema.partial()

// User data validation schema - for validating the UserWithProfile structure
export const userWithProfileSchema = z.object({
  id: z.string().uuid("Invalid user ID"),
  email: emailValidation.optional(),
  profile: z.object({
    id: z.string().uuid("Invalid profile ID"),
    username: usernameValidation,
    avatar_url: avatarUrlValidation,
    bio: z.string().max(500).optional().nullable(),
    website_url: urlValidation,
    github_url: urlValidation,
    x_url: urlValidation,
    youtube_url: urlValidation,
    is_username_customized: z.boolean().optional(),
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
    total_likes_received: z.number().optional(),
    public_prompts_count: z.number().optional(),
    public_collections_count: z.number().optional(),
    email_on_comment: z.boolean().optional(),
    email_on_reply: z.boolean().optional(),
    email_on_like: z.boolean().optional(),
    email_product_updates: z.boolean().optional()
  }).optional().nullable()
})

// Type exports
export type SignUpInput = z.infer<typeof signUpSchema>
export type SignInInput = z.infer<typeof signInSchema>
export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>
export type UsernameInput = z.infer<typeof usernameSchema>
export type ProfileUpdateInput = z.infer<typeof profileUpdateSchema>
export type ProfilePartialUpdateInput = z.infer<typeof profilePartialUpdateSchema>
export type UserWithProfileInput = z.infer<typeof userWithProfileSchema>
