// This file contains the list of AI tools available for prompts
// Admins can easily update this list by modifying this file

export interface Tool {
  id: string
  name: string
  slug: string
  promptCount?: number
  icon?: string
  description?: string
  website?: string
}

// List of all available AI tools
export const allTools: Tool[] = [
  { id: "1", name: "ChatGPT", slug: "chatgpt", promptCount: 215, icon: "💬" },
  { id: "2", name: "<PERSON>", slug: "claude", promptCount: 187, icon: "🧠" },
  { id: "3", name: "<PERSON>", slug: "gemini", promptCount: 98, icon: "♊" },
  { id: "4", name: "Midjourney", slug: "midjourney", promptCount: 156, icon: "🎨" },
  { id: "5", name: "Grok", slug: "grok", promptCount: 76, icon: "✖️" },
  { id: "6", name: "Stable Diffusion", slug: "stable-diffusion", promptCount: 112, icon: "🌈" },
  { id: "7", name: "D<PERSON><PERSON>-<PERSON>", slug: "dall-e", promptCount: 134, icon: "🖼️" },
  { id: "8", name: "ElevenL<PERSON><PERSON>", slug: "elevenlabs", promptCount: 65, icon: "🎙️" },
  { id: "9", name: "Suno", slug: "suno", promptCount: 42, icon: "🎵" },
  { id: "10", name: "Runway", slug: "runway", promptCount: 58, icon: "🎬" },
  { id: "11", name: "Pika Labs", slug: "pika-labs", promptCount: 37, icon: "⚡" },
  { id: "12", name: "Sora", slug: "sora", promptCount: 45, icon: "🎥" },
  { id: "13", name: "Jasper AI", slug: "jasper-ai", promptCount: 52, icon: "✍️" },
  { id: "14", name: "Perplexity AI", slug: "perplexity-ai", promptCount: 48, icon: "🔍" },
  { id: "15", name: "DeepSeek", slug: "deepseek", promptCount: 39, icon: "🐳" },
  { id: "16", name: "Llama", slug: "llama", promptCount: 76, icon: "🦙" },
  { id: "17", name: "Other", slug: "other", promptCount: 54, icon: "⚙️" },
]

// Helper function to get popular tools (for homepage)
export const getPopularTools = (count = 6): Tool[] => {
  return allTools.sort((a, b) => (b.promptCount || 0) - (a.promptCount || 0)).slice(0, count)
}

// Helper function to get a tool by ID
export const getToolById = (id: string): Tool | undefined => {
  return allTools.find((tool) => tool.id === id)
}

// Helper function to get a tool by slug
export const getToolBySlug = (slug: string): Tool | undefined => {
  return allTools.find((tool) => tool.slug === slug)
}
