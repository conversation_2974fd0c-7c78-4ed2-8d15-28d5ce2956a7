// This file contains the color definitions for each category
// These colors are used consistently across the application for thumbnails, tags, and other UI elements

export interface CategoryColor {
  name: string
  slug: string
  primary: string
  secondary: string
  gradient: string
  textColor: string
  darkGradient: string
  darkTextColor: string
  iconColor: string
  iconTextColor: string // Added new property
  badgeColor: string
}

export const categoryColors: CategoryColor[] = [
  {
    name: "Code Generation",
    slug: "code-generation",
    primary: "#60a5fa", // blue-400
    secondary: "#3b82f6", // blue-500
    gradient: "from-blue-500 to-blue-700",
    textColor: "text-blue-500",
    darkGradient: "bg-gradient-code-generation", // Updated to custom class
    darkTextColor: "text-blue-100",
    iconColor: "text-blue-500", // Keep for now
    iconTextColor: "text-blue-200", // Updated to match reference
    badgeColor: "bg-blue-500/20 text-blue-300",
  },
  {
    name: "Creative Writing",
    slug: "creative-writing",
    primary: "#4ade80", // green-400
    secondary: "#22c55e", // green-500
    gradient: "from-green-500 to-green-700",
    textColor: "text-green-500",
    darkGradient: "bg-gradient-creative-writing", // Updated to custom class
    darkTextColor: "text-green-100",
    iconColor: "text-green-500", // Keep for now
    iconTextColor: "text-green-200", // Updated to match reference
    badgeColor: "bg-green-500/20 text-green-300",
  },
  {
    name: "Marketing",
    slug: "marketing",
    primary: "#f472b6", // pink-400
    secondary: "#ec4899", // pink-500
    gradient: "from-pink-500 to-pink-700",
    textColor: "text-pink-500",
    darkGradient: "bg-gradient-marketing", // Updated to custom class
    darkTextColor: "text-pink-100",
    iconColor: "text-pink-500", // Keep for now
    iconTextColor: "text-pink-200", // Updated to match reference
    badgeColor: "bg-pink-500/20 text-pink-300",
  },
  {
    name: "Image Generation",
    slug: "image-generation",
    primary: "#c084fc", // purple-400
    secondary: "#a855f7", // purple-500
    gradient: "from-purple-500 to-purple-700",
    textColor: "text-purple-500",
    darkGradient: "bg-gradient-image-generation", // Updated to custom class
    darkTextColor: "text-purple-100",
    iconColor: "text-purple-500", // Keep for now
    iconTextColor: "text-purple-200", // Updated to match reference
    badgeColor: "bg-purple-500/20 text-purple-300",
  },
  {
    name: "Data Analysis",
    slug: "data-analysis",
    primary: "#2dd4bf", // teal-400
    secondary: "#14b8a6", // teal-500
    gradient: "from-teal-500 to-teal-700",
    textColor: "text-teal-500",
    darkGradient: "bg-gradient-data-analysis", // Updated to custom class
    darkTextColor: "text-teal-100",
    iconColor: "text-teal-500", // Keep for now
    iconTextColor: "text-teal-200", // Updated to match reference
    badgeColor: "bg-teal-500/20 text-teal-300",
  },
  {
    name: "Business",
    slug: "business",
    primary: "#38bdf8", // sky-400
    secondary: "#0ea5e9", // sky-500
    gradient: "from-sky-500 to-sky-700",
    textColor: "text-sky-500",
    darkGradient: "bg-gradient-business", // Updated to custom class
    darkTextColor: "text-sky-100",
    iconColor: "text-sky-500", // Keep for now
    iconTextColor: "text-sky-200", // Updated to match reference
    badgeColor: "bg-sky-500/20 text-sky-300",
  },
  {
    name: "Education",
    slug: "education",
    primary: "#a3e635", // lime-400
    secondary: "#84cc16", // lime-500
    gradient: "from-lime-500 to-lime-700",
    textColor: "text-lime-500",
    darkGradient: "bg-gradient-education", // Updated to custom class
    darkTextColor: "text-lime-100",
    iconColor: "text-lime-500", // Keep for now
    iconTextColor: "text-lime-200", // Updated to match reference
    badgeColor: "bg-lime-500/20 text-lime-300",
  },
  {
    name: "Personal",
    slug: "personal",
    primary: "#a78bfa", // violet-400
    secondary: "#8b5cf6", // violet-500
    gradient: "from-violet-500 to-violet-700",
    textColor: "text-violet-500",
    darkGradient: "bg-gradient-personal", // Updated to custom class
    darkTextColor: "text-violet-100",
    iconColor: "text-violet-500", // Keep for now
    iconTextColor: "text-violet-200", // Updated to match reference
    badgeColor: "bg-violet-500/20 text-violet-300",
  },
  {
    name: "Research",
    slug: "research",
    primary: "#22d3ee", // cyan-400
    secondary: "#06b6d4", // cyan-500
    gradient: "from-cyan-500 to-cyan-700",
    textColor: "text-cyan-500",
    darkGradient: "bg-gradient-research", // Updated to custom class
    darkTextColor: "text-cyan-100",
    iconColor: "text-cyan-500", // Keep for now
    iconTextColor: "text-cyan-200", // Updated to match reference
    badgeColor: "bg-cyan-500/20 text-cyan-300",
  },
  {
    name: "Social Media",
    slug: "social-media",
    primary: "#f472b6", // pink-400 (using pink from reference)
    secondary: "#ec4899", // pink-500 (using pink from reference)
    gradient: "from-pink-400 to-pink-600",
    textColor: "text-pink-500",
    darkGradient: "bg-gradient-social-media", // Updated to custom class
    darkTextColor: "text-pink-100",
    iconColor: "text-pink-500", // Keep for now
    iconTextColor: "text-pink-100", // Updated to match reference
    badgeColor: "bg-pink-500/20 text-pink-300",
  },
  {
    name: "Email",
    slug: "email",
    primary: "#fb923c", // orange-400
    secondary: "#f97316", // orange-500
    gradient: "from-orange-500 to-orange-700",
    textColor: "text-orange-500",
    darkGradient: "bg-gradient-email", // Updated to custom class
    darkTextColor: "text-orange-100",
    iconColor: "text-orange-500", // Keep for now
    iconTextColor: "text-orange-200", // Updated to match reference
    badgeColor: "bg-orange-500/20 text-orange-300",
  },
  {
    name: "SEO",
    slug: "seo",
    primary: "#34d399", // emerald-400
    secondary: "#10b981", // emerald-500
    gradient: "from-emerald-500 to-emerald-700",
    textColor: "text-emerald-500",
    darkGradient: "bg-gradient-seo", // Updated to custom class
    darkTextColor: "text-emerald-100",
    iconColor: "text-emerald-500", // Keep for now
    iconTextColor: "text-emerald-200", // Updated to match reference
    badgeColor: "bg-emerald-500/20 text-emerald-300",
  },
  {
    name: "Copywriting",
    slug: "copywriting",
    primary: "#d946ef", // fuchsia-500 (using fuchsia from reference)
    secondary: "#c084fc", // purple-400 (keeping purple secondary for now, could adjust later if needed)
    gradient: "from-fuchsia-500 to-fuchsia-700",
    textColor: "text-fuchsia-500",
    darkGradient: "bg-gradient-copywriting", // Updated to custom class
    darkTextColor: "text-fuchsia-100",
    iconColor: "text-fuchsia-500", // Keep for now
    iconTextColor: "text-fuchsia-200", // Updated to match reference
    badgeColor: "bg-fuchsia-500/20 text-fuchsia-300", // using fuchsia from reference
  },
  {
    name: "Fiction",
    slug: "fiction",
    primary: "#a78bfa", // violet-400
    secondary: "#8b5cf6", // violet-500
    gradient: "from-violet-500 to-violet-700",
    textColor: "text-violet-500",
    darkGradient: "bg-gradient-fiction", // Updated to custom class
    darkTextColor: "text-violet-100",
    iconColor: "text-violet-500", // Keep for now
    iconTextColor: "text-violet-200", // Updated to match reference
    badgeColor: "bg-violet-500/20 text-violet-300",
  },
  {
    name: "Non-Fiction",
    slug: "non-fiction",
    primary: "#818cf8", // indigo-400
    secondary: "#6366f1", // indigo-500
    gradient: "from-indigo-500 to-indigo-700",
    textColor: "text-indigo-500",
    darkGradient: "bg-gradient-non-fiction", // Updated to custom class
    darkTextColor: "text-indigo-100",
    iconColor: "text-indigo-500", // Keep for now
    iconTextColor: "text-indigo-200", // Updated to match reference
    badgeColor: "bg-indigo-500/20 text-indigo-300",
  },
  {
    name: "Audio",
    slug: "audio",
    primary: "#c084fc", // purple-400
    secondary: "#a855f7", // purple-500
    gradient: "from-purple-500 to-purple-700",
    textColor: "text-purple-500",
    darkGradient: "bg-gradient-audio", // Updated to custom class
    darkTextColor: "text-purple-100",
    iconColor: "text-purple-500", // Keep for now
    iconTextColor: "text-purple-200", // Updated to match reference
    badgeColor: "bg-purple-500/20 text-purple-300",
  },
  {
    name: "Video",
    slug: "video",
    primary: "#f87171", // red-400
    secondary: "#ef4444", // red-500
    gradient: "from-red-500 to-red-700",
    textColor: "text-red-500",
    darkGradient: "bg-gradient-video", // Updated to custom class
    darkTextColor: "text-red-100",
    iconColor: "text-red-500", // Keep for now
    iconTextColor: "text-red-200", // Updated to match reference
    badgeColor: "bg-red-500/20 text-red-300",
  },
  {
    name: "Music",
    slug: "music",
    primary: "#c084fc", // purple-400
    secondary: "#a855f7", // purple-500
    gradient: "from-purple-500 to-purple-700", // Updated to match other purple categories
    textColor: "text-purple-500",
    darkGradient: "bg-gradient-music", // Updated to custom class
    darkTextColor: "text-purple-100",
    iconColor: "text-purple-500", // Keep for now
    iconTextColor: "text-purple-200", // Updated to match other purple categories
    badgeColor: "bg-purple-500/20 text-purple-300",
  },
  {
    name: "Other",
    slug: "other",
    primary: "#94A3B8",
    secondary: "#64748B",
    gradient: "from-slate-500 to-slate-700",
    textColor: "text-slate-500",
    darkGradient: "bg-gradient-other", // Updated to custom class
    darkTextColor: "text-slate-100",
    iconColor: "text-slate-500", // Keep for now
    iconTextColor: "text-slate-200", // Updated to match reference
    badgeColor: "bg-slate-500/15 text-slate-400",
  },
]

// Helper function to get colors by category name
export const getCategoryColorByName = (name: string): CategoryColor => {
  const normalizedName = name.toLowerCase()
  const category = categoryColors.find(
    (cat) =>
      cat.name.toLowerCase() === normalizedName ||
      cat.slug === normalizedName ||
      normalizedName.includes(cat.slug) ||
      cat.slug.includes(normalizedName),
  )

  return category || categoryColors[categoryColors.length - 1] // Return "Other" as fallback
}

// Helper function to get colors by category slug
export const getCategoryColorBySlug = (slug: string): CategoryColor => {
  const category = categoryColors.find((cat) => cat.slug === slug)
  return category || categoryColors[categoryColors.length - 1] // Return "Other" as fallback
}
