// This file contains the list of tags available for prompts
// Admins can easily update this list by modifying this file

export interface Tag {
  id: string
  name: string
  slug: string
  promptCount?: number
  category?: string // Optional grouping for tags
}

// List of all available tags for prompts
export const allTags: Tag[] = [
  { id: "1", name: "3D", slug: "3d", promptCount: 42 },
  { id: "2", name: "3D Render", slug: "3d-render", promptCount: 38 },
  { id: "3", name: "Abstract", slug: "abstract", promptCount: 56 },
  { id: "4", name: "Academic", slug: "academic", promptCount: 63 },
  { id: "5", name: "Ad Copy", slug: "ad-copy", promptCount: 74 },
  { id: "6", name: "Advanced", slug: "advanced", promptCount: 89 },
  { id: "7", name: "Adventure", slug: "adventure", promptCount: 45 },
  { id: "8", name: "Algorith<PERSON>", slug: "algorithm", promptCount: 67 },
  { id: "9", name: "Analyze", slug: "analyze", promptCount: 82 },
  { id: "10", name: "Analytics", slug: "analytics", promptCount: 71 },
  { id: "11", name: "Animation", slug: "animation", promptCount: 53 },
  { id: "12", name: "API", slug: "api", promptCount: 64 },
  { id: "13", name: "API Response", slug: "api-response", promptCount: 48 },
  { id: "14", name: "Art & Design", slug: "art-design", promptCount: 92 },
  { id: "15", name: "Article", slug: "article", promptCount: 87 },
  { id: "16", name: "Artistic Style", slug: "artistic-style", promptCount: 76 },
  { id: "17", name: "Aspect Ratio", slug: "aspect-ratio", promptCount: 43 },
  { id: "18", name: "Audio", slug: "audio", promptCount: 58 },
  { id: "19", name: "Automation", slug: "automation", promptCount: 79 },
  { id: "20", name: "Beginner", slug: "beginner", promptCount: 94 },
  { id: "21", name: "Best Practices", slug: "best-practices", promptCount: 86 },
  { id: "22", name: "Big Data", slug: "big-data", promptCount: 68 },
  { id: "23", name: "Blog Post", slug: "blog-post", promptCount: 91 },
  { id: "24", name: "Brainstorm", slug: "brainstorm", promptCount: 87 },
  { id: "25", name: "Brand Voice", slug: "brand-voice", promptCount: 72 },
  { id: "26", name: "Branding", slug: "branding", promptCount: 83 },
  { id: "27", name: "Business", slug: "business", promptCount: 95 },
  { id: "28", name: "C#", slug: "c-sharp", promptCount: 57 },
  { id: "29", name: "C++", slug: "cpp", promptCount: 61 },
  { id: "30", name: "Case Study", slug: "case-study", promptCount: 68 },
  { id: "31", name: "Casual", slug: "casual", promptCount: 54 },
  { id: "32", name: "Categorization", slug: "categorization", promptCount: 47 },
  { id: "33", name: "Chain-of-Thought", slug: "chain-of-thought", promptCount: 63 },
  { id: "34", name: "Challenge", slug: "challenge", promptCount: 59 },
  { id: "35", name: "Chaos", slug: "chaos", promptCount: 41 },
  { id: "36", name: "Character", slug: "character", promptCount: 73 },
  { id: "37", name: "Checklist", slug: "checklist", promptCount: 65 },
  { id: "38", name: "Cinematic", slug: "cinematic", promptCount: 57 },
  { id: "39", name: "Classify", slug: "classify", promptCount: 49 },
  { id: "40", name: "Claude", slug: "claude", promptCount: 78 },
  { id: "41", name: "Code Development", slug: "code-development", promptCount: 84 },
  { id: "42", name: "Code Snippet", slug: "code-snippet", promptCount: 76 },
  { id: "43", name: "Comedy", slug: "comedy", promptCount: 62 },
  { id: "44", name: "Compare", slug: "compare", promptCount: 58 },
  { id: "45", name: "Comparison", slug: "comparison", promptCount: 64 },
  { id: "46", name: "Complex Task", slug: "complex-task", promptCount: 71 },
  { id: "47", name: "Concise", slug: "concise", promptCount: 83 },
  { id: "48", name: "Concept Art", slug: "concept-art", promptCount: 69 },
  { id: "49", name: "Constraint Based", slug: "constraint-based", promptCount: 52 },
  { id: "50", name: "Content Creation", slug: "content-creation", promptCount: 93 },
  { id: "51", name: "Content Marketing", slug: "content-marketing", promptCount: 87 },
  { id: "52", name: "Context Setting", slug: "context-setting", promptCount: 64 },
  { id: "53", name: "Conversion", slug: "conversion", promptCount: 73 },
  { id: "54", name: "Create", slug: "create", promptCount: 96 },
  { id: "55", name: "Creative", slug: "creative", promptCount: 98 },
  { id: "56", name: "Creative Exploration", slug: "creative-exploration", promptCount: 81 },
  { id: "57", name: "Critique", slug: "critique", promptCount: 67 },
  { id: "58", name: "CSS", slug: "css", promptCount: 72 },
  { id: "59", name: "Customization", slug: "customization", promptCount: 78 },
  { id: "60", name: "Customer Service", slug: "customer-service", promptCount: 69 },
  { id: "61", name: "Cyberpunk", slug: "cyberpunk", promptCount: 54 },
  { id: "62", name: "Cybersecurity", slug: "cybersecurity", promptCount: 63 },
  { id: "63", name: "Data Cleaning", slug: "data-cleaning", promptCount: 58 },
  { id: "64", name: "Data Ethics", slug: "data-ethics", promptCount: 47 },
  { id: "65", name: "Data Science", slug: "data-science", promptCount: 82 },
  { id: "66", name: "Data Structure", slug: "data-structure", promptCount: 71 },
  { id: "67", name: "Data Visualization", slug: "data-visualization", promptCount: 76 },
  { id: "68", name: "Debugging", slug: "debugging", promptCount: 79 },
  { id: "69", name: "Deep Learning", slug: "deep-learning", promptCount: 68 },
  { id: "70", name: "Detailed", slug: "detailed", promptCount: 87 },
  { id: "71", name: "Dialogue", slug: "dialogue", promptCount: 73 },
  { id: "72", name: "Draft", slug: "draft", promptCount: 81 },
  { id: "73", name: "Drama", slug: "drama", promptCount: 59 },
  { id: "74", name: "E-commerce", slug: "e-commerce", promptCount: 78 },
  { id: "75", name: "Edit", slug: "edit", promptCount: 86 },
  { id: "76", name: "Education", slug: "education", promptCount: 91 },
  { id: "77", name: "Educational", slug: "educational", promptCount: 89 },
  { id: "78", name: "Efficiency Boost", slug: "efficiency-boost", promptCount: 74 },
  { id: "79", name: "Elixir", slug: "elixir", promptCount: 43 },
  { id: "80", name: "Email", slug: "email", promptCount: 85 },
  { id: "81", name: "Email Format", slug: "email-format", promptCount: 76 },
  { id: "82", name: "Email Marketing", slug: "email-marketing", promptCount: 79 },
  { id: "83", name: "Empathetic", slug: "empathetic", promptCount: 62 },
  { id: "84", name: "Engagement", slug: "engagement", promptCount: 77 },
  { id: "85", name: "Entrepreneurship", slug: "entrepreneurship", promptCount: 71 },
  { id: "86", name: "Essay", slug: "essay", promptCount: 83 },
  { id: "87", name: "Example", slug: "example", promptCount: 92 },
  { id: "88", name: "Example Driven", slug: "example-driven", promptCount: 68 },
  { id: "89", name: "Explain", slug: "explain", promptCount: 94 },
  { id: "90", name: "Experiment", slug: "experiment", promptCount: 63 },
  { id: "91", name: "Expert", slug: "expert", promptCount: 81 },
  { id: "92", name: "Extract", slug: "extract", promptCount: 72 },
  { id: "93", name: "Fantasy", slug: "fantasy", promptCount: 67 },
  { id: "94", name: "FAQ", slug: "faq", promptCount: 78 },
  { id: "95", name: "Fashion", slug: "fashion", promptCount: 59 },
  { id: "96", name: "Feature Engineering", slug: "feature-engineering", promptCount: 53 },
  { id: "97", name: "Few-Shot", slug: "few-shot", promptCount: 61 },
  { id: "98", name: "Film & TV", slug: "film-tv", promptCount: 73 },
  { id: "99", name: "Finance", slug: "finance", promptCount: 76 },
  { id: "100", name: "Flashcards", slug: "flashcards", promptCount: 57 },
  { id: "101", name: "Food & Drink", slug: "food-drink", promptCount: 64 },
  { id: "102", name: "Formal", slug: "formal", promptCount: 71 },
  { id: "103", name: "Format", slug: "format", promptCount: 83 },
  { id: "104", name: "Framework", slug: "framework", promptCount: 75 },
  { id: "105", name: "Gaming", slug: "gaming", promptCount: 68 },
  { id: "106", name: "Generate", slug: "generate", promptCount: 97 },
  { id: "107", name: "Go", slug: "go", promptCount: 54 },
  { id: "108", name: "Guide", slug: "guide", promptCount: 86 },
  { id: "109", name: "Graphic Design", slug: "graphic-design", promptCount: 79 },
  { id: "110", name: "Haskell", slug: "haskell", promptCount: 41 },
  { id: "111", name: "Healthcare", slug: "healthcare", promptCount: 67 },
  { id: "112", name: "Historical", slug: "historical", promptCount: 63 },
  { id: "113", name: "History", slug: "history", promptCount: 72 },
  { id: "114", name: "Homework", slug: "homework", promptCount: 81 },
  { id: "115", name: "Horror", slug: "horror", promptCount: 58 },
  { id: "116", name: "HR", slug: "hr", promptCount: 65 },
  { id: "117", name: "HTML", slug: "html", promptCount: 77 },
  { id: "118", name: "Humanities", slug: "humanities", promptCount: 69 },
  { id: "119", name: "Humorous", slug: "humorous", promptCount: 74 },
  { id: "120", name: "Ideate", slug: "ideate", promptCount: 68 },
  { id: "121", name: "Illustration", slug: "illustration", promptCount: 73 },
  { id: "122", name: "Image", slug: "image", promptCount: 89 },
  { id: "123", name: "Image Prompt", slug: "image-prompt", promptCount: 84 },
  { id: "124", name: "Image Weight", slug: "image-weight", promptCount: 51 },
  { id: "125", name: "In-Depth Analysis", slug: "in-depth-analysis", promptCount: 76 },
  { id: "126", name: "Influencer Marketing", slug: "influencer-marketing", promptCount: 67 },
  { id: "127", name: "Infographic", slug: "infographic", promptCount: 72 },
  { id: "128", name: "Inspiration", slug: "inspiration", promptCount: 83 },
  { id: "129", name: "Instruct", slug: "instruct", promptCount: 79 },
  { id: "130", name: "Instructional", slug: "instructional", promptCount: 81 },
  { id: "131", name: "Intermediate", slug: "intermediate", promptCount: 74 },
  { id: "132", name: "Java", slug: "java", promptCount: 68 },
  { id: "133", name: "JavaScript", slug: "javascript", promptCount: 87 },
  { id: "134", name: "JSON", slug: "json", promptCount: 73 },
  { id: "135", name: "Julia", slug: "julia", promptCount: 42 },
  { id: "136", name: "Keywords", slug: "keywords", promptCount: 78 },
  { id: "137", name: "Landing Page", slug: "landing-page", promptCount: 76 },
  { id: "138", name: "Landscape", slug: "landscape", promptCount: 69 },
  { id: "139", name: "Language Learning", slug: "language-learning", promptCount: 71 },
  { id: "140", name: "Leadership", slug: "leadership", promptCount: 64 },
  { id: "141", name: "Learning", slug: "learning", promptCount: 88 },
  { id: "142", name: "Legal", slug: "legal", promptCount: 62 },
  { id: "143", name: "Length", slug: "length", promptCount: 57 },
  { id: "144", name: "Lesson Plan", slug: "lesson-plan", promptCount: 73 },
  { id: "145", name: "Library Management", slug: "library-management", promptCount: 48 },
  { id: "146", name: "Lifestyle", slug: "lifestyle", promptCount: 67 },
  { id: "147", name: "Lisp", slug: "lisp", promptCount: 39 },
  { id: "148", name: "List", slug: "list", promptCount: 82 },
  { id: "149", name: "Literature", slug: "literature", promptCount: 71 },
  { id: "150", name: "Logo", slug: "logo", promptCount: 76 },
  { id: "151", name: "Machine Learning", slug: "machine-learning", promptCount: 79 },
  { id: "152", name: "Markdown", slug: "markdown", promptCount: 68 },
  { id: "153", name: "Marketing & Sales", slug: "marketing-sales", promptCount: 86 },
  { id: "154", name: "MATLAB", slug: "matlab", promptCount: 47 },
  { id: "155", name: "Metadata", slug: "metadata", promptCount: 53 },
  { id: "156", name: "Midjourney", slug: "midjourney", promptCount: 74 },
  { id: "157", name: "Minimalist", slug: "minimalist", promptCount: 68 },
  { id: "158", name: "Mojo", slug: "mojo", promptCount: 41 },
  { id: "159", name: "Monochromatic", slug: "monochromatic", promptCount: 54 },
  { id: "160", name: "Music & Audio", slug: "music-audio", promptCount: 72 },
  { id: "161", name: "Muted", slug: "muted", promptCount: 46 },
  { id: "162", name: "Mystery", slug: "mystery", promptCount: 63 },
  { id: "163", name: "Negative Prompt", slug: "negative-prompt", promptCount: 59 },
  { id: "164", name: "Neon", slug: "neon", promptCount: 52 },
  { id: "165", name: "Newsletter", slug: "newsletter", promptCount: 74 },
  { id: "166", name: "NLP", slug: "nlp", promptCount: 67 },
  { id: "167", name: "Objective", slug: "objective", promptCount: 71 },
  { id: "168", name: "One-Pager", slug: "one-pager", promptCount: 68 },
  { id: "169", name: "Online Course", slug: "online-course", promptCount: 73 },
  { id: "170", name: "Operations", slug: "operations", promptCount: 61 },
  { id: "171", name: "Optimize", slug: "optimize", promptCount: 78 },
  { id: "172", name: "Outline", slug: "outline", promptCount: 84 },
  { id: "173", name: "Parameters", slug: "parameters", promptCount: 63 },
  { id: "174", name: "Pastel", slug: "pastel", promptCount: 51 },
  { id: "175", name: "Persona", slug: "persona", promptCount: 72 },
  { id: "176", name: "Persuasive", slug: "persuasive", promptCount: 79 },
  { id: "177", name: "Photorealistic", slug: "photorealistic", promptCount: 76 },
  { id: "178", name: "Pixel Art", slug: "pixel-art", promptCount: 64 },
  { id: "179", name: "Plan", slug: "plan", promptCount: 83 },
  { id: "180", name: "Planning", slug: "planning", promptCount: 81 },
  { id: "181", name: "Plot", slug: "plot", promptCount: 74 },
  { id: "182", name: "Poem", slug: "poem", promptCount: 67 },
  { id: "183", name: "Portrait", slug: "portrait", promptCount: 71 },
  { id: "184", name: "Predictive Modeling", slug: "predictive-modeling", promptCount: 58 },
  { id: "185", name: "Problem Solving", slug: "problem-solving", promptCount: 86 },
  { id: "186", name: "Product Description", slug: "product-description", promptCount: 79 },
  { id: "187", name: "Productivity", slug: "productivity", promptCount: 87 },
  { id: "188", name: "Professional", slug: "professional", promptCount: 82 },
  { id: "189", name: "Project Management", slug: "project-management", promptCount: 76 },
  { id: "190", name: "Prolog", slug: "prolog", promptCount: 38 },
  { id: "191", name: "Proposal", slug: "proposal", promptCount: 73 },
  { id: "192", name: "Python", slug: "python", promptCount: 89 },
  { id: "193", name: "Q&A", slug: "q-and-a", promptCount: 84 },
  { id: "194", name: "Quick Start", slug: "quick-start", promptCount: 76 },
  { id: "195", name: "Quiz", slug: "quiz", promptCount: 71 },
  { id: "196", name: "R", slug: "r", promptCount: 53 },
  { id: "197", name: "Real Estate", slug: "real-estate", promptCount: 64 },
  { id: "198", name: "Realistic", slug: "realistic", promptCount: 78 },
  { id: "199", name: "Refinement", slug: "refinement", promptCount: 69 },
  { id: "200", name: "Report", slug: "report", promptCount: 81 },
  { id: "201", name: "Research", slug: "research", promptCount: 87 },
  { id: "202", name: "Review", slug: "review", promptCount: 76 },
  { id: "203", name: "Rewrite", slug: "rewrite", promptCount: 83 },
  { id: "204", name: "Role", slug: "role", promptCount: 72 },
  { id: "205", name: "Role Play", slug: "role-play", promptCount: 68 },
  { id: "206", name: "Romance", slug: "romance", promptCount: 64 },
  { id: "207", name: "Ruby", slug: "ruby", promptCount: 57 },
  { id: "208", name: "Rust", slug: "rust", promptCount: 61 },
  { id: "209", name: "Sales", slug: "sales", promptCount: 78 },
  { id: "210", name: "Scala", slug: "scala", promptCount: 46 },
  { id: "211", name: "Science", slug: "science", promptCount: 79 },
  { id: "212", name: "Science Fiction", slug: "science-fiction", promptCount: 71 },
  { id: "213", name: "Science & Tech", slug: "science-tech", promptCount: 82 },
  { id: "214", name: "Script", slug: "script", promptCount: 76 },
  { id: "215", name: "Seed", slug: "seed", promptCount: 54 },
  { id: "216", name: "SEO", slug: "seo", promptCount: 84 },
  { id: "217", name: "SEO Content", slug: "seo-content", promptCount: 79 },
  { id: "218", name: "Setting", slug: "setting", promptCount: 67 },
  { id: "219", name: "Simple Task", slug: "simple-task", promptCount: 73 },
  { id: "220", name: "Smalltalk", slug: "smalltalk", promptCount: 58 },
  { id: "221", name: "Social Media", slug: "social-media", promptCount: 86 },
  { id: "222", name: "Social Media Post", slug: "social-media-post", promptCount: 81 },
  { id: "223", name: "SQL", slug: "sql", promptCount: 74 },
  { id: "224", name: "Statistics", slug: "statistics", promptCount: 69 },
  { id: "225", name: "Steampunk", slug: "steampunk", promptCount: 53 },
  { id: "226", name: "STEM", slug: "stem", promptCount: 76 },
  { id: "227", name: "Step-by-Step", slug: "step-by-step", promptCount: 87 },
  { id: "228", name: "Story", slug: "story", promptCount: 82 },
  { id: "229", name: "Strategy", slug: "strategy", promptCount: 78 },
  { id: "230", name: "Study Guide", slug: "study-guide", promptCount: 73 },
  { id: "231", name: "Style", slug: "style", promptCount: 79 },
  { id: "232", name: "Stylize", slug: "stylize", promptCount: 71 },
  { id: "233", name: "Summarize", slug: "summarize", promptCount: 88 },
  { id: "234", name: "Survey Questions", slug: "survey-questions", promptCount: 64 },
  { id: "235", name: "Swift", slug: "swift", promptCount: 59 },
  { id: "236", name: "Table", slug: "table", promptCount: 72 },
  { id: "237", name: "Tagging", slug: "tagging", promptCount: 63 },
  { id: "238", name: "Task", slug: "task", promptCount: 81 },
  { id: "239", name: "Technical", slug: "technical", promptCount: 77 },
  { id: "240", name: "Technology", slug: "technology", promptCount: 84 },
  { id: "241", name: "Template", slug: "template", promptCount: 86 },
  { id: "242", name: "Template Based", slug: "template-based", promptCount: 74 },
  { id: "243", name: "Test Prep", slug: "test-prep", promptCount: 68 },
  { id: "244", name: "Testing", slug: "testing", promptCount: 73 },
  { id: "245", name: "Text Document", slug: "text-document", promptCount: 79 },
  { id: "246", name: "Theme", slug: "theme", promptCount: 76 },
  { id: "247", name: "Thriller", slug: "thriller", promptCount: 61 },
  { id: "248", name: "Time Series", slug: "time-series", promptCount: 57 },
  { id: "249", name: "Tips", slug: "tips", promptCount: 82 },
  { id: "250", name: "Tone", slug: "tone", promptCount: 78 },
  { id: "251", name: "Training Document", slug: "training-document", promptCount: 69 },
  { id: "252", name: "Translate", slug: "translate", promptCount: 83 },
  { id: "253", name: "Travel", slug: "travel", promptCount: 71 },
  { id: "254", name: "Tutorial", slug: "tutorial", promptCount: 84 },
  { id: "255", name: "User Persona", slug: "user-persona", promptCount: 67 },
  { id: "256", name: "UI/UX", slug: "ui-ux", promptCount: 76 },
  { id: "257", name: "Version Control", slug: "version-control", promptCount: 63 },
  { id: "258", name: "Vibrant", slug: "vibrant", promptCount: 59 },
  { id: "259", name: "Video", slug: "video", promptCount: 77 },
  { id: "260", name: "Vintage", slug: "vintage", promptCount: 64 },
  { id: "261", name: "Voiceover", slug: "voiceover", promptCount: 58 },
  { id: "262", name: "Wolfram Language", slug: "wolfram-language", promptCount: 41 },
  { id: "263", name: "Write", slug: "write", promptCount: 93 },
  { id: "264", name: "Zero-Shot", slug: "zero-shot", promptCount: 62 },
]

// Helper function to get popular tags (for homepage)
export const getPopularTags = (count = 10): Tag[] => {
  return allTags.sort((a, b) => (b.promptCount || 0) - (a.promptCount || 0)).slice(0, count)
}

// Helper function to get a tag by ID
export const getTagById = (id: string): Tag | undefined => {
  return allTags.find((tag) => tag.id === id)
}

// Helper function to get a tag by slug
export const getTagBySlug = (slug: string): Tag | undefined => {
  return allTags.find((tag) => tag.slug === slug)
}

// Helper function to get a tag by name
export const getTagByName = (name: string): Tag | undefined => {
  return allTags.find((tag) => tag.name.toLowerCase() === name.toLowerCase())
}

// Helper function to get tags by category
export const getTagsByCategory = (category: string): Tag[] => {
  return allTags.filter((tag) => tag.category === category)
}

// Helper function to search tags by name (partial match)
export const searchTags = (query: string): Tag[] => {
  const lowercaseQuery = query.toLowerCase()
  return allTags.filter((tag) => tag.name.toLowerCase().includes(lowercaseQuery))
}
