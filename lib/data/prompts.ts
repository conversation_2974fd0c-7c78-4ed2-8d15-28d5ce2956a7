import promptDb from "./prompt-db.json"

export interface Prompt {
  id: string
  title: string
  description: string
  text: string
  instructions?: string
  exampleInput?: string
  exampleOutput?: string
  imageUrl?: string
  category: {
    id: string
    name: string
    slug: string
  }
  tool?: {
    id: string
    name: string
    slug: string
  }
  tags?: Array<{
    id: string
    name: string
    slug: string
  }>
  user: {
    id: string
    username: string
    createdAt: string
  }
  likeCount: number
  commentCount: number
  viewCount?: number
  remixCount?: number
  isRemix?: boolean
  originalPromptId?: string
  remixNotes?: string
  createdAt: string
}

// Export the prompts from the JSON file
export const prompts: Prompt[] = promptDb.prompts

// Helper function to get a prompt by ID
export const getPromptById = (id: string): Prompt | undefined => {
  return prompts.find((prompt) => prompt.id === id)
}
