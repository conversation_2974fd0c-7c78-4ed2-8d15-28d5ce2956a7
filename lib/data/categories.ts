// This file contains the list of categories available for prompts
// Admins can easily update this list by modifying this file

export interface Category {
  id: number
  name: string
  slug: string
  promptCount?: number
  imagePath?: string
  description?: string
}

// Featured categories (shown on the home page)
export const featuredCategories: Category[] = [
  {
    id: 1,
    name: "Creative Writing",
    slug: "creative-writing",
    promptCount: 124,
    imagePath: "/images/categories/creative-writing.png",
    description: "Prompts for creative writing, storytelling, and narrative development",
  },
  {
    id: 2,
    name: "Code Generation",
    slug: "code-generation",
    promptCount: 98,
    imagePath: "/images/categories/code-generation.png",
    description: "Prompts for generating, refactoring, and optimizing code",
  },
  {
    id: 3,
    name: "Marketing",
    slug: "marketing",
    promptCount: 87,
    imagePath: "/images/categories/marketing.png",
    description: "Prompts for marketing content, strategies, and campaigns",
  },
  {
    id: 4,
    name: "Image Generation",
    slug: "image-generation",
    promptCount: 76,
    imagePath: "/images/categories/image-generation.png",
    description: "Prompts for generating images with AI tools",
  },
  {
    id: 5,
    name: "Data Analysis",
    slug: "data-analysis",
    promptCount: 65,
    imagePath: "/images/categories/data-analysis.png",
    description: "Prompts for analyzing and visualizing data",
  },
]

// Additional categories (shown when expanded)
export const additionalCategories: Category[] = [
  {
    id: 6,
    name: "Education",
    slug: "education",
    promptCount: 54,
    imagePath: "/images/categories/education.png",
    description: "Prompts for educational content and learning materials",
  },
  {
    id: 7,
    name: "Business",
    slug: "business",
    promptCount: 43,
    imagePath: "/images/categories/business.png",
    description: "Prompts for business documents, plans, and strategies",
  },
  {
    id: 8,
    name: "Personal",
    slug: "personal",
    promptCount: 32,
    imagePath: "/images/categories/personal.png",
    description: "Prompts for personal use and self-improvement",
  },
  {
    id: 9,
    name: "Research",
    slug: "research",
    promptCount: 28,
    imagePath: "/images/categories/research.png",
    description: "Prompts for research, analysis, and academic writing",
  },
  {
    id: 10,
    name: "Social Media",
    slug: "social-media",
    promptCount: 26,
    imagePath: "/images/categories/social-media.png",
    description: "Prompts for social media content and engagement",
  },
  {
    id: 11,
    name: "Email",
    slug: "email",
    promptCount: 24,
    imagePath: "/images/categories/email.png",
    description: "Prompts for email writing and communication",
  },
  {
    id: 12,
    name: "SEO",
    slug: "seo",
    promptCount: 22,
    imagePath: "/images/categories/seo.png",
    description: "Prompts for SEO optimization and content",
  },
  {
    id: 13,
    name: "Copywriting",
    slug: "copywriting",
    promptCount: 20,
    imagePath: "/images/categories/copywriting.png",
    description: "Prompts for copywriting and persuasive content",
  },
  {
    id: 14,
    name: "Fiction",
    slug: "fiction",
    promptCount: 18,
    imagePath: "/images/categories/fiction.png",
    description: "Prompts for fiction writing and storytelling",
  },
  {
    id: 15,
    name: "Non-Fiction",
    slug: "non-fiction",
    promptCount: 16,
    imagePath: "/images/categories/non-fiction.png",
    description: "Prompts for non-fiction writing and factual content",
  },
  {
    id: 16,
    name: "Audio",
    slug: "audio",
    promptCount: 14,
    imagePath: "/images/categories/audio.png",
    description: "Prompts for audio generation and voice content",
  },
  {
    id: 17,
    name: "Video",
    slug: "video",
    promptCount: 13,
    imagePath: "/images/categories/video.png",
    description: "Prompts for video content creation and editing",
  },
  {
    id: 18,
    name: "Other",
    slug: "other",
    promptCount: 12,
    imagePath: "/images/categories/other.png",
    description: "Miscellaneous prompts that don't fit other categories",
  },
]

// All categories combined
export const allCategories: Category[] = [...featuredCategories, ...additionalCategories]

// Helper function to get a category by ID
export const getCategoryById = (id: number): Category | undefined => {
  return allCategories.find((category) => category.id === id)
}

// Helper function to get a category by slug
export const getCategoryBySlug = (slug: string): Category | undefined => {
  return allCategories.find((category) => category.slug === slug)
}
