import { supabase } from "./supabase/client";
import { Collection } from "./types";
import { transformCollection } from "./transformers";

/**
 * Get collections for a user with optional filtering for dialog purpose
 */
export async function getUserCollections(
  userId: string,
  options: {
    includePrivate?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: "created_at" | "prompt_count" | "name";
    sortOrder?: "asc" | "desc";
    purpose?: "dialog" | "general";
  } = {}
): Promise<Collection[]> {
  const {
    includePrivate = false,
    limit = 10,
    offset = 0,
    sortBy = "created_at",
    sortOrder = "desc",
    purpose = "general",
  } = options;

  try {
    let query = supabase
      .from("collections")
      .select(`
        id,
        user_id,
        name,
        description,
        icon,
        is_public,
        is_default,
        default_type,
        prompt_count,
        view_count,
        created_at,
        updated_at,
        user:profiles!collections_user_id_fkey (username, avatar_url)
      `)
      .eq("user_id", userId);
    
    // For dialog purpose, exclude "My Prompts" collection and prioritize "Saved Prompts"
    if (purpose === "dialog") {
      query = query.neq("default_type", "my_prompts"); // Exclude "My Prompts"
      // Add specific ordering for "Saved Prompts" first
      query = query.order("is_default", { ascending: false }) // Puts true (default) first
                   .order("default_type", { ascending: true }); // 'saved_prompts' first
    } else {
      // Use the standard ordering for general purpose
      query = query.order(sortBy, { ascending: sortOrder === "asc" });
    }
    
    // Apply range for pagination
    query = query.range(offset, offset + limit - 1);

    if (!includePrivate) {
      query = query.eq("is_public", true);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching user collections:", error.message);
      throw error;
    }

    return (data || []).map(transformCollection);
  } catch (error) {
    console.error("Error in getUserCollections:", error);
    return [];
  }
}

/**
 * Adds a prompt to multiple collections at once
 * Uses the add_prompt_to_multiple_collections RPC function
 */
export async function addPromptToMultipleCollections(
  userId: string,
  promptId: string,
  collectionIds: string[]
): Promise<{ success: boolean; error?: any }> {
  try {
    const { data, error } = await supabase.rpc("add_prompt_to_multiple_collections", {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_collection_ids: collectionIds,
    });

    if (error) {
      console.error("Error adding prompt to multiple collections:", error);
      return { success: false, error };
    }
    if (data === false) { // RPC returns boolean
      console.warn("add_prompt_to_multiple_collections RPC returned false.");
      return { success: false, error: "Failed to add prompt to one or more collections (RPC)." };
    }
    return { success: true };
  } catch (e) {
    console.error("Unexpected error in addPromptToMultipleCollections service:", e);
    return { success: false, error: e };
  }
}

/**
 * Gets the collection IDs that a prompt belongs to for a specific user
 * This is used to determine if a prompt is saved in any of the user's collections
 */
export async function getPromptCollectionMembership(
  userId: string,
  promptId: string
): Promise<{ collectionIds: string[]; error?: any }> {
  try {
    // Get all collections that belong to the user
    const { data: userCollections, error: collectionsError } = await supabase
      .from("collections")
      .select("id")
      .eq("user_id", userId);

    if (collectionsError) {
      console.error("Error fetching user collections:", collectionsError);
      return { collectionIds: [], error: collectionsError };
    }

    if (!userCollections || userCollections.length === 0) {
      // User has no collections
      return { collectionIds: [] };
    }

    // Get all collection_prompts entries for this prompt
    const userCollectionIds = userCollections.map((c: any) => c.id as string);
    
    const { data: memberships, error: membershipsError } = await supabase
      .from("collection_prompts")
      .select("collection_id")
      .eq("prompt_id", promptId)
      .in("collection_id", userCollectionIds);

    if (membershipsError) {
      console.error("Error fetching prompt collection memberships:", membershipsError);
      return { collectionIds: [], error: membershipsError };
    }

    // Return the collection IDs
    return { 
      collectionIds: memberships ? memberships.map((m: any) => m.collection_id as string) : [],
      error: null
    };
  } catch (e) {
    console.error("Unexpected error in getPromptCollectionMembership service:", e);
    return { collectionIds: [], error: e };
  }
}
