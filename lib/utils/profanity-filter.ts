import { Profanity, profanity as defaultProfanity } from '@2toad/profanity';

// Store user timeout information
interface UserTimeout {
  userId: string;
  expiresAt: number; // Unix timestamp in milliseconds
}

// Keep track of profanity attempts by users
interface ProfanityAttempt {
  userId: string;
  count: number;
  lastAttemptTime: number; // Unix timestamp in milliseconds
}

// <PERSON>ie names
const TIMEOUT_COOKIE_NAME = 'profanity_timeout';
const ATTEMPTS_COOKIE_NAME = 'profanity_attempts';

// Reset period for profanity attempts (in milliseconds)
const RESET_PERIOD = 30 * 60 * 1000; // 30 minutes
const MAX_ATTEMPTS = 5;
const TIMEOUT_DURATION = 10 * 60 * 1000; // 10 minutes

/**
 * Get the profanity filter instance - uses the default exported instance
 * which is already initialized with English words
 */
export const getProfanityFilter = () => {
  return defaultProfanity;
};

/**
 * Check if text contains profanity
 * @param text The text to check
 * @returns True if the text contains profanity, false otherwise
 */
export const containsProfanity = (text: string): boolean => {
  if (!text) return false;
  
  const profanityFilter = getProfanityFilter();
  // Use the exists method to check if the text contains profanity
  // This will return true if any profane word is found in the text
  return profanityFilter.exists(text);
};

/**
 * Set a cookie in the browser
 * @param name Cookie name
 * @param value Cookie value
 * @param expiryMs Expiry time in milliseconds from now
 */
const setCookie = (name: string, value: string, expiryMs: number): void => {
  if (typeof window === 'undefined') return; // Server-side check
  
  const expires = new Date(Date.now() + expiryMs).toUTCString();
  document.cookie = `${name}=${encodeURIComponent(value)};expires=${expires};path=/;SameSite=Strict`;
};

/**
 * Get a cookie value by name
 * @param name Cookie name
 * @returns Cookie value or null if not found
 */
const getCookie = (name: string): string | null => {
  if (typeof window === 'undefined') return null; // Server-side check
  
  const cookies = document.cookie.split(';');
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    if (cookie.startsWith(name + '=')) {
      return decodeURIComponent(cookie.substring(name.length + 1));
    }
  }
  return null;
};

/**
 * Delete a cookie by name
 * @param name Cookie name
 */
const deleteCookie = (name: string): void => {
  if (typeof window === 'undefined') return; // Server-side check
  
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;SameSite=Strict`;
};

/**
 * Save user timeout information to a cookie
 * @param userId The user's ID
 * @param expiresAt Expiry timestamp in milliseconds
 */
const saveTimeoutToCookie = (userId: string, expiresAt: number): void => {
  const timeoutInfo: UserTimeout = { userId, expiresAt };
  setCookie(TIMEOUT_COOKIE_NAME, JSON.stringify(timeoutInfo), expiresAt - Date.now());
};

/**
 * Get user timeout information from cookie
 * @param userId The user's ID to check
 * @returns Timeout information or null if not found or expired
 */
const getTimeoutFromCookie = (userId: string): UserTimeout | null => {
  const cookieValue = getCookie(TIMEOUT_COOKIE_NAME);
  if (!cookieValue) return null;
  
  try {
    const timeoutInfo = JSON.parse(cookieValue) as UserTimeout;
    if (timeoutInfo.userId === userId) {
      // Check if timeout is still valid
      if (timeoutInfo.expiresAt > Date.now()) {
        return timeoutInfo;
      } else {
        // Timeout expired, clean up cookie
        deleteCookie(TIMEOUT_COOKIE_NAME);
      }
    }
  } catch (e) {
    // Invalid cookie format, clean up
    deleteCookie(TIMEOUT_COOKIE_NAME);
  }
  
  return null;
};

/**
 * Save profanity attempts to a cookie
 * @param userId The user's ID
 * @param count Number of attempts
 */
const saveAttemptsToCookie = (userId: string, count: number): void => {
  const attemptInfo: ProfanityAttempt = { 
    userId, 
    count, 
    lastAttemptTime: Date.now() 
  };
  setCookie(ATTEMPTS_COOKIE_NAME, JSON.stringify(attemptInfo), RESET_PERIOD);
};

/**
 * Get profanity attempts from cookie
 * @param userId The user's ID to check
 * @returns Attempt information or null if not found or reset period passed
 */
const getAttemptsFromCookie = (userId: string): ProfanityAttempt | null => {
  const cookieValue = getCookie(ATTEMPTS_COOKIE_NAME);
  if (!cookieValue) return null;
  
  try {
    const attemptInfo = JSON.parse(cookieValue) as ProfanityAttempt;
    if (attemptInfo.userId === userId) {
      // Check if attempts are still within reset period
      if (Date.now() - attemptInfo.lastAttemptTime < RESET_PERIOD) {
        return attemptInfo;
      } else {
        // Reset period passed, clean up cookie
        deleteCookie(ATTEMPTS_COOKIE_NAME);
      }
    }
  } catch (e) {
    // Invalid cookie format, clean up
    deleteCookie(ATTEMPTS_COOKIE_NAME);
  }
  
  return null;
};

/**
 * Check if a user is on timeout
 * @param userId The user's ID
 * @returns True if the user is on timeout, false otherwise
 */
export const isUserOnTimeout = (userId: string): boolean => {
  const timeoutInfo = getTimeoutFromCookie(userId);
  return timeoutInfo !== null;
};

/**
 * Get the remaining timeout time in seconds
 * @param userId The user's ID
 * @returns The remaining timeout time in seconds, or 0 if not on timeout
 */
export const getRemainingTimeoutSeconds = (userId: string): number => {
  const timeoutInfo = getTimeoutFromCookie(userId);
  
  if (!timeoutInfo) return 0;
  
  return Math.ceil((timeoutInfo.expiresAt - Date.now()) / 1000);
};

/**
 * Record a profanity attempt for a user
 * @param userId The user's ID
 * @returns Object containing whether the user is now on timeout and the number of attempts
 */
export const recordProfanityAttempt = (userId: string): { onTimeout: boolean; attemptsCount: number } => {
  // Check if user is already on timeout
  if (isUserOnTimeout(userId)) {
    return { onTimeout: true, attemptsCount: MAX_ATTEMPTS };
  }
  
  // Get existing attempts from cookie
  const existingAttempt = getAttemptsFromCookie(userId);
  
  if (!existingAttempt) {
    // First attempt for this user
    saveAttemptsToCookie(userId, 1);
    return { onTimeout: false, attemptsCount: 1 };
  } else {
    // Increment attempt count
    const newCount = existingAttempt.count + 1;
    saveAttemptsToCookie(userId, newCount);
    
    // Check if user should be put on timeout
    if (newCount >= MAX_ATTEMPTS) {
      const expiresAt = Date.now() + TIMEOUT_DURATION;
      
      // Save timeout to cookie
      saveTimeoutToCookie(userId, expiresAt);
      
      // Reset attempts after putting on timeout
      deleteCookie(ATTEMPTS_COOKIE_NAME);
      
      return { onTimeout: true, attemptsCount: MAX_ATTEMPTS };
    }
    
    return { onTimeout: false, attemptsCount: newCount };
  }
};
