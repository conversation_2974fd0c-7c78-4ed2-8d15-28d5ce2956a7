import slugify from 'slugify'; // Import the slugify library
import type { Prompt, PromptCard as PromptCardType } from "@/lib/types";
import { logPromptObject } from "./debug-helpers";

// Your fillerWords set can still be useful for pre-processing.
const fillerWords = new Set([
  "a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for", "with", "of", "by", "as",
  "is", "am", "are", "was", "were", "be", "being", "been", "it", "its", "i", "you", "he", "she",
  "they", "we", "me", "him", "her", "us", "them", "my", "your", "his", "its", "our", "their",
  "this", "that", "these", "those", "how", "what", "when", "where", "why", "do", "does", "did",
  "will", "shall", "should", "can", "could", "may", "might", "must", "about", "above", "across",
  "after", "against", "along", "among", "around", "before", "behind", "below", "beneath",
  "beside", "between", "beyond", "down", "during", "except", "inside", "into", "like", "near",
  "onto", "out", "outside", "over", "past", "since", "through", "throughout", "toward", "under",
  "underneath", "until", "unto", "up", "upon", "without", "from",
]);

/**
 * Creates an SEO-friendly slug from a title using the slugify library,
 * after removing common filler words and applying length constraints.
 */
export function createTitleSlug(title: string): string {
  if (!title || typeof title !== "string") {
    return "untitled";
  }

  // 1. Convert to lowercase and split into words
  const words = title.toLowerCase().split(/\s+/);

  // 2. Filter out filler words and empty strings
  const significantWords = words.filter(word => word && !fillerWords.has(word));

  // If no significant words are left (e.g., title was only filler words),
  // use the original title for slugification to avoid an empty slug.
  const titleToSlugify = significantWords.length > 0 ? significantWords.join(' ') : title;

  if (!titleToSlugify.trim()) {
    return "untitled"; // Handle cases where title becomes empty after processing
  }

  // 3. Use the slugify library
  let slug = slugify(titleToSlugify, {
    lower: true,        // Ensure lowercase
    strict: true,       // Remove special characters not handled by replacement
    remove: /[*+~.()'"!:@#?]/g, // Regex to remove specific unwanted characters
    replacement: '-',   // Replace spaces and other characters with a hyphen
    trim: true          // Trim leading/trailing hyphens and spaces
  });

  // 4. Limit length to approximately 50 characters, trying to keep whole words.
  // This logic remains similar to your original, applied *after* slugification.
  if (slug.length > 50) {
    let truncatedSlug = slug.substring(0, 50);
    // Try to cut at the last hyphen to avoid splitting words
    const lastHyphenIndex = truncatedSlug.lastIndexOf('-');
    if (lastHyphenIndex > 0) { // Ensure hyphen is not at the beginning
      slug = truncatedSlug.substring(0, lastHyphenIndex);
    } else {
      // If no hyphen, or it's at the beginning, just truncate
      slug = truncatedSlug;
    }
    // Remove any trailing hyphen that might result from truncation
    slug = slug.replace(/-+$/, "");
  }

  // 5. If slug is empty after all processing (e.g., title was just "."), return "untitled"
  if (!slug) {
    return "untitled";
  }

  return slug;
}

/**
 * Generate the full SEO-friendly URL for a prompt
 */
export function generatePromptUrl(prompt: PromptCardType): string {
  // Disable verbose logging
  // logPromptObject(prompt, "generatePromptUrl (input - with slugify)");

  if (!prompt || !prompt.shortId) {
    console.error(`[generatePromptUrl] CRITICAL: Prompt object or its shortId is missing/empty. Cannot generate valid URL. Prompt ID: ${prompt?.id}, Title: ${prompt?.title}`);
    return `/prompt/error/missing-shortid/${prompt?.id || 'unknown'}`;
  }

  const categorySlug = prompt.category?.slug || "uncategorized";
  const toolSlug = prompt.tool?.slug || "no-tool";
  const tagSlug = prompt.primary_tag_slug || (prompt.tags && prompt.tags.length > 0 ? prompt.tags[0]?.slug : undefined) || "no-tag";
  
  // Use the new slug generation function for the title
  const titleSlug = createTitleSlug(prompt.title || "untitled"); 
  const finalShortId = prompt.shortId;

  // Disable verbose logging
  // logPromptObject({ ...prompt, shortId: finalShortId, generatedTitleSlug: titleSlug }, "generatePromptUrl (output values - with slugify)");

  return `/prompt/${categorySlug}/${toolSlug}/${tagSlug}/${titleSlug}/${finalShortId}`;
}

// This function might still be useful if you need a very short, predictable ID from a UUID elsewhere,
// but it's not directly used for the main title slug generation anymore.
export function generateShortId(id: string): string {
  if (!id) return Math.random().toString(36).substring(2, 10);
  return id.substring(0, 8);
}
