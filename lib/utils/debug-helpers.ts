interface BasicPromptInfo {
  id: string;
  shortId?: string | null;
  short_id?: string | null; // Expected from DB/view before transformation
  title?: string | null;
  category?: { name?: string; slug?: string } | null;
  tool?: { name?: string; slug?: string } | null;
  generatedTitleSlug?: string | null; // Added for logging in generatePromptUrl
}

export function logPromptShortIdIssues(prompts: BasicPromptInfo[] | null | undefined, source: string): void {
  if (!prompts || !Array.isArray(prompts) || prompts.length === 0) {
    return;
  }

  const promptsWithMissingDbShortId = prompts.filter(p => {
    if (!p) return true; // Treat null/undefined prompt objects as an issue
    // Check if the 'short_id' field (expected from DB/view) is missing, null, or undefined
    return p.short_id === null || p.short_id === undefined || p.short_id === "";
  });

  if (promptsWithMissingDbShortId.length > 0) {
    console.warn(`[DEBUG ${source}] Found ${promptsWithMissingDbShortId.length} prompts with missing, null, or empty 'short_id' from DB/view:`);
    promptsWithMissingDbShortId.forEach((prompt, index) => {
      if (!prompt) {
        console.warn(`  [${index + 1}] Null or undefined prompt object in the array.`);
      } else {
        console.warn(`  [${index + 1}] ID: ${prompt.id}, Title: ${prompt.title || "N/A"}, DB short_id: '${prompt.short_id}', Transformed shortId: '${prompt.shortId}'`);
      }
    });
  }
}

export function logPromptObject(prompt: BasicPromptInfo | null | undefined, source: string): void {
  if (!prompt) {
    console.warn(`[DEBUG ${source}] Prompt object is null or undefined.`);
    return;
  }

  const logData = {
    source,
    id: prompt.id,
    // Prioritize transformed shortId, then DB short_id, then indicate missing
    actualShortIdUsed: prompt.shortId || prompt.short_id || 'MISSING_OR_UNDEFINED_SHORT_ID_FIELD',
    title: prompt.title || 'Untitled',
    categorySlug: prompt.category?.slug || 'no-category-slug',
    toolSlug: prompt.tool?.slug || 'no-tool-slug',
    // For checking the source of shortId
    prompt_shortId_field: prompt.shortId, // Value of the camelCase field after transformation
    prompt_short_id_field: prompt.short_id, // Value of the snake_case field (raw from DB/view)
    generatedTitleSlug: prompt.generatedTitleSlug, // Added for logging
  };

  console.log(`[DEBUG ${source}] Prompt Details for URL generation:`, logData);

  if (!prompt.shortId && !prompt.short_id) {
    console.error(`[CRITICAL DEBUG ${source}] Prompt ID ${prompt.id} has NO shortId (transformed) AND NO short_id (raw) field!`);
  } else if (!prompt.shortId && prompt.short_id) {
    // This case should ideally not happen if transformPromptCard is correct
    console.warn(`[DEBUG ${source}] Prompt ID ${prompt.id} had short_id='${prompt.short_id}' from DB/view, but transformed shortId is missing/falsy.`);
  } else if (prompt.shortId && !prompt.short_id && prompt.shortId !== prompt.id.substring(0,8)) {
     // This case indicates shortId might have been derived from UUID if short_id was missing from raw data
     console.warn(`[DEBUG ${source}] Prompt ID ${prompt.id} has transformed shortId='${prompt.shortId}', but raw short_id was missing. Check if it's UUID-derived.`);
  }
}

export function logObjectStructure(obj: any, name: string = "Object"): void {
  console.log(`[DEBUG Structure: ${name}]`, JSON.stringify(obj, null, 2));
}
