import React from 'react'

/**
 * Highlights text enclosed in double brackets [[like this]] with green color
 * Used for prompt and instruction text to indicate customizable parts
 *
 * @param text The text to process
 * @returns React elements with highlighted sections
 */
export const highlightDoubleBracketedText = (text: string | undefined) => {
  // If text is undefined or null, return an empty fragment
  if (!text) return null

  const result: React.ReactNode[] = []
  let currentIndex = 0
  let highlightIndex = 0

  // Process the text character by character to avoid overlapping highlights
  while (currentIndex < text.length) {
    // Look for the start of a double bracket pattern
    const startMatch = text.indexOf('[[', currentIndex)
    
    if (startMatch === -1) {
      // No more double brackets found, add the rest of the text
      if (currentIndex < text.length) {
        result.push(text.substring(currentIndex))
      }
      break
    }

    // Add any text before the double bracket
    if (startMatch > currentIndex) {
      result.push(text.substring(currentIndex, startMatch))
    }

    // Look for the closing double bracket
    const endMatch = text.indexOf(']]', startMatch + 2)
    
    if (endMatch === -1) {
      // No closing bracket found, treat the rest as regular text
      result.push(text.substring(startMatch))
      break
    }

    // Extract the content including the brackets
    const highlightedContent = text.substring(startMatch, endMatch + 2)
    
    // Create the highlighted span
    result.push(
      <span
        key={`highlight-${highlightIndex}-${startMatch}`}
        className="bg-accent-green/20 text-accent-green px-1 py-0.5 rounded font-mono"
      >
        {highlightedContent}
      </span>
    )

    // Move past this highlight
    currentIndex = endMatch + 2
    highlightIndex++
  }

  return result
}

/**
 * Processes text for CodeEditor by adding highlighting markers that will be styled by CSS
 * This is used when we can't inject React components directly into the CodeEditor
 *
 * @param text The text to process
 * @returns Text with special markers for highlighting
 */
export const processTextForCodeEditor = (text: string | undefined): string => {
  if (!text) return ""
  
  // For CodeEditor, we'll rely on CSS styling of the double brackets themselves
  // The highlighting will be handled by the enhanced CSS in the component
  return text
}
