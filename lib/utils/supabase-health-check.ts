/**
 * Utility to check Supabase connection health and configuration
 */

import { getSupabaseClient } from "../supabase/client";

export interface HealthCheckResult {
  isHealthy: boolean;
  error?: string;
  details: {
    environmentVariables: {
      url: boolean;
      anonKey: boolean;
    };
    clientInitialization: boolean;
    databaseConnection: boolean;
  };
}

/**
 * Performs a comprehensive health check of the Supabase connection
 */
export async function performSupabaseHealthCheck(): Promise<HealthCheckResult> {
  const result: HealthCheckResult = {
    isHealthy: false,
    details: {
      environmentVariables: {
        url: false,
        anonKey: false,
      },
      clientInitialization: false,
      databaseConnection: false,
    },
  };

  try {
    // Check environment variables
    result.details.environmentVariables.url = !!process.env.NEXT_PUBLIC_SUPABASE_URL;
    result.details.environmentVariables.anonKey = !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!result.details.environmentVariables.url || !result.details.environmentVariables.anonKey) {
      result.error = "Missing Supabase environment variables";
      return result;
    }

    // Check client initialization
    try {
      const supabase = getSupabaseClient();
      result.details.clientInitialization = !!supabase;
    } catch (error) {
      result.error = `Client initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      return result;
    }

    // Check database connection with a simple query
    try {
      const supabase = getSupabaseClient();
      const { data, error } = await supabase
        .from("categories") // Use a simple table that should always exist
        .select("id")
        .limit(1);

      if (error) {
        result.error = `Database connection failed: ${error.message}`;
        return result;
      }

      result.details.databaseConnection = true;
      result.isHealthy = true;
    } catch (error) {
      result.error = `Database query failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      return result;
    }

    return result;
  } catch (error) {
    result.error = `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    return result;
  }
}

/**
 * Logs the health check results to console
 */
export function logHealthCheckResults(result: HealthCheckResult) {
  console.log("[Supabase Health Check]", {
    isHealthy: result.isHealthy,
    error: result.error,
    details: result.details,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Quick health check that can be called from components
 */
export async function quickHealthCheck(): Promise<boolean> {
  try {
    const result = await performSupabaseHealthCheck();
    if (!result.isHealthy) {
      console.warn("[Quick Health Check] Supabase is not healthy:", result.error);
    }
    return result.isHealthy;
  } catch (error) {
    console.error("[Quick Health Check] Failed:", error);
    return false;
  }
}
