import { supabase } from "lib/supabase/client";

const ANONYMOUS_VIEWER_ID_KEY = "phq_anon_vid";

export async function getViewerHash(): Promise<string> {
  const { data: { session } } = await supabase.auth.getSession();

  if (session?.user) {
    return session.user.id;
  } else {
    let anonymousId = localStorage.getItem(ANONYMOUS_VIEWER_ID_KEY);
    if (!anonymousId) {
      anonymousId = crypto.randomUUID ? crypto.randomUUID() : `anon-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
      try {
        localStorage.setItem(ANONYMOUS_VIEWER_ID_KEY, anonymousId);
      } catch (e) {
        console.warn("Failed to set anonymous viewer ID in localStorage:", e);
        // Fallback to session-only ID if localStorage is unavailable/full
        // This won't persist across sessions but is better than nothing.
        return anonymousId;
      }
    }
    return anonymousId;
  }
} 