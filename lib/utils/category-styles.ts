// Utility functions for category-based styling

// Function to get the linear gradient style string with darker shades
export const getLinearGradientStyle = (categorySlug: string): string => {
  const gradients: Record<string, { from: string; to: string }> = {
    "code-generation": { from: "#1e3a8a", to: "#172554" }, // blue-800 to blue-950
    "creative-writing": { from: "#14532d", to: "#052e16" }, // green-900 to green-950
    "marketing": { from: "#a21caf", to: "#701a75" }, // fuchsia-700 to fuchsia-900
    "image-generation": { from: "#7e22ce", to: "#4c0519" }, // purple-700 to rose-950 (adding some contrast)
    "data-analysis": { from: "#0f766e", to: "#042f2e" }, // teal-700 to teal-950
    "business": { from: "#0369a1", to: "#082f49" }, // sky-700 to sky-950
    "education": { from: "#4d7c0f", to: "#1a2e05" }, // lime-700 to lime-950
    "personal": { from: "#6d28d9", to: "#312e81" }, // violet-700 to indigo-900 (adding some contrast)
    "research": { from: "#0891b2", to: "#014e5e" }, // cyan-700 to cyan-900
    "social-media": { from: "#db2777", to: "#831843" }, // pink-600 to pink-900
    "email": { from: "#c2410c", to: "#7c2d12" }, // orange-700 to orange-900
    "seo": { from: "#047857", to: "#064e3b" }, // emerald-700 to emerald-900
    "copywriting": { from: "#a21caf", to: "#701a75" }, // fuchsia-700 to fuchsia-900 (matching marketing)
    "fiction": { from: "#6d28d9", to: "#312e81" }, // violet-700 to indigo-900 (matching personal)
    "non-fiction": { from: "#4338ca", to: "#312e81" }, // indigo-700 to indigo-900
    "audio": { from: "#7e22ce", to: "#4c0519" }, // purple-700 to rose-950 (matching image generation)
    "video": { from: "#b91c1c", to: "#7f1d1d" }, // red-700 to red-900
    "music": { from: "#7e22ce", to: "#4c0519" }, // purple-700 to rose-950 (matching audio)
    "other": { from: "#334155", to: "#1e293b" }, // slate-700 to slate-800
  };

  const gradient = gradients[categorySlug] || gradients["other"];
  return `linear-gradient(to bottom right, ${gradient.from}, ${gradient.to})`;
};

// Function to get the icon color hex code (using *-200 or *-300 shades for visibility)
export const getIconColorHex = (categorySlug: string): string => {
   const iconColors: Record<string, string> = {
    "code-generation": "#bfdbfe", // blue-200
    "creative-writing": "#bbf7d0", // green-200
    "marketing": "#f9a8d4", // pink-300
    "image-generation": "#e9d5ff", // purple-200
    "data-analysis": "#99f6e4", // teal-200
    "business": "#bae6fd", // sky-200
    "education": "#bef264", // lime-300
    "personal": "#c4b5fd", // violet-300
    "research": "#a5f3fc", // cyan-300
    "social-media": "#f9a8d4", // pink-300 (using pink-300 for visibility)
    "email": "#fdba74", // orange-300
    "seo": "#a7f3d0", // emerald-200
    "copywriting": "#f0abfc", // fuchsia-300
    "fiction": "#c4b5fd", // violet-300 (matching personal)
    "non-fiction": "#a5b4fc", // indigo-300
    "audio": "#e9d5ff", // purple-200 (matching image generation)
    "video": "#fca5a5", // red-300
    "music": "#e9d5ff", // purple-200 (matching audio)
    "other": "#d1d5db", // gray-300
   };
   return iconColors[categorySlug] || iconColors["other"];
};
