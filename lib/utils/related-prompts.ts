// Update the Prompt interface to match our prompts.ts structure
export interface Prompt {
  id: string
  title: string
  description: string
  prompt?: string
  text?: string
  instructions?: string
  exampleInput?: string
  exampleOutput?: string
  image?: string
  imageUrl?: string
  category: any
  tool?: any
  tags?: any[]
  likeCount?: number
  user?: any
  createdAt?: string
}

// Update the calculateRelevanceScore function to handle the new data structure
// Modify the function to better handle the structure from prompt-db.json

export function calculateRelevanceScore(
  sourcePrompt: Prompt,
  targetPrompt: Prompt,
  weights = { category: 0.5, tool: 0.3, tags: 0.2 },
): number {
  let score = 0

  // Category match (highest weight)
  if (sourcePrompt.category && targetPrompt.category) {
    const sourceCategory =
      typeof sourcePrompt.category === "string"
        ? sourcePrompt.category
        : sourcePrompt.category.id || sourcePrompt.category.name || sourcePrompt.category.slug

    const targetCategory =
      typeof targetPrompt.category === "string"
        ? targetPrompt.category
        : targetPrompt.category.id || targetPrompt.category.name || targetPrompt.category.slug

    if (sourceCategory === targetCategory) {
      score += weights.category
    }
  }

  // Tool match (medium weight)
  if (sourcePrompt.tool && targetPrompt.tool) {
    const sourceTool =
      typeof sourcePrompt.tool === "string"
        ? sourcePrompt.tool
        : sourcePrompt.tool.id || sourcePrompt.tool.name || sourcePrompt.tool.slug

    const targetTool =
      typeof targetPrompt.tool === "string"
        ? targetPrompt.tool
        : targetPrompt.tool.id || targetPrompt.tool.name || targetPrompt.tool.slug

    if (sourceTool === targetTool) {
      score += weights.tool
    }
  }

  // Tag overlap (lowest weight but cumulative)
  if (sourcePrompt.tags && targetPrompt.tags && sourcePrompt.tags.length > 0 && targetPrompt.tags.length > 0) {
    const sourceTags = sourcePrompt.tags.map((tag) => (typeof tag === "string" ? tag : tag.id || tag.name || tag.slug))

    const targetTags = targetPrompt.tags.map((tag) => (typeof tag === "string" ? tag : tag.id || tag.name || tag.slug))

    // Calculate tag overlap
    const overlapCount = sourceTags.filter((tag) => targetTags.includes(tag)).length
    const maxPossibleOverlap = Math.min(sourceTags.length, targetTags.length)

    if (maxPossibleOverlap > 0) {
      // Normalize tag score based on percentage of overlap
      const tagScore = (overlapCount / maxPossibleOverlap) * weights.tags
      score += tagScore
    }
  }

  return score
}

/**
 * Get related prompts based on category, tool, and tag similarity
 */
export function getRelatedPrompts(
  sourcePrompt: Prompt,
  allPrompts: Prompt[],
  limit = 4,
  minScore = 0.1, // Lowered from 0.2 to 0.1 to be more inclusive
  weights = { category: 0.5, tool: 0.3, tags: 0.2 },
): Prompt[] {
  // Skip if source prompt is missing or allPrompts is empty
  if (!sourcePrompt || !allPrompts || allPrompts.length === 0) {
    return []
  }

  // Calculate scores for all prompts except the source prompt
  const scoredPrompts = allPrompts
    .filter((prompt) => prompt.id !== sourcePrompt.id)
    .map((prompt) => ({
      prompt,
      score: calculateRelevanceScore(sourcePrompt, prompt, weights),
    }))

  // Log for debugging
  console.log(`Found ${scoredPrompts.length} potential related prompts before filtering`)
  if (scoredPrompts.length > 0) {
    console.log(
      `Score range: ${Math.min(...scoredPrompts.map((p) => p.score))} to ${Math.max(...scoredPrompts.map((p) => p.score))}`,
    )
  }

  // Filter by minimum score
  const filteredPrompts = scoredPrompts.filter((item) => item.score >= minScore)
  console.log(`${filteredPrompts.length} prompts remain after filtering with min score ${minScore}`)

  // Sort by score (descending)
  filteredPrompts.sort((a, b) => {
    // First sort by score
    if (b.score !== a.score) {
      return b.score - a.score
    }

    // If scores are equal, use secondary factors as tiebreakers

    // Tiebreaker 1: Popularity (if available)
    if (a.prompt.likeCount !== undefined && b.prompt.likeCount !== undefined) {
      return b.prompt.likeCount - a.prompt.likeCount
    }

    // Tiebreaker 2: Recency (if available)
    if (a.prompt.createdAt && b.prompt.createdAt) {
      return new Date(b.prompt.createdAt).getTime() - new Date(a.prompt.createdAt).getTime()
    }

    return 0
  })

  // Return the top N prompts
  return filteredPrompts.slice(0, limit).map((item) => item.prompt)
}
