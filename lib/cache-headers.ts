// lib/cache-headers.ts
export const CACHE_HEADERS = {
    // Static data - cache for 1 hour
    STATIC: 'public, s-maxage=3600, stale-while-revalidate=86400',
    
    // Dynamic but stable data - cache for 5 minutes  
    DYNAMIC: 'public, s-maxage=300, stale-while-revalidate=3600',
    
    // User-specific data - cache for 1 minute
    USER_SPECIFIC: 'private, s-maxage=60, stale-while-revalidate=300',
    
    // No cache for real-time data
    NO_CACHE: 'no-store, must-revalidate'
  } as const
  
  export function addCacheHeaders(response: Response, cacheType: keyof typeof CACHE_HEADERS) {
    response.headers.set('Cache-Control', CACHE_HEADERS[cacheType])
    return response
  }