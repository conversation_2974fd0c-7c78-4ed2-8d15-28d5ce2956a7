import { createBrowserClient } from "@supabase/ssr"
import type { Database } from "./database.types"

// Create a Supabase client for use in client components
export const supabase = createBrowserClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

/**
 * Fetch with authentication headers
 * @param endpoint API endpoint
 * @param options Fetch options
 * @returns Fetch response
 */
export async function fetchWithAuth(endpoint: string, options: RequestInit = {}) {
  const session = await supabase.auth.getSession()
  // Create a new headers object with proper typing
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  }
  
  // Add any custom headers from options
  if (options.headers) {
    Object.entries(options.headers).forEach(([key, value]) => {
      if (typeof value === 'string') {
        headers[key] = value;
      }
    });
  }

  if (session?.data?.session) {
    headers["Authorization"] = `Bearer ${session.data.session.access_token}`
  }

  return fetch(endpoint, {
    ...options,
    headers,
  })
}
