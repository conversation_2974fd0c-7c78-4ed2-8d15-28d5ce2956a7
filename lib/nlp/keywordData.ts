// lib/nlp/keywordData.ts
// Static keyword, regex maps, and category-tool links for NLP analysis
// Based on existing categories.ts, tools.ts, and tags.ts

export const categoryKeywords: Record<string, string[]> = {
  "creative-writing": [
    "story", "narrative", "novel", "character", "dialogue", "plot", "screenplay", 
    "poem", "poetry", "verse", "rhyme", "stanza", "fiction", "creative", "writing", "storytelling", "character development",
    "worldbuilding", "fantasy", "drama", "comedy", "adventure", "mystery", "horror",
    "script", "prose", "verse", "literature", "author", "book", "chapter", "scene",
    "write", "create", "generate", "compose", "craft", "imagine", "invent", "beautiful"
  ],
  "code-generation": [
    "python", "javascript", "java", "code", "script", "function", "class", "algorithm", 
    "debug", "programming", "development", "software", "api", "database", "framework",
    "library", "module", "syntax", "bug", "error", "compile", "execute", "refactor",
    "optimization", "performance", "testing", "deployment", "version control", "git",
    "build", "implement", "develop", "program", "coding", "calculate", "compute",
    "automation", "regex", "loop", "conditional", "variable", "array", "object"
  ],
  "image-generation": [
    "photorealistic", "epic", "cinematic lighting", "unreal engine", "octane render", 
    "illustration", "artwork", "concept art", "3d render", "digital art", "painting",
    "portrait", "landscape", "abstract", "minimalist", "artistic style", "visual",
    "design", "graphic", "aesthetic", "color palette", "composition", "texture",
    "image", "picture", "photo", "art", "draw", "sketch", "render", "create art",
    "visualize", "artistic", "beautiful", "stunning", "masterpiece", "gallery",
    "make picture", "generate image", "create image", "make art", "draw picture",
    "/imagine", "imagine prompt", "--ar", "--v", "--style", "--niji", "--chaos",
    "--quality", "--stylize", "midjourney", "stable diffusion", "dall-e"
  ],
  "marketing": [
    "ad copy", "slogan", "campaign", "seo", "social media", "email marketing", 
    "brand voice", "conversion", "engagement", "audience", "target market",
    "content marketing", "influencer", "branding", "promotion", "advertisement",
    "copywriting", "persuasive", "call to action", "lead generation", "analytics",
    "marketing", "sell", "promote", "advertise", "brand", "customer", "sales",
    "market", "target", "reach", "awareness", "viral", "trending",
    "slogan", "tagline", "brand message", "catchphrase", "motto", "brand slogan"
  ],
  "audio": [
    "music", "song", "lyrics", "melody", "bpm", "genre", "instrument", "chord progression", 
    "song structure", "voice", "sound", "audio", "recording", "mixing", "mastering",
    "beat", "rhythm", "harmony", "composition", "instrumental", "vocal", "podcast",
    "sound effect", "audio clip", "voice over", "narration", "speech", "listen"
  ],
  "video": [
    "video", "film", "movie", "animation", "editing", "cinematic", "scene", "shot",
    "camera", "visual effects", "vfx", "motion graphics", "storyboard", "screenplay",
    "director", "producer", "cinematography", "montage", "transition", "timeline",
    "clip", "footage", "sequence", "frame", "motion", "movement", "action"
  ],
  "data-analysis": [
    "data", "analytics", "statistics", "chart", "graph", "visualization", "dataset",
    "analysis", "metrics", "insights", "trends", "correlation", "regression",
    "machine learning", "predictive modeling", "big data", "dashboard", "reporting",
    "analyze", "examine", "process", "calculate", "measure", "evaluate", "assess"
  ],
  "education": [
    "teach", "learn", "lesson", "course", "tutorial", "guide", "instruction",
    "educational", "academic", "study", "homework", "assignment", "curriculum",
    "pedagogy", "training", "workshop", "seminar", "lecture", "classroom",
    "explain", "understand", "knowledge", "skill", "practice", "exercise", "quiz"
  ],
  "business": [
    "business", "strategy", "plan", "proposal", "meeting", "presentation", "report",
    "analysis", "management", "leadership", "operations", "finance", "revenue",
    "profit", "budget", "investment", "growth", "market research", "competitive",
    "company", "organization", "corporate", "professional", "executive", "client"
  ],
  "research": [
    "research", "study", "analysis", "investigation", "thesis", "paper", "article",
    "academic", "scientific", "methodology", "literature review", "hypothesis",
    "experiment", "survey", "interview", "case study", "evidence", "citation",
    "find", "discover", "explore", "investigate", "examine", "search", "information"
  ],
  "social-media": [
    "social media", "facebook", "twitter", "instagram", "linkedin", "tiktok",
    "youtube", "post", "content", "engagement", "followers", "viral", "hashtag",
    "influencer", "community", "share", "like", "comment", "story", "reel",
    "social", "network", "platform", "online", "digital", "internet"
  ],
  "email": [
    "email", "message", "communication", "newsletter", "outreach", "follow up",
    "subject line", "greeting", "closing", "professional", "formal", "informal",
    "customer service", "support", "inquiry", "response", "template",
    "letter", "correspondence", "contact", "reply", "send", "receive"
  ],
  "seo": [
    "seo", "search engine", "optimization", "keywords", "ranking", "google",
    "meta description", "title tag", "backlinks", "organic traffic", "serp",
    "content optimization", "on-page", "off-page", "technical seo",
    "search", "optimize", "rank", "visibility", "traffic", "website"
  ],
  "copywriting": [
    "copywriting", "persuasive", "sales copy", "landing page", "conversion",
    "headline", "call to action", "product description", "benefits", "features",
    "value proposition", "target audience", "pain points", "solution",
    "copy", "text", "content", "writing", "words", "message", "communicate"
  ],
  "fiction": [
    "fiction", "novel", "short story", "character", "plot", "setting", "theme",
    "conflict", "resolution", "protagonist", "antagonist", "narrative", "dialogue",
    "point of view", "genre fiction", "literary fiction", "fantasy", "sci-fi",
    "imaginary", "fictional", "story", "tale", "epic", "adventure", "romance"
  ],
  "non-fiction": [
    "non-fiction", "factual", "informative", "how-to", "guide", "manual",
    "biography", "memoir", "essay", "article", "documentary", "journalism",
    "educational", "instructional", "reference", "encyclopedia",
    "facts", "real", "true", "actual", "documentary", "history", "biography"
  ],
  "personal": [
    "personal", "self-improvement", "productivity", "habits", "goals", "motivation",
    "wellness", "health", "fitness", "relationships", "lifestyle", "mindfulness",
    "growth", "development", "reflection", "journal", "diary",
    "life", "self", "improvement", "better", "change", "transform", "grow",
    "grocery", "shopping", "list", "todo", "task", "reminder", "note", "checklist"
  ],
  "other": [
    "miscellaneous", "various", "general", "other", "different", "mixed",
    "random", "diverse", "multiple", "custom", "unique", "special",
    "help", "assist", "task", "work", "job", "project", "activity", "thing"
  ]
};

export const toolData: Record<string, { keywords: string[], regex?: RegExp[], categories: string[] }> = {
  "chatgpt": {
    keywords: [
      "chatgpt", "openai", "gpt-3.5", "gpt-4", "gpt-4o", "chat gpt", "assistant", "conversation",
      // General text tasks
      "text", "writing", "content", "generate", "create", "help", "explain", "analyze", 
      "write", "draft", "compose", "answer", "response", "question", "task", "prompt",
      "creative", "story", "article", "email", "letter", "document", "summary", "review"
    ],
    regex: [
      /```(python|javascript|java|html|css|typescript|go|rust|csharp|cpp|sql|json|xml|yaml|markdown)/i,
      /role:\s*(system|user|assistant)/i,
      /you are a/i,
      /act as/i
    ],
    categories: ["code-generation", "creative-writing", "research", "business", "education", "personal"]
  },
  "claude": {
    keywords: [
      "claude", "anthropic", "claude-3", "sonnet", "opus", "haiku", "claude ai",
      // General assistance and analysis tasks
      "analysis", "detailed", "thorough", "comprehensive", "professional", "business",
      "research", "study", "academic", "thoughtful", "careful", "precise", "nuanced"
    ],
    regex: [
      /claude/i,
      /anthropic/i,
      /thinking step by step/i,
      /let me think about this/i
    ],
    categories: ["creative-writing", "research", "business", "email", "education"]
  },
  "gemini": {
    keywords: [
      "gemini", "google", "bard", "google ai", "gemini pro",
      // Search and information tasks
      "search", "information", "facts", "data", "knowledge", "learn", "education",
      "research", "google", "web", "internet", "online", "find", "discover"
    ],
    regex: [/gemini/i, /bard/i],
    categories: ["research", "education", "creative-writing", "business"]
  },
  "midjourney": {
    keywords: ["midjourney", "mj", "imagine prompt", "/imagine"],
    regex: [
      /\/imagine\s+prompt:/i,
      /\/imagine/i,
      /--ar\s+\d+:\d+/i,
      /--v\s+\d+(\.\d+)?/i,
      /--style\s+\w+/i,
      /--style\s+raw/i,
      /--niji/i,
      /::\d+/i,
      /--chaos\s+\d+/i,
      /--quality\s+\d+/i,
      /--stylize\s+\d+/i,
      /--seed\s+\d+/i,
      /--weird\s+\d+/i,
      /--tile/i,
      /--no\s+\w+/i
    ],
    categories: ["image-generation"]
  },
  "stable-diffusion": {
    keywords: ["stable diffusion", "sd", "automatic1111", "webui", "civitai", "checkpoint"],
    regex: [
      /\(.*?\):/i, // Emphasis syntax
      /\[.*?\]:/i, // Attention syntax
      /negative prompt/i,
      /cfg scale/i,
      /sampling steps/i,
      /seed/i
    ],
    categories: ["image-generation"]
  },
  "dall-e": {
    keywords: ["dall-e", "dalle", "dall e", "openai image", "image generation"],
    regex: [
      /dall.?e/i,
      /create an image/i,
      /generate.*image/i
    ],
    categories: ["image-generation"]
  },
  "elevenlabs": {
    keywords: ["elevenlabs", "eleven labs", "voice cloning", "text to speech", "tts", "voice synthesis"],
    regex: [
      /elevenlabs/i,
      /voice.*clon/i,
      /text.?to.?speech/i,
      /tts/i
    ],
    categories: ["audio"]
  },
  "suno": {
    keywords: ["suno", "music generation", "song creation", "ai music", "melody", "lyrics"],
    regex: [
      /suno/i,
      /\[verse\]/i,
      /\[chorus\]/i,
      /\[bridge\]/i,
      /genre:.*style:/i
    ],
    categories: ["audio"]
  },
  "runway": {
    keywords: ["runway", "video generation", "motion", "video editing", "gen-2", "gen-3"],
    regex: [
      /runway/i,
      /gen-[23]/i,
      /video.*motion/i
    ],
    categories: ["video"]
  },
  "pika-labs": {
    keywords: ["pika labs", "pika", "video generation", "motion"],
    regex: [/pika/i],
    categories: ["video"]
  },
  "sora": {
    keywords: ["sora", "openai video", "video generation"],
    regex: [/sora/i],
    categories: ["video"]
  },
  "grok": {
    keywords: ["grok", "x ai", "xai", "twitter ai"],
    regex: [/grok/i],
    categories: ["research", "creative-writing", "business"]
  },
  "jasper-ai": {
    keywords: ["jasper", "jasper ai", "content generation", "marketing copy"],
    regex: [/jasper/i],
    categories: ["marketing", "copywriting", "business"]
  },
  "perplexity-ai": {
    keywords: ["perplexity", "search ai", "research assistant"],
    regex: [/perplexity/i],
    categories: ["research", "education"]
  },
  "deepseek": {
    keywords: ["deepseek", "deep seek"],
    regex: [/deepseek/i],
    categories: ["code-generation", "research"]
  },
  "llama": {
    keywords: ["llama", "meta ai", "llama 2", "llama 3", "ollama"],
    regex: [/llama/i, /meta.?ai/i],
    categories: ["code-generation", "creative-writing", "research"]
  },
  "other": {
    keywords: ["ai", "artificial intelligence", "machine learning", "neural network"],
    regex: [],
    categories: ["other"]
  }
};

export const tagKeywords: Record<string, string[]> = {
  "3d": ["3d", "three dimensional", "3d model", "3d render"],
  "3d-render": ["3d render", "blender", "octane", "arnold", "cgi", "vfx", "rendering"],
  "abstract": ["abstract", "conceptual", "non-representational", "theoretical"],
  "academic": ["academic", "scholarly", "research", "university", "thesis", "dissertation"],
  "ad-copy": ["ad copy", "advertisement", "marketing copy", "promotional text"],
  "advanced": ["advanced", "expert", "complex", "sophisticated", "high-level"],
  "adventure": ["adventure", "exploration", "journey", "quest", "expedition"],
  "algorithm": ["algorithm", "computational", "logic", "procedure", "method"],
  "analyze": ["analyze", "analysis", "examination", "study", "investigate"],
  "analytics": ["analytics", "metrics", "data analysis", "statistics", "insights"],
  "animation": ["animation", "animated", "motion graphics", "keyframe", "tweening"],
  "api": ["api", "application programming interface", "endpoint", "rest", "graphql"],
  "api-response": ["api response", "json response", "xml response", "data format"],
  "art-design": ["art", "design", "visual", "aesthetic", "artistic", "creative design"],
  "article": ["article", "blog post", "written piece", "publication", "content"],
  "artistic-style": ["artistic style", "art style", "visual style", "aesthetic approach"],
  "aspect-ratio": ["aspect ratio", "dimensions", "proportions", "16:9", "4:3"],
  "audio": ["audio", "sound", "music", "voice", "acoustic", "hearing"],
  "automation": ["automation", "automated", "scripted", "workflow", "process"],
  "beginner": ["beginner", "novice", "starter", "basic", "introductory", "entry-level"],
  "best-practices": ["best practices", "standards", "guidelines", "conventions", "methodology"],
  "big-data": ["big data", "large dataset", "data science", "analytics", "massive data"],
  "blog-post": ["blog post", "blog", "article", "content", "publication"],
  "brainstorm": ["brainstorm", "ideation", "creative thinking", "idea generation"],
  "brand-voice": ["brand voice", "tone", "personality", "brand identity", "messaging"],
  "branding": ["branding", "brand", "identity", "logo", "brand strategy"],
  "business": ["business", "commercial", "enterprise", "corporate", "professional"],
  "c-sharp": ["c#", "csharp", "c sharp", "dotnet", ".net"],
  "cpp": ["c++", "cpp", "c plus plus"],
  "case-study": ["case study", "example", "real-world application", "use case"],
  "casual": ["casual", "informal", "relaxed", "conversational", "friendly"],
  "categorization": ["categorization", "classification", "taxonomy", "sorting"],
  "chain-of-thought": ["chain of thought", "step by step", "reasoning", "logical progression"],
  "challenge": ["challenge", "problem", "difficulty", "obstacle", "task"],
  "chaos": ["chaos", "random", "unpredictable", "disorder", "entropy"],
  "character": ["character", "persona", "role", "personality", "individual"],
  "checklist": ["checklist", "list", "tasks", "items", "steps"],
  "cinematic": ["cinematic", "film-like", "movie style", "cinematic lighting", "dramatic"],
  "classify": ["classify", "categorize", "sort", "organize", "group"],
  "claude": ["claude", "anthropic", "claude ai"],
  "code-development": ["code development", "programming", "software development", "coding"],
  "code-snippet": ["code snippet", "code example", "programming code", "script"],
  "comedy": ["comedy", "humor", "funny", "comedic", "humorous"],
  "compare": ["compare", "comparison", "versus", "contrast", "evaluate"],
  "comparison": ["comparison", "compare", "versus", "difference", "similarity"],
  "complex-task": ["complex task", "complicated", "advanced", "sophisticated"],
  "concise": ["concise", "brief", "short", "succinct", "compact"],
  "concept-art": ["concept art", "conceptual design", "artistic concept", "visual development"],
  "constraint-based": ["constraint based", "limited", "restricted", "bounded"],
  "content-creation": ["content creation", "content", "creative content", "material"],
  "content-marketing": ["content marketing", "marketing content", "promotional material"],
  "context-setting": ["context setting", "background", "setting", "environment"],
  "conversion": ["conversion", "transform", "change", "convert"],
  "create": ["create", "make", "generate", "build", "produce"],
  "creative": ["creative", "imaginative", "innovative", "artistic", "original"],
  "creative-exploration": ["creative exploration", "experimental", "innovative approach"],
  "critique": ["critique", "review", "analysis", "evaluation", "assessment"],
  "css": ["css", "cascading style sheets", "styling", "stylesheet"],
  "customization": ["customization", "customize", "personalize", "tailor", "modify"],
  "customer-service": ["customer service", "support", "help", "assistance", "service"],
  "cyberpunk": ["cyberpunk", "futuristic", "neon", "sci-fi", "dystopian"],
  "cybersecurity": ["cybersecurity", "security", "cyber", "protection", "safety"],
  "data-cleaning": ["data cleaning", "data preprocessing", "data preparation"],
  "data-ethics": ["data ethics", "ethical data", "privacy", "responsible ai"],
  "data-science": ["data science", "analytics", "machine learning", "statistics"],
  "data-structure": ["data structure", "algorithm", "programming", "computer science"],
  "data-visualization": ["data visualization", "chart", "graph", "visual data"],
  "debugging": ["debugging", "debug", "troubleshooting", "error fixing", "bug fix"],
  "deep-learning": ["deep learning", "neural network", "ai", "machine learning"],
  "detailed": ["detailed", "comprehensive", "thorough", "in-depth", "extensive"],
  "dialogue": ["dialogue", "conversation", "speech", "discussion", "exchange"],
  "draft": ["draft", "rough", "preliminary", "initial version", "first draft"],
  "drama": ["drama", "dramatic", "theater", "theatrical", "emotional"],
  "e-commerce": ["e-commerce", "ecommerce", "online store", "shopping", "retail"],
  "edit": ["edit", "editing", "revision", "modification", "change"],
  "education": ["education", "educational", "learning", "teaching", "academic"],
  "educational": ["educational", "learning", "instructional", "pedagogical"],
  "efficiency-boost": ["efficiency boost", "productivity", "optimization", "improvement"],
  "elixir": ["elixir", "functional programming", "erlang"],
  "email": ["email", "electronic mail", "message", "communication"],
  "email-format": ["email format", "email structure", "message format"],
  "email-marketing": ["email marketing", "newsletter", "email campaign"],
  "empathetic": ["empathetic", "compassionate", "understanding", "caring"],
  "engagement": ["engagement", "interaction", "involvement", "participation"],
  "entrepreneurship": ["entrepreneurship", "startup", "business", "innovation"],
  "essay": ["essay", "written piece", "composition", "article"],
  "example": ["example", "instance", "case", "illustration", "sample"],
  "example-driven": ["example driven", "example-based", "demonstrative"],
  "explain": ["explain", "explanation", "clarify", "describe", "elaborate"],
  "experiment": ["experiment", "test", "trial", "investigation", "research"],
  "expert": ["expert", "advanced", "professional", "specialist", "master"],
  "extract": ["extract", "extraction", "retrieve", "obtain", "pull"],
  "fantasy": ["fantasy", "magical", "mythical", "fictional", "imaginative"],
  "faq": ["faq", "frequently asked questions", "questions", "help"],
  "fashion": ["fashion", "style", "clothing", "apparel", "trend"],
  "feature-engineering": ["feature engineering", "data preprocessing", "machine learning"],
  "few-shot": ["few shot", "few-shot learning", "limited examples"],
  "film-tv": ["film", "tv", "television", "movie", "cinema"],
  "finance": ["finance", "financial", "money", "economics", "investment"],
  "flashcards": ["flashcards", "study cards", "memory cards", "learning"],
  "food-drink": ["food", "drink", "culinary", "cooking", "recipe"],
  "formal": ["formal", "professional", "official", "structured"],
  "format": ["format", "structure", "layout", "organization"],
  "framework": ["framework", "structure", "foundation", "architecture"],
  "gaming": ["gaming", "games", "video games", "entertainment"],
  "generate": ["generate", "create", "produce", "make", "build"],
  "go": ["go", "golang", "programming language"],
  "guide": ["guide", "tutorial", "how-to", "instruction", "manual"],
  "graphic-design": ["graphic design", "visual design", "design", "graphics"],
  "haskell": ["haskell", "functional programming"],
  "healthcare": ["healthcare", "medical", "health", "medicine"],
  "historical": ["historical", "history", "past", "heritage", "historical"],
  "history": ["history", "historical", "past events", "chronicle"],
  "homework": ["homework", "assignment", "schoolwork", "study"],
  "horror": ["horror", "scary", "frightening", "spooky", "terror"],
  "hr": ["hr", "human resources", "personnel", "employment"],
  "html": ["html", "hypertext markup language", "web", "markup"],
  "humanities": ["humanities", "liberal arts", "culture", "society"],
  "humorous": ["humorous", "funny", "comedy", "witty", "amusing"],
  "ideate": ["ideate", "brainstorm", "idea generation", "creative thinking"],
  "illustration": ["illustration", "drawing", "artwork", "visual"],
  "image": ["image", "picture", "visual", "graphic", "photo"],
  "image-prompt": ["image prompt", "visual prompt", "picture description"],
  "image-weight": ["image weight", "visual emphasis", "image importance"],
  "in-depth-analysis": ["in-depth analysis", "detailed analysis", "comprehensive study"],
  "influencer-marketing": ["influencer marketing", "social media marketing"],
  "infographic": ["infographic", "visual information", "data visualization"],
  "inspiration": ["inspiration", "creative inspiration", "motivational"],
  "instruct": ["instruct", "teach", "guide", "direct", "command"],
  "instructional": ["instructional", "educational", "teaching", "learning"],
  "intermediate": ["intermediate", "middle level", "moderate", "standard"],
  "java": ["java", "programming language", "object oriented"],
  "javascript": ["javascript", "js", "web programming", "scripting"],
  "json": ["json", "javascript object notation", "data format"],
  "julia": ["julia", "programming language", "scientific computing"],
  "keywords": ["keywords", "key terms", "important words", "tags"],
  "landing-page": ["landing page", "web page", "conversion page"],
  "landscape": ["landscape", "scenery", "outdoor", "nature"],
  "language-learning": ["language learning", "linguistics", "foreign language"],
  "leadership": ["leadership", "management", "leading", "guidance"],
  "learning": ["learning", "education", "study", "knowledge"],
  "legal": ["legal", "law", "judicial", "legislation", "attorney"],
  "length": ["length", "size", "duration", "extent"],
  "lesson-plan": ["lesson plan", "curriculum", "teaching plan", "educational"],
  "library-management": ["library management", "organization", "cataloging"],
  "lifestyle": ["lifestyle", "living", "personal", "way of life"],
  "lisp": ["lisp", "functional programming", "symbolic"],
  "list": ["list", "enumeration", "items", "catalog", "inventory"],
  "literature": ["literature", "literary", "books", "writing"],
  "logo": ["logo", "brand", "identity", "symbol", "design"],
  "machine-learning": ["machine learning", "ml", "ai", "artificial intelligence"],
  "markdown": ["markdown", "markup language", "formatting", "documentation"],
  "marketing-sales": ["marketing", "sales", "promotion", "advertising"],
  "matlab": ["matlab", "scientific computing", "mathematical"],
  "metadata": ["metadata", "data about data", "information", "tags"],
  "midjourney": ["midjourney", "ai art", "image generation"],
  "minimalist": ["minimalist", "simple", "clean", "minimal"],
  "mojo": ["mojo", "programming language"],
  "monochromatic": ["monochromatic", "single color", "black and white"],
  "music-audio": ["music", "audio", "sound", "musical"],
  "muted": ["muted", "subtle", "understated", "soft"],
  "mystery": ["mystery", "suspense", "unknown", "puzzle"],
  "negative-prompt": ["negative prompt", "exclude", "avoid", "not"],
  "neon": ["neon", "bright", "glowing", "electric"],
  "newsletter": ["newsletter", "email", "subscription", "communication"],
  "nlp": ["nlp", "natural language processing", "linguistics", "text"],
  "objective": ["objective", "goal", "target", "purpose"],
  "one-pager": ["one pager", "single page", "summary", "brief"],
  "online-course": ["online course", "e-learning", "digital education"],
  "operations": ["operations", "operational", "business process"],
  "optimize": ["optimize", "optimization", "improve", "enhance"],
  "outline": ["outline", "structure", "framework", "plan"],
  "parameters": ["parameters", "settings", "variables", "options"],
  "pastel": ["pastel", "soft colors", "light tones", "gentle"],
  "persona": ["persona", "character", "personality", "role"],
  "persuasive": ["persuasive", "convincing", "compelling", "influential"],
  "photorealistic": ["photorealistic", "realistic", "lifelike", "detailed"],
  "pixel-art": ["pixel art", "8-bit", "retro", "pixelated"],
  "plan": ["plan", "planning", "strategy", "scheme"],
  "planning": ["planning", "plan", "strategy", "organization"],
  "plot": ["plot", "story", "narrative", "storyline"],
  "poem": ["poem", "poetry", "verse", "rhyme"],
  "portrait": ["portrait", "face", "person", "headshot"],
  "predictive-modeling": ["predictive modeling", "forecasting", "prediction"],
  "problem-solving": ["problem solving", "solution", "troubleshooting"],
  "product-description": ["product description", "product info", "specifications"],
  "productivity": ["productivity", "efficiency", "effectiveness", "output"]
};

// Category-Tool associations for boosting scores
export const categoryToolLinks: Record<string, string[]> = {
  "image-generation": ["midjourney", "stable-diffusion", "dall-e"],
  "code-generation": ["chatgpt", "claude", "gemini", "deepseek", "llama"],
  "creative-writing": ["chatgpt", "claude", "gemini", "llama"],
  "marketing": ["chatgpt", "claude", "jasper-ai"],
  "audio": ["elevenlabs", "suno"],
  "video": ["runway", "pika-labs", "sora"],
  "research": ["chatgpt", "claude", "gemini", "perplexity-ai"],
  "business": ["chatgpt", "claude", "jasper-ai"],
  "education": ["chatgpt", "claude", "gemini"],
  "email": ["chatgpt", "claude", "jasper-ai"],
  "copywriting": ["chatgpt", "jasper-ai"],
  "seo": ["chatgpt", "claude", "jasper-ai"]
};

// Tag-Category associations for boosting scores
export const tagCategoryLinks: Record<string, string[]> = {
  "3d": ["image-generation"],
  "3d-render": ["image-generation"],
  "abstract": ["image-generation", "creative-writing"],
  "academic": ["education", "research"],
  "ad-copy": ["marketing", "copywriting"],
  "code-development": ["code-generation"],
  "code-snippet": ["code-generation"],
  "content-creation": ["marketing", "creative-writing"],
  "email-marketing": ["email", "marketing"],
  "image-prompt": ["image-generation"],
  "music-audio": ["audio"],
  "photorealistic": ["image-generation"],
  "pixel-art": ["image-generation"],
  "product-description": ["marketing", "copywriting"]
}; 