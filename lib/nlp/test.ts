// lib/nlp/test.ts
// Simple test file to verify NLP implementation

import { analysePrompt } from './index';

/**
 * Test the NLP analysis with different prompt examples
 */
export async function runNLPTests() {
  console.log('🧠 Running NLP Analysis Tests...\n');

  const testPrompts = [
    {
      name: 'Midjourney Art Prompt',
      details: {
        title: 'Epic Fantasy Castle',
        promptText: 'A majestic fantasy castle perched on a cliff, cinematic lighting, photorealistic, --ar 16:9 --v 6',
        description: 'Generate an epic fantasy scene'
      },
      expected: { category: 'image-generation', tool: 'midjourney' }
    },
    {
      name: 'Code Generation Prompt',
      details: {
        title: 'Python Function',
        promptText: 'Write a Python function that sorts a list of dictionaries by a specific key. Include error handling and type hints.',
        description: 'Need help with Python programming'
      },
      expected: { category: 'code-generation', tool: 'chatgpt' }
    },
    {
      name: 'Creative Writing Prompt',
      details: {
        title: 'Short Story',
        promptText: 'Write a mystery story about a detective who discovers that the criminal they are chasing is actually their future self.',
        description: 'Creative writing exercise'
      },
      expected: { category: 'creative-writing', tool: 'chatgpt' }
    },
    {
      name: 'Marketing Copy Prompt',
      details: {
        title: 'Product Description',
        promptText: 'Create compelling ad copy for a new eco-friendly water bottle. Focus on sustainability benefits and target environmentally conscious consumers.',
        description: 'Marketing content needed'
      },
      expected: { category: 'marketing', tool: 'chatgpt' }
    },
    {
      name: 'Music Generation Prompt',
      details: {
        title: 'Song Creation',
        promptText: '[Verse 1] Walking down the street in the morning light [Chorus] This is our time to shine bright [Bridge] Never looking back, always moving forward',
        description: 'Generate a pop song'
      },
      expected: { category: 'audio', tool: 'suno' }
    }
  ];

  for (const test of testPrompts) {
    console.log(`\n📝 Testing: ${test.name}`);
    console.log(`Prompt: "${test.details.promptText?.substring(0, 80)}..."`);
    
    try {
      const result = await analysePrompt(test.details, { 
        includeDebug: true, 
        enableMLFallback: false // Disable for faster testing
      });

      console.log(`\n✅ Results:`);
      console.log(`  Category: ${result.category || 'None'} (expected: ${test.expected.category})`);
      console.log(`  Tool: ${result.tool || 'None'} (expected: ${test.expected.tool})`);
      console.log(`  Tags: ${result.tags?.join(', ') || 'None'}`);
      console.log(`  Confidence: Category=${result.confidence.category}, Tool=${result.confidence.tool}`);
      
      if (result.debug) {
        console.log(`  Debug: ${result.debug.processedTokensCount} tokens, ${result.debug.extractedKeywords.length} keywords`);
        console.log(`  Top keywords: ${result.debug.extractedKeywords.slice(0, 5).join(', ')}`);
      }

      // Check if prediction matches expectations
      const categoryMatch = result.category === test.expected.category;
      const toolMatch = result.tool === test.expected.tool;
      
      console.log(`\n${categoryMatch && toolMatch ? '✅ PASS' : '⚠️  PARTIAL/FAIL'}: ${categoryMatch ? 'Category✓' : 'Category✗'} ${toolMatch ? 'Tool✓' : 'Tool✗'}`);

    } catch (error) {
      console.log(`\n❌ ERROR: ${error}`);
    }

    console.log('\n' + '─'.repeat(80));
  }

  console.log('\n🎉 NLP Tests Complete!');
}

/**
 * Test specific functions in isolation
 */
export async function testIndividualFunctions() {
  console.log('\n🔧 Testing Individual Functions...\n');

  // Test tokenization
  const { getProcessedDoc } = await import('./tokenise');
  const doc = getProcessedDoc('Create a Python function for machine learning');
  console.log('Tokenization test:', doc.tokens);

  // Test keyword extraction
  const { extractKeyphrases } = await import('./keywordExtractor');
  const keywords = await extractKeyphrases('Generate a photorealistic image of a futuristic city');
  console.log('Keyword extraction test:', keywords);

  // Test rule engine
  const { calculateKeywordScores } = await import('./ruleEngine');
  const { categoryKeywords } = await import('./keywordData');
  const scores = calculateKeywordScores(['python', 'code', 'function'], categoryKeywords);
  console.log('Rule engine test:', Object.entries(scores).filter(([_, score]) => score > 0));
}

// Run tests if this file is executed directly
if (typeof window === 'undefined' && require.main === module) {
  runNLPTests()
    .then(() => testIndividualFunctions())
    .catch(console.error);
} 