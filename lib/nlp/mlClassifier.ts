// lib/nlp/mlClassifier.ts
// ML fallback classifier using fasttext.wasm for advanced categorization

export interface MLPrediction {
  label: string;
  probability: number;
}

/**
 * Classify text using fasttext.wasm for fallback ML classification
 * Only loads when the rule engine lacks confidence
 */
export async function classifyWithML(text: string): Promise<MLPrediction | null> {
  // Only run on client side
  if (typeof window === 'undefined') {
    console.warn('ML classification is only available on the client side');
    return null;
  }

  try {
    // Dynamic import of fasttext.wasm to avoid loading in initial bundle
    const { FastText } = await import('fasttext.wasm');
    
    // Initialize FastText instance
    const ft = await FastText.create();
    
    // Load the language identification model (or custom trained model)
    // Note: You would need to place a custom model in /public/models/
    // For now, this is a placeholder for the language ID model
    await ft.loadModel('/models/lid.176.ftz');
    
    // Get prediction (returns language for the default model)
    const result = ft.detect(text);
    
    // For language ID, we could map languages to categories/tools
    // This is a simplified example - in practice you'd need a custom model
    // trained on your prompt categorization data
    
    return {
      label: result || 'unknown',
      probability: 0.5 // Placeholder confidence
    };
    
  } catch (error) {
    console.warn('ML classification failed:', error);
    return null;
  }
}

/**
 * Check if ML classification should be used as fallback
 * Based on rule engine confidence and text characteristics
 */
export function shouldUseMlFallback(
  ruleEngineResults: {
    categoryConfidence?: 'high' | 'medium' | 'low' | null;
    toolConfidence?: 'high' | 'medium' | 'low' | null;
  },
  textLength: number,
  minTextLength: number = 20
): boolean {
  // Don't use ML for very short texts
  if (textLength < minTextLength) {
    return false;
  }

  // Use ML if rule engine has low confidence or no results
  const hasLowConfidence = 
    ruleEngineResults.categoryConfidence === 'low' || 
    ruleEngineResults.toolConfidence === 'low' ||
    (!ruleEngineResults.categoryConfidence && !ruleEngineResults.toolConfidence);

  return hasLowConfidence;
}

/**
 * Map ML prediction labels to our category/tool slugs
 * This would be customized based on your trained model's labels
 */
export function mapMLPredictionToCategories(prediction: MLPrediction): {
  category?: string;
  tool?: string;
  confidence: number;
} {
  // This is a placeholder mapping - you'd implement based on your model
  const labelToCategoryMap: Record<string, string> = {
    'en': 'creative-writing', // English text -> creative writing
    'code': 'code-generation',
    'tech': 'code-generation',
    // Add more mappings based on your model
  };

  const labelToToolMap: Record<string, string> = {
    'en': 'chatgpt', // Default to ChatGPT for English
    'code': 'chatgpt',
    // Add more mappings
  };

  return {
    category: labelToCategoryMap[prediction.label],
    tool: labelToToolMap[prediction.label],
    confidence: prediction.probability
  };
}

/**
 * Initialize ML models if needed
 * This can be called during app startup to pre-load models
 */
export async function initializeMLModels(): Promise<boolean> {
  try {
    // Pre-load fasttext.wasm in the background
    await import('fasttext.wasm');
    return true;
  } catch (error) {
    console.warn('Failed to initialize ML models:', error);
    return false;
  }
}

/**
 * Training data structure for custom model training
 * This would be used if you want to train a custom fasttext model
 */
export interface TrainingData {
  text: string;
  category: string;
  tool?: string;
  tags?: string[];
}

/**
 * Prepare training data for fasttext format
 * Converts our data structure to fasttext training format
 */
export function prepareTrainingData(data: TrainingData[]): string {
  return data
    .map(item => {
      const labels = [`__label__${item.category}`];
      if (item.tool) {
        labels.push(`__label__tool_${item.tool}`);
      }
      if (item.tags) {
        labels.push(...item.tags.map(tag => `__label__tag_${tag}`));
      }
      return `${labels.join(' ')} ${item.text}`;
    })
    .join('\n');
}

/**
 * Evaluate ML model performance
 * Used for testing custom trained models
 */
export interface ModelEvaluation {
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
}

export async function evaluateModel(
  testData: TrainingData[]
): Promise<ModelEvaluation | null> {
  // Placeholder for model evaluation logic
  // Would implement actual evaluation using test data
  console.log('Model evaluation not implemented yet');
  
  return {
    accuracy: 0.85,
    precision: 0.83,
    recall: 0.87,
    f1Score: 0.85
  };
} 