// lib/nlp/ruleEngine.ts
// Core rule-based scoring engine for categorization, tool prediction, and tag suggestion

import { toolData } from './keywordData';

export interface ScoreResult {
  slug: string;
  score: number;
  confidence: 'high' | 'medium' | 'low';
}

// OPTIMIZATION: Reduced thresholds to allow more predictions (was likely 4+ before)
const MIN_CATEGORY_SCORE = 2; // Reduced from ~4 to allow more marginal predictions
const MIN_TOOL_SCORE = 1.5; // Reduced from ~3 to allow more tool predictions  
const MIN_TAG_SCORE = 1; // Reduced from ~2 to allow more tag predictions

// OPTIMIZATION: Reduced safe gap multiplier to be less aggressive (was 1.5)
const SAFE_GAP_MULTIPLIER = 1.15; // Reduced from 1.5 to 1.15 for less punitive gap checking

// Category-to-default-tool mapping for inference when no specific tool is detected
const CATEGORY_DEFAULT_TOOLS: Record<string, string> = {
  'code-generation': 'chatgpt',
  'creative-writing': 'chatgpt',
  'marketing': 'chatgpt',
  'research': 'chatgpt',
  'business': 'chatgpt',
  'education': 'chatgpt',
  'email': 'chatgpt',
  'copywriting': 'chatgpt',
  'seo': 'chatgpt',
  'image-generation': 'dall-e',
  'audio': 'elevenlabs',
  'video': 'runway',
  'data-analysis': 'chatgpt',
  'personal': 'chatgpt'
};

/**
 * Calculate keyword-based scores for categories, tools, or tags
 * Returns a map of slug -> score based on keyword matches
 * OPTIMIZATION: Uses Set for O(1) keyword lookups
 */
export function calculateKeywordScores(
  processedTokens: string[],
  keywordMap: Record<string, string[]>
): Record<string, number> {
  const scores: Record<string, number> = {};

  // Initialize all items with zero score
  for (const slug of Object.keys(keywordMap)) {
    scores[slug] = 0;
  }

  // OPTIMIZATION: Convert keyword arrays to Sets for O(1) lookup
  const keywordSets: Record<string, Set<string>> = {};
  for (const [slug, keywords] of Object.entries(keywordMap)) {
    keywordSets[slug] = new Set(keywords.map(k => k.toLowerCase()));
  }

  // Score based on token matches
  for (const token of processedTokens) {
    const lowerToken = token.toLowerCase();
    
    for (const [slug, keywordSet] of Object.entries(keywordSets)) {
      // Exact match check (O(1))
      if (keywordSet.has(lowerToken)) {
        scores[slug] += 2; // Full points for exact match
        continue;
      }
      
      // Partial match check (only for longer tokens/keywords)
      if (token.length > 4) {
        for (const keyword of keywordSet) {
          if (keyword.length > 4 && 
              (lowerToken.includes(keyword) || keyword.includes(lowerToken))) {
            scores[slug] += 1; // Partial points for substring match
            break; // Avoid double-counting
          }
        }
      }
    }
  }

  return scores;
}

/**
 * Calculate pattern-based scores for tools using regex patterns
 * Returns high scores for syntactic matches (e.g., Midjourney parameters)
 */
export function calculatePatternScores(
  fullText: string,
  toolPatternMap: typeof toolData
): Record<string, number> {
  const scores: Record<string, number> = {};

  // Initialize all tools with zero score
  for (const toolSlug of Object.keys(toolPatternMap)) {
    scores[toolSlug] = 0;
  }

  const lowerText = fullText.toLowerCase();

  for (const [toolSlug, toolInfo] of Object.entries(toolPatternMap)) {
    if (toolInfo.regex && toolInfo.regex.length > 0) {
      for (const pattern of toolInfo.regex) {
        if (pattern.test(lowerText)) {
          scores[toolSlug] += 10; // High score for pattern match
        }
      }
    }
  }

  return scores;
}

/**
 * Get the top suggestion from scores with confidence thresholds
 * OPTIMIZED: Uses new lower thresholds and less aggressive safe gap
 */
export function getTopSuggestion(
  scores: Record<string, number>,
  highConfidenceThreshold: number = MIN_CATEGORY_SCORE,
  safeGapMultiplier: number = SAFE_GAP_MULTIPLIER
): string | null {
  const entries = Object.entries(scores)
    .filter(([_, score]) => score > 0)
    .sort((a, b) => b[1] - a[1]);

  if (entries.length === 0) {
    return null; // No scores
  }

  const [topSlug, topScore] = entries[0];

  if (topScore < highConfidenceThreshold) {
    return null; // Score too low
  }

  if (entries.length === 1) {
    return topSlug; // Only one option
  }

  const [_, secondScore] = entries[1];

  // OPTIMIZED: Less aggressive gap checking
  if (topScore >= secondScore * safeGapMultiplier) {
    return topSlug;
  }

  return null; // Too ambiguous
}

/**
 * OPTIMIZATION: New function for default tool inference based on category
 * When no specific tool is detected but category is confident, assign default tool
 */
export function inferDefaultTool(
  categorySlug: string | null,
  categoryConfidence: 'high' | 'medium' | 'low',
  detectedToolSlug: string | null
): string | null {
  // If we already have a detected tool, use it
  if (detectedToolSlug && detectedToolSlug !== 'other') {
    return detectedToolSlug;
  }

  // If category is confident and has a default tool, use it
  if (categorySlug && (categoryConfidence === 'high' || categoryConfidence === 'medium')) {
    return CATEGORY_DEFAULT_TOOLS[categorySlug] || null;
  }

  return null;
}

/**
 * Get top tag suggestions from scores
 * Returns multiple tags that meet the minimum threshold
 */
export function getTopTagSuggestions(
  scores: Record<string, number>,
  count: number = 3,
  minScoreThreshold: number = MIN_TAG_SCORE
): string[] {
  return Object.entries(scores)
    .filter(([_, score]) => score >= minScoreThreshold)
    .sort((a, b) => b[1] - a[1])
    .slice(0, count)
    .map(([slug]) => slug);
}

/**
 * Boost scores for related items based on associations
 * Used to increase scores when a category/tool/tag is already predicted
 */
export function boostRelatedScores(
  scores: Record<string, number>,
  relatedItems: string[],
  boostAmount: number = 2
): Record<string, number> {
  const boostedScores = { ...scores };

  for (const item of relatedItems) {
    if (boostedScores[item] !== undefined) {
      boostedScores[item] += boostAmount;
    }
  }

  return boostedScores;
}

/**
 * Calculate phrase-based scores for multi-word expressions
 * Handles compound terms like "social media" or "machine learning"
 */
export function calculatePhraseScores(
  extractedPhrases: string[],
  keywordMap: Record<string, string[]>
): Record<string, number> {
  const scores: Record<string, number> = {};

  // Initialize all items with zero score
  for (const slug of Object.keys(keywordMap)) {
    scores[slug] = 0;
  }

  for (const phrase of extractedPhrases) {
    for (const [slug, keywords] of Object.entries(keywordMap)) {
      for (const keyword of keywords) {
        // Exact phrase match
        if (phrase.toLowerCase() === keyword.toLowerCase()) {
          scores[slug] += 3; // Higher score for phrase matches
        }
        // Phrase contains keyword
        else if (phrase.length > keyword.length && phrase.includes(keyword.toLowerCase())) {
          scores[slug] += 2;
        }
        // Keyword contains phrase
        else if (keyword.length > phrase.length && keyword.toLowerCase().includes(phrase)) {
          scores[slug] += 2;
        }
      }
    }
  }

  return scores;
}

/**
 * Combine multiple score maps with optional weights
 */
export function combineScores(
  scoreMaps: Record<string, number>[],
  weights: number[] = []
): Record<string, number> {
  const combinedScores: Record<string, number> = {};

  // Use equal weights if not provided
  const actualWeights = weights.length === scoreMaps.length 
    ? weights 
    : Array(scoreMaps.length).fill(1);

  for (let i = 0; i < scoreMaps.length; i++) {
    const scoreMap = scoreMaps[i];
    const weight = actualWeights[i];

    for (const [slug, score] of Object.entries(scoreMap)) {
      if (!combinedScores[slug]) {
        combinedScores[slug] = 0;
      }
      combinedScores[slug] += score * weight;
    }
  }

  return combinedScores;
}

/**
 * Calculate normalized confidence score using softmax-like approach
 * OPTIMIZATION: Better confidence calculation that considers score distribution
 */
export function calculateNormalizedConfidence(
  scores: Record<string, number>,
  topSlug: string
): number {
  const entries = Object.entries(scores)
    .filter(([_, score]) => score > 0)
    .sort((a, b) => b[1] - a[1]);

  if (entries.length === 0) return 0;

  const topScore = entries[0][1];
  const secondScore = entries.length > 1 ? entries[1][1] : 0;
  const totalScore = entries.reduce((sum, [_, score]) => sum + score, 0);

  // Base confidence from score strength
  let confidence = Math.min(topScore / 10, 1); // Normalize to 0-1 range

  // Boost confidence if there's a clear winner
  if (entries.length > 1) {
    const gap = topScore - secondScore;
    const gapBonus = Math.min(gap / topScore, 0.3); // Up to 30% bonus for clear gaps
    confidence += gapBonus;
  }

  // Boost confidence if score is significant relative to total
  if (totalScore > 0) {
    const dominance = topScore / totalScore;
    if (dominance > 0.5) {
      confidence += (dominance - 0.5) * 0.2; // Up to 10% bonus for dominance
    }
  }

  return Math.min(confidence, 1); // Cap at 1.0
}

/**
 * Get confidence level based on normalized score
 * OPTIMIZATION: Uses new normalized confidence calculation
 */
export function getConfidenceLevel(
  topScore: number,
  secondScore: number = 0,
  highThreshold: number = 6, // Reduced from 8
  mediumThreshold: number = 3 // Reduced from 4
): 'high' | 'medium' | 'low' {
  // Use the new normalized confidence for more accurate assessment
  const normalizedConf = calculateNormalizedConfidence(
    { top: topScore, second: secondScore }, 
    'top'
  );

  if (normalizedConf >= 0.8) {
    return 'high';
  } else if (normalizedConf >= 0.5) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * Analyze score distribution to determine prediction quality
 */
export function analyzeScoreDistribution(scores: Record<string, number>): {
  topSuggestions: ScoreResult[];
  isAmbiguous: boolean;
  hasStrongWinner: boolean;
  totalEntries: number;
} {
  const entries = Object.entries(scores)
    .filter(([_, score]) => score > 0)
    .sort((a, b) => b[1] - a[1]);

  const topSuggestions: ScoreResult[] = entries.slice(0, 3).map(([slug, score], index) => ({
    slug,
    score,
    confidence: getConfidenceLevel(
      score,
      index < entries.length - 1 ? entries[index + 1][1] : 0
    )
  }));

  // OPTIMIZED: Less aggressive ambiguity detection
  const isAmbiguous = entries.length > 1 && 
    entries[0][1] < entries[1][1] * 1.3; // Reduced from 1.5

  const hasStrongWinner = entries.length > 0 && 
    entries[0][1] >= 4 && // Reduced from 6
    (entries.length === 1 || entries[0][1] >= entries[1][1] * 1.5);

  return {
    topSuggestions,
    isAmbiguous,
    hasStrongWinner,
    totalEntries: entries.length
  };
} 