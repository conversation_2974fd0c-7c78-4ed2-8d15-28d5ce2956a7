// lib/nlp/tokenise.ts
// Compromise wrapper for text processing, tokenization, and lemmatization

import nlp from 'compromise';

// Configure compromise for fast processing
nlp.plugin({
  // Add custom lexicon entries if needed
  words: {
    // Custom words can be added here
  }
});

export interface ProcessedDoc {
  tokens: string[];
  lemmas: string[];
  text: string;
  length: number;
}

/**
 * Process text using compromise NLP library
 * Returns normalized tokens, lemmas, and metadata
 * OPTIMIZATION: Uses lightweight processing for short prompts
 */
export function getProcessedDoc(text: string): ProcessedDoc {
  if (!text || typeof text !== 'string') {
    return {
      tokens: [],
      lemmas: [],
      text: '',
      length: 0
    };
  }

  // Clean and normalize text
  const cleanText = text
    .toLowerCase()
    .replace(/[^\w\s-]/g, ' ') // Keep word chars, spaces, and hyphens
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  if (!cleanText) {
    return {
      tokens: [],
      lemmas: [],
      text: cleanText,
      length: 0
    };
  }

  // OPTIMIZATION: Use lightweight processing for short prompts
  if (cleanText.length < 50) {
    const simpleTokens = cleanText
      .split(/\s+/)
      .filter(token => token.length > 2) // Filter very short terms
      .filter(token => !isStopWord(token)) // Remove stop words
      .map(token => token.toLowerCase());

    // Deduplicate
    const uniqueTokens = Array.from(new Set(simpleTokens));

    return {
      tokens: uniqueTokens,
      lemmas: uniqueTokens, // Use tokens as lemmas for simplicity
      text: cleanText,
      length: uniqueTokens.length
    };
  }

  // Full processing with compromise for longer texts
  const doc = nlp(cleanText);

  // Extract tokens (terms)
  const tokens = doc
    .terms()
    .out('array')
    .filter((term: string) => term.length > 2) // Filter very short terms
    .filter((term: string) => !isStopWord(term)) // Remove stop words
    .map((term: string) => term.toLowerCase());

  // For now, use tokens as lemmas (compromise lemmatization is complex)
  // This can be enhanced later with proper lemmatization
  const lemmas = [...tokens];

  // Combine and deduplicate
  const combinedTokens = Array.from(new Set([...tokens, ...lemmas]));

  return {
    tokens: combinedTokens,
    lemmas: lemmas,
    text: cleanText,
    length: combinedTokens.length
  };
}

/**
 * Simple stop word filter
 * Removes common English words that don't contribute to categorization
 */
function isStopWord(word: string): boolean {
  const stopWords = new Set([
    'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
    'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
    'to', 'was', 'will', 'with', 'you', 'your', 'i', 'me', 'my', 'we',
    'us', 'our', 'they', 'them', 'their', 'this', 'these', 'those',
    'what', 'where', 'when', 'why', 'how', 'who', 'which', 'can', 'could',
    'would', 'should', 'may', 'might', 'must', 'do', 'does', 'did',
    'have', 'had', 'get', 'got', 'make', 'made', 'go', 'went', 'come',
    'came', 'take', 'took', 'see', 'saw', 'know', 'knew', 'think',
    'thought', 'say', 'said', 'tell', 'told', 'give', 'gave', 'find',
    'found', 'use', 'used', 'work', 'worked', 'call', 'called'
  ]);

  return stopWords.has(word.toLowerCase());
}

/**
 * Extract key phrases and compound terms from text
 * Useful for finding multi-word expressions
 */
export function extractPhrases(text: string): string[] {
  if (!text || typeof text !== 'string') {
    return [];
  }

  const doc = nlp(text.toLowerCase());
  
  // Extract noun phrases
  const nounPhrases = doc
    .nouns()
    .filter((noun: any) => noun.text().length > 3) // Filter short phrases
    .out('array')
    .map((phrase: string) => phrase.toLowerCase());

  // Extract compound terms (hyphenated words)
  const compounds = text
    .toLowerCase()
    .match(/\b\w+[-]\w+\b/g) || [];

  // Combine and deduplicate
  return Array.from(new Set([...nounPhrases, ...compounds]));
}

/**
 * Get weighted text combination with different emphasis
 * Used to combine title, description, prompt text with different weights
 */
export function getWeightedText(fields: {
  title?: string;
  description?: string;
  promptText?: string;
  instructions?: string;
  exampleInput?: string;
  exampleOutput?: string;
}): string {
  const parts: string[] = [];

  // Add fields with repetition for weighting
  if (fields.title) {
    // Title gets 3x weight
    parts.push(fields.title, fields.title, fields.title);
  }

  if (fields.promptText) {
    // Prompt text gets 5x weight
    parts.push(
      fields.promptText,
      fields.promptText,
      fields.promptText,
      fields.promptText,
      fields.promptText
    );
  }

  if (fields.description) {
    // Description gets 1x weight
    parts.push(fields.description);
  }

  if (fields.instructions) {
    // Instructions get 2x weight
    parts.push(fields.instructions, fields.instructions);
  }

  if (fields.exampleInput) {
    parts.push(fields.exampleInput);
  }

  if (fields.exampleOutput) {
    parts.push(fields.exampleOutput);
  }

  return parts.join(' ').trim();
}

/**
 * Check if text has enough content for meaningful analysis
 */
export function hasMinimumContent(text: string, minWords: number = 5): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }

  const words = text.trim().split(/\s+/).filter(word => word.length > 0);
  return words.length >= minWords;
} 