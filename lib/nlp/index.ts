// lib/nlp/index.ts
// Main NLP analysis service that combines all modules

import { 
  calculateKeywordScores, 
  calculatePatternScores, 
  calculatePhraseScores,
  getTopSuggestion, 
  getTopTagSuggestions,
  boostRelatedScores,
  combineScores,
  analyzeScoreDistribution,
  inferDefaultTool
} from './ruleEngine';

// Conditional import for client-side only
let classifyWithML: any = null;
let shouldUseMlFallback: any = null;
let mapMLPredictionToCategories: any = null;

if (typeof window !== 'undefined') {
  import('./mlClassifier').then(module => {
    classifyWithML = module.classifyWithML;
    shouldUseMlFallback = module.shouldUseMlFallback;
    mapMLPredictionToCategories = module.mapMLPredictionToCategories;
  }).catch(err => {
    console.warn('Failed to load ML classifier:', err);
  });
}

export interface PromptDetails {
  title?: string;
  description?: string;
  promptText?: string;
  instructions?: string;
  exampleInput?: string;
  exampleOutput?: string;
}

export interface AnalysisResult {
  category?: string;
  tool?: string;
  tags?: string[];
  confidence: {
    category?: 'high' | 'medium' | 'low';
    tool?: 'high' | 'medium' | 'low';
    tags?: 'high' | 'medium' | 'low';
  };
  debug?: {
    processedTokensCount: number;
    extractedPhrases: string[];
    categoryScores: Record<string, number>;
    toolScores: Record<string, number>;
    tagScores: Record<string, number>;
    usedMLFallback: boolean;
  };
}

/**
 * Main function to analyze prompt details and suggest categorization
 * Combines rule-based analysis with optional ML fallback
 */
export async function analysePrompt(
  details: PromptDetails,
  options: {
    includeDebug?: boolean;
    enableMLFallback?: boolean;
    maxTags?: number;
  } = {}
): Promise<AnalysisResult> {
  const { includeDebug = false, enableMLFallback = true, maxTags = 3 } = options;

  // Lazy load heavy NLP dependencies
  const [
    { getProcessedDoc, getWeightedText, hasMinimumContent, extractPhrases },
    { categoryKeywords, toolData, tagKeywords, categoryToolLinks, tagCategoryLinks }
  ] = await Promise.all([
    import('./tokenise'),
    import('./keywordData')
  ]);

  // Check if we have enough content to analyze
  const combinedText = getWeightedText(details);
  if (!hasMinimumContent(combinedText)) {
    return {
      confidence: {},
      debug: includeDebug ? {
        processedTokensCount: 0,
        extractedPhrases: [],
        categoryScores: {},
        toolScores: {},
        tagScores: {},
        usedMLFallback: false
      } : undefined
    };
  }

  // Process text with compromise
  const processedDoc = getProcessedDoc(combinedText);
  
  // Extract phrases using existing tokenise module
  const extractedPhrases = extractPhrases(combinedText);
  
  // Combine all extracted terms
  const allTerms = [
    ...processedDoc.tokens,
    ...extractedPhrases
  ];

  // **TOOL PREDICTION (Syntactical patterns first)**
  const patternScores = calculatePatternScores(details.promptText || combinedText, toolData);
  let suggestedToolSlug = getTopSuggestion(patternScores, 8); // High threshold for patterns

  // **CATEGORY PREDICTION**
  const categoryKeywordScores = calculateKeywordScores(allTerms, categoryKeywords);
  const categoryPhraseScores = calculatePhraseScores(extractedPhrases, categoryKeywords);
  
  // Combine category scores
  const combinedCategoryScores = combineScores([categoryKeywordScores, categoryPhraseScores], [1, 1.5]);
  
  // Boost categories linked to the suggested tool
  let finalCategoryScores = combinedCategoryScores;
  if (suggestedToolSlug && toolData[suggestedToolSlug]?.categories) {
    finalCategoryScores = boostRelatedScores(
      combinedCategoryScores, 
      toolData[suggestedToolSlug].categories, 
      3
    );
  }

  let suggestedCategorySlug = getTopSuggestion(finalCategoryScores, 2);

  // **TOOL PREDICTION (Keyword-based fallback)**
  if (!suggestedToolSlug) {
    // Create tool keyword map from toolData
    const toolKeywordMap = Object.fromEntries(
      Object.entries(toolData).map(([slug, data]) => [slug, data.keywords])
    );
    
    const toolKeywordScores = calculateKeywordScores(allTerms, toolKeywordMap);
    
    // Boost tools linked to suggested category
    let finalToolScores = toolKeywordScores;
    if (suggestedCategorySlug && categoryToolLinks[suggestedCategorySlug]) {
      finalToolScores = boostRelatedScores(
        toolKeywordScores,
        categoryToolLinks[suggestedCategorySlug],
        2
      );
    }

    suggestedToolSlug = getTopSuggestion(finalToolScores, 1.5);
  }

  // **OPTIMIZATION: Default tool inference based on category**
  // If still no tool detected but we have a confident category, infer default tool
  if (!suggestedToolSlug) {
    const categoryAnalysis = analyzeScoreDistribution(finalCategoryScores);
    const categoryConfidence = categoryAnalysis.hasStrongWinner ? 'high' : 
                              categoryAnalysis.isAmbiguous ? 'low' : 'medium';
    
    suggestedToolSlug = inferDefaultTool(
      suggestedCategorySlug,
      categoryConfidence,
      suggestedToolSlug
    );
  }

  // **TAG PREDICTION**
  const tagKeywordScores = calculateKeywordScores(allTerms, tagKeywords);
  const tagPhraseScores = calculatePhraseScores(extractedPhrases, tagKeywords);
  
  // Combine tag scores
  let finalTagScores = combineScores([tagKeywordScores, tagPhraseScores], [1, 1.2]);
  
  // Boost tags related to predicted category
  if (suggestedCategorySlug) {
    const relatedTags = Object.keys(tagCategoryLinks).filter(tag =>
      tagCategoryLinks[tag].includes(suggestedCategorySlug!)
    );
    finalTagScores = boostRelatedScores(finalTagScores, relatedTags, 2);
  }

  const suggestedTagSlugs = getTopTagSuggestions(finalTagScores, maxTags, 1);

  // **ML FALLBACK (if enabled and needed)**
  let usedMLFallback = false;
  let mlResult: any = null;

  if (enableMLFallback && shouldUseMlFallback && classifyWithML && mapMLPredictionToCategories) {
    const categoryAnalysis = analyzeScoreDistribution(finalCategoryScores);
    const toolAnalysis = analyzeScoreDistribution(patternScores);

    const ruleEngineResults = {
      categoryConfidence: (categoryAnalysis.hasStrongWinner ? 'high' : 
                          categoryAnalysis.isAmbiguous ? 'low' : 'medium') as 'high' | 'medium' | 'low',
      toolConfidence: (toolAnalysis.hasStrongWinner ? 'high' : 
                     toolAnalysis.isAmbiguous ? 'low' : 'medium') as 'high' | 'medium' | 'low'
    };

    if (shouldUseMlFallback(ruleEngineResults, processedDoc.length)) {
      mlResult = await classifyWithML(combinedText);
      if (mlResult) {
        usedMLFallback = true;
        const mlMapping = mapMLPredictionToCategories(mlResult);
        
        // Use ML results if rule engine was uncertain
        if (!suggestedCategorySlug && mlMapping.category) {
          suggestedCategorySlug = mlMapping.category;
        }
        if (!suggestedToolSlug && mlMapping.tool) {
          suggestedToolSlug = mlMapping.tool;
        }
      }
    }
  }

  // **CONFIDENCE CALCULATION**
  const categoryAnalysis = analyzeScoreDistribution(finalCategoryScores);
  const toolKeywordMap = Object.fromEntries(
    Object.entries(toolData).map(([slug, data]) => [slug, data.keywords])
  );
  const toolKeywordScores = calculateKeywordScores(allTerms, toolKeywordMap);
  const toolAnalysis = analyzeScoreDistribution({...patternScores, ...toolKeywordScores});
  const tagAnalysis = analyzeScoreDistribution(finalTagScores);

  const result: AnalysisResult = {
    category: suggestedCategorySlug || undefined,
    tool: suggestedToolSlug || undefined,
    tags: suggestedTagSlugs.length > 0 ? suggestedTagSlugs : undefined,
    confidence: {
      category: categoryAnalysis.hasStrongWinner ? 'high' : 
                categoryAnalysis.isAmbiguous ? 'low' : 'medium',
      tool: toolAnalysis.hasStrongWinner ? 'high' : 
            toolAnalysis.isAmbiguous ? 'low' : 'medium',
      tags: tagAnalysis.hasStrongWinner ? 'high' : 
            tagAnalysis.isAmbiguous ? 'low' : 'medium'
    }
  };

  // Add debug information if requested
  if (includeDebug) {
    result.debug = {
      processedTokensCount: processedDoc.length,
      extractedPhrases: extractedPhrases,
      categoryScores: finalCategoryScores,
      toolScores: { ...patternScores, ...toolKeywordScores },
      tagScores: finalTagScores,
      usedMLFallback
    };
  }

  return result;
}

/**
 * Simple wrapper for basic categorization without full analysis
 */
export async function quickAnalyse(text: string): Promise<{
  category?: string;
  tool?: string;
}> {
  const result = await analysePrompt({ promptText: text }, { enableMLFallback: false });
  return {
    category: result.category,
    tool: result.tool
  };
}

/**
 * Batch analysis for multiple prompts
 */
export async function batchAnalyse(
  prompts: PromptDetails[],
  options?: { 
    includeDebug?: boolean;
    enableMLFallback?: boolean;
  }
): Promise<AnalysisResult[]> {
  return Promise.all(prompts.map(prompt => analysePrompt(prompt, options)));
}

// Export utilities - these will now be lazy-loaded when needed
export async function getProcessedDocAsync(text: string) {
  const { getProcessedDoc } = await import('./tokenise');
  return getProcessedDoc(text);
}

export { calculateKeywordScores, getTopSuggestion } from './ruleEngine'; 