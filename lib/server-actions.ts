"use server"

import { createReadOnlySupabaseClient } from "./supabase/server"
import { transformPromptCard, transformCategory, transformTool, transformTag, transformPromptCardWithSaved } from "./transformers"
import type { PromptCard, Category, Tool, Tag } from "./types"
import { logPromptShortIdIssues } from "./utils/debug-helpers"

// Server action for fetching prompts with saved status
export async function getPromptsServerAction({
  categorySlugs,
  toolSlugs,
  tagSlugs,
  aiModelSlugs,
  searchQuery,
  userId,
  currentUserId,
  limit = 20,
  offset = 0,
  sortBy = "created_at",
  sortOrder = "desc",
}: {
  categorySlugs?: string[];
  toolSlugs?: string[];
  tagSlugs?: string[];
  aiModelSlugs?: string[];
  searchQuery?: string;
  userId?: string;
  currentUserId?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}): Promise<PromptCard[]> {
  try {
    const supabase = await createReadOnlySupabaseClient()

    // Always use the unified RPC function for all users (authenticated and anonymous)
    console.log("[getPromptsServerAction] Using unified RPC function for all users");

    // Validate currentUserId if provided
    if (currentUserId) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(currentUserId)) {
        console.warn("[getPromptsServerAction] Invalid UUID format for currentUserId, treating as anonymous:", currentUserId);
        currentUserId = undefined; // Treat as anonymous user
      }
    }

    // Convert empty arrays to null (RPC function doesn't handle empty arrays correctly)
    const normalizeArray = (arr: string[] | undefined) => {
      return arr && arr.length > 0 ? arr : null;
    };

    const { data, error } = await supabase.rpc("get_prompts_unified", {
      p_user_id: currentUserId || null, // Pass null for anonymous users
      p_limit: limit,
      p_offset: offset,
      p_category_slugs: normalizeArray(categorySlugs),
      p_tool_slugs: normalizeArray(toolSlugs),
      p_tag_slugs: normalizeArray(tagSlugs),
      p_ai_model_slugs: normalizeArray(aiModelSlugs),
      p_search_query: searchQuery || null,
      p_author_id: userId || null,
      p_sort_by: sortBy,
      p_sort_order: sortOrder,
    });

    if (error) {
      console.error("[getPromptsServerAction] RPC function error:", {
        message: error.message || 'No error message',
        details: error.details || 'No error details',
        hint: error.hint || 'No error hint',
        code: error.code || 'No error code',
        fullError: error,
        errorType: typeof error,
        errorKeys: Object.keys(error || {}),
        queryParams: {
          categorySlugs,
          toolSlugs,
          tagSlugs,
          aiModelSlugs,
          searchQuery,
          userId,
          currentUserId,
          sortBy,
          sortOrder,
          limit,
          offset
        }
      });
      throw error;
    }

    console.log("[getPromptsServerAction] RPC function success, data length:", Array.isArray(data) ? data.length : 0);
    const transformedData = Array.isArray(data) ? data.map(transformPromptCardWithSaved) : [];
    logPromptShortIdIssues(data as any[], "getPromptsServerAction unified");
    return transformedData;
  } catch (error) {
    console.error("Error in getPromptsServerAction:", {
      error,
      message: error instanceof Error ? error.message : 'Unknown error',
      errorType: typeof error,
      errorKeys: Object.keys(error || {}),
      stack: error instanceof Error ? error.stack : 'No stack trace'
    });
    return [];
  }
}

// Server action for fetching categories
export async function getCategoriesServerAction(): Promise<Category[]> {
  try {
    const supabase = await createReadOnlySupabaseClient()
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .order("name");

    if (error) {
      console.error("Error fetching categories:", error);
      throw error;
    }

    return (data || []).map(transformCategory);
  } catch (error) {
    console.error("Error in getCategoriesServerAction:", error);
    return [];
  }
}

// Server action for fetching tools
export async function getToolsServerAction(): Promise<Tool[]> {
  try {
    const supabase = await createReadOnlySupabaseClient()
    const { data, error } = await supabase
      .from("tools")
      .select("*")
      .order("name");

    if (error) {
      console.error("Error fetching tools:", error);
      throw error;
    }

    return (data || []).map(transformTool);
  } catch (error) {
    console.error("Error in getToolsServerAction:", error);
    return [];
  }
}

// Server action for fetching tags
export async function getTagsServerAction(): Promise<Tag[]> {
  try {
    const supabase = await createReadOnlySupabaseClient()
    const { data, error } = await supabase
      .from("tags")
      .select("*")
      .order("name");

    if (error) {
      console.error("Error fetching tags:", error);
      throw error;
    }

    return (data || []).map(transformTag);
  } catch (error) {
    console.error("Error in getTagsServerAction:", error);
    return [];
  }
}

// Server action for fetching saved prompts data for instant tab switching
export async function getSavedPromptsDataServerAction(currentUserId: string): Promise<{
  allPrompts: PromptCard[];
  savedPrompts: PromptCard[];
  myPrompts: PromptCard[];
  counts: {
    all: number;
    saved: number;
    my: number;
  };
}> {
  try {
    const supabase = await createReadOnlySupabaseClient()

    // Fetch user's own prompts using the unified RPC function
    const { data: myPromptsData, error: myPromptsError } = await supabase.rpc("get_prompts_unified", {
      p_user_id: currentUserId,
      p_limit: 1000, // Get all user's prompts
      p_offset: 0,
      p_category_slugs: null,
      p_tool_slugs: null,
      p_tag_slugs: null,
      p_ai_model_slugs: null,
      p_search_query: null,
      p_author_id: currentUserId, // Filter by author
      p_sort_by: "created_at",
      p_sort_order: "desc",
    });

    if (myPromptsError) {
      console.error("Error fetching my prompts:", myPromptsError);
    }

    const myPrompts = (myPromptsData || []).map(transformPromptCardWithSaved);

    // Fetch saved prompts from all user's collections
    const { data: userCollections, error: collectionsError } = await supabase
      .from("collections")
      .select("id")
      .eq("user_id", currentUserId);

    let savedPrompts: PromptCard[] = [];
    if (!collectionsError && userCollections && userCollections.length > 0) {
      const collectionIds = userCollections.map((c: any) => c.id);
      
      // Get saved prompts with details
      const { data: savedPromptsData, error: savedPromptsError } = await supabase
        .from("collection_prompts")
        .select(`
          prompt_id,
          prompts!inner(*)
        `)
        .in("collection_id", collectionIds);

      if (!savedPromptsError && savedPromptsData) {
        // Transform saved prompts data with isSaved flag
        savedPrompts = savedPromptsData
          .map((item: any) => item.prompts)
          .filter(Boolean)
          .map((prompt: any) => transformPromptCardWithSaved({ ...prompt, is_saved_by_user: true }));
      }
    }

    // Combine all prompts (user's + saved, removing duplicates)
    const allPromptsMap = new Map();
    
    // Add user's prompts
    myPrompts.forEach(prompt => {
      allPromptsMap.set(prompt.id, prompt);
    });
    
    // Add saved prompts (mark as saved)
    savedPrompts.forEach(prompt => {
      if (allPromptsMap.has(prompt.id)) {
        // Update existing prompt to mark as saved
        const existing = allPromptsMap.get(prompt.id);
        allPromptsMap.set(prompt.id, { ...existing, isSaved: true });
      } else {
        // Add new saved prompt
        allPromptsMap.set(prompt.id, { ...prompt, isSaved: true });
      }
    });

    const allPrompts = Array.from(allPromptsMap.values())
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return {
      allPrompts,
      savedPrompts,
      myPrompts,
      counts: {
        all: allPrompts.length,
        saved: savedPrompts.length,
        my: myPrompts.length,
      },
    };
  } catch (error) {
    console.error("Error in getSavedPromptsDataServerAction:", error);
    return {
      allPrompts: [],
      savedPrompts: [],
      myPrompts: [],
      counts: { all: 0, saved: 0, my: 0 },
    };
  }
} 