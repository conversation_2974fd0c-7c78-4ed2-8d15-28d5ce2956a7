/**
 * Custom Next.js Image Loader for Supabase Storage with Pro Image Transformations
 * 
 * This loader enables Next.js Image Optimization using Supabase Pro's native
 * image transformation capabilities. It uses the /render/image endpoint which
 * supports width, height, quality, and resize parameters.
 * 
 * Supabase Pro Image Transformation Features:
 * - Automatic WebP conversion for supported browsers
 * - Quality optimization (20-100, defaults to 80)
 * - Resize modes: cover (default), contain, fill
 * - Width/height between 1-2500px
 * - Max 25MB image size, 50MP resolution
 * 
 * URL Format: https://project.supabase.co/storage/v1/render/image/public/bucket/image.jpg?width=500&quality=80
 */

// Function to extract Supabase project ID from environment variable
function getSupabaseProjectId() {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!url) return '';
  try {
    const hostname = url.replace('https://', '').replace('http://', '');
    return hostname.split('.')[0];
  } catch (error) {
    console.error('Failed to extract Supabase project ID:', error);
    return '';
  }
}

// Known Supabase Storage buckets in your project
const SUPABASE_BUCKETS = [
  'prompt-images',
  'collection-images', 
  'profile-pictures'
];

export default function supabaseImageLoader({ src, width, quality }) {
  // Validate required parameters
  if (!src) {
    throw new Error('Image src is required');
  }
  
  if (!width) {
    throw new Error('Image width is required for custom loaders');
  }

  // Handle local public assets (e.g., /images/categories/video.png)
  if (src.startsWith('/images/') || src.startsWith('/icons/') || src.startsWith('/placeholder')) {
    // For local images, we need to construct a URL that includes width for Next.js optimization
    // Since these are local assets, we'll use Next.js built-in optimization by constructing
    // a proper URL with the width parameter
    const url = new URL(src, 'http://localhost:3000'); // Base URL doesn't matter for relative paths
    url.searchParams.set('w', width.toString());
    if (quality) {
      url.searchParams.set('q', quality.toString());
    }
    return `${src}?w=${width}&q=${quality || 75}`;
  }

  // Handle full Supabase Storage URLs
  if (src.startsWith('http') && src.includes('/storage/v1/object/public/')) {
    // Extract the bucket and path from the full URL
    const url = new URL(src);
    const pathParts = url.pathname.split('/storage/v1/object/public/');
    if (pathParts.length === 2) {
      const bucketAndPath = pathParts[1];
      const bucket = bucketAndPath.split('/')[0];
      
      // Only apply Supabase transformations to known Supabase buckets
      if (SUPABASE_BUCKETS.includes(bucket)) {
        return buildTransformationURL(bucketAndPath, width, quality);
      }
    }
    // Unknown Supabase URL - return with basic width parameter
    const url2 = new URL(src);
    url2.searchParams.set('w', width.toString());
    if (quality) {
      url2.searchParams.set('q', quality.toString());
    }
    return url2.href;
  }

  // Handle relative Supabase Storage paths (e.g., "prompt-images/image.jpg")
  if (typeof src === 'string' && !src.startsWith('/')) {
    const bucket = src.split('/')[0];
    
    // Only apply transformations to known Supabase buckets
    if (SUPABASE_BUCKETS.includes(bucket)) {
      return buildTransformationURL(src, width, quality);
    }
  }

  // Default: return src with width parameter for Next.js compatibility
  if (src.includes('?')) {
    return `${src}&w=${width}&q=${quality || 75}`;
  } else {
    return `${src}?w=${width}&q=${quality || 75}`;
  }
}

function buildTransformationURL(bucketAndPath, width, quality) {
  // Build the Supabase image transformation URL
  const projectId = getSupabaseProjectId();
  const baseUrl = `https://${projectId}.supabase.co/storage/v1/render/image/public/${bucketAndPath}`;
  
  // Create URL object to properly handle query parameters
  const url = new URL(baseUrl);
  
  // Add transformation parameters
  url.searchParams.set('width', width.toString());
  
  // Set quality (20-100, default 80 if not specified)
  const imageQuality = quality || 80;
  url.searchParams.set('quality', Math.max(20, Math.min(100, imageQuality)).toString());
  
  // Use 'cover' resize mode by default (maintains aspect ratio, crops if needed)
  // Other options: 'contain' (fits within dimensions) or 'fill' (stretches to fit)
  url.searchParams.set('resize', 'cover');
  
  return url.href;
} 