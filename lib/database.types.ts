export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      ai_models: {
        Row: {
          created_at: string | null
          deprecated: boolean | null
          id: number
          provider: string
          slug: string | null
          tool: string | null
          tool_id: number | null
          tool_name: string
          type: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          deprecated?: boolean | null
          id?: number
          provider: string
          slug?: string | null
          tool?: string | null
          tool_id?: number | null
          tool_name: string
          type: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          deprecated?: boolean | null
          id?: number
          provider?: string
          slug?: string | null
          tool?: string | null
          tool_id?: number | null
          tool_name?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_ai_models_tool_id"
            columns: ["tool_id"]
            isOneToOne: false
            referencedRelation: "tools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_ai_models_tool_id"
            columns: ["tool_id"]
            isOneToOne: false
            referencedRelation: "view_tool_models"
            referencedColumns: ["tool_id"]
          },
        ]
      }
      categories: {
        Row: {
          description: string | null
          id: number
          image_path: string | null
          name: string
          slug: string
        }
        Insert: {
          description?: string | null
          id?: number
          image_path?: string | null
          name: string
          slug: string
        }
        Update: {
          description?: string | null
          id?: number
          image_path?: string | null
          name?: string
          slug?: string
        }
        Relationships: []
      }
      collection_prompts: {
        Row: {
          added_at: string
          collection_id: string
          prompt_id: string
        }
        Insert: {
          added_at?: string
          collection_id: string
          prompt_id: string
        }
        Update: {
          added_at?: string
          collection_id?: string
          prompt_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "collection_prompts_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collection_prompts_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_card_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collection_prompts_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_statistics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collection_prompts_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collection_prompts_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "trending_prompts"
            referencedColumns: ["id"]
          },
        ]
      }
      collections: {
        Row: {
          color: string | null
          created_at: string
          description: string | null
          icon: string | null
          id: string
          is_default: boolean
          is_public: boolean
          name: string
          prompt_count: number | null
          slug: string | null
          updated_at: string
          user_id: string
          view_count: number | null
        }
        Insert: {
          color?: string | null
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          is_default?: boolean
          is_public?: boolean
          name: string
          prompt_count?: number | null
          slug?: string | null
          updated_at?: string
          user_id: string
          view_count?: number | null
        }
        Update: {
          color?: string | null
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          is_default?: boolean
          is_public?: boolean
          name?: string
          prompt_count?: number | null
          slug?: string | null
          updated_at?: string
          user_id?: string
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "collections_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      comment_votes: {
        Row: {
          comment_id: string
          created_at: string
          user_id: string
          vote_type: number
        }
        Insert: {
          comment_id: string
          created_at?: string
          user_id: string
          vote_type?: number
        }
        Update: {
          comment_id?: string
          created_at?: string
          user_id?: string
          vote_type?: number
        }
        Relationships: [
          {
            foreignKeyName: "comment_votes_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comment_display_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comment_votes_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comment_votes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      comments: {
        Row: {
          created_at: string
          id: string
          parent_comment_id: string | null
          prompt_id: string
          text: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          parent_comment_id?: string | null
          prompt_id: string
          text: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          parent_comment_id?: string | null
          prompt_id?: string
          text?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "comment_display_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_card_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_statistics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "trending_prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          actor_user_id: string | null
          created_at: string
          entity_id: string | null
          entity_title: string | null
          entity_type: string | null
          id: string
          is_read: boolean
          link: string | null
          prompt_category_slug: string | null
          prompt_primary_tag_slug: string | null
          prompt_short_id: string | null
          prompt_title_slug: string | null
          prompt_tool_slug: string | null
          recipient_user_id: string
          type: string
        }
        Insert: {
          actor_user_id?: string | null
          created_at?: string
          entity_id?: string | null
          entity_title?: string | null
          entity_type?: string | null
          id?: string
          is_read?: boolean
          link?: string | null
          prompt_category_slug?: string | null
          prompt_primary_tag_slug?: string | null
          prompt_short_id?: string | null
          prompt_title_slug?: string | null
          prompt_tool_slug?: string | null
          recipient_user_id: string
          type: string
        }
        Update: {
          actor_user_id?: string | null
          created_at?: string
          entity_id?: string | null
          entity_title?: string | null
          entity_type?: string | null
          id?: string
          is_read?: boolean
          link?: string | null
          prompt_category_slug?: string | null
          prompt_primary_tag_slug?: string | null
          prompt_short_id?: string | null
          prompt_title_slug?: string | null
          prompt_tool_slug?: string | null
          recipient_user_id?: string
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_actor_user_id_fkey"
            columns: ["actor_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_recipient_user_id_fkey"
            columns: ["recipient_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          bio: string | null
          created_at: string
          email_on_comment: boolean | null
          email_on_like: boolean | null
          email_on_reply: boolean | null
          email_product_updates: boolean | null
          github_url: string | null
          id: string
          is_username_customized: boolean | null
          public_collections_count: number | null
          public_prompts_count: number | null
          total_likes_received: number | null
          updated_at: string
          username: string
          website_url: string | null
          x_url: string | null
          youtube_url: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          email_on_comment?: boolean | null
          email_on_like?: boolean | null
          email_on_reply?: boolean | null
          email_product_updates?: boolean | null
          github_url?: string | null
          id: string
          is_username_customized?: boolean | null
          public_collections_count?: number | null
          public_prompts_count?: number | null
          total_likes_received?: number | null
          updated_at?: string
          username: string
          website_url?: string | null
          x_url?: string | null
          youtube_url?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string
          email_on_comment?: boolean | null
          email_on_like?: boolean | null
          email_on_reply?: boolean | null
          email_product_updates?: boolean | null
          github_url?: string | null
          id?: string
          is_username_customized?: boolean | null
          public_collections_count?: number | null
          public_prompts_count?: number | null
          total_likes_received?: number | null
          updated_at?: string
          username?: string
          website_url?: string | null
          x_url?: string | null
          youtube_url?: string | null
        }
        Relationships: []
      }
      prompt_tags: {
        Row: {
          prompt_id: string
          tag_id: number
        }
        Insert: {
          prompt_id: string
          tag_id: number
        }
        Update: {
          prompt_id?: string
          tag_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "prompt_tags_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_card_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_tags_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_statistics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_tags_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_tags_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "trending_prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
        ]
      }
      prompt_votes: {
        Row: {
          created_at: string
          prompt_id: string
          user_id: string
          vote_type: number
        }
        Insert: {
          created_at?: string
          prompt_id: string
          user_id: string
          vote_type: number
        }
        Update: {
          created_at?: string
          prompt_id?: string
          user_id?: string
          vote_type?: number
        }
        Relationships: [
          {
            foreignKeyName: "prompt_votes_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_card_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_votes_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_statistics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_votes_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_votes_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "trending_prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_votes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      prompts: {
        Row: {
          ai_model_id: number | null
          category_id: number
          created_at: string
          description: string
          example_input: string | null
          example_output_image_url: string | null
          example_output_text: string | null
          id: string
          image_url: string | null
          instructions: string | null
          is_public: boolean
          original_prompt_id: string | null
          primary_tag_id: number | null
          prompt_text: string
          search_vector: unknown | null
          short_id: string
          slug: string | null
          tag_slugs: string[] | null
          title: string
          tool_id: number
          updated_at: string
          updated_by_user_id: string | null
          user_id: string
          view_count: number
        }
        Insert: {
          ai_model_id?: number | null
          category_id: number
          created_at?: string
          description: string
          example_input?: string | null
          example_output_image_url?: string | null
          example_output_text?: string | null
          id?: string
          image_url?: string | null
          instructions?: string | null
          is_public?: boolean
          original_prompt_id?: string | null
          primary_tag_id?: number | null
          prompt_text: string
          search_vector?: unknown | null
          short_id: string
          slug?: string | null
          tag_slugs?: string[] | null
          title: string
          tool_id: number
          updated_at?: string
          updated_by_user_id?: string | null
          user_id: string
          view_count?: number
        }
        Update: {
          ai_model_id?: number | null
          category_id?: number
          created_at?: string
          description?: string
          example_input?: string | null
          example_output_image_url?: string | null
          example_output_text?: string | null
          id?: string
          image_url?: string | null
          instructions?: string | null
          is_public?: boolean
          original_prompt_id?: string | null
          primary_tag_id?: number | null
          prompt_text?: string
          search_vector?: unknown | null
          short_id?: string
          slug?: string | null
          tag_slugs?: string[] | null
          title?: string
          tool_id?: number
          updated_at?: string
          updated_by_user_id?: string | null
          user_id?: string
          view_count?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_prompts_ai_model"
            columns: ["ai_model_id"]
            isOneToOne: false
            referencedRelation: "ai_models"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_prompts_ai_model"
            columns: ["ai_model_id"]
            isOneToOne: false
            referencedRelation: "view_tool_models"
            referencedColumns: ["ai_model_id"]
          },
          {
            foreignKeyName: "fk_prompts_primary_tag"
            columns: ["primary_tag_id"]
            isOneToOne: false
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_original_prompt_id_fkey"
            columns: ["original_prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_card_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_original_prompt_id_fkey"
            columns: ["original_prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_statistics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_original_prompt_id_fkey"
            columns: ["original_prompt_id"]
            isOneToOne: false
            referencedRelation: "prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_original_prompt_id_fkey"
            columns: ["original_prompt_id"]
            isOneToOne: false
            referencedRelation: "trending_prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_tool_id_fkey"
            columns: ["tool_id"]
            isOneToOne: false
            referencedRelation: "tools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_tool_id_fkey"
            columns: ["tool_id"]
            isOneToOne: false
            referencedRelation: "view_tool_models"
            referencedColumns: ["tool_id"]
          },
          {
            foreignKeyName: "prompts_updated_by_user_id_fkey"
            columns: ["updated_by_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      saved_prompts: {
        Row: {
          created_at: string
          prompt_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          prompt_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          prompt_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "saved_prompts_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_card_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "saved_prompts_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_statistics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "saved_prompts_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "saved_prompts_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "trending_prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "saved_prompts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      tags: {
        Row: {
          id: number
          name: string
          slug: string
        }
        Insert: {
          id?: number
          name: string
          slug: string
        }
        Update: {
          id?: number
          name?: string
          slug?: string
        }
        Relationships: []
      }
      tools: {
        Row: {
          description: string | null
          icon: string | null
          id: number
          name: string
          slug: string
          website: string | null
        }
        Insert: {
          description?: string | null
          icon?: string | null
          id?: number
          name: string
          slug: string
          website?: string | null
        }
        Update: {
          description?: string | null
          icon?: string | null
          id?: number
          name?: string
          slug?: string
          website?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      comment_display_details: {
        Row: {
          author_avatar_url: string | null
          author_username: string | null
          created_at: string | null
          id: string | null
          like_count: number | null
          parent_comment_id: string | null
          prompt_id: string | null
          text: string | null
          updated_at: string | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "comment_display_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_card_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_statistics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_prompt_id_fkey"
            columns: ["prompt_id"]
            isOneToOne: false
            referencedRelation: "trending_prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      prompt_card_details: {
        Row: {
          ai_model_deprecated: boolean | null
          ai_model_id: number | null
          ai_model_name: string | null
          ai_model_provider: string | null
          ai_model_slug: string | null
          author_avatar_url: string | null
          author_id: string | null
          author_username: string | null
          category_id: number | null
          category_name: string | null
          category_slug: string | null
          comment_count: number | null
          created_at: string | null
          description: string | null
          id: string | null
          image_url: string | null
          is_public: boolean | null
          primary_tag_id: number | null
          primary_tag_slug: string | null
          rating: number | null
          search_vector: unknown | null
          short_id: string | null
          tag_slugs_array: string[] | null
          tags: Json | null
          title: string | null
          tool_id: number | null
          tool_name: string | null
          tool_slug: string | null
          trending_score: number | null
          updated_at: string | null
          view_count: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_prompts_ai_model"
            columns: ["ai_model_id"]
            isOneToOne: false
            referencedRelation: "ai_models"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_prompts_ai_model"
            columns: ["ai_model_id"]
            isOneToOne: false
            referencedRelation: "view_tool_models"
            referencedColumns: ["ai_model_id"]
          },
          {
            foreignKeyName: "fk_prompts_primary_tag"
            columns: ["primary_tag_id"]
            isOneToOne: false
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_tool_id_fkey"
            columns: ["tool_id"]
            isOneToOne: false
            referencedRelation: "tools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_tool_id_fkey"
            columns: ["tool_id"]
            isOneToOne: false
            referencedRelation: "view_tool_models"
            referencedColumns: ["tool_id"]
          },
          {
            foreignKeyName: "prompts_user_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      prompt_statistics: {
        Row: {
          category_id: number | null
          comment_count: number | null
          created_at: string | null
          description: string | null
          downvotes: number | null
          example_input: string | null
          example_output_image_url: string | null
          example_output_text: string | null
          id: string | null
          image_url: string | null
          instructions: string | null
          is_public: boolean | null
          original_prompt_id: string | null
          primary_tag_id: number | null
          prompt_text: string | null
          rating: number | null
          remix_count: number | null
          search_vector: unknown | null
          short_id: string | null
          tag_slugs: string[] | null
          title: string | null
          tool_id: number | null
          updated_at: string | null
          updated_by_user_id: string | null
          upvotes: number | null
          user_id: string | null
          view_count: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_prompts_primary_tag"
            columns: ["primary_tag_id"]
            isOneToOne: false
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_original_prompt_id_fkey"
            columns: ["original_prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_card_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_original_prompt_id_fkey"
            columns: ["original_prompt_id"]
            isOneToOne: false
            referencedRelation: "prompt_statistics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_original_prompt_id_fkey"
            columns: ["original_prompt_id"]
            isOneToOne: false
            referencedRelation: "prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_original_prompt_id_fkey"
            columns: ["original_prompt_id"]
            isOneToOne: false
            referencedRelation: "trending_prompts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_tool_id_fkey"
            columns: ["tool_id"]
            isOneToOne: false
            referencedRelation: "tools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_tool_id_fkey"
            columns: ["tool_id"]
            isOneToOne: false
            referencedRelation: "view_tool_models"
            referencedColumns: ["tool_id"]
          },
          {
            foreignKeyName: "prompts_updated_by_user_id_fkey"
            columns: ["updated_by_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      trending_prompts: {
        Row: {
          comment_count: number | null
          created_at: string | null
          downvotes: number | null
          id: string | null
          is_public: boolean | null
          remix_count: number | null
          title: string | null
          trending_score: number | null
          upvotes: number | null
          user_id: string | null
          view_count: number | null
        }
        Relationships: [
          {
            foreignKeyName: "prompts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      view_tool_models: {
        Row: {
          ai_model_deprecated: boolean | null
          ai_model_id: number | null
          ai_model_name: string | null
          ai_model_provider: string | null
          ai_model_slug: string | null
          ai_model_type: string | null
          tool_id: number | null
          tool_name: string | null
          tool_slug: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      add_comment: {
        Args: {
          p_user_id: string
          p_prompt_id: string
          p_text: string
          p_parent_comment_id?: string
        }
        Returns: string
      }
      add_prompt: {
        Args: {
          p_user_id: string
          p_category_id: number
          p_tool_id: number
          p_title: string
          p_description: string
          p_prompt_text: string
          p_instructions?: string
          p_example_input?: string
          p_example_output_text?: string
          p_example_output_image_url?: string
          p_image_url?: string
          p_is_public?: boolean
          p_original_prompt_id?: string
          p_tag_ids?: number[]
        }
        Returns: string
      }
      add_prompt_to_collection: {
        Args: {
          p_user_id: string
          p_prompt_id: string
          p_collection_id?: string
        }
        Returns: boolean
      }
      calculate_trending_score: {
        Args:
          | {
              upvotes: number
              downvotes: number
              views: number
              comments: number
              remixes: number
              created_timestamp: string
            }
          | {
              upvotes: number
              downvotes: number
              views: number
              comments: number
              remixes: number
              created_timestamp: string
            }
        Returns: number
      }
      create_collection: {
        Args: {
          p_user_id: string
          p_name: string
          p_description?: string
          p_icon?: string
          p_color?: string
          p_is_public?: boolean
        }
        Returns: string
      }
      delete_comment: {
        Args: { p_user_id: string; p_comment_id: string }
        Returns: boolean
      }
      generate_prompt_search_vector: {
        Args: {
          p_title: string
          p_description: string
          p_prompt_text: string
          p_instructions: string
          p_example_input: string
          p_example_output_text: string
        }
        Returns: unknown
      }
      generate_prompt_slug: {
        Args: {
          p_title: string
          p_category_slug: string
          p_tool_slug: string
          p_tag_slug: string
          p_short_id: string
        }
        Returns: string
      }
      generate_short_id: {
        Args: { length?: number }
        Returns: string
      }
      get_prompt_details_for_notification: {
        Args: { p_prompt_id: string }
        Returns: Record<string, unknown>
      }
      increment_prompt_view_count: {
        Args: { prompt_id: string }
        Returns: undefined
      }
      process_search_query: {
        Args: { query: string }
        Returns: string
      }
      random_timestamp: {
        Args: { start_date: string; end_date: string }
        Returns: string
      }
      remove_prompt_from_collection: {
        Args: {
          p_user_id: string
          p_prompt_id: string
          p_collection_id: string
        }
        Returns: boolean
      }
      search_prompts: {
        Args: {
          search_query: string
          category_id_filter?: number
          tool_id_filter?: number
          tag_id_filter?: number
          limit_count?: number
          offset_count?: number
        }
        Returns: {
          id: string
          short_id: string
          title: string
          slug: string
          description: string
          user_id: string
          category_id: number
          tool_id: number
          image_url: string
          created_at: string
          rating: number
          comment_count: number
          remix_count: number
          view_count: number
          rank: number
        }[]
      }
      search_prompts_improved: {
        Args: {
          search_query: string
          category_id_filter?: number
          tool_id_filter?: number
          tag_ids_filter?: number[]
          limit_count?: number
          offset_count?: number
        }
        Returns: {
          id: string
          short_id: string
          title: string
          slug: string
          description: string
          user_id: string
          category_id: number
          tool_id: number
          image_url: string
          created_at: string
          rating: number
          comment_count: number
          remix_count: number
          view_count: number
          rank: number
        }[]
      }
      slugify: {
        Args: { input_text: string }
        Returns: string
      }
      update_collection: {
        Args: {
          p_user_id: string
          p_collection_id: string
          p_name: string
          p_description?: string
          p_icon?: string
          p_color?: string
          p_is_public?: boolean
        }
        Returns: boolean
      }
      update_comment: {
        Args: { p_user_id: string; p_comment_id: string; p_text: string }
        Returns: boolean
      }
      update_prompt: {
        Args: {
          p_prompt_id: string
          p_user_id: string
          p_category_id: number
          p_tool_id: number
          p_title: string
          p_description: string
          p_prompt_text: string
          p_instructions?: string
          p_example_input?: string
          p_example_output_text?: string
          p_example_output_image_url?: string
          p_image_url?: string
          p_is_public?: boolean
          p_tag_ids?: number[]
        }
        Returns: boolean
      }
      vote_on_prompt: {
        Args: { p_user_id: string; p_prompt_id: string; p_vote_type: number }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
