export interface CreatePromptData {
  userId: string;
  categoryId: number;
  toolId: number;
  title: string;
  description?: string;
  promptText: string;
  tagIds: number[];
  aiModelId?: number | null;
  userEnteredAiModel?: string; // New field for raw user input that will be standardized
  instructions?: string;
  exampleInput?: string;
  exampleOutputText?: string;
  exampleOutputImageUrl?: string | null;
  imageUrl?: string | null;
  isPublic: boolean;
  originalPromptId?: string | null; // For remixes
}

export interface AIModel {
  id: number;
  provider: string;
  name: string; // From ai_models.tool_name
  slug: string;
  type?: string | null; // from ai_models.type
  deprecated: boolean;
  tool_id: number;
  tool_slug?: string;
  tool_name?: string;
}

export interface Prompt {
  id: string
  shortId?: string // From prompts table
  short_id?: string // Original snake_case field preserved for debugging
  slug?: string // Title slug from prompts table
  title: string
  description: string
  text: string // From prompts.prompt_text
  prompt?: string // Alias or specific field if different
  instructions?: string
  exampleInput?: string
  exampleOutput?: string
  exampleOutputImageUrl?: string
  imageUrl?: string
  isPublic?: boolean
  ai_model_id?: number | null;
  user_entered_ai_model?: string | null; // Added for edit prompt functionality
  ai_model?: {
    id: number;
    provider: string;
    name: string;
    slug: string;
    deprecated: boolean;
    type?: string | null;
  } | null;
  category: // From categories table or prompt_card_details view
    | {
        id?: number | string
        name: string
        slug: string
      }
    | string
  tool?: // From tools table or prompt_card_details view
    {
        id?: number | string
        name: string
        slug: string
      }
    // Removed | string option
  user?: Profile // Reference to the Profile interface
  author?: string // Legacy, try to phase out
  tags?: Array<{ // From tags table or prompt_card_details view
    id?: number | string
    name: string
    slug?: string
    primary_tag_slug?: string // If joined specifically for URL
  }>
  originalPromptId?: string
  originalPrompt?: { // Details of the original prompt if this is a remix
    title: string;
    shortId: string;
    isPublic: boolean;
  } | null;
  rating?: number // Aggregated
  upvotes?: number // Aggregated or direct
  likeCount?: number // Aggregated
  commentCount?: number // Aggregated
  remixCount?: number // Aggregated
  viewCount?: number
  createdAt?: string
  updatedAt?: string
  isPremium?: boolean
  relatedPrompts?: Prompt[] // For prompt detail page
}

export interface Category {
  id: number | string; // DB uses number, ensure consistency if string is used
  name: string;
  slug: string;
  description?: string;
  imagePath?: string; // from categories.image_path
  promptCount?: number; // Aggregated
  formattedCount?: string; // Formatted prompt count (e.g., "1.2K")
}

export interface Tag {
  id: number | string; // DB uses number
  name: string;
  slug: string; // from tags.slug
  promptCount?: number; // Aggregated
  formattedCount?: string; // Formatted prompt count (e.g., "1.2K")
}

export interface Tool {
  id: number | string; // DB uses number
  name: string;
  slug: string; // from tools.slug
  icon?: string; // from tools.icon
  emoji?: string; // Emoji representation of the tool
  description?: string; // Added description
  website?: string;       // Added website
  promptCount?: number; // Aggregated
  formattedCount?: string; // Formatted prompt count (e.g., "1.2K")
}

export interface PromptCard {
  id: string
  shortId?: string // from prompts.short_id or prompt_card_details.short_id
  short_id?: string // Original snake_case field preserved for debugging
  slug?: string // from prompts.slug or prompt_card_details.slug (title slug)
  title: string
  description: string
  imageUrl?: string | null
  createdAt?: string
  updatedAt?: string
  isPublic?: boolean
  viewCount?: number
  ai_model_id?: number | null;
  user_entered_ai_model?: string | null; // Added for edit prompt functionality
  ai_model?: {
    id: number;
    provider: string;
    name: string;
    slug: string;
    deprecated: boolean;
    type?: string | null;
  } | null;
  category: { // from prompt_card_details (joined from categories)
    name: string
    slug: string
  }
  tool?: { // from prompt_card_details (joined from tools)
    name: string
    slug: string
  }
  user: { // from prompt_card_details (joined from profiles)
    id: string
    username: string
    avatarUrl?: string | null // Added | null
  }
  tags?: Array<{ // from prompt_card_details (JSONB or joined)
    id?: number | string
    name: string
    slug?: string
  }>
  primary_tag_slug?: string; // From prompt_card_details (joined from prompts.primary_tag_id -> tags.slug)
  rating?: number // Aggregated
  likeCount?: number // Aggregated (should match rating or be sum of upvotes)
  commentCount?: number // Aggregated
  remixCount?: number // Aggregated
  trendingScore?: number // Aggregated
  difficultyLevel?: number // Custom field, not in current DB schema
  wordCount?: number // Custom field, not in current DB schema
  isPremium?: boolean // Custom field, not in current DB schema
  savedAt?: string; // For saved prompts page, indicates when it was saved by the user
  isSaved?: boolean; // NEW: indicates if current user has saved this prompt
}

export interface Profile {
  id: string;
  username: string;
  email?: string; // From profiles table, synced from auth.users
  avatar_url?: string | null;
  bio?: string | null;
  website_url?: string | null;
  github_url?: string | null;
  x_url?: string | null;
  youtube_url?: string | null;
  is_username_customized?: boolean;
  created_at?: string;
  updated_at?: string;
  // New fields for stats
  total_likes_received?: number;
  public_prompts_count?: number;
  public_collections_count?: number;
  // New fields for notification preferences
  email_on_comment?: boolean;
  email_on_reply?: boolean;
  email_on_like?: boolean;
  email_product_updates?: boolean;
}

export interface Collection {
  id: string; // UUID from DB
  userId: string; // Camel case for frontend
  name: string;
  description: string | null;
  icon: string | null; // URL to image, either in public folder or Supabase storage
  isPublic: boolean;
  isDefault: boolean; // Whether this is a system-generated default collection
  defaultType: 'saved_prompts' | 'my_prompts' | null; // Type of default collection, null for custom
  createdAt: string;
  updatedAt: string;
  promptCount?: number; // Optional count of prompts in collection
  viewCount?: number;
  user?: { // For public view of collections
    username: string;
    avatar_url?: string | null;
  };
  prompts?: PromptCard[]; // For collection detail page
}

export interface SavedPrompt {
  user_id: string;
  prompt_id: string;
  created_at: string;
  prompt?: PromptCard; // Optionally joined prompt data
}

export interface Notification {
  id: string;
  recipient_user_id: string;
  actor_user_id?: string | null;
  actor?: { username: string; avatar_url?: string | null; } | null; // If joining profiles
  type: string; // e.g., 'like_prompt', 'comment_prompt'
  entity_id?: string | null;
  entity_type?: string | null;
  entity_title?: string | null; // e.g., Prompt title
  link?: string; // Pre-generated link is simplest for client
  // OR individual parts for client-side generation if 'link' is not stored
  prompt_short_id?: string;
  prompt_category_slug?: string;
  prompt_tool_slug?: string;
  prompt_primary_tag_slug?: string;
  prompt_title_slug?: string; // The slugified title part
  is_read: boolean;
  created_at: string;
}

// Updated Comment type
export interface Comment {
  id: string;
  text: string;
  user: Profile; // User who made the comment
  prompt_id: string; // ID of the prompt this comment belongs to
  parent_comment_id?: string | null; // For replies
  created_at: string; // Timestamp of creation
  updated_at: string; // Timestamp of last update
  likes: number; // Number of likes
  dislikes: number; // Number of dislikes
  liked_by_user?: boolean; // Has the current user liked this comment?
  replies: Comment[]; // Nested replies
}
