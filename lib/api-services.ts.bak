import { supabase } from "./supabase/client";
import { transformPrompt, transformPromptCard, transformCategory, transformTool, transformTag, transformProfile, transformCollection } from "./transformers";
import { createTitleSlug } from "./utils/url-helpers"; // Import for slug generation
import type { Profile, Collection, PromptCard, Notification, Prompt, Category, Tool, Tag, Comment, AIModel, CreatePromptData } from "./types"; // Import all types
import { logPromptShortIdIssues } from "./utils/debug-helpers";
import { standardizeAndMatchAIModel } from "./services/ai-model-standardization";

// --- Prompts ---
export async function getPrompts({
  categorySlugs,
  toolSlugs,
  tagSlugs,
  aiModelSlugs,
  searchQuery,
  userId,
  limit = 20,
  offset = 0,
  sortBy = "created_at", // Default sort
  sortOrder = "desc",
}: {
  categorySlugs?: string[];
  toolSlugs?: string[];
  tagSlugs?: string[];
  aiModelSlugs?: string[];
  searchQuery?: string;
  userId?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}): Promise<PromptCard[]> {
  try {
    let query = supabase
      .from("prompt_card_details") // Query the optimized view
      .select("*")
      .eq("is_public", true)
      .order(sortBy, { ascending: sortOrder === "asc" })
      .range(offset, offset + limit - 1);

    if (categorySlugs && categorySlugs.length > 0) {
      query = query.in("category_slug", categorySlugs);
    }
    if (toolSlugs && toolSlugs.length > 0) {
      query = query.in("tool_slug", toolSlugs);
    }
    if (tagSlugs && tagSlugs.length > 0) {
      query = query.overlaps("tag_slugs_array", tagSlugs); // Assumes tag_slugs_array is TEXT[]
    }
    if (aiModelSlugs && aiModelSlugs.length > 0) {
      query = query.in("ai_model_slug", aiModelSlugs);
    }
    if (searchQuery) {
      query = query.textSearch("search_vector", searchQuery, { type: 'plain' });
    }
    if (userId) {
      query = query.eq("author_id", userId);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching prompt cards:", error.message);
      throw error;
    }
    logPromptShortIdIssues(data as any[], "getPrompts (api-services)"); // Cast data to any[] for debug helper
    return (data || []).map(transformPromptCard);
  } catch (error) {
    console.error("Error in getPrompts (api-services):", error);
    return [];
  }
}

export async function searchPrompts(
  searchQuery: string,
  options: {
    categorySlugs?: string[];
    toolSlugs?: string[];
    tagSlugs?: string[];
    limit?: number;
    offset?: number;
  } = {}
): Promise<PromptCard[]> {
  const { categorySlugs, toolSlugs, tagSlugs, limit = 10, offset = 0 } = options;
  return getPrompts({ searchQuery, categorySlugs, toolSlugs, tagSlugs, limit, offset, sortBy: 'trending_score' }); // Default sort for search
}

export async function createPrompt(
  promptData: CreatePromptData
): Promise<{ promptId: string | null; shortId: string | null; error: any }> {
  try {
    console.log('Creating prompt with data:', JSON.stringify({
      ...promptData,
      promptText: promptData.promptText?.substring(0, 50) + '...' // Truncate for logging
    }, null, 2));
    
    // Standardize AI model immediately if user entered one
    let aiModelId = promptData.aiModelId;
    
    if (promptData.userEnteredAiModel) {
      console.log('Attempting to standardize AI model:', promptData.userEnteredAiModel);
      const standardizedModelId = await standardizeAndMatchAIModel(promptData.userEnteredAiModel);
      console.log('Standardization result:', standardizedModelId);
      if (standardizedModelId) {
        // Use the standardized model ID if found
        aiModelId = standardizedModelId;
      }
    }
    
    // The issue might be with the p_user_entered_ai_model parameter
    // Let's check if the RPC function accepts this parameter
    // If not, we'll need to update the SQL function
    
    // Now that we've fixed the function overloading issue, include all parameters
    const rpcParams = {
      p_user_id: promptData.userId,
      p_category_id: promptData.categoryId,
      p_tool_id: promptData.toolId,
      p_title: promptData.title,
      p_description: promptData.description,
      p_prompt_text: promptData.promptText,
      p_instructions: promptData.instructions,
      p_example_input: promptData.exampleInput,
      p_example_output_text: promptData.exampleOutputText,
      p_example_output_image_url: promptData.exampleOutputImageUrl,
      p_image_url: promptData.imageUrl,
      p_is_public: promptData.isPublic,
      p_original_prompt_id: promptData.originalPromptId,
      p_tag_ids: promptData.tagIds,
      p_ai_model_id: aiModelId,
      p_user_entered_ai_model: promptData.userEnteredAiModel || null,
    };
    
    console.log('Calling RPC with params:', JSON.stringify(rpcParams, null, 2));
    
    const { data, error } = await supabase.rpc("add_prompt", rpcParams);
    
    console.log('RPC response:', { data, error });

    if (error) {
      console.error("Error creating prompt via RPC:", JSON.stringify(error, null, 2));
      return { promptId: null, shortId: null, error };
    }
    
    // The RPC now returns a single row with created_prompt_id and created_short_id
    if (data && Array.isArray(data) && data.length > 0) {
      const result = data[0];
      return { promptId: result.created_prompt_id, shortId: result.created_short_id, error: null };
    } else if (data && !Array.isArray(data)) { // If Supabase client auto-unwraps single row object
      const result = data as any; // Cast to any to access properties
      return { promptId: result.created_prompt_id, shortId: result.created_short_id, error: null };
    }

    console.warn("No data returned from add_prompt RPC or unexpected format:", data);
    return { promptId: null, shortId: null, error: "No data returned from prompt creation." };
  } catch (e) {
    console.error("Unexpected error in createPrompt service:", e);
    return { promptId: null, shortId: null, error: e };
  }
}

export async function addPromptToCollection(
  userId: string,
  promptId: string,
  collectionId: string | null // Pass null to use/create default collection
): Promise<{ success: boolean; error?: any }> {
  try {
    const { data, error } = await supabase.rpc("add_prompt_to_collection", {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_collection_id: collectionId,
    });

    if (error) {
      console.error("Error adding prompt to collection:", error);
      return { success: false, error };
    }
    if (data === false) { // RPC returns boolean
      console.warn("add_prompt_to_collection RPC returned false.");
      return { success: false, error: "Failed to add prompt to collection (RPC)." };
    }
    return { success: true };
  } catch (e) {
    console.error("Unexpected error in addPromptToCollection service:", e);
    return { success: false, error: e };
  }
}

/**
 * Adds a prompt to multiple collections at once
 * Uses the add_prompt_to_multiple_collections RPC function
 */
export async function addPromptToMultipleCollections(
  userId: string,
  promptId: string,
  collectionIds: string[]
): Promise<{ success: boolean; error?: any }> {
  try {
    const { data, error } = await supabase.rpc("add_prompt_to_multiple_collections", {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_collection_ids: collectionIds,
    });

    if (error) {
      console.error("Error adding prompt to multiple collections:", error);
      return { success: false, error };
    }
    if (data === false) { // RPC returns boolean
      console.warn("add_prompt_to_multiple_collections RPC returned false.");
      return { success: false, error: "Failed to add prompt to one or more collections (RPC)." };
    }
    return { success: true };
  } catch (e) {
    console.error("Unexpected error in addPromptToMultipleCollections service:", e);
    return { success: false, error: e };
  }
}

/**
 * Adds a prompt to multiple collections at once
 * Uses the add_prompt_to_multiple_collections RPC function
 */
export async function addPromptToMultipleCollections(
  userId: string,
  promptId: string,
  collectionIds: string[]
): Promise<{ success: boolean; error?: any }> {
  try {
    const { data, error } = await supabase.rpc("add_prompt_to_multiple_collections", {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_collection_ids: collectionIds,
    });

    if (error) {
      console.error("Error adding prompt to multiple collections:", error);
      return { success: false, error };
    }
    if (data === false) { // RPC returns boolean
      console.warn("add_prompt_to_multiple_collections RPC returned false.");
      return { success: false, error: "Failed to add prompt to one or more collections (RPC)." };
    }
    return { success: true };
  } catch (e) {
    console.error("Unexpected error in addPromptToMultipleCollections service:", e);
    return { success: false, error: e };
  }
}

export async function getPromptById(id: string): Promise<Prompt | null> {
  try {
    const { data, error } = await supabase
      .from("prompts")
      .select(`*, category:categories(*), tool:tools(*), user:user_id(*), tags:prompt_tags(tag:tags(*)), ai_model:ai_models(*)`)
      .eq("id", id)
      .single();

    if (error || !data) {
      console.error("Error fetching prompt by ID:", error?.message);
      return null;
    }
    return transformPrompt(data);
  } catch (error) {
    console.error("Error in getPromptById (api-services):", error);
    return null;
  }
}

export async function getPromptByShortId(shortId: string): Promise<Prompt | null> {
  try {
    console.log(`[api-services/getPromptByShortId] Querying DB for short_id: "${shortId}"`);

    // 1. Fetch prompt data without embedding stats
    const { data: promptData, error: promptError, status: promptStatus } = await supabase
      .from("prompts")
      .select(`
        *,
        category:categories(*),
        tool:tools(*),
        user:user_id(*),
        tags:prompt_tags(tag:tags(*)),
        ai_model:ai_models(*)
      `)
      .eq("short_id", shortId)
      .single();

    if (promptError && promptError.code !== 'PGRST116') {
      console.error(`[api-services/getPromptByShortId] Supabase error fetching prompt data for short_id "${shortId}":`, promptError.message, `Status: ${promptStatus}`);
      return null;
    }
    if (!promptData) {
      console.warn(`[api-services/getPromptByShortId] No prompt data returned from DB for short_id "${shortId}".`);
      return null;
    }

    console.log(`[api-services/getPromptByShortId] DB returned prompt data: ${promptData.title} for short_id "${shortId}"`);

    // 2. Fetch statistics data separately
    const { data: statsData, error: statsError } = await supabase
      .from("prompt_statistics")
      .select("rating, upvotes, downvotes, comment_count, remix_count")
      .eq("id", promptData.id as string) // Join on the prompt's ID, cast to string
      .single();

    if (statsError && statsError.code !== 'PGRST116') {
       console.warn(`[api-services/getPromptByShortId] Supabase error fetching stats for prompt ID "${promptData.id}":`, statsError.message);
       // Do not return null, proceed with prompt data even if stats fail
    }

    console.log(`[api-services/getPromptByShortId] DB returned stats data for prompt ID "${promptData.id}":`, statsData);


    // 3. Combine data and pass to transformer
    const combinedData = {
      ...promptData,
      stats: statsData || null, // Add stats data, or null if fetching failed or no stats found
    };

    return transformPrompt(combinedData);

  } catch (e) {
    console.error(`[api-services/getPromptByShortId] Unexpected error for short_id "${shortId}":`, e);
    return null;
  }
}

// --- Categories, Tools, Tags ---
export async function getCategories(): Promise<Category[]> {
  try {
    const { data, error } = await supabase.from("categories").select("*").order("name");
    if (error) throw error;
    return (data || []).map(transformCategory);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
}

export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    const { data, error } = await supabase.from("categories").select("*").eq("slug", slug).single();
    if (error || !data) return null;
    return transformCategory(data);
  } catch (error) {
    console.error("Error fetching category by slug:", error);
    return null;
  }
}

export async function getTools(): Promise<Tool[]> {
  try {
    const { data, error } = await supabase.from("tools").select("*").order("name");
    if (error) throw error;
    return (data || []).map(transformTool);
  } catch (error) {
    console.error("Error fetching tools:", error);
    return [];
  }
}

export async function getToolBySlug(slug: string): Promise<Tool | null> {
  try {
    const { data, error } = await supabase.from("tools").select("*").eq("slug", slug).single();
    if (error || !data) return null;
    return transformTool(data);
  } catch (error) {
    console.error("Error fetching tool by slug:", error);
    return null;
  }
}

export async function getTags(): Promise<Tag[]> {
  try {
    const { data, error } = await supabase.from("tags").select("*").order("name");
    if (error) throw error;
    return (data || []).map(transformTag);
  } catch (error) {
    console.error("Error fetching tags:", error);
    return [];
  }
}

export async function getTagBySlug(slug: string): Promise<Tag | null> {
  try {
    const { data, error } = await supabase.from("tags").select("*").eq("slug", slug).single();
    if (error || !data) return null;
    return transformTag(data);
  } catch (error) {
    console.error("Error fetching tag by slug:", error);
    return null;
  }
}

// --- Profiles ---
export async function getProfileById(userId: string): Promise<Profile | null> {
  try {
    const { data, error } = await supabase.from("profiles").select("*").eq("id", userId).single();
    if (error || !data) return null;
    return transformProfile(data);
  } catch (error) {
    console.error("Error fetching profile by ID:", error);
    return null;
  }
}

export async function getProfileByUsername(username: string): Promise<Profile | null> {
  try {
    const { data, error } = await supabase.from("profiles").select("*").eq("username", username).single();
    if (error || !data) return null;
    return transformProfile(data);
  } catch (error) {
    console.error("Error fetching profile by username:", error);
    return null;
  }
}

// --- Collections ---
export async function getUserCollections(
  userId: string,
  options: {
    includePrivate?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: "created_at" | "prompt_count" | "name";
    sortOrder?: "asc" | "desc";
    purpose?: "dialog" | "general";
  } = {}
): Promise<Collection[]> {
  const {
    includePrivate = false,
    limit = 10,
    offset = 0,
    sortBy = "created_at",
    sortOrder = "desc",
    purpose = "general",
  } = options;

  try {
    let query = supabase
      .from("collections")
      .select(`
        id,
        user_id,
        name,
        description,
        icon,
        is_public,
        is_default,
        default_type,
        prompt_count,
        view_count,
        created_at,
        updated_at,
        user:profiles!collections_user_id_fkey (username, avatar_url)
      `)
      .eq("user_id", userId);
    
    // For dialog purpose, exclude "My Prompts" collection and prioritize "Saved Prompts"
    if (purpose === "dialog") {
      query = query.neq("default_type", "my_prompts"); // Exclude "My Prompts"
      // Add specific ordering for "Saved Prompts" first
      query = query.order("is_default", { ascending: false }) // Puts true (default) first
                   .order("default_type", { ascending: true }); // 'saved_prompts' first
    } else {
      // Use the standard ordering for general purpose
      query = query.order(sortBy, { ascending: sortOrder === "asc" });
    }
    
    // Apply range for pagination
    query = query.range(offset, offset + limit - 1);

    if (!includePrivate) {
      query = query.eq("is_public", true);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching user collections:", error.message);
      throw error;
    }

    return (data || []).map(transformCollection);
  } catch (error) {
    console.error("Error in getUserCollections:", error);
    return [];
  }
}

export async function createCollection(
  userId: string,
  collectionData: {
    name: string;
    description?: string | null;
    imageFile?: File | null;
    is_public: boolean;
  }
): Promise<Collection> {
  try {
    let iconUrl: string | null = null;

    // Handle image upload logic
    if (collectionData.imageFile) {
      const file = collectionData.imageFile;
      const fileName = `${userId}/${Date.now()}-${file.name.replace(/\s+/g, '_')}`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('collection-images')
        .upload(fileName, file, { cacheControl: '3600', upsert: false });

      if (uploadError) throw uploadError;
      iconUrl = supabase.storage.from('collection-images').getPublicUrl(fileName).data.publicUrl;
    }

    // Call RPC without color parameter
    const { data: newCollectionData, error: rpcError } = await supabase
      .rpc("create_custom_collection", {
        p_user_id: userId,
        p_name: collectionData.name,
        p_description: collectionData.description,
        p_icon_url: iconUrl,
        p_is_public: collectionData.is_public,
      })
      .select("*")
      .single();

    if (rpcError) {
      if (rpcError.code === '23505') { // Unique violation
        throw new Error(`A collection with the name "${collectionData.name}" might already exist. Try a different name.`);
      }
      console.error("Error creating collection via RPC:", rpcError.message);
      throw rpcError;
    }
    if (!newCollectionData) throw new Error("Collection creation returned no data.");
    
    return transformCollection(newCollectionData);
  } catch (error) {
    console.error("Error in createCollection service:", error);
    throw error;
  }
}

/**
 * Update an existing collection
 * Note: Default collections cannot be edited
 */
export async function updateCollection(
  userId: string,
  collectionId: string,
  collectionData: {
    name?: string;
    description?: string | null;
    imageFile?: File | null;
    removeCurrentImage?: boolean;
    icon?: string | null;
    is_public?: boolean;
  }
): Promise<Collection> {
  console.log('[api-services] updateCollection called with:', {
    userId,
    collectionId,
    collectionData: {
      name: collectionData.name,
      description: collectionData.description,
      imageFile: collectionData.imageFile ? {
        name: collectionData.imageFile.name,
        type: collectionData.imageFile.type,
        size: collectionData.imageFile.size
      } : null,
      removeCurrentImage: collectionData.removeCurrentImage,
      icon: collectionData.icon,
      is_public: collectionData.is_public
    }
  });
  
  try {
    // Handle image update logic
    let iconUrlUpdate = collectionData.icon;
    console.log('[api-services] Initial iconUrlUpdate:', iconUrlUpdate);
    
    if (collectionData.imageFile) {
    console.log('[api-services] Processing image file upload');
    const file = collectionData.imageFile;
    
    // Validate file
    if (!file.type.startsWith('image/')) {
      console.error('[api-services] Invalid file type:', file.type);
      throw new Error('Only image files are allowed');
    }
    
    // Create a unique filename to avoid collisions
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 10);
    const safeFileName = file.name.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_.-]/g, '');
    const fileName = `${userId}/${timestamp}-${randomId}-${safeFileName}`;
    
    console.log('[api-services] Generated storage path:', fileName);
    
    // Convert File to Blob for Supabase storage upload
    let fileBlob;
    try {
      fileBlob = file;
      console.log('[api-services] File prepared for upload:', {
        size: fileBlob.size,
        type: fileBlob.type
      });
    } catch (error) {
      console.error('[api-services] Error preparing file for upload:', error);
      throw new Error('Failed to process image file');
    }
    
    console.log('[api-services] Starting Supabase storage upload');
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('collection-images')
      .upload(fileName, fileBlob, { 
        cacheControl: '3600', 
        upsert: true, // Use upsert:true to overwrite if file exists
        contentType: file.type // Explicitly set content type
      });

    if (uploadError) {
      console.error('[api-services] Supabase storage upload error:', uploadError);
      throw uploadError;
    }
    
    console.log('[api-services] Upload successful, data:', uploadData);
    const publicUrlResult = supabase.storage.from('collection-images').getPublicUrl(fileName);
    console.log('[api-services] Public URL result:', publicUrlResult);
    
    iconUrlUpdate = publicUrlResult.data.publicUrl;
    console.log('[api-services] Set iconUrlUpdate to:', iconUrlUpdate);
    } else if (collectionData.removeCurrentImage) {
      console.log('[api-services] Removing current image (setting iconUrlUpdate to null)');
      iconUrlUpdate = null;
    }

    // Prepare RPC parameters - no color parameter
    const rpcParams: any = {
      p_collection_id: collectionId,
      p_user_id: userId,
    };
    
    if (collectionData.name !== undefined) rpcParams.p_name = collectionData.name;
    if (collectionData.description !== undefined) rpcParams.p_description = collectionData.description;
    if (iconUrlUpdate !== undefined) {
      rpcParams.p_icon_url = iconUrlUpdate;
      console.log('[api-services] Added p_icon_url to RPC params:', iconUrlUpdate);
    }
    if (collectionData.is_public !== undefined) rpcParams.p_is_public = collectionData.is_public;

    console.log('[api-services] Final RPC parameters:', rpcParams);

    // Call RPC
    console.log('[api-services] Calling update_user_collection RPC');
    
    // When calling an RPC function that returns a table type (SETOF collections),
    // we need to handle it differently than a scalar return type
    const { data: updatedCollectionData, error: rpcError } = await supabase
      .rpc("update_user_collection", rpcParams);
      
    console.log('[api-services] RPC response:', { data: updatedCollectionData, error: rpcError });

    if (rpcError) {
      console.error('[api-services] RPC error details:', {
        code: rpcError.code,
        message: rpcError.message,
        details: rpcError.details,
        hint: rpcError.hint
      });
      
      if (rpcError.code === '23505') { // Unique violation
        throw new Error(`A collection with the name "${collectionData.name}" might already exist. Try a different name.`);
      } else if (rpcError.message && rpcError.message.includes('default collection')) {
        throw new Error("Default collections cannot be edited.");
      }
      console.error("[api-services] Error updating collection via RPC:", rpcError.message);
      throw rpcError;
    }
    
    // For RPC functions that return SETOF, the data will be an array
    if (!updatedCollectionData || !Array.isArray(updatedCollectionData) || updatedCollectionData.length === 0) {
      console.error('[api-services] RPC returned no data:', updatedCollectionData);
      
      // Since we've already uploaded the image, let's fetch the collection directly
      console.log('[api-services] Falling back to direct collection fetch');
      const { data: fetchedCollection, error: fetchError } = await supabase
        .from("collections")
        .select("*")
        .eq("id", collectionId)
        .single();
        
      if (fetchError || !fetchedCollection) {
        console.error('[api-services] Failed to fetch collection after update:', fetchError);
        throw new Error("Collection update could not be verified.");
      }
      
      console.log('[api-services] Successfully fetched collection after update:', fetchedCollection);
      return transformCollection(fetchedCollection);
    }
    
    // Take the first item from the array (should be the only one)
    const updatedCollection = updatedCollectionData[0];
    console.log('[api-services] Successfully updated collection, raw data:', updatedCollection);
    const transformedCollection = transformCollection(updatedCollection);
    console.log('[api-services] Transformed collection:', transformedCollection);
    
    return transformedCollection;
  } catch (error) {
    console.error("[api-services] Error in updateCollection service:", error);
    throw error;
  }
}

/**
 * Remove a prompt from a collection
 * This function allows users to remove prompts from their collections
 */
export async function removePromptFromCollection(
  userId: string,
  promptId: string,
  collectionId: string
): Promise<{ success: boolean; error?: any }> {
  try {
    const { data, error } = await supabase.rpc("remove_prompt_from_collection", {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_collection_id: collectionId,
    });

    if (error) {
      console.error("Error removing prompt from collection:", error);
      return { success: false, error };
    }
    
    if (data === false) { // RPC returns boolean
      console.warn("remove_prompt_from_collection RPC returned false.");
      return { success: false, error: "Failed to remove prompt from collection (RPC)." };
    }
    
    return { success: true };
  } catch (e) {
    console.error("Unexpected error in removePromptFromCollection service:", e);
    return { success: false, error: e };
  }
}

/**
 * Gets the collection IDs that a prompt belongs to for a specific user
 * This is used to determine if a prompt is saved in any of the user's collections
 */
export async function getPromptCollectionMembership(
  userId: string,
  promptId: string
): Promise<{ collectionIds: string[]; error?: any }> {
  try {
    // Get all collections that belong to the user
    const { data: userCollections, error: collectionsError } = await supabase
      .from("collections")
      .select("id")
      .eq("user_id", userId);

    if (collectionsError) {
      console.error("Error fetching user collections:", collectionsError);
      return { collectionIds: [], error: collectionsError };
    }

    if (!userCollections || userCollections.length === 0) {
      // User has no collections
      return { collectionIds: [] };
    }

    // Get all collection_prompts entries for this prompt
    const userCollectionIds = userCollections.map(c => c.id);
    
    const { data: memberships, error: membershipsError } = await supabase
      .from("collection_prompts")
      .select("collection_id")
      .eq("prompt_id", promptId)
      .in("collection_id", userCollectionIds);

    if (membershipsError) {
      console.error("Error fetching prompt collection memberships:", membershipsError);
      return { collectionIds: [], error: membershipsError };
    }

    // Return the collection IDs
    return { 
      collectionIds: memberships ? memberships.map(m => m.collection_id) : [],
      error: null
    };
  } catch (e) {
    console.error("Unexpected error in getPromptCollectionMembership service:", e);
    return { collectionIds: [], error: e };
  }
}

export async function updateNotificationPreferences(
  userId: string,
  preferences: {
    email_on_comment?: boolean;
    email_on_reply?: boolean;
    email_on_like?: boolean;
    email_product_updates?: boolean;
  }
): Promise<Profile> {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .update({
        ...preferences,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId)
      .select()
      .single();

    if (error) {
      console.error("Error updating notification preferences:", error.message);
      throw error;
    }
    if (!data) throw new Error("Failed to update preferences, no data returned.");
    return data as unknown as Profile;
  } catch (error) {
    console.error("Error in updateNotificationPreferences:", error);
    throw error;
  }
}

export async function getNotificationPreferences(userId: string): Promise<Partial<Profile> | null> {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("email_on_comment, email_on_reply, email_on_like, email_product_updates")
      .eq("id", userId)
      .single();

    if (error) {
      console.error("Error fetching notification preferences:", error.message);
      throw error;
    }
    return data as unknown as Partial<Profile> | null;
  } catch (error) {
    console.error("Error in getNotificationPreferences:", error);
    return null;
  }
}

// --- Notifications ---
export async function getNotifications(
  userId: string,
  options: {
    limit?: number;
    offset?: number;
    unreadOnly?: boolean;
  } = {}
): Promise<Notification[]> {
  const { limit = 10, offset = 0, unreadOnly = false } = options;

  try {
    let query = supabase
      .from("notifications")
      .select(`
        id,
        recipient_user_id,
        actor_user_id,
        type,
        entity_id,
        entity_type,
        entity_title,
        link,
        is_read,
        created_at,
        actor:profiles!notifications_actor_user_id_fkey (username, avatar_url),
        prompt:entity_id ( short_id, title, category:category_id(slug), tool:tool_id(slug), primary_tag:primary_tag_id(slug) )
      `)
      // The join for prompt might need adjustment based on how entity_id links to prompts table
      // Assuming entity_id is prompt.id and entity_type is 'prompt'
      .eq("recipient_user_id", userId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (unreadOnly) {
      query = query.eq("is_read", false);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching notifications:", error.message);
      throw error;
    }
    const notificationsData: any[] = data || []; // Explicitly type data
    return notificationsData.map(n => ({
      id: n.id,
      recipient_user_id: n.recipient_user_id,
      actor_user_id: n.actor_user_id,
      actor: n.actor ? { username: n.actor.username, avatar_url: n.actor.avatar_url } : null,
      type: n.type,
      entity_id: n.entity_id,
      entity_type: n.entity_type,
      entity_title: n.entity_title || n.prompt?.title,
      link: n.link, // Prefer pre-generated link
      prompt_short_id: n.prompt?.short_id, // For fallback URL generation
      // ... other prompt slug fields from n.prompt if needed for generatePromptUrl
      is_read: n.is_read,
      created_at: n.created_at,
    })) as Notification[];
  } catch (error) {
    console.error("Error in getNotifications:", error);
    return [];
  }
}

export async function markNotificationAsRead(notificationId: string, userId: string): Promise<boolean> {
    const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .eq('recipient_user_id', userId); // Ensure user can only mark their own
    if (error) {
        console.error("Error marking notification as read:", error);
        return false;
    }
    return true;
}

export async function markAllNotificationsAsRead(userId: string): Promise<boolean> {
    const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('recipient_user_id', userId)
        .eq('is_read', false);
    if (error) {
        console.error("Error marking all notifications as read:", error);
        return false;
    }
    return true;
}

// --- For Homepage ---
export async function getCategoriesWithPromptCount(): Promise<Category[]> {
  try {
    // Get all categories without prompt count
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .order("name");
    
    if (error) {
      console.error("Error fetching categories:", error);
      throw error;
    }
    
    // Just transform the categories without adding prompt count
    return (data || []).map(transformCategory);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
}

export async function getToolsWithPromptCount(): Promise<Tool[]> {
  try {
    // Get all tools without prompt count
    const { data, error } = await supabase
      .from("tools")
      .select("*")
      .order("name");
    
    if (error) {
      console.error("Error fetching tools:", error);
      throw error;
    }
    
    // Just transform the tools without adding prompt count
    return (data || []).map(transformTool);
  } catch (error) {
    console.error("Error fetching tools:", error);
    return [];
  }
}

export async function getTagsWithPromptCount(): Promise<Tag[]> {
  try {
    // Get all tags without prompt count
    const { data, error } = await supabase
      .from("tags")
      .select("*")
      .order("name");
    
    if (error) {
      console.error("Error fetching tags:", error);
      throw error;
    }
    
    // Just transform the tags without adding prompt count
    return (data || []).map(transformTag);
  } catch (error) {
    console.error("Error fetching tags:", error);
    return [];
  }
}

// Define a type for the data fetched from the comment_display_details view
interface CommentDisplayDetails {
  id: string;
  prompt_id: string;
  parent_comment_id: string | null;
  user_id: string;
  author_username: string;
  author_avatar_url: string | null;
  text: string;
  like_count: number;
  dislike_count: number;
  created_at: string;
  updated_at: string;
  // Add other fields from the view if necessary
}

// --- Comments ---
export async function getCommentsForPrompt(promptShortIdOrId: string): Promise<Comment[]> {
  try {
    const { data: topLevelCommentsData, error: topLevelError } = await supabase
      .from("comment_display_details") // Use the view
      .select("*")
      .eq("prompt_id", promptShortIdOrId) // Assuming prompt_id in view can be short_id or UUID
      .is("parent_comment_id", null)
      .order("like_count", { ascending: false }); // Default sort by top

    if (topLevelError) {
      console.error("Error fetching top-level comments:", topLevelError.message);
      throw topLevelError;
    }

    const topLevelComments: CommentDisplayDetails[] = (topLevelCommentsData || []) as unknown as CommentDisplayDetails[];

    // Fetch replies separately
    const { data: repliesData, error: repliesError } = await supabase
      .from("comment_display_details")
      .select("*")
      .eq("prompt_id", promptShortIdOrId)
      .not("parent_comment_id", "is", null) // Only fetch replies
      .order("created_at", { ascending: true }); // Order replies chronologically

    if (repliesError) {
      console.error("Error fetching replies:", repliesError.message);
      throw repliesError;
    }

    const replies: CommentDisplayDetails[] = (repliesData || []) as unknown as CommentDisplayDetails[];

    // Group replies by parent_comment_id
    const repliesByParentId = replies.reduce(
      (acc, reply) => {
        const parentId = reply.parent_comment_id;
        if (parentId) { // Ensure parentId is not null
          if (!acc[parentId]) {
            acc[parentId] = [];
          }
          acc[parentId].push(reply);
        }
        return acc;
      },
      {} as Record<string, CommentDisplayDetails[]>
    );

    // Map fetched data to the Comment type and attach replies
    const comments: Comment[] = topLevelComments.map((commentData) => ({
      id: commentData.id,
      user_id: commentData.user_id,
      prompt_id: commentData.prompt_id,
      parent_comment_id: commentData.parent_comment_id,
      text: commentData.text,
      like_count: commentData.like_count || 0,
      dislike_count: commentData.dislike_count || 0,
      created_at: commentData.created_at,
      updated_at: commentData.updated_at,
      // Map user details from the view
      user: {
        id: commentData.user_id, // Assuming user_id is the user's ID
        username: commentData.author_username || "Anonymous",
        avatar_url: commentData.author_avatar_url,
      },
      // Map other properties needed for the Comment type
      likes: commentData.like_count || 0, // Assuming 'likes' is the same as 'like_count'
      dislikes: commentData.dislike_count || 0, // Assuming 'dislikes' is the same as 'dislike_count'
      recommended: (commentData.like_count || 0) > (commentData.dislike_count || 0), // Example logic
      liked_by_user: false, // This needs to be determined based on user's likes, potentially in the component
      replies: (repliesByParentId[commentData.id] || []).map(replyData => ({
         id: replyData.id,
         user_id: replyData.user_id,
         prompt_id: replyData.prompt_id,
         parent_comment_id: replyData.parent_comment_id,
         text: replyData.text,
         like_count: replyData.like_count || 0,
         dislike_count: replyData.dislike_count || 0,
         created_at: replyData.created_at,
         updated_at: replyData.updated_at,
         user: {
           id: replyData.user_id,
           username: replyData.author_username || "Anonymous",
           avatar_url: replyData.author_avatar_url,
         },
         likes: replyData.like_count || 0,
         dislikes: replyData.dislike_count || 0,
         recommended: (replyData.like_count || 0) > (replyData.dislike_count || 0),
         liked_by_user: false, // Needs to be determined based on user's likes
         replies: [], // Replies of replies are not currently supported in this structure
      })),
    }));

    return comments;
  } catch (error) {
    console.error("Error in getCommentsForPrompt:", error);
    return [];
  }
}

// --- AI Models ---
export async function getAllAIModels(): Promise<AIModel[]> {
  try {
    const { data, error } = await supabase
      .from("ai_models")
      .select("*")
      .order("provider");

    if (error) {
      console.error("Error fetching AI models:", error.message);
      throw error;
    }

    return data?.map((model: any) => ({
      id: Number(model.id),
      provider: String(model.provider),
      name: String(model.tool_name),
      slug: String(model.slug),
      type: model.type ? String(model.type) : null,
      deprecated: Boolean(model.deprecated),
      tool_id: Number(model.tool_id),
    })) || [];
  } catch (error) {
    console.error("Error in getAllAIModels:", error);
    return [];
  }
}

export async function getAIModelsForTool(toolSlug: string): Promise<AIModel[]> {
  try {
    // First get the tool ID from the slug
    const { data: toolData, error: toolError } = await supabase
      .from("tools")
      .select("id, name")
      .eq("slug", toolSlug)
      .single();

    if (toolError) {
      console.error(`Error fetching tool ID for slug ${toolSlug}:`, toolError.message);
      throw toolError;
    }

    if (!toolData) {
      console.error(`No tool found with slug ${toolSlug}`);
      return [];
    }

    // Now fetch AI models for this tool ID
    const { data, error } = await supabase
      .from("ai_models")
      .select("*")
      .eq("tool_id", toolData.id);

    if (error) {
      console.error(`Error fetching AI models for tool ${toolSlug}:`, error.message);
      throw error;
    }

    // Type assertion to help TypeScript understand the structure
    const typedData = data as Array<{
      id: number;
      provider: string;
      tool_name: string;
      slug?: string;
      type?: string;
      deprecated: boolean;
      tool_id: number;
      tool?: string;
    }>;

    return typedData.map((model) => ({
      id: Number(model.id),
      provider: String(model.provider || ''),
      name: String(model.tool_name || ''),  // This is the actual model name in the database
      slug: String(model.slug || model.id || ''),
      type: model.type ? String(model.type) : null,
      tool_id: Number(model.tool_id),
      tool_slug: toolSlug,
      tool_name: String(model.tool || toolData.name || ''),
      deprecated: Boolean(model.deprecated),
    }));
  } catch (error) {
    console.error(`Error in getAIModelsForTool for ${toolSlug}:`, error);
    return [];
  }
}

export async function getAIModelBySlug(slug: string): Promise<AIModel | null> {
  try {
    const { data, error } = await supabase
      .from("ai_models")
      .select("*")
      .eq("slug", slug)
      .single();

    if (error || !data) {
      console.error(`Error fetching AI model by slug ${slug}:`, error?.message);
      return null;
    }

    return {
      id: Number(data.id),
      provider: String(data.provider),
      name: String(data.tool_name),
      slug: String(data.slug),
      type: data.type ? String(data.type) : null,
      deprecated: Boolean(data.deprecated),
      tool_id: Number(data.tool_id),
    };
  } catch (error) {
    console.error(`Error in getAIModelBySlug for ${slug}:`, error);
    return null;
  }
}

// --- Related Prompts ---
export async function updatePrompt(
  promptId: string,
  userId: string, // For authorization
  promptData: Partial<CreatePromptData>
): Promise<{ success: boolean; error: any; updatedSlug?: string }> {
  try {
    // Fetch the existing prompt to verify ownership and get short_id
    const { data: existingPrompt, error: fetchError } = await supabase
      .from('prompts')
      .select('user_id, short_id, slug, title, category_id, tool_id, primary_tag_id')
      .eq('id', promptId)
      .single();

    if (fetchError || !existingPrompt) {
      console.error('Error fetching existing prompt or prompt not found:', fetchError?.message);
      return { success: false, error: fetchError || new Error("Prompt not found") };
    }

    if (existingPrompt.user_id !== userId) {
      console.error('User not authorized to update this prompt.');
      return { success: false, error: new Error("Unauthorized") };
    }

    // Prepare data for update, including slug regeneration if title changed
    const updatePayload: any = {
      updated_at: new Date().toISOString(),
      updated_by_user_id: userId,
    };

    if (promptData.title !== undefined) updatePayload.title = promptData.title;
    if (promptData.description !== undefined) updatePayload.description = promptData.description;
    if (promptData.promptText !== undefined) updatePayload.prompt_text = promptData.promptText;
    if (promptData.instructions !== undefined) updatePayload.instructions = promptData.instructions;
    if (promptData.exampleInput !== undefined) updatePayload.example_input = promptData.exampleInput;
    if (promptData.exampleOutputText !== undefined) updatePayload.example_output_text = promptData.exampleOutputText;
    if (promptData.imageUrl !== undefined) updatePayload.image_url = promptData.imageUrl; // Already handles null
    if (promptData.isPublic !== undefined) updatePayload.is_public = promptData.isPublic;
    if (promptData.categoryId !== undefined) updatePayload.category_id = promptData.categoryId;
    if (promptData.toolId !== undefined) updatePayload.tool_id = promptData.toolId;
    if (promptData.aiModelId !== undefined) updatePayload.ai_model_id = promptData.aiModelId;
    if (promptData.userEnteredAiModel !== undefined) updatePayload.user_entered_ai_model = promptData.userEnteredAiModel;

    let newSlug = existingPrompt?.slug || ''; // Keep existing slug by default

    // Regenerate slug if title, category, or tool changes
    // Or if a primary tag is involved and it changes (more complex, handle if needed)
    const currentTitle = promptData.title || (existingPrompt?.title as string);
    const currentCategoryId = promptData.categoryId || (existingPrompt?.category_id as number);
    const currentToolId = promptData.toolId || (existingPrompt?.tool_id as number);
    // For slug generation, we need category and tool slugs, and first tag slug
    const { data: categoryData } = await supabase.from('categories').select('slug').eq('id', currentCategoryId).single();
    const { data: toolData } = await supabase.from('tools').select('slug').eq('id', currentToolId).single();
    
    // Type assertions for categoryData and toolData
    const categorySlug = categoryData?.slug as string | undefined;
    const toolSlug = toolData?.slug as string | undefined;
    
    let firstTagSlug = 'untagged';
    if (promptData.tagIds && promptData.tagIds.length > 0) {
        const { data: tagData } = await supabase.from('tags').select('slug').eq('id', promptData.tagIds[0]).single();
        if (tagData) firstTagSlug = tagData.slug as string;
    } else if (existingPrompt?.primary_tag_id) {
        const { data: tagData } = await supabase.from('tags').select('slug').eq('id', existingPrompt.primary_tag_id).single();
        if (tagData) firstTagSlug = tagData.slug as string;
    }

    if (categorySlug && toolSlug) {
        newSlug = `prompt/${categorySlug}/${toolSlug}/${firstTagSlug}/${createTitleSlug(currentTitle)}/${existingPrompt?.short_id as string}`;
        updatePayload.slug = newSlug;
    }

    // Update the prompt table
    const { error } = await supabase
      .from('prompts')
      .update(updatePayload)
      .eq('id', promptId);
    
    if (error) {
      console.error('Error updating prompt:', error.message);
      return { success: false, error };
    }
    
    // Handle tags: delete existing and insert new ones
    if (promptData.tagIds !== undefined) {
      // First delete all existing tag associations
      const { error: deleteError } = await supabase
        .from('prompt_tags')
        .delete()
        .eq('prompt_id', promptId);
      
      if (deleteError) {
        console.error('Error deleting existing tags:', deleteError.message);
        return { success: false, error: deleteError };
      }
      
      // Then insert new tag associations if there are any tags
      if (promptData.tagIds.length > 0) {
        const newPromptTags = promptData.tagIds.map(tagId => ({
          prompt_id: promptId,
          tag_id: tagId,
        }));
        const { error: insertError } = await supabase
          .from('prompt_tags')
          .insert(newPromptTags);

        if (insertError) {
          console.error('Error inserting new tags:', insertError.message);
          return { success: false, error: insertError };
        }
      }
      
      // Update primary_tag_id on prompts table if tags were changed
      if (promptData.tagIds.length > 0) {
        await supabase.from('prompts').update({ primary_tag_id: promptData.tagIds[0] }).eq('id', promptId);
      } else {
        await supabase.from('prompts').update({ primary_tag_id: null }).eq('id', promptId);
      }
    }
    
    return { success: true, error: null, updatedSlug: newSlug };
  } catch (error) {
    console.error('Error in updatePrompt:', error);
    return { success: false, error };
  }
}

// This section has been cleaned up
export async function getPromptCollectionMembership(
  userId: string,
  promptId: string
): Promise<{ collectionIds: string[]; error?: any }> {
  try {
    // Get all collections that belong to the user
    const { data: userCollections, error: collectionsError } = await supabase
      .from("collections")
      .select("id")
      .eq("user_id", userId);

    if (collectionsError) {
      console.error("Error fetching user collections:", collectionsError);
      return { collectionIds: [], error: collectionsError };
    }

    if (!userCollections || userCollections.length === 0) {
      // User has no collections
      return { collectionIds: [] };
    }

    // Get all collection_prompts entries for this prompt
    const userCollectionIds = userCollections.map(c => c.id);
    
    const { data: memberships, error: membershipsError } = await supabase
      .from("collection_prompts")
      .select("collection_id")
      .eq("prompt_id", promptId)
      .in("collection_id", userCollectionIds);

    if (membershipsError) {
      console.error("Error fetching prompt collection memberships:", membershipsError);
      return { collectionIds: [], error: membershipsError };
    }

    // Return the collection IDs
    return { 
      collectionIds: memberships ? memberships.map(m => m.collection_id) : [],
      error: null
    };
  } catch (e) {
    console.error("Unexpected error in getPromptCollectionMembership service:", e);
    return { collectionIds: [], error: e };
  }
}

export async function getRelatedPromptsForPrompt(sourcePrompt: Prompt, limit = 3): Promise<PromptCard[]> {
  try {
    // This is a placeholder for actual related prompt logic.
    // A real implementation would use vector embeddings, tag matching, category/tool similarity, etc.
    // For now, we'll just fetch a few other random public prompts excluding the source.

    const { data, error } = await supabase
      .from("prompt_card_details")
      .select("*")
      .eq("is_public", true)
      .neq("id", sourcePrompt.id) // Exclude the source prompt
      .limit(limit); // Limit the number of results

    if (error) {
      console.error("Error fetching related prompts:", error.message);
      throw error;
    }

    // Simple shuffling for a "random" feel in the placeholder
    const shuffledData = data ? data.sort(() => 0.5 - Math.random()) : [];

    return shuffledData.map(transformPromptCard);
  } catch (error) {
    console.error("Error in getRelatedPromptsForPrompt:", error);
    return [];
  }
}
