import { getPrompts, getCategories, getTools, getTags } from "./api-services"

// Test function to verify our API services
export async function testStep2() {
  console.log("Testing Step 2: Data Fetching Implementation")

  try {
    // Test fetching prompts
    console.log("Fetching prompts...")
    const prompts = await getPrompts({ limit: 5 })
    console.log(`Fetched ${prompts.length} prompts`)

    if (prompts.length > 0) {
      console.log("Sample prompt:", {
        id: prompts[0].id,
        title: prompts[0].title,
        category: prompts[0].category?.name,
        tool: prompts[0].tool?.name,
        user: prompts[0].user?.username,
      })
    }

    // Test fetching categories
    console.log("Fetching categories...")
    const categories = await getCategories()
    console.log(`Fetched ${categories.length} categories`)

    if (categories.length > 0) {
      console.log(
        "Sample categories:",
        categories.slice(0, 3).map((c) => ({
          id: c.id,
          name: c.name,
          promptCount: c.promptCount,
        })),
      )
    }

    // Test fetching tools
    console.log("Fetching tools...")
    const tools = await getTools()
    console.log(`Fetched ${tools.length} tools`)

    if (tools.length > 0) {
      console.log(
        "Sample tools:",
        tools.slice(0, 3).map((t) => ({
          id: t.id,
          name: t.name,
          promptCount: t.promptCount,
        })),
      )
    }

    // Test fetching tags
    console.log("Fetching tags...")
    const tags = await getTags()
    console.log(`Fetched ${tags.length} tags`)

    if (tags.length > 0) {
      console.log(
        "Sample tags:",
        tags.slice(0, 3).map((t) => ({
          id: t.id,
          name: t.name,
          promptCount: t.promptCount,
        })),
      )
    }

    return {
      success: true,
      message: "Step 2 data fetching completed successfully",
      data: {
        promptCount: prompts.length,
        categoryCount: categories.length,
        toolCount: tools.length,
        tagCount: tags.length,
      },
    }
  } catch (error) {
    console.error("Error testing Step 2:", error)
    return {
      success: false,
      message: "Error testing Step 2",
      error,
    }
  }
}
