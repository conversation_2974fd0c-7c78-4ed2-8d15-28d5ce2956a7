import { supabase } from "./api-client"
import { transformPrompt, transformCategory } from "./transformers"

// Test function to verify our setup
export async function testStep1() {
  console.log("Testing Step 1: Setup and Configuration")

  try {
    // Test Supabase connection
    const { data: categories, error } = await supabase.from("categories").select("*").limit(1)

    if (error) {
      console.error("Error connecting to Supabase:", error)
      console.log("Falling back to mock data for testing transformers")

      // Test transformers with mock data
      const mockDbPrompt = {
        id: "123",
        short_id: "abc123",
        user_id: "user123",
        category_id: 1,
        tool_id: 1,
        title: "Test Prompt",
        slug: "test-prompt",
        description: "A test prompt",
        prompt_text: "This is a test prompt",
        instructions: "Test instructions",
        example_input: "Test input",
        example_output_text: "Test output",
        example_output_image_url: null,
        image_url: null,
        is_public: true,
        original_prompt_id: null,
        view_count: 10,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        updated_by_user_id: null,
        category: {
          id: 1,
          name: "Test Category",
          slug: "test-category",
          description: "A test category",
          image_path: null,
        },
        tool: {
          id: 1,
          name: "Test Tool",
          slug: "test-tool",
          description: "A test tool",
          website: null,
          icon: "🔧",
        },
        user: {
          id: "user123",
          username: "testuser",
          bio: "Test bio",
          avatar_url: null,
          website_url: null,
          github_url: null,
          x_url: null,
          youtube_url: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        tags: [
          {
            tag: {
              id: 1,
              name: "Test Tag",
              slug: "test-tag",
            },
          },
        ],
        rating: 5,
        comment_count: 2,
        remix_count: 1,
      }

      const transformedPrompt = transformPrompt(mockDbPrompt)
      console.log("Transformed prompt:", transformedPrompt)

      return {
        success: true,
        message: "Transformers tested with mock data",
        data: { transformedPrompt },
      }
    }

    console.log("Successfully connected to Supabase")
    console.log("Categories:", categories)

    if (categories && categories.length > 0) {
      const transformedCategory = transformCategory(categories[0])
      console.log("Transformed category:", transformedCategory)
    }

    return {
      success: true,
      message: "Step 1 setup completed successfully",
      data: { categories },
    }
  } catch (error) {
    console.error("Error testing Step 1:", error)
    return {
      success: false,
      message: "Error testing Step 1",
      error,
    }
  }
}
