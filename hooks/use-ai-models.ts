import { useState, useEffect } from "react"
import { getAllAIModels } from "@/lib/api-services"
import type { AIModel } from "@/lib/types"

interface UseAIModelsResult {
  models: AIModel[]
  isLoading: boolean
  error: Error | null
}

export function useAIModels(includeDeprecated: boolean = false): UseAIModelsResult {
  const [models, setModels] = useState<AIModel[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    async function fetchModels() {
      try {
        setIsLoading(true)
        const fetchedModels = await getAllAIModels()
        
        // Filter out deprecated models if includeDeprecated is false
        const filteredModels = includeDeprecated 
          ? fetchedModels 
          : fetchedModels.filter(model => !model.deprecated)
        
        setModels(filteredModels)
      } catch (err) {
        console.error("Error fetching AI models:", err)
        setError(err instanceof Error ? err : new Error("Failed to fetch AI models"))
      } finally {
        setIsLoading(false)
      }
    }

    fetchModels()
  }, [includeDeprecated])

  return { models, isLoading, error }
}
