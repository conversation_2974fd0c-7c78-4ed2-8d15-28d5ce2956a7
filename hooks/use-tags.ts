"use client"

import { useState, useEffect } from "react"
import { getTags, getTagBySlug } from "@/lib/api-services"
import type { Tag } from "@/lib/types"

export function useTags() {
  const [tags, setTags] = useState<Tag[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchTags = async () => {
      try {
        setIsLoading(true)
        const data = await getTags()

        if (isMounted) {
          setTags(data)
          setError(null)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchTags()

    return () => {
      isMounted = false
    }
  }, [])

  return { tags, isLoading, error }
}

export function useTagBySlug(slug: string) {
  const [tag, setTag] = useState<Tag | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchTag = async () => {
      try {
        setIsLoading(true)
        const data = await getTagBySlug(slug)

        if (isMounted) {
          setTag(data)
          setError(null)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchTag()

    return () => {
      isMounted = false
    }
  }, [slug])

  return { tag, isLoading, error }
}
