"use client"

import { useState, useEffect } from "react"
import { getProfileById, getProfileByUsername } from "@/lib/api-services"
import type { Profile } from "@/lib/types"

export function useProfileById(userId: string) {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchProfile = async () => {
      try {
        setIsLoading(true)
        const data = await getProfileById(userId)

        if (isMounted) {
          setProfile(data)
          setError(null)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchProfile()

    return () => {
      isMounted = false
    }
  }, [userId])

  return { profile, isLoading, error }
}

export function useProfileByUsername(username: string) {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchProfile = async () => {
      try {
        setIsLoading(true)
        const data = await getProfileByUsername(username)

        if (isMounted) {
          setProfile(data)
          setError(null)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchProfile()

    return () => {
      isMounted = false
    }
  }, [username])

  return { profile, isLoading, error }
}
