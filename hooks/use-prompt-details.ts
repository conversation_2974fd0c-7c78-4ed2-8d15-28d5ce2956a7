"use client"

import { useState, useEffect } from "react"
import { getPromptById, getCommentsForPrompt, getRelatedPromptsForDisplay } from "@/lib/api-services"
import type { Prompt, Comment, PromptCard } from "@/lib/types"

export function usePromptDetails(id: string) {
  const [prompt, setPrompt] = useState<Prompt | null>(null)
  const [comments, setComments] = useState<Comment[]>([])
  const [relatedPrompts, setRelatedPrompts] = useState<PromptCard[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingComments, setIsLoadingComments] = useState(true)
  const [isLoadingRelated, setIsLoadingRelated] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchPromptDetails = async () => {
      try {
        setIsLoading(true)

        // Fetch prompt details
        const promptData = await getPromptById(id)

        if (isMounted) {
          setPrompt(promptData)
          setError(null)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    const fetchComments = async () => {
      try {
        setIsLoadingComments(true)

        // Fetch comments
        const commentsData = await getCommentsForPrompt(id)

        if (isMounted) {
          setComments(commentsData)
        }
      } catch (err) {
        console.error("Error fetching comments:", err)
      } finally {
        if (isMounted) {
          setIsLoadingComments(false)
        }
      }
    }

    const fetchRelatedPrompts = async () => {
      try {
        setIsLoadingRelated(true)

        // Fetch related prompts - assuming id is a shortId for this function
        const relatedData = await getRelatedPromptsForDisplay(id)

        if (isMounted) {
          setRelatedPrompts(relatedData)
        }
      } catch (err) {
        console.error("Error fetching related prompts:", err)
      } finally {
        if (isMounted) {
          setIsLoadingRelated(false)
        }
      }
    }

    fetchPromptDetails()
    fetchComments()
    fetchRelatedPrompts()

    return () => {
      isMounted = false
    }
  }, [id])

  return {
    prompt,
    comments,
    relatedPrompts,
    isLoading,
    isLoadingComments,
    isLoadingRelated,
    error,
  }
}
