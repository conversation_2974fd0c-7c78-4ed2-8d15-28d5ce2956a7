"use client"

import { useState, useEffect, useMemo } from "react"
import { searchPrompts } from "@/lib/api-services"
// Use PromptCard type for results list
import type { PromptCard } from "@/lib/types"

// Rename hook for clarity
export function usePromptSearch(
  query: string,
  options: {
    categoryId?: number
    toolId?: number
    tagIds?: number[]
    limit?: number
  } = {},
) {
  // Changed type from Prompt[] to PromptCard[]
  const [results, setResults] = useState<PromptCard[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  // Removed pagination for simplicity in suggestions context
  // const [hasMore, setHasMore] = useState(true);
  // const [page, setPage] = useState(0);

  const { categoryId, toolId, tagIds, limit = 5 } = options // Default limit to 5 for suggestions

  // Reset when search parameters change
  useEffect(() => {
    setResults([])
    // setPage(0);
    // setHasMore(true);
  }, [query, categoryId, toolId, tagIds, limit])

  // Fetch search results
  useEffect(() => {
    // Don't search if query is empty
    if (!query.trim()) {
      setResults([])
      setIsLoading(false)
      // setHasMore(false);
      return
    }

    let isMounted = true

    const fetchResults = async () => {
      try {
        setIsLoading(true)
        // Removed offset/pagination logic
        // const offset = page * limit;

        const newResults = await searchPrompts(query, {
          limit,
          offset: 0, // Always fetch from the beginning for suggestions
        })

        if (isMounted) {
          setResults(newResults) // Replace results, don't append
          // setHasMore(newResults.length === limit);
          setError(null)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    const debounceTimer = setTimeout(() => {
        fetchResults()
    }, 150); // Reduced from 300ms to 150ms for faster response

    return () => {
        isMounted = false;
        clearTimeout(debounceTimer); // Clear timeout on cleanup
    }
    // Removed page from dependency array
  }, [query, categoryId, toolId, tagIds, limit])

  // Removed loadMore function

  // Return only necessary values for suggestions
  return { results, isLoading, error }
}

/**
 * Hook for universal search typeahead (prompts + collections + profiles)
 * Optimized version to prevent infinite loops
 */
export function useUniversalSearch(query: string, options: { limit?: number } = {}) {
  const [results, setResults] = useState<{
    id: string;
    title: string;
    itemType: "prompt" | "collection" | "profile";
    shortId?: string;
    slug?: string;
    userId: string;
    username: string;
    avatarUrl?: string;
    itemIcon?: string;
    rank: number;
  }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize the trimmed query to prevent unnecessary effect triggers
  const trimmedQuery = useMemo(() => query?.trim() || "", [query]);
  
  // Memoize options to prevent unnecessary effect triggers
  const memoizedOptions = useMemo(() => options, [options.limit]);

  useEffect(() => {
    // Early return for empty queries
    if (!trimmedQuery || trimmedQuery.length === 0) {
      setResults([]);
      setIsLoading(false);
      setError(null);
      return;
    }
    
    let isMounted = true;

    const searchUniversal = async () => {
      if (!isMounted) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const { universalSearchTypeahead } = await import("@/lib/api-services");
        const searchResults = await universalSearchTypeahead(trimmedQuery, undefined, memoizedOptions.limit);
        
        if (isMounted) {
          setResults(searchResults);
        }
      } catch (err) {
        console.error("Error in universal search:", err);
        if (isMounted) {
          setError(err instanceof Error ? err.message : "Search failed");
          setResults([]);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    // Debounce the search
    const debounceTimer = setTimeout(() => {
      searchUniversal();
    }, 150);

    return () => {
      isMounted = false;
      clearTimeout(debounceTimer);
    };
  }, [trimmedQuery, memoizedOptions.limit]);

  return { results, isLoading, error };
}
