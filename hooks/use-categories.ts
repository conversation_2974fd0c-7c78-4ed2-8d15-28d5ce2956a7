"use client"

import { useState, useEffect } from "react"
import { getCategories, getCategoryBySlug } from "@/lib/api-services"
import type { Category } from "@/lib/types"

export function useCategories() {
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    const fetchCategories = async () => {
      try {
        setIsLoading(true)
        const data = await getCategories()

        if (!signal.aborted) {
          setCategories(data)
          setError(null)
        }
      } catch (err) {
        if (!signal.aborted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (!signal.aborted) {
          setIsLoading(false)
        }
      }
    }

    fetchCategories()

    return () => {
      controller.abort();
    }
  }, [])

  return { categories, isLoading, error }
}

export function useCategoryBySlug(slug: string) {
  const [category, setCategory] = useState<Category | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    const fetchCategory = async () => {
      try {
        setIsLoading(true)
        const data = await getCategoryBySlug(slug)

        if (!signal.aborted) {
          setCategory(data)
          setError(null)
        }
      } catch (err) {
        if (!signal.aborted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (!signal.aborted) {
          setIsLoading(false)
        }
      }
    }

    fetchCategory()

    return () => {
      controller.abort();
    }
  }, [slug])

  return { category, isLoading, error }
}
