"use client"

import { useState, useEffect } from "react"
import { getTools, getToolBySlug } from "@/lib/api-services"
import type { Tool } from "@/lib/types"

export function useTools() {
  const [tools, setTools] = useState<Tool[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchTools = async () => {
      try {
        setIsLoading(true)
        const data = await getTools()

        if (isMounted) {
          setTools(data)
          setError(null)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchTools()

    return () => {
      isMounted = false
    }
  }, [])

  return { tools, isLoading, error }
}

export function useToolBySlug(slug: string) {
  const [tool, setTool] = useState<Tool | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchTool = async () => {
      try {
        setIsLoading(true)
        const data = await getToolBySlug(slug)

        if (isMounted) {
          setTool(data)
          setError(null)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchTool()

    return () => {
      isMounted = false
    }
  }, [slug])

  return { tool, isLoading, error }
}
