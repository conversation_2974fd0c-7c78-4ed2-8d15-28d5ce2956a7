"use client"

import { useState, useEffect } from "react"
import { getPrompts as getPromptCards } from "@/lib/api-services" // Renamed import
import type { PromptCard } from "@/lib/types" // Changed to PromptCard
 
export function usePrompts({ // This hook should return PromptCard[] for listings
  categoryId,
  toolId,
  tagId,
  userId,
  limit = 20,
  sortBy = "created_at",
  sortOrder = "desc",
}: {
  categoryId?: number
  toolId?: number
  tagId?: number
  userId?: string
  limit?: number
  sortBy?: string
  sortOrder?: "asc" | "desc"
}) { // Parameters for filtering
  const [prompts, setPrompts] = useState<PromptCard[]>([]) // Changed to PromptCard[]
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(0)

  useEffect(() => {
    setPrompts([])
    setPage(0)
    setHasMore(true)
    setIsLoading(true)
    setError(null)
  }, [categoryId, toolId, tagId, userId, sortBy, sortOrder])

  useEffect(() => {
    let isMounted = true

    const fetchPrompts = async () => {
      try {
        setIsLoading(true)
        const offset = page * limit
        const newPrompts = await getPromptCards({ // Use renamed function
          categorySlugs: categoryId !== undefined ? [categoryId.toString()] : undefined, // Pass as array of strings
          toolSlugs: toolId !== undefined ? [toolId.toString()] : undefined,             // Pass as array of strings
          // tagId needs to be handled if filtering by single tag ID is still needed,
          // but getPrompts now expects tagSlugs (array of strings).
          // Assuming tagId filtering is not currently used or needs update.
          // If tagId filtering is needed, it should be converted to tagSlugs array.
          // For now, removing tagId from parameters as getPrompts doesn't accept it directly.
          userId,
          limit,
          offset,
          sortBy,
          sortOrder,
        })

        if (isMounted) {
          setPrompts((prev) => (page === 0 ? newPrompts : [...prev, ...newPrompts]))
          setHasMore(newPrompts.length === limit)
          setError(null)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error("Unknown error occurred"))
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    fetchPrompts()

    return () => {
      isMounted = false
    }
  }, [page, categoryId, toolId, tagId, userId, limit, sortBy, sortOrder])

  const loadMore = () => {
    if (!isLoading && hasMore) {
      setPage((prev) => prev + 1)
    }
  }

  return { prompts, isLoading, error, hasMore, loadMore }
}
