"use client"

import { useState, useCallback } from "react";
import { getRelatedPromptsForDisplay } from "@/lib/api-services";
import type { PromptCard } from "@/lib/types";

export function useRelatedPrompts(sourcePromptShortId: string | undefined, limit: number = 6) {
  const [relatedPrompts, setRelatedPrompts] = useState<PromptCard[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchPrompts = useCallback(async () => {
    if (!sourcePromptShortId) {
      // Optionally set an error or clear prompts if shortId is not available
      // setRelatedPrompts([]); 
      // setError(new Error("Source prompt ID is undefined."));
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const data = await getRelatedPromptsForDisplay(sourcePromptShortId, limit);
      setRelatedPrompts(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Failed to fetch related prompts"));
    } finally {
      setIsLoading(false);
    }
  }, [sourcePromptShortId, limit]);

  return { relatedPrompts, isLoading, error, fetchPrompts };
} 