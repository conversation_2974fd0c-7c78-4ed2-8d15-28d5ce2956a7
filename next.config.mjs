/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  images: {
    // Enable Next.js Image Optimization
    loader: 'custom',
    loaderFile: './lib/supabase-image-loader.js',
    
    // Configure remote image patterns for Supabase Storage
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
      {
        protocol: 'https',
        hostname: (() => {
          const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
          if (!url) return 'localhost';
          return url.replace('https://', '').replace('http://', '');
        })(),
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
    
    // Supported image formats (in order of preference)
    formats: ['image/webp', 'image/avif'],
    
    // Device sizes for responsive images
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    
    // Image sizes for different breakpoints
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    
    // Minimize layout shift during image loading
    minimumCacheTTL: 60,
    
    // Allow optimization of SVGs
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  webpack: (config, { isServer }) => {
    // Handle fasttext.wasm and other WASM modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        'fs/promises': false,
        path: false,
        os: false,
        crypto: false,
        stream: false,
        util: false,
      };
    }

    // Handle .wasm files
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
    };

    // Ignore fasttext.wasm on server side
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push('fasttext.wasm');
    }

    return config;
  },
}

export default nextConfig
