// Force rebuild after dependency fix - updated timestamp: 2024
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { createServerClient } from "@supabase/ssr"
import type { Database } from "./lib/database.types"

// Simple Edge-compatible title slug function
function createTitleSlug(title: string): string {
  if (!title || typeof title !== "string") {
    return "untitled";
  }

  // Convert to lowercase, replace spaces and special chars with hyphens
  let slug = title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

  // Limit length to 50 characters
  if (slug.length > 50) {
    slug = slug.substring(0, 50).replace(/-[^-]*$/, ''); // Cut at last complete word
  }

  return slug || "untitled";
}

export async function middleware(req: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request: req,
  })
  
  // Create Supabase client for middleware using the latest SSR pattern with getAll/setAll
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return req.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) => req.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request: req,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )
  
  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getSession(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.
  
  // Use getSession() instead of the deprecated getUser() method
  const {
    data: { session },
    error: sessionError,
  } = await supabase.auth.getSession()
  
  // Extract user from session (will be null if no session exists)
  const user = session?.user || null

  const url = req.nextUrl.clone()
  const pathname = url.pathname

  const protectedRoutes = [
    '/settings',
    '/saved',
    '/collections',
    '/prompt/submit',
    '/prompt/remix',
  ]
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  )

  if (isProtectedRoute) {
    if (!user) {
      url.pathname = '/sign-in'
      return NextResponse.redirect(url)
    }
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('is_username_customized')
        .eq('id', user.id)
        .single()
      if (!error && profile && profile.is_username_customized === false) {
        url.pathname = '/auth/set-username'
        return NextResponse.redirect(url)
      }
    } catch (error) {
      console.error('Error checking profile in middleware:', error)
    }
  }

  if (user && (pathname === '/sign-in' || pathname === '/sign-up' || pathname === '/forgot-password')) {
    url.pathname = '/'
    return NextResponse.redirect(url)
  }

  if (
    pathname.match(/^\/prompt\/[^/]+$/) &&
    !pathname.includes("/prompt/submit") &&
    !pathname.includes("/prompt/templates") &&
    !pathname.includes("/prompt/remix")
  ) {
    console.log("Old prompt URL format detected, attempting redirect:", pathname)
    const id = pathname.split("/").pop()

    if (!id) {
      console.error("Could not extract ID from old prompt URL:", pathname)
      return supabaseResponse
    }

    try {
      // First try to find by UUID (for old links)
      let { data: promptData, error } = await supabase
        .from("prompts")
        .select(`
          short_id,
          title,
          category:category_id (slug),
          tool:tool_id (slug),
          primary_tag:primary_tag_id (slug)
        `)
        .eq("id", id)
        .single()

      // If not found by UUID, try by shortId
      if (error || !promptData) {
        console.log(`Prompt not found by UUID ${id}, trying shortId lookup...`)
        const { data: shortIdData, error: shortIdError } = await supabase
          .from("prompts")
          .select(`
            short_id,
            title,
            category:category_id (slug),
            tool:tool_id (slug),
            primary_tag:primary_tag_id (slug)
          `)
          .eq("short_id", id)
          .single()

        if (shortIdError || !shortIdData) {
          console.error(`Error fetching prompt data for redirect (ID: ${id}):`, shortIdError?.message || error?.message)
          url.pathname = `/prompt/not-found/${id}`
          return NextResponse.rewrite(url)
        }

        promptData = shortIdData
      }

      const categorySlug = promptData.category?.slug || "uncategorized"
      const toolSlug = promptData.tool?.slug || "general"
      const tagSlug = promptData.primary_tag?.slug || "misc"
      const titleSlug = createTitleSlug(promptData.title || "untitled")
      const actualShortId = promptData.short_id

      if (!actualShortId) {
        console.error(`[Middleware] Prompt with ID ${id} is MISSING its short_id. Title: ${promptData.title}`)
        return supabaseResponse
      }
      const newPath = `/prompt/${categorySlug}/${toolSlug}/${tagSlug}/${titleSlug}/${actualShortId}`;
      console.log(`[Middleware] Redirecting from ${pathname} to ${newPath}. actualShortId: "${actualShortId}"`);
      url.pathname = newPath;
      return NextResponse.redirect(url);
    } catch (redirectError) {
      console.error("Error in middleware redirect logic:", redirectError)
      return supabaseResponse
    }
  }

  return supabaseResponse
}

export const config = {
  matcher: [
    "/prompt/:path*",
    "/settings/:path*",
    "/saved/:path*",
    "/collections/:path*",
    "/sign-in",
    "/sign-up",
    "/forgot-password",
    "/reset-password",
    "/auth/:path*"
  ],
}
