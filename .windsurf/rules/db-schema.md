---
trigger: model_decision
description: whenever we fetch or access database data, or need to reference supabase database schema
---

whenever we fetch or access database data, or need to reference database schema use the documentation and docs in /docs/

there should be a recent .sql file of a db schema

db dump:
if its not available you may also access the db data or dump the schema locally 

- use PostgreSQL 15 version of pg_dump (to match your Supabase server's version 15.8), it's saved in /opt/homebrew/opt/postgresql@15
- the project id is "xsiipracopbtzslfkpkz"
- use the credentials from .env.local 
-  don't use docker

this is the cmd format that works:
PGPASSWORD=GP7V7rb4KeJIwTHg /opt/homebrew/opt/postgresql@15/bin/pg_dump -h db.xsiipracopbtzslfkpkz.supabase.co -U postgres -d postgres --schema-only --no-owner --no-privileges -f docs/database-schema.sql