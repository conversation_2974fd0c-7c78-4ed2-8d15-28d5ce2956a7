-- Update the view to fix column ambiguity issue
-- This fixes the "column reference 'id' is ambiguous" error

-- Drop and recreate the view with explicit column selection
DROP VIEW IF EXISTS prompt_card_details CASCADE;

CREATE OR REPLACE VIEW prompt_card_details AS
SELECT 
  id,
  short_id,
  title,
  description,
  image_url,
  created_at,
  updated_at,
  is_public,
  view_count,
  primary_tag_id,
  category_id,
  tool_id,
  author_id,
  search_vector,
  tag_slugs_array,
  category_name,
  category_slug,
  tool_name,
  tool_slug,
  author_username,
  author_avatar_url,
  primary_tag_slug,
  tags,
  rating,
  comment_count,
  trending_score,
  ai_model_id,
  ai_model_provider,
  ai_model_name,
  ai_model_slug,
  ai_model_deprecated,
  is_saved_by_user,
  remix_count
FROM get_prompts_unified(NULL, 1000, 0);

COMMENT ON VIEW prompt_card_details IS 'Backward compatibility view. Use get_prompts_unified function directly for better performance.';
