-- Comprehensive test script for get_prompts_unified function
-- This will help us verify if the function is working correctly

-- Test 1: Check if function exists
SELECT 'Test 1: Function exists' as test_name;
SELECT 
    proname as function_name,
    pronargs as arg_count,
    proargnames as arg_names
FROM pg_proc 
WHERE proname = 'get_prompts_unified';

-- Test 2: Basic anonymous user test (should work without errors)
SELECT 'Test 2: Anonymous user - basic call' as test_name;
SELECT COUNT(*) as total_prompts
FROM get_prompts_unified(NULL, 5, 0);

-- Test 3: Check if we get actual data for anonymous users
SELECT 'Test 3: Anonymous user - sample data' as test_name;
SELECT 
    id,
    title,
    is_saved_by_user,
    category_name,
    author_username
FROM get_prompts_unified(NULL, 3, 0)
LIMIT 3;

-- Test 4: Test with different sort options
SELECT 'Test 4: Sort by rating (desc)' as test_name;
SELECT 
    title,
    rating,
    is_saved_by_user
FROM get_prompts_unified(NULL, 3, 0, NULL, NULL, NULL, NULL, NULL, NULL, 'rating', 'desc')
LIMIT 3;

-- Test 5: Test with different sort options
SELECT 'Test 5: Sort by created_at (asc)' as test_name;
SELECT 
    title,
    created_at,
    is_saved_by_user
FROM get_prompts_unified(NULL, 3, 0, NULL, NULL, NULL, NULL, NULL, NULL, 'created_at', 'asc')
LIMIT 3;

-- Test 6: Test with search query
SELECT 'Test 6: Search functionality' as test_name;
SELECT 
    title,
    description,
    is_saved_by_user
FROM get_prompts_unified(NULL, 3, 0, NULL, NULL, NULL, NULL, 'prompt', NULL, 'created_at', 'desc')
LIMIT 3;

-- Test 7: Check if all required columns are present
SELECT 'Test 7: All columns present' as test_name;
SELECT 
    id IS NOT NULL as has_id,
    short_id IS NOT NULL as has_short_id,
    title IS NOT NULL as has_title,
    is_saved_by_user IS NOT NULL as has_saved_status,
    category_name IS NOT NULL as has_category,
    author_username IS NOT NULL as has_author
FROM get_prompts_unified(NULL, 1, 0)
LIMIT 1;

-- Test 8: Test with a real user ID (if you have one)
-- Replace 'your-user-id-here' with an actual UUID from your profiles table
SELECT 'Test 8: Real user test (replace UUID)' as test_name;
-- Uncomment and replace with real UUID:
-- SELECT 
--     title,
--     is_saved_by_user,
--     category_name
-- FROM get_prompts_unified('your-user-id-here', 3, 0)
-- LIMIT 3;

-- Test 9: Check for any NULL issues in critical fields
SELECT 'Test 9: NULL checks' as test_name;
SELECT 
    COUNT(*) as total_rows,
    COUNT(id) as non_null_ids,
    COUNT(title) as non_null_titles,
    COUNT(CASE WHEN is_saved_by_user IS NULL THEN 1 END) as null_saved_status
FROM get_prompts_unified(NULL, 10, 0);

-- Test 10: Performance test - larger dataset
SELECT 'Test 10: Performance test' as test_name;
SELECT 
    COUNT(*) as total_fetched,
    MIN(created_at) as oldest_prompt,
    MAX(created_at) as newest_prompt
FROM get_prompts_unified(NULL, 50, 0);

-- Test 11: Check if tables exist and have data
SELECT 'Test 11: Table existence and data check' as test_name;
SELECT 
    'prompts' as table_name,
    COUNT(*) as row_count
FROM prompts
WHERE is_public = true
UNION ALL
SELECT 
    'categories' as table_name,
    COUNT(*) as row_count
FROM categories
UNION ALL
SELECT 
    'tools' as table_name,
    COUNT(*) as row_count
FROM tools
UNION ALL
SELECT 
    'profiles' as table_name,
    COUNT(*) as row_count
FROM profiles;

-- Test 12: Check if dependent tables exist
SELECT 'Test 12: Dependent tables check' as test_name;
SELECT 
    table_name,
    CASE WHEN table_name IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('prompt_statistics', 'trending_prompts', 'ai_models', 'prompt_tags', 'tags', 'collections', 'collection_prompts')
ORDER BY table_name;

-- Test 13: Simple direct query to compare
SELECT 'Test 13: Direct prompts query for comparison' as test_name;
SELECT 
    COUNT(*) as direct_count,
    MIN(created_at) as oldest,
    MAX(created_at) as newest
FROM prompts 
WHERE is_public = true;

-- Final summary
SELECT 'SUMMARY: If all tests above show data and no errors, the function is working!' as result;
