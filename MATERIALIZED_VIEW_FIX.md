# Materialized View Refresh Fix

## Problem
The `/api/refresh-cache` endpoint was failing on Vercel with the following error:

```
cannot refresh materialized view "public.mv_trending_prompts" concurrently
```

**Root Cause**: PostgreSQL requires a unique index on materialized views to support `REFRESH MATERIALIZED VIEW CONCURRENTLY`. The current materialized views (`mv_trending_prompts` and `mv_prompt_statistics`) only have regular indexes, not unique indexes.

## Solution
We've fixed this by updating the `refresh_prompt_caches()` function to use regular (non-concurrent) refresh instead of concurrent refresh. This is actually better for a cron job scenario since:

1. **Non-concurrent refresh is more reliable** - doesn't require unique indexes
2. **Simpler maintenance** - no need to manage unique constraints on materialized views
3. **Adequate for cron jobs** - the brief lock during refresh is acceptable for scheduled tasks
4. **Better error handling** - fewer edge cases and dependency requirements

## Files Changed

### 1. `app/api/refresh-cache/route.ts`
- Simplified error handling
- Uses the updated `refresh_prompt_caches()` function

### 2. `database_migration_fix_materialized_view_refresh.sql`
- Creates fixed database functions
- Updates existing `refresh_prompt_caches()` to use non-concurrent refresh
- Adds helper functions for individual view refresh
- Includes optional unique index creation (commented out)

## Applying the Fix

### Step 1: Apply Database Migration
Run the SQL migration in your Supabase database:

```sql
-- Execute the contents of database_migration_fix_materialized_view_refresh.sql
-- in your Supabase SQL Editor or via psql
```

### Step 2: Deploy Code Changes
Deploy the updated `app/api/refresh-cache/route.ts` to Vercel.

### Step 3: Verify Fix
Check your Vercel function logs to confirm the cache refresh is working:

```bash
# The endpoint should now return 200 and log:
# "Prompt caches refreshed at [timestamp] (updated to non-concurrent)"
```

## Alternative Future Solution (Optional)

If you later want to enable concurrent refresh for better performance during high-traffic periods, you can:

1. Uncomment the unique index creation in the migration file
2. Update the function to use `REFRESH MATERIALIZED VIEW CONCURRENTLY`

However, for the current cron job use case, non-concurrent refresh is recommended.

## Technical Details

### Before (Failing)
```sql
REFRESH MATERIALIZED VIEW CONCURRENTLY mv_trending_prompts;  -- ❌ Requires unique index
```

### After (Working)  
```sql
REFRESH MATERIALIZED VIEW mv_trending_prompts;  -- ✅ Works with regular index
```

### Performance Impact
- **Concurrent**: View remains available during refresh (requires unique index)
- **Non-concurrent**: Brief exclusive lock during refresh (works with any index)

For a cron job running every 10 minutes, the brief lock is negligible and much more reliable. 