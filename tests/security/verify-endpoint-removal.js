#!/usr/bin/env node

/**
 * Security Verification: Test Voting Endpoint Removal
 * 
 * This script verifies that the dangerous test-voting endpoint has been
 * completely removed from the codebase, resolving the critical security vulnerability.
 * 
 * Security Issue: CRITICAL - Unauthenticated API endpoint with admin privileges
 * Resolution: Complete removal of the test endpoint file
 */

import { existsSync, readFileSync } from 'fs';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Get the project root directory
const projectRoot = join(__dirname, '..', '..');

console.log(`
🔒 SECURITY VERIFICATION: Test Voting Endpoint Removal
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Verifying resolution of CRITICAL security vulnerability:
Issue: Unauthenticated test endpoint with admin privileges
File: app/api/test-voting/route.ts
Resolution: Complete endpoint removal

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
`);

function runSecurityVerification() {
  let allTestsPassed = true;
  
  // Test 1: Verify the dangerous endpoint file has been removed
  console.log('1. Verifying test-voting endpoint file removal...');
  const dangerousEndpointPath = join(projectRoot, 'app', 'api', 'test-voting', 'route.ts');
  
  if (existsSync(dangerousEndpointPath)) {
    console.log('   ❌ CRITICAL SECURITY RISK: test-voting endpoint still exists!');
    console.log(`   📁 File found at: ${dangerousEndpointPath}`);
    console.log('   🚨 This endpoint allows unauthenticated voting manipulation');
    allTestsPassed = false;
  } else {
    console.log('   ✅ PASS: Dangerous test-voting endpoint successfully removed');
  }

  // Test 2: Verify no references to the endpoint remain in build artifacts
  console.log('\n2. Checking for endpoint references in build logs...');
  const buildLogPath = join(projectRoot, 'build.log');
  
  if (existsSync(buildLogPath)) {
    const buildLog = readFileSync(buildLogPath, 'utf8');
    if (buildLog.includes('/api/test-voting')) {
      console.log('   ⚠️  WARNING: Build log still references test-voting endpoint');
      console.log('   💡 This is expected if the build log is from before the fix');
      console.log('   🔄 Run a new build to update the build log');
    } else {
      console.log('   ✅ PASS: No test-voting references in current build log');
    }
  } else {
    console.log('   ℹ️  INFO: No build log found (this is normal)');
  }

  // Test 3: Verify the endpoint directory has been cleaned up
  console.log('\n3. Verifying endpoint directory cleanup...');
  const endpointDirPath = join(projectRoot, 'app', 'api', 'test-voting');
  
  if (existsSync(endpointDirPath)) {
    console.log('   ⚠️  WARNING: test-voting directory still exists');
    console.log('   💡 Directory should be removed if it\'s empty');
  } else {
    console.log('   ✅ PASS: test-voting endpoint directory properly cleaned up');
  }

  // Test 4: Check for any remaining dangerous test endpoints
  console.log('\n4. Scanning for other potentially dangerous test endpoints...');
  const apiDir = join(projectRoot, 'app', 'api');
  const dangerousPatterns = [
    'test-admin',
    'test-auth', 
    'test-database',
    'debug',
    'admin-test'
  ];

  let foundDangerousEndpoints = [];
  
  for (const pattern of dangerousPatterns) {
    const testPath = join(apiDir, pattern);
    if (existsSync(testPath)) {
      foundDangerousEndpoints.push(pattern);
    }
  }

  if (foundDangerousEndpoints.length > 0) {
    console.log('   ⚠️  WARNING: Other potentially dangerous test endpoints found:');
    foundDangerousEndpoints.forEach(endpoint => {
      console.log(`      - /api/${endpoint}`);
    });
    console.log('   💡 Review these endpoints for security risks');
  } else {
    console.log('   ✅ PASS: No other dangerous test endpoints detected');
  }

  // Test 5: Verify security audit report has been updated
  console.log('\n5. Verifying security audit report update...');
  const auditReportPath = join(projectRoot, 'docs', 'task-briefs', 'security-audit-report.md');
  
  if (existsSync(auditReportPath)) {
    const auditReport = readFileSync(auditReportPath, 'utf8');
    if (auditReport.includes('✅ **RESOLVED**') && auditReport.includes('Endpoint removed')) {
      console.log('   ✅ PASS: Security audit report properly updated');
    } else {
      console.log('   ❌ FAIL: Security audit report not updated with resolution');
      allTestsPassed = false;
    }
  } else {
    console.log('   ❌ FAIL: Security audit report not found');
    allTestsPassed = false;
  }

  // Final results
  console.log(`
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔒 SECURITY VERIFICATION RESULTS
`);

  if (allTestsPassed) {
    console.log(`
🎉 ALL SECURITY TESTS PASSED!

✅ Critical vulnerability successfully resolved:
   - Dangerous test-voting endpoint completely removed
   - No unauthorized access to voting system possible  
   - Service role key no longer exposed in client routes
   - Voting data manipulation prevented

🔒 Security Status: CRITICAL VULNERABILITY RESOLVED
   Risk Level: CRITICAL → RESOLVED
   Impact: Complete voting system compromise → MITIGATED

📋 Implementation Summary:
   - Removed: app/api/test-voting/route.ts
   - Updated: Security audit report with resolution details
   - Verified: No remaining dangerous test endpoints
   - Status: Ready for production deployment

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
`);
  } else {
    console.log(`
❌ SECURITY VERIFICATION FAILED

🚨 Critical issues still need attention:
   - Review failed tests above
   - Complete all required security fixes
   - Re-run verification after fixes

⚠️  DO NOT DEPLOY TO PRODUCTION until all tests pass

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
`);
  }

  return allTestsPassed;
}

// Run the verification
const success = runSecurityVerification();
process.exit(success ? 0 : 1);
