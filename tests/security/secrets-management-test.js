#!/usr/bin/env node

/**
 * Security Test: Secrets Management
 * 
 * This test verifies that hardcoded secrets have been properly removed
 * and replaced with environment variable extraction.
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 Running Secrets Management Security Test...\n');

// Test 1: Verify next.config.mjs uses environment variables
function testNextConfig() {
  console.log('📋 Test 1: next.config.mjs secrets management');
  
  const configPath = path.join(process.cwd(), 'next.config.mjs');
  const content = fs.readFileSync(configPath, 'utf8');
  
  // Should NOT contain hardcoded project ID
  const hasHardcodedId = content.includes('xsiipracopbtzslfkpkz');
  
  // Should contain environment variable usage
  const usesEnvVar = content.includes('process.env.NEXT_PUBLIC_SUPABASE_URL');
  
  if (hasHardcodedId) {
    console.log('❌ FAIL: next.config.mjs still contains hardcoded project ID');
    return false;
  }
  
  if (!usesEnvVar) {
    console.log('❌ FAIL: next.config.mjs does not use environment variables');
    return false;
  }
  
  console.log('✅ PASS: next.config.mjs properly uses environment variables');
  return true;
}

// Test 2: Verify image loader uses environment variables
function testImageLoader() {
  console.log('📋 Test 2: supabase-image-loader.js secrets management');
  
  const loaderPath = path.join(process.cwd(), 'lib/supabase-image-loader.js');
  const content = fs.readFileSync(loaderPath, 'utf8');
  
  // Should NOT contain hardcoded project ID
  const hasHardcodedId = content.includes("'xsiipracopbtzslfkpkz'");
  
  // Should contain environment variable usage
  const usesEnvVar = content.includes('process.env.NEXT_PUBLIC_SUPABASE_URL');
  
  if (hasHardcodedId) {
    console.log('❌ FAIL: supabase-image-loader.js still contains hardcoded project ID');
    return false;
  }
  
  if (!usesEnvVar) {
    console.log('❌ FAIL: supabase-image-loader.js does not use environment variables');
    return false;
  }
  
  console.log('✅ PASS: supabase-image-loader.js properly uses environment variables');
  return true;
}

// Test 3: Verify .gitignore includes .temp files
function testGitignore() {
  console.log('📋 Test 3: .gitignore includes .temp files');
  
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  const content = fs.readFileSync(gitignorePath, 'utf8');
  
  const includesTemp = content.includes('supabase/.temp/');
  
  if (!includesTemp) {
    console.log('❌ FAIL: .gitignore does not exclude supabase/.temp/ directory');
    return false;
  }
  
  console.log('✅ PASS: .gitignore properly excludes .temp files');
  return true;
}

// Test 4: Verify SQL files use configurable URLs
function testSQLFiles() {
  console.log('📋 Test 4: SQL files use configurable URLs');
  
  const sqlFiles = [
    'profile-pictures-storage-fixed.sql',
    'profile-pictures-storage.sql', 
    'profile-pictures-clean.sql',
    'docs/database-schema.sql'
  ];
  
  let allPassed = true;
  
  for (const file of sqlFiles) {
    const filePath = path.join(process.cwd(), file);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  SKIP: ${file} does not exist`);
      continue;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check for hardcoded URLs (should be minimal or none)
    const hasHardcodedUrl = content.includes("'https://xsiipracopbtzslfkpkz.supabase.co'");
    
    // Should use current_setting for URL configuration
    const usesCurrentSetting = content.includes("current_setting('app.supabase_url'");
    
    if (hasHardcodedUrl && !content.includes('RAISE EXCEPTION')) {
      console.log(`❌ FAIL: ${file} contains hardcoded URL without proper error handling`);
      allPassed = false;
    } else if (!usesCurrentSetting) {
      console.log(`❌ FAIL: ${file} does not use current_setting for URL configuration`);
      allPassed = false;
    } else {
      console.log(`✅ PASS: ${file} properly uses configurable URL approach`);
    }
  }
  
  return allPassed;
}

// Test 5: Functional test of image loader
function testImageLoaderFunctionality() {
  console.log('📋 Test 5: Image loader functionality with environment variables');
  
  // Set environment variable for test
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://xsiipracopbtzslfkpkz.supabase.co';
  
  try {
    // Clear require cache to ensure fresh load
    const loaderPath = path.resolve(process.cwd(), 'lib/supabase-image-loader.js');
    delete require.cache[loaderPath];
    
    const loader = require(loaderPath).default;
    
    const result = loader({ 
      src: 'prompt-images/test.jpg', 
      width: 500, 
      quality: 80 
    });
    
    const expectedUrl = 'https://xsiipracopbtzslfkpkz.supabase.co/storage/v1/render/image/public/prompt-images/test.jpg?width=500&quality=80&resize=cover';
    
    if (result !== expectedUrl) {
      console.log('❌ FAIL: Image loader does not generate expected URL');
      console.log('Expected:', expectedUrl);
      console.log('Actual:', result);
      return false;
    }
    
    console.log('✅ PASS: Image loader generates correct URLs from environment variables');
    return true;
    
  } catch (error) {
    console.log('❌ FAIL: Image loader threw an error:', error.message);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  const tests = [
    testNextConfig,
    testImageLoader,
    testGitignore,
    testSQLFiles,
    testImageLoaderFunctionality
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    if (test()) {
      passed++;
    }
    console.log(''); // Add spacing between tests
  }
  
  console.log('🎯 Test Results:');
  console.log(`✅ Passed: ${passed}/${total}`);
  console.log(`❌ Failed: ${total - passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 All secrets management security tests passed!');
    console.log('✅ Critical security issue resolved: Hardcoded secrets removed');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please review and fix the issues.');
    process.exit(1);
  }
}

// Run the tests
runAllTests().catch(console.error);
