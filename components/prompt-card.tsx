"use client"

import React, { useState } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Heart, Bookmark as BookmarkIcon, Eye, MessageSquare, Edit, GitBranch, Loader2 } from "lucide-react"
import { getCategoryTheme } from "./category-icon-mapper"
import PromptThumbnail from "./prompt-thumbnail"
import AddToCollectionDialog from "./add-to-collection-dialog"
import type { PromptCard as PromptCardType } from "@/lib/types"
import { useUser } from "@/lib/hooks/use-user"
import { generatePromptUrl } from "@/lib/utils/url-helpers"
import slugify from 'slugify'

interface PromptCardProps {
  prompt: PromptCardType
  placeholderType?: "icon" | "pattern" | "letter"
  maxTags?: number
  isOwner?: boolean
  isSaved?: boolean
  onToggleSave?: (promptId: string, currentSaveStatus: boolean) => Promise<void>
  onUnsave?: (promptId: string) => Promise<void>
}

export default function PromptCard({ prompt, placeholderType = "icon", maxTags = 2, isOwner, isSaved: propIsSaved, onToggleSave, onUnsave }: PromptCardProps) {
  const [isAddToCollectionDialogOpen, setIsAddToCollectionDialogOpen] = useState(false);
  const [localIsSaved, setLocalIsSaved] = useState<boolean | undefined>(undefined);
  const [isBookmarkAnimating, setIsBookmarkAnimating] = useState(false);
  const [isSaveOperationInProgress, setIsSaveOperationInProgress] = useState(false);
  const { user, isLoading: isUserLoading } = useUser()

  // Use the actual prompt data now that remix_count is properly included from the database
  const enhancedPrompt = prompt

  // Get the theme for this category
  const categoryName = typeof enhancedPrompt.category === "string" ? enhancedPrompt.category : enhancedPrompt.category?.name || "Other"
  const theme = getCategoryTheme(categoryName)
  const { icon: Icon } = theme
  const categoryColor = theme.colors

  // Check if the current user is the prompt author
  const isPromptOwner = isOwner !== undefined ? isOwner : (user?.id === enhancedPrompt.user?.id)

  // Use local state override if set, then prop override if provided, otherwise use prompt's saved status
  // For logged-in users, prioritize the local state, then prop value, for non-logged-in users, always false
  const isSaved = user ? (localIsSaved !== undefined ? localIsSaved : (propIsSaved !== undefined ? propIsSaved : prompt.isSaved)) : false;





  // Improved bookmark visibility logic with caching consideration
  // If user is loading but we have cached prompt data with isSaved status, we can infer user was logged in
  const hasIsSavedData = propIsSaved !== undefined || prompt.isSaved !== undefined;
  const shouldShowBookmark = isUserLoading ? hasIsSavedData : (user && !isPromptOwner);

  // Only show loading during actual save/unsave operations, not for user authentication loading
  // We should NOT show loading just because user is loading - that creates unnecessary delay perception
  const showBookmarkLoading = user && !isPromptOwner && isSaveOperationInProgress;

  // Format large numbers with k/M suffix
  const formatNumber = (num: number | undefined | null) => {
    if (!num) return "0"
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "k"
    }
    return num.toString()
  }

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(date)
  }

  const username = enhancedPrompt.user?.username || "anonymous"
  const imageUrl = enhancedPrompt.imageUrl

  // Generate prompt URL using the standard format
  const promptUrl = generatePromptUrl(enhancedPrompt)

  const handleSaveToggle = async () => {
    if (onToggleSave) {
      setIsSaveOperationInProgress(true);
      setIsBookmarkAnimating(true);
      try {
        await onToggleSave(prompt.id, isSaved || false)
      } finally {
        setIsSaveOperationInProgress(false);
        setTimeout(() => setIsBookmarkAnimating(false), 300);
      }
    }
  }

  const handleUnsave = async () => {
    if (onUnsave) {
      setIsSaveOperationInProgress(true);
      setIsBookmarkAnimating(true);
      try {
        await onUnsave(prompt.id)
      } finally {
        setIsSaveOperationInProgress(false);
        setTimeout(() => setIsBookmarkAnimating(false), 300);
      }
    }
  }

  const handleBookmarkClick = () => {
    setIsBookmarkAnimating(true);
    setIsAddToCollectionDialogOpen(true);
    setTimeout(() => setIsBookmarkAnimating(false), 300);
  }

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:border-accent-green/40 hover:shadow-lg hover:shadow-accent-green/15 hover:scale-[1.02] group relative">
      <div className="relative">
        <Link href={promptUrl} className="block">
          <div className="aspect-video overflow-hidden">
            <PromptThumbnail
              title={enhancedPrompt.title}
              category={categoryName}
              imageUrl={imageUrl}
              placeholderType={placeholderType}
            />
            {/* Only show category badge on prompts with images (not on placeholders) as per design spec */}
            {imageUrl && categoryName && (
              <Badge
                className="absolute top-2 left-2 border-none px-2 py-1 font-medium text-xs text-white rounded-md"
                style={{
                  backgroundColor: categoryColor.secondary,
                  textShadow: "0px 1px 2px rgba(0,0,0,0.3)",
                }}
              >
                {categoryName}
              </Badge>
            )}
          </div>
        </Link>
        
        {/* Bookmark icon positioned at top right of thumbnail */}
        {shouldShowBookmark && (
          <Button
            variant="ghost"
            size="sm"
            className={`absolute top-2 right-2 h-8 w-8 p-0 rounded-full transition-all duration-300 transform ${
              showBookmarkLoading
                ? "bg-gray-500/50 text-white"
                : isSaved
                ? "bg-accent-green text-white hover:bg-accent-green/90 shadow-lg scale-110"
                : "bg-black/50 text-white hover:bg-black/70 hover:scale-110"
            } ${isBookmarkAnimating ? "animate-pulse scale-125" : ""}`}
            onClick={handleBookmarkClick}
            title={showBookmarkLoading ? "Loading..." : isSaved ? "Manage collections" : "Save to collection"}
            disabled={!!showBookmarkLoading}
          >
            {showBookmarkLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <BookmarkIcon className={`h-4 w-4 transition-all duration-200 ${isSaved ? "fill-current" : ""} ${isBookmarkAnimating ? "scale-125" : ""}`} />
            )}
          </Button>
        )}

        {/* Owner actions for owned prompts */}
        {isPromptOwner && (
          <div className="absolute top-2 right-2 flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full bg-black/50 text-white hover:bg-black/70 hover:scale-110 transition-all duration-200"
              asChild
            >
              <Link href={`/prompt/edit/${enhancedPrompt.shortId}`}>
                <Edit className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        )}
      </div>

      <CardHeader className="p-4 pb-0">
        <div className="flex items-center justify-between">
          <Link href={`/user/${username}`} className="flex items-center gap-2 group/author">
            <Avatar className="h-6 w-6 transition-all duration-200 group-hover/author:scale-110">
              <AvatarImage src={enhancedPrompt.user?.avatarUrl || "/placeholder-user.jpg"} alt={username} />
              <AvatarFallback>{username.charAt(0).toUpperCase()}</AvatarFallback>
            </Avatar>
            <span className="text-sm font-medium transition-colors duration-200 group-hover/author:text-accent-green group-hover/author:underline">
              {username}
            </span>
          </Link>

          {enhancedPrompt.tool && (
            <Link href={`/tool/${enhancedPrompt.tool.slug}`}>
              <Badge
                variant="outline"
                className="px-2 py-1 text-xs border-gray-300 hover:bg-gray-100 hover:text-gray-900 hover:scale-105 transition-all duration-200 font-medium"
              >
                {enhancedPrompt.tool.name}
              </Badge>
            </Link>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <Link href={promptUrl} className="block">
          <h3 className="mb-2 line-clamp-1 font-semibold transition-all duration-200 group-hover:text-accent-green">
            {enhancedPrompt.title}
          </h3>
          <p className="line-clamp-2 text-sm text-muted-foreground">{enhancedPrompt.description}</p>
        </Link>

        {/* Tags */}
        {enhancedPrompt.tags && enhancedPrompt.tags.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-1">
            {enhancedPrompt.tags.slice(0, maxTags).map((tag) => {
              // Use tag.slug if it exists, otherwise create a slug from tag.name
              const tagSlug = tag.slug || slugify(tag.name, { lower: true, strict: true })
              return (
                <Link
                  key={tag.slug || `tag-${tag.name}`}
                  href={`/tag/${tagSlug}`}
                >
                  <Badge className="text-xs bg-gray-800 hover:bg-gray-700 hover:scale-105 text-white rounded-full px-2 py-0.5 transition-all duration-200">
                    #{tag.name}
                  </Badge>
                </Link>
              )
            })}
          </div>
        )}

        {/* Enhanced Stats with better visual hierarchy and hover effects */}
        <div className="mt-4 flex items-center justify-between text-xs">
          <div className="flex items-center gap-4">
            <div
              className="flex items-center gap-1 text-muted-foreground hover:text-red-500 transition-colors duration-200 cursor-pointer group/stat"
              title="Number of likes this prompt has received"
            >
              <Heart className="h-3.5 w-3.5 group-hover/stat:scale-110 transition-transform duration-200" />
              <span className="font-medium">{formatNumber(enhancedPrompt.likeCount)}</span>
            </div>
            <div
              className="flex items-center gap-1 text-muted-foreground hover:text-blue-500 transition-colors duration-200 cursor-pointer group/stat"
              title="Number of times this prompt has been viewed"
            >
              <Eye className="h-3.5 w-3.5 group-hover/stat:scale-110 transition-transform duration-200" />
              <span className="font-medium">{formatNumber(enhancedPrompt.viewCount)}</span>
            </div>
            <div
              className="flex items-center gap-1 text-muted-foreground hover:text-green-500 transition-colors duration-200 cursor-pointer group/stat"
              title="Number of comments on this prompt"
            >
              <MessageSquare className="h-3.5 w-3.5 group-hover/stat:scale-110 transition-transform duration-200" />
              <span className="font-medium">{formatNumber(enhancedPrompt.commentCount)}</span>
            </div>
            {/* Add remix count if available and greater than 0 */}
            {enhancedPrompt.remixCount !== undefined && enhancedPrompt.remixCount !== null && enhancedPrompt.remixCount > 0 && (
              <div
                className="flex items-center gap-1 text-muted-foreground hover:text-purple-500 transition-colors duration-200 cursor-pointer group/stat"
                title="Number of times this prompt has been remixed by other users"
              >
                <GitBranch className="h-3.5 w-3.5 group-hover/stat:scale-110 transition-transform duration-200" />
                <span className="font-medium">{formatNumber(enhancedPrompt.remixCount)}</span>
              </div>
            )}
          </div>
          <span className="text-muted-foreground font-medium">{formatDate(enhancedPrompt.createdAt)}</span>
        </div>
      </CardContent>

      {/* Add to Collection Dialog */}
      <AddToCollectionDialog
        isOpen={isAddToCollectionDialogOpen}
        onClose={() => setIsAddToCollectionDialogOpen(false)}
        promptId={enhancedPrompt.id}
        promptTitle={enhancedPrompt.title}
        onSuccess={(isSaved: boolean) => {
          // Update the saved status based on the final state from the dialog
          setLocalIsSaved(isSaved);
        }}
      />
    </Card>
  )
} 