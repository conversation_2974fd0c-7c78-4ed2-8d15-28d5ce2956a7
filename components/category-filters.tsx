"use client"

import { useCategories } from "hooks/use-categories"
import { useTags } from "hooks/use-tags"
import { useTools } from "hooks/use-tools"
import { useAIModels } from "hooks/use-ai-models"
import { Search, X } from "lucide-react"
import { <PERSON><PERSON> } from "components/ui/button"
import { MultiSelect } from "components/ui/multi-select"
import { Input } from "components/ui/input"
import type { Category, Tag, Tool, AIModel } from "lib/types"
import { Skeleton } from "components/ui/skeleton" // Import Skeleton

interface CategoryFiltersProps {
  // Removed initial slugs, state is managed in parent now
  selectedCategories: string[]
  onSelectCategories: (categories: string[]) => void
  selectedTags: string[]
  onSelectTags: (tags: string[]) => void
  selectedTools: string[]
  onSelectTools: (tools: string[]) => void
  selectedModels?: string[]
  onSelectModels?: (models: string[]) => void
  searchTerm: string
  onSearchChange: (term: string) => void
  onClearAll: () => void
  showClearButton: boolean
}

export default function CategoryFilters({
  selectedCategories,
  onSelectCategories,
  selectedTags,
  onSelectTags,
  selectedTools,
  onSelectTools,
  selectedModels = [],
  onSelectModels = () => {},
  searchTerm,
  onSearchChange,
  onClearAll,
  showClearButton,
}: CategoryFiltersProps) {
  const { categories, isLoading: isLoadingCategories } = useCategories()
  const { tags, isLoading: isLoadingTags } = useTags()
  const { tools, isLoading: isLoadingTools } = useTools()
  const { models, isLoading: isLoadingModels } = useAIModels(false) // Don't include deprecated models by default

  const categoryOptions = categories.map((cat: Category) => ({
    value: cat.slug,
    label: cat.name,
  }))

  const tagOptions = tags.map((tag: Tag) => ({
    // Ensure slug exists, fallback to empty string or name if needed
    value: tag.slug || tag.name.toLowerCase().replace(/\s+/g, '-') || '',
    label: tag.name,
  }));

  const toolOptions = tools.map((tool: Tool) => ({
    value: tool.slug,
    label: tool.name,
  }))
  
  const modelOptions = models.map((model: AIModel) => ({
    value: model.slug,
    label: `${model.provider} - ${model.name}`,
  }))

  return (
    <div className="flex flex-col space-y-3 w-full">

      {/* Category Filter */}
      <div className="w-full">
        <h3 className="mb-1.5 text-sm font-medium text-muted-foreground">Categories</h3>
        {isLoadingCategories ? (
          <Skeleton className="h-9 w-full rounded-lg" />
        ) : (
          <MultiSelect
            options={categoryOptions}
            selected={selectedCategories}
            onSelect={onSelectCategories}
            placeholder="Select categories"
            className="rounded-lg min-h-9 w-full"
          />
        )}
      </div>

      {/* Tag Filter */}
      <div className="w-full">
        <h3 className="mb-1.5 text-sm font-medium text-muted-foreground">Tags</h3>
        {isLoadingTags ? (
          <Skeleton className="h-9 w-full rounded-lg" />
        ) : (
          <MultiSelect
            options={tagOptions}
            selected={selectedTags}
            onSelect={onSelectTags}
            placeholder="Select tags"
            className="rounded-lg min-h-9 w-full"
          />
        )}
      </div>

      {/* Tool Filter */}
      <div className="w-full">
        <h3 className="mb-1.5 text-sm font-medium text-muted-foreground">Tools</h3>
        {isLoadingTools ? (
          <Skeleton className="h-9 w-full rounded-lg" />
        ) : (
          <MultiSelect
            options={toolOptions}
            selected={selectedTools}
            onSelect={onSelectTools}
            placeholder="Select tools"
            className="rounded-lg min-h-9 w-full"
          />
        )}
      </div>

      {/* AI Model Filter */}
      <div className="w-full">
        <h3 className="mb-1.5 text-sm font-medium text-muted-foreground">AI Models</h3>
        {isLoadingModels ? (
          <Skeleton className="h-9 w-full rounded-lg" />
        ) : (
          <MultiSelect
            options={modelOptions}
            selected={selectedModels}
            onSelect={onSelectModels}
            placeholder="Select AI models"
            className="rounded-lg min-h-9 w-full"
          />
        )}
      </div>

      {/* Removed additional accordions for Difficulty, Word Count, Premium */}
      {/* Clear All Button */}
      {showClearButton && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearAll}
          className="w-full justify-center text-sm text-muted-foreground hover:text-foreground mt-2 rounded-md" // Added rounded-md
          style={{ borderRadius: "6px" }} // Explicit radius
        >
          Clear All Filters
        </Button>
      )}
    </div>
  )
}
