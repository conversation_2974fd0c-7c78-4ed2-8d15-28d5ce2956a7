"use client";

import { useState, useMemo } from "react";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import type { Tag } from "@/lib/types";

interface TagListProps {
  tags: Tag[];
}

export default function TagList({ tags }: TagListProps) {
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  
  // Sort tags alphabetically
  const sortedTags = useMemo(() => {
    return [...tags].sort((a, b) => a.name.localeCompare(b.name));
  }, [tags]);
  
  // Get unique first letters for the filter buttons
  const uniqueFirstLetters = useMemo(() => {
    const letters = sortedTags.map(tag => tag.name.charAt(0).toUpperCase());
    return Array.from(new Set(letters)).sort();
  }, [sortedTags]);
  
  // Filter tags by selected letter
  const filteredTags = useMemo(() => {
    if (!selectedLetter) return sortedTags;
    return sortedTags.filter(tag => 
      tag.name.charAt(0).toUpperCase() === selectedLetter
    );
  }, [sortedTags, selectedLetter]);

  // Format number with K/M suffix
  const formatNumber = (num: number | undefined): string => {
    if (num === undefined) return "0";
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1).replace(/\.0$/, "")}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1).replace(/\.0$/, "")}K`;
    } else {
      return num.toString();
    }
  };
  
  return (
    <div className="space-y-6">
      {/* Letter filter buttons */}
      <div className="flex flex-wrap gap-2 sticky top-0 bg-background py-2 z-10">
        <Button
          variant={selectedLetter === null ? "default" : "outline"}
          className="min-w-10 h-10"
          onClick={() => setSelectedLetter(null)}
        >
          All
        </Button>
        {uniqueFirstLetters.map(letter => (
          <Button
            key={letter}
            variant={selectedLetter === letter ? "default" : "outline"}
            className="min-w-10 h-10"
            onClick={() => setSelectedLetter(letter)}
          >
            {letter}
          </Button>
        ))}
      </div>
      
      {/* Tag grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {filteredTags.map((tag) => (
          <Link key={tag.id} href={`/search?tags=${tag.slug}`}>
            <Card className="overflow-hidden h-full transition-all duration-300 hover:border-accent-green/40 hover:shadow-lg hover:shadow-accent-green/20 hover:-translate-y-1 group relative bg-gradient-to-br from-background to-background/95">
              <CardContent className="p-4 flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full mb-3 flex items-center justify-center text-primary text-xl font-semibold transition-all duration-300 group-hover:bg-accent-green/20 group-hover:scale-110 group-hover:text-accent-green">
                  <span className="transition-transform duration-300 group-hover:scale-110">#</span>
                </div>
                <h3 className="font-medium group-hover:text-accent-green transition-colors duration-300">{tag.name}</h3>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
      
      {/* Show message if no tags match the selected letter */}
      {filteredTags.length === 0 && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No tags found for letter '{selectedLetter}'</p>
          <Button 
            variant="link" 
            onClick={() => setSelectedLetter(null)}
            className="mt-2"
          >
            Show all tags
          </Button>
        </div>
      )}
    </div>
  );
}
