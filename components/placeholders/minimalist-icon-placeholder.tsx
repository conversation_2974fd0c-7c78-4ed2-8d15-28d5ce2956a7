import { getCategoryTheme } from "../category-icon-mapper"
import { getCategoryColorByName } from "@/lib/data/category-colors"

interface MinimalistIconPlaceholderProps {
  category: string
  className?: string
}

/**
 * MinimalistIconPlaceholder component
 *
 * Creates a clean, icon-centered design for prompt thumbnails:
 * - Uses a gradient background based on category
 * - Displays a large, centered icon representing the category
 * - Shows the category name below the icon
 */
export default function MinimalistIconPlaceholder({ category, className = "" }: MinimalistIconPlaceholderProps) {
  // Get the theme for this category using our centralized system
  const theme = getCategoryTheme(category)
  const { icon: Icon } = theme
  const categoryColor = theme.colors

  return (
    <div className={`aspect-video overflow-hidden rounded-lg ${className}`}>
      <div
        className={`h-full w-full bg-gradient-to-br ${categoryColor.darkGradient} flex items-center justify-center`}
        aria-label={`${category} category thumbnail`}
      >
        <div className="flex flex-col items-center justify-center text-center">
          <Icon className={`h-12 w-12 ${categoryColor.iconColor}`} />
          <span className={`mt-2 font-medium ${categoryColor.darkTextColor} px-2`}>{category}</span>
        </div>
      </div>
    </div>
  )
}
