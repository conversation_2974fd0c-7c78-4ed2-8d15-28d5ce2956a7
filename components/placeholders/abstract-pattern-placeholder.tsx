import type React from "react"
import { getCategoryTheme } from "../category-icon-mapper"

interface AbstractPatternPlaceholderProps {
  seed: string
  className?: string
  category?: string
}

export default function AbstractPatternPlaceholder({ seed, className = "", category }: AbstractPatternPlaceholderProps) {
  // Get category theme if category is provided
  const theme = category ? getCategoryTheme(category) : null
  const categoryColor = theme?.colors
  // Generate a pattern based on the seed
  const getPatternDetails = (seed: string) => {
    // Use the seed to generate a consistent pattern
    const seedSum = seed.split("").reduce((sum, char) => sum + char.charCodeAt(0), 0)

    // Background colors - emphasize vibrant green colors as per user preference
    const bgColors = [
      "bg-green-500/20",
      "bg-emerald-500/20",
      "bg-lime-500/20",
      "bg-teal-500/20",
      "bg-blue-500/20",
    ]

    // Shape colors - emphasize vibrant green colors as per user preference
    const shapeColors = ["bg-green-500", "bg-emerald-500", "bg-lime-500", "bg-teal-500", "bg-blue-500"]

    // Pattern types
    const patternTypes = ["circles", "triangles", "squares", "mixed"]

    return {
      bgColor: bgColors[seedSum % bgColors.length],
      shapeColor: shapeColors[(seedSum + 2) % shapeColors.length],
      patternType: patternTypes[seedSum % patternTypes.length],
      density: (seedSum % 3) + 1, // 1, 2, or 3 (low, medium, high)
    }
  }

  const { bgColor, shapeColor, patternType, density } = getPatternDetails(seed)

  // Generate shapes based on pattern type and density
  const generateShapes = () => {
    const shapes = []
    const shapeCount = density * 5 // 5, 10, or 15 shapes

    for (let i = 0; i < shapeCount; i++) {
      // Use the seed and index to generate consistent positions
      const seedIndex = (seed.charCodeAt(i % seed.length) + i) % 100

      // Calculate position (0-100%)
      const left = `${seedIndex}%`
      const top = `${(seedIndex * 1.7) % 100}%`

      // Calculate size (0.5-2rem)
      const size = `${0.5 + (seedIndex % 3) * 0.5}rem`

      // Calculate opacity (0.3-0.9)
      const opacity = 0.3 + (seedIndex % 7) * 0.1

      // Generate shape based on pattern type
      let shape
      if (patternType === "circles" || (patternType === "mixed" && i % 3 === 0)) {
        shape = (
          <div
            key={i}
            className={`${shapeColor} absolute rounded-full opacity-${Math.floor(opacity * 10)}`}
            style={{
              left,
              top,
              width: size,
              height: size,
              opacity,
            }}
          />
        )
      } else if (patternType === "triangles" || (patternType === "mixed" && i % 3 === 1)) {
        // Create a triangle using a div with border tricks
        shape = (
          <div
            key={i}
            className="absolute opacity-80"
            style={
              {
                left,
                top,
                width: 0,
                height: 0,
                borderLeft: `${size} solid transparent`,
                borderRight: `${size} solid transparent`,
                borderBottom: `${size} solid var(--shape-color)`,
                opacity,
                // Use CSS variable to apply the shape color
                "--shape-color": `hsl(var(--${shapeColor.replace("bg-", "")}))`,
              } as React.CSSProperties
            }
          />
        )
      } else {
        // Squares or remaining mixed shapes
        shape = (
          <div
            key={i}
            className={`${shapeColor} absolute opacity-${Math.floor(opacity * 10)}`}
            style={{
              left,
              top,
              width: size,
              height: size,
              opacity,
              transform: `rotate(${seedIndex * 3.6}deg)`,
            }}
          />
        )
      }

      shapes.push(shape)
    }

    return shapes
  }

  return <div className={`aspect-video relative overflow-hidden ${bgColor} rounded-lg ${className}`}>{generateShapes()}</div>
}
