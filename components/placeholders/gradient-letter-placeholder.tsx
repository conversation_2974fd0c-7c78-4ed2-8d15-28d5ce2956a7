import { getCategoryTheme } from "../category-icon-mapper"

interface GradientLetterPlaceholderProps {
  title: string
  className?: string
  category?: string
}

export default function GradientLetterPlaceholder({ title, category, className = "" }: GradientLetterPlaceholderProps) {
  // Get category theme if category is provided
  const theme = category ? getCategoryTheme(category) : null
  const categoryColor = theme?.colors
  // Generate a gradient based on the title
  const getGradient = (title: string) => {
    // Emphasize vibrant green colors as per user preference
    const gradients = [
      "bg-gradient-to-br from-green-400 to-green-600", // Vibrant green
      "bg-gradient-to-tr from-green-400 to-blue-500",
      "bg-gradient-to-br from-emerald-400 to-teal-600",
      "bg-gradient-to-tr from-lime-400 to-green-600",
      "bg-gradient-to-r from-teal-400 to-green-500",
      "bg-gradient-to-bl from-blue-400 to-green-500",
    ]

    // Use the sum of character codes to determine the gradient
    const charSum = title.split("").reduce((sum, char) => sum + char.charCodeAt(0), 0)
    return gradients[charSum % gradients.length]
  }

  // Get the first letter of the title
  const firstLetter = title.charAt(0).toUpperCase()

  // Use category gradient if available, otherwise generate one based on title
  const gradient = categoryColor ? categoryColor.darkGradient : getGradient(title)

  return (
    <div className={`aspect-video flex items-center justify-center bg-gradient-to-br ${gradient} rounded-lg ${className}`}>
      <span className="text-6xl font-bold text-white/90 drop-shadow-md">{firstLetter}</span>
    </div>
  )
}
