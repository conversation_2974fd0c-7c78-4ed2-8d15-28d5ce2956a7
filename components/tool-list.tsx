import { useMemo, useEffect, useState } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import type { Tool } from "@/lib/types"
import { allTools } from "@/lib/data/tools"

interface ToolListProps {
  tools: Tool[]
}

export default function ToolList({ tools }: ToolListProps) {
  // State to store tools with proper icons
  const [toolsWithIcons, setToolsWithIcons] = useState<Tool[]>([]);
  
  // Process tools to ensure they have the correct icons
  useEffect(() => {
    const processedTools = tools.map(tool => {
      // Try to find the matching tool in allTools to get the icon
      const matchingTool = allTools.find(t => 
        t.slug === tool.slug || t.name.toLowerCase() === tool.name.toLowerCase()
      );
      
      return {
        ...tool,
        // Use the icon from allTools if available, otherwise use the tool's icon or a default
        icon: matchingTool?.icon || tool.icon || getDefaultEmoji(tool.name)
      };
    });
    
    setToolsWithIcons(processedTools);
  }, [tools]);
  
  // Sort tools alphabetically
  const sortedTools = useMemo(() => {
    return [...toolsWithIcons].sort((a, b) => a.name.localeCompare(b.name));
  }, [toolsWithIcons]);

  // Format number with K/M suffix
  const formatNumber = (num: number | undefined): string => {
    if (num === undefined) return "0";
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1).replace(/\.0$/, "")}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1).replace(/\.0$/, "")}K`;
    } else {
      return num.toString();
    }
  };

  // Default emoji if none is provided
  const getDefaultEmoji = (toolName: string): string => {
    const defaultEmojis: {[key: string]: string} = {
      "chatgpt": "🤖",
      "gpt": "🤖",
      "dalle": "🎨",
      "midjourney": "🖼️",
      "stable diffusion": "🖌️",
      "claude": "🧠",
      "bard": "💬",
      "copilot": "👨‍💻",
      "gemini": "💎",
      "llama": "🦙",
    };
    
    // Try to find a matching default emoji
    const lowerName = toolName.toLowerCase();
    for (const [key, emoji] of Object.entries(defaultEmojis)) {
      if (lowerName.includes(key)) {
        return emoji;
      }
    }
    
    // Return a generic AI emoji if no match
    return "🔧";
  };
  
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
      {sortedTools.map((tool) => (
        <Link key={tool.id} href={`/search?tools=${tool.slug}`}>
          <Card className="overflow-hidden h-full transition-all duration-300 hover:border-accent-green/40 hover:shadow-lg hover:shadow-accent-green/20 hover:-translate-y-1 group relative bg-gradient-to-br from-background to-background/95">
            <CardContent className="p-4 flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full mb-3 flex items-center justify-center transition-all duration-300 group-hover:bg-accent-green/20 group-hover:scale-110">
                <span className="text-4xl transition-transform duration-300 group-hover:scale-110" role="img" aria-label={tool.name}>
                  {tool.icon || getDefaultEmoji(tool.name)}
                </span>
              </div>
              <h3 className="font-medium group-hover:text-accent-green transition-colors duration-300">{tool.name}</h3>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  )
}
