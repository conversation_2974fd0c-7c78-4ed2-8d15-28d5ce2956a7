import type React from "react"
import {
  Code,
  BookOpen,
  BarChart2,
  ImageIcon,
  Briefcase,
  GraduationCap,
  User,
  Search,
  Mail,
  Globe,
  PenTool,
  BookMarked,
  FileText,
  Lightbulb,
  Headphones,
  Grid,
  Video,
  Music,
} from "lucide-react"
import { getCategoryColorByName } from "@/lib/data/category-colors"

// Define the category icon and color mapping
interface CategoryTheme {
  icon: React.ElementType
  gradient: string
  textColor: string
  darkGradient: string
  darkTextColor: string
  colors: any // Full category colors object
}

// Map category names to their respective icons and color themes
export const getCategoryTheme = (categoryName: string): CategoryTheme => {
  const normalizedCategory = typeof categoryName === "string" ? categoryName.toLowerCase() : "other"
  const colors = getCategoryColorByName(normalizedCategory)

  // Mapping of categories to icons
  const categoryIcons: Record<string, React.ElementType> = {
    "code generation": Code,
    "creative writing": BookOpen,
    marketing: PenTool,
    "image generation": ImageIcon,
    "data analysis": BarChart2,
    business: Briefcase,
    education: GraduationCap,
    personal: User,
    research: Search,
    "social media": Globe,
    email: Mail,
    seo: Search,
    copywriting: PenTool,
    fiction: BookMarked,
    "non-fiction": FileText,
    audio: Headphones,
    music: Music,
    video: Video,
    other: Grid,
  }

  // Find the icon for the category - first try exact match
  let icon: React.ElementType = Lightbulb

  // First try direct match
  if (categoryIcons[normalizedCategory]) {
    icon = categoryIcons[normalizedCategory]
  } else {
    // Then try partial match
    for (const [key, value] of Object.entries(categoryIcons)) {
      if (normalizedCategory.includes(key) || key.includes(normalizedCategory)) {
        icon = value
        break
      }
    }
  }

  return {
    icon,
    gradient: colors.gradient,
    textColor: colors.textColor,
    darkGradient: colors.darkGradient,
    darkTextColor: colors.darkTextColor,
    colors, // Return the full colors object for direct access
  }
}

// Add the CategoryIconMapper component
interface CategoryIconMapperProps {
  category: string
  className?: string
}

export const CategoryIconMapper: React.FC<CategoryIconMapperProps> = ({ category, className }) => {
  const theme = getCategoryTheme(category)
  const IconComponent = theme.icon
  return <IconComponent className={className} />
}
