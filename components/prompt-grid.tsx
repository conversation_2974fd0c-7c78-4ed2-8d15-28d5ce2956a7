import PromptCard from "@/components/prompt-card" // Corrected path
import PromptListItemComponent from "@/components/prompt-list-item" // Import list item component
import type { PromptCard as PromptCardType } from "@/lib/types" // Corrected path
 
 interface PromptGridProps {
  prompts: PromptCardType[]
  emptyMessage?: string
  maxTags?: number // Added maxTags prop
  viewMode: "grid" | "list" // Added viewMode prop
  onToggleSave?: (promptId: string, isSaved: boolean) => Promise<void>
}
 
 export default function PromptGrid({ prompts, emptyMessage = "No prompts found", maxTags, viewMode, onToggleSave }: PromptGridProps) {
  if (!prompts || prompts.length === 0) { // Added check for undefined prompts array
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-muted-foreground text-center">{emptyMessage}</p>
      </div>
    )
  }

  return (
    <>
      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6"> {/* Removed lg:grid-cols-4 */}
          {prompts.map((prompt) => (
            <PromptCard key={prompt.id} prompt={prompt} placeholderType="icon" maxTags={maxTags} onToggleSave={onToggleSave} />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {prompts.map((prompt) => (
            <PromptListItemComponent 
              key={prompt.id} 
              prompt={{ ...prompt, imageUrl: prompt.imageUrl || '' }} 
              isSaved={prompt.isSaved}
              onToggleSave={onToggleSave} 
            />
          ))}
        </div>
      )}
    </>
  )
}
