"use client";

import { useState, useEffect } from "react";
import { useUser } from "@/lib/hooks/use-user"; 
import { getUserCollections } from "@/lib/api-services";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Bookmark, Loader2, PlusCircle, Lock, Info } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Command, CommandInput, CommandList, CommandItem, CommandEmpty, CommandGroup } from "@/components/ui/command";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import type { Collection, PromptCard as PromptCardType } from "@/lib/types";
import React from "react"; 

interface AddToCollectionPopoverProps {
  promptId: string; 
  isOwnPrompt: boolean; 
  isSavedInCollections?: string[]; 
  onAddToCollection: (promptId: string, collectionId: string) => Promise<void>;
  onCreateAndAddToCollection: (promptId: string, collectionName: string) => Promise<void>;
  trigger: React.ReactNode; 
  onDismiss?: () => void; 
}

export default function AddToCollectionPopover({
  promptId,
  isOwnPrompt,
  isSavedInCollections = [],
  onAddToCollection,
  onCreateAndAddToCollection,
  trigger, 
  onDismiss,
}: AddToCollectionPopoverProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingCollections, setIsFetchingCollections] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [userCollections, setUserCollections] = useState<Collection[]>([]);
  const [selectedCollectionId, setSelectedCollectionId] = useState<string | null>(null);
  const { user } = useUser();

  // Fetch user collections when the popover is opened and user is available
  useEffect(() => {
    const fetchUserCollections = async () => {
      console.log(`[add-to-collection-popover] isOpen: ${isOpen}, user: ${user ? JSON.stringify({id: user.id}) : 'null'}`);
      
      if (isOpen && user?.id) {
        console.log(`[add-to-collection-popover] Fetching collections for user ${user.id}`);
        setIsFetchingCollections(true);
        try {
          // Fetch collections with purpose='dialog' to get proper filtering and sorting
          // Use a higher limit to ensure we get all collections in one request
          const collections = await getUserCollections(user.id, {
            includePrivate: true,
            limit: 50, // Increased limit to avoid pagination
            purpose: 'dialog' // This will exclude "My Prompts" and prioritize "Saved Prompts"
          });
          console.log(`[add-to-collection-popover] Fetched ${collections.length} collections successfully`);
          setUserCollections(collections);
        } catch (error) {
          console.error("[add-to-collection-popover] Error fetching user collections:", error);
          // Set empty array in case of error
          setUserCollections([]);
        } finally {
          setIsFetchingCollections(false);
        }
      }
    };

    // Only fetch when the popover is opened
    if (isOpen) {
      fetchUserCollections();
    }
  }, [isOpen, user?.id]);
  
  // Debug log when component mounts with important information only
  useEffect(() => {
    console.log(`[add-to-collection-popover] Component mounted, promptId: ${promptId}, isOwnPrompt: ${isOwnPrompt}`);
    console.log(`[add-to-collection-popover] isSavedInCollections count: ${isSavedInCollections.length}`);
  }, []);
  
  // Log only when userCollections count changes to reduce noise
  useEffect(() => {
    console.log(`[add-to-collection-popover] userCollections updated, count: ${userCollections.length}`);
  }, [userCollections.length]);

  useEffect(() => {
    if (isOpen) {
      // Reset state when opening
      setSearchTerm(""); 
      setSelectedCollectionId(null); 
    } else {
      // If closed without making a selection, call onDismiss
      if (onDismiss && !selectedCollectionId) {
        onDismiss();
      }
    }
  }, [isOpen, onDismiss, selectedCollectionId]);

  const handleAddToSelectedCollection = async () => {
    if (!selectedCollectionId) return;

    setIsLoading(true);
    try {
      await onAddToCollection(promptId, selectedCollectionId);
      // Close on success
      setIsOpen(false);
    } catch (error) {
      console.error(`Failed to add prompt ${promptId} to collection ${selectedCollectionId}:`, error);
      // TODO: Show error message to user
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAndAddToNewCollection = async () => {
    if (!searchTerm.trim()) return;

    setIsLoading(true);
    try {
      await onCreateAndAddToCollection(promptId, searchTerm.trim());
      // Close on success
      setIsOpen(false);
    } catch (error) {
      console.error(`Failed to create collection "${searchTerm.trim()}" and add prompt ${promptId}:`, error);
      // TODO: Show error message to user
    } finally {
      setIsLoading(false);
    }
  }

  // Helper function to determine if a collection is valid for this prompt
  const isValidCollection = (collection: Collection): boolean => {
    // Content rule: My Prompts can only contain user's own prompts
    if (collection.defaultType === "my_prompts" && !isOwnPrompt) {
      return false;
    }
    
    // Content rule: Saved Prompts can only contain other users' prompts
    if (collection.defaultType === "saved_prompts" && isOwnPrompt) {
      return false;
    }
    
    // Custom collections can contain any prompts
    return true;
  };

  // Filter collections based on search term and content rules
  const filteredCollections = userCollections
    .filter(col => {
      // Log each collection being filtered
      console.log(`[add-to-collection-popover] Filtering collection: ${col.id} - ${col.name}`);
      
      // If search term is empty, show all collections
      if (!searchTerm.trim()) {
        console.log(`[add-to-collection-popover] Search term is empty, including collection: ${col.name}`);
        return true;
      }
      
      // Otherwise, filter by name
      const matches = col.name.toLowerCase().includes(searchTerm.toLowerCase());
      console.log(`[add-to-collection-popover] Collection ${col.name} ${matches ? 'matches' : 'does not match'} search term: ${searchTerm}`);
      return matches;
    });

  // Log the filtering results for debugging
  console.log(`[add-to-collection-popover] Total collections: ${userCollections.length}, Filtered collections: ${filteredCollections.length}`);
  console.log(`[add-to-collection-popover] Collections:`, userCollections);
  console.log(`[add-to-collection-popover] Filtered collections:`, filteredCollections);
  
  // Check if collections have the required properties
  if (userCollections.length > 0) {
    console.log(`[add-to-collection-popover] First collection properties:`, Object.keys(userCollections[0]));
    console.log(`[add-to-collection-popover] First collection name:`, userCollections[0].name);
  }

  // Check if search term matches an existing collection name exactly
  const searchTermMatchesExisting = userCollections.some(col => col.name.toLowerCase() === searchTerm.toLowerCase());


  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {/* Use the trigger prop here */}
        {trigger}
      </PopoverTrigger>
      <PopoverContent className="w-64 p-0">
        <Command>
            <CommandInput
                placeholder="Search or create new..."
                value={searchTerm}
                onValueChange={setSearchTerm}
            />
            <CommandList>
              {isFetchingCollections ? (
                <div className="p-4 text-sm text-center text-muted-foreground">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin inline-block" />
                  Loading collections...
                </div>
              ) : (
                <>
                  <CommandEmpty>
                    {/* Log when CommandEmpty is rendering */}
                    <>
                      {console.log(`[add-to-collection-popover] CommandEmpty rendering - userCollections: ${userCollections.length}, filteredCollections: ${filteredCollections.length}, searchTerm: ${searchTerm}`)}
                      {userCollections.length === 0 && !searchTerm.trim() 
                        ? "No collections found. Start typing to create one."
                        : userCollections.length > 0 && filteredCollections.length === 0 && searchTerm.trim()
                        ? `No collections match "${searchTerm}"`
                        : "No collections found."}
                    </>
                  </CommandEmpty>

                  {/* Debug log for collections */}
                  <div className="px-2 py-1 text-xs text-muted-foreground">
                    Collections found: {userCollections.length} | Filtered: {filteredCollections.length}
                  </div>
                  
                  {/* Force render each collection directly */}
                  {filteredCollections.length > 0 ? (
                    <CommandGroup heading="Your Collections">
                      {filteredCollections.map((collection, index) => {
                        // Log each collection being rendered with index
                        console.log(`[add-to-collection-popover] Rendering collection ${index}:`, collection);
                        
                        // Ensure collection has required properties
                        if (!collection || !collection.id || !collection.name) {
                          console.error(`[add-to-collection-popover] Invalid collection at index ${index}:`, collection);
                          return null;
                        }
                        
                        const isValid = isValidCollection(collection);
                        const isDisabled = !isValid || isLoading;
                        const tooltipText = !isValid
                          ? collection.defaultType === "my_prompts"
                            ? "Only your own prompts can be added to 'My Prompts'"
                            : "Only prompts from other users can be added to 'Saved Prompts'"
                          : "";

                        return (
                          <CommandItem
                            key={collection.id}
                            value={collection.name}
                            onSelect={() => !isDisabled && setSelectedCollectionId(collection.id)}
                            className={`flex items-center justify-between ${!isValid ? 'opacity-50' : ''}`}
                            disabled={isDisabled}
                          >
                            <div className="flex items-center gap-2 flex-1">
                              <Label htmlFor={`col-${collection.id}`} className="flex items-center gap-2 cursor-pointer">
                                {collection.isDefault && (
                                  <Bookmark className="h-3 w-3 text-accent-blue" />
                                )}
                                {collection.name}
                              </Label>
                              {!isValid && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Info className="h-3 w-3 text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>{tooltipText}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                            </div>
                            <Checkbox
                              id={`col-${collection.id}`}
                              checked={selectedCollectionId === collection.id}
                              onCheckedChange={() => !isDisabled && setSelectedCollectionId(collection.id)}
                              disabled={isDisabled}
                            />
                          </CommandItem>
                        );
                      })}
                    </CommandGroup>
                  ) : (
                    <div className="p-4 text-sm text-center text-muted-foreground">
                      No collections found. Create one below.
                    </div>
                  )}

                  {searchTerm.trim() && !searchTermMatchesExisting && (
                    <CommandGroup heading="Create New">
                      <CommandItem
                        value={searchTerm}
                        onSelect={handleCreateAndAddToNewCollection}
                        className="flex items-center gap-2"
                      >
                        <PlusCircle className="mr-2 h-4 w-4" /> Create and add to "{searchTerm.trim()}"
                      </CommandItem>
                    </CommandGroup>
                  )}
                </>
              )}
            </CommandList>
        </Command>
         <div className="p-2 border-t">
             <Button
                 onClick={handleAddToSelectedCollection}
                 disabled={isLoading || !selectedCollectionId}
                 className="w-full"
             >
                 {isLoading ? (
                     <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Adding...</>
                 ) : (
                     "Add to Selected Collection"
                 )}
             </Button>
         </div>
      </PopoverContent>
    </Popover>
  );
}