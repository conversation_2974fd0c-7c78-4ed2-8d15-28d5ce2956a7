import { useMemo } from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import type { Category } from "@/lib/types"

interface CategoryListProps {
  categories: Category[]
}

export default function CategoryList({ categories }: CategoryListProps) {
  // Sort categories alphabetically
  const sortedCategories = useMemo(() => {
    return [...categories].sort((a, b) => a.name.localeCompare(b.name));
  }, [categories]);

  // Format number with K/M suffix
  const formatNumber = (num: number | undefined): string => {
    if (num === undefined) return "0";
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1).replace(/\.0$/, "")}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1).replace(/\.0$/, "")}K`;
    } else {
      return num.toString();
    }
  };
  
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
      {sortedCategories.map((category) => (
        <Link key={category.id} href={`/search?categories=${category.slug}`}>
          <Card className="overflow-hidden h-full transition-all duration-300 hover:border-accent-green/40 hover:shadow-lg hover:shadow-accent-green/20 hover:-translate-y-1 group relative bg-gradient-to-br from-background to-background/95">
            {category.imagePath ? (
              <div className="relative w-full aspect-square overflow-hidden">
                <Image
                  src={category.imagePath || "/placeholder.svg"}
                  alt={category.name}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                  sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                  quality={85}
                />
                {/* Hover overlay for better visual feedback */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            ) : (
              <div className="w-full aspect-square bg-muted flex items-center justify-center transition-all duration-300 group-hover:brightness-110">
                <span className="text-2xl font-bold transition-transform duration-300 group-hover:scale-110">{category.name.charAt(0)}</span>
              </div>
            )}
            <CardContent className="p-4 flex flex-col items-center text-center">
              <h3 className="font-medium group-hover:text-accent-green transition-colors duration-300">{category.name}</h3>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  )
}
