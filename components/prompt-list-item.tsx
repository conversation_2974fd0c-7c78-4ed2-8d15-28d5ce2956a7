"use client"

import { <PERSON>, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Heart, Bookmark as BookmarkIcon, Eye, MessageSquare, GitBranch, Loader2 } from "lucide-react" // Renamed Bookmark to BookmarkIcon
import { getIconColorHex } from "@/lib/utils/category-styles"
import { useState } from "react"

import PromptThumbnail from "./prompt-thumbnail"
import { generatePromptUrl } from "@/lib/utils/url-helpers"
import AddToCollectionDialog from "./add-to-collection-dialog" // Import the dialog component
import type { PromptCard as PromptCardType } from "@/lib/types" // Import PromptCardType
import { useUser } from "@/lib/hooks/use-user"
import slugify from 'slugify'

interface PromptListItemProps {
  prompt: PromptCardType; // Use PromptCardType
  isSaved?: boolean; 
  onToggleSave?: (promptId: string, currentSaveStatus: boolean) => Promise<void>; // Added save toggle handler
}

export default function PromptListItem({ prompt, isSaved: propIsSaved }: PromptListItemProps) { // Removed unused onToggleSave prop
  const [isAddToCollectionDialogOpen, setIsAddToCollectionDialogOpen] = useState(false);
  const [localIsSaved, setLocalIsSaved] = useState<boolean | undefined>(undefined);
  const [isBookmarkAnimating, setIsBookmarkAnimating] = useState(false);
  const [isSaveOperationInProgress, setIsSaveOperationInProgress] = useState(false);
  const { user, isLoading: isUserLoading } = useUser()

  // Use local state override if set, then prop override if provided, otherwise use prompt's saved status
  // For logged-in users, prioritize the local state, then prop value, for non-logged-in users, always false
  const isSaved = user ? (localIsSaved !== undefined ? localIsSaved : (propIsSaved !== undefined ? propIsSaved : prompt.isSaved)) : false;

  // Check if the current user is the prompt author
  const isPromptOwner = user?.id === prompt.user?.id

  // Only show loading during actual save/unsave operations, not for user authentication loading
  // We should NOT show loading just because user is loading - that creates unnecessary delay perception
  const showBookmarkLoading = user && !isPromptOwner && isSaveOperationInProgress;

  // Improved bookmark visibility logic with caching consideration
  // If user is loading but we have cached prompt data with isSaved status, we can infer user was logged in
  const hasIsSavedData = propIsSaved !== undefined || prompt.isSaved !== undefined;
  const shouldShowBookmark = isUserLoading ? hasIsSavedData : (user && !isPromptOwner);

  // Debug logging for bookmark visibility
  if (process.env.NODE_ENV === 'development') {
    console.log(`[PromptListItem] Bookmark visibility for prompt ${prompt.id} (${prompt.title?.substring(0, 30)}...):`, {
      hasUser: !!user,
      isUserLoading,
      userId: user?.id,
      promptUserId: prompt.user?.id,
      promptUserUsername: prompt.user?.username,
      isPromptOwner,
      shouldShowBookmark,
      originalLogic: user && !isPromptOwner,
      isSaved,
      propIsSaved,
      promptIsSaved: prompt.isSaved,
      localIsSaved,
      showBookmarkLoading,
      isBookmarkAnimating
    });
  }

  const handleBookmarkClick = () => {
    setIsBookmarkAnimating(true);
    setIsAddToCollectionDialogOpen(true);
    setTimeout(() => setIsBookmarkAnimating(false), 300);
  }

  // Format large numbers with k/M suffix
  const formatNumber = (num: number | undefined | null) => {
    if (!num) return "0"
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "k"
    }
    return num.toString()
  }

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(date)
  }

  const promptUrl = prompt.shortId ? generatePromptUrl(prompt) : `/prompt/${prompt.id}`

  return (
    <Card className="w-full overflow-hidden transition-all duration-300 hover:border-accent-green/40 hover:shadow-lg hover:shadow-accent-green/15 hover:scale-[1.01] group relative">
      {/* Bookmark icon positioned at top right - matching front page cards */}
      {shouldShowBookmark && (
        <Button
          variant="ghost"
          size="sm"
          className={`absolute top-2 right-2 h-8 w-8 p-0 rounded-full transition-all duration-300 transform ${
            showBookmarkLoading
              ? "bg-gray-500/50 text-white"
              : isSaved
              ? "bg-accent-green text-white hover:bg-accent-green/90 shadow-lg scale-110"
              : "bg-black/50 text-white hover:bg-black/70 hover:scale-110"
          } ${isBookmarkAnimating ? "animate-pulse scale-125" : ""}`}
          onClick={handleBookmarkClick}
          title={showBookmarkLoading ? "Loading..." : isSaved ? "Manage collections" : "Save to collection"}
          disabled={!!showBookmarkLoading}
        >
          {showBookmarkLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <BookmarkIcon className={`h-4 w-4 transition-all duration-200 ${isSaved ? "fill-current" : ""} ${isBookmarkAnimating ? "scale-125" : ""}`} />
          )}
        </Button>
      )}
      


      <CardContent className="flex gap-4 p-4">
        {/* Thumbnail */}
        <div className="hidden sm:block relative">
          <Link href={promptUrl}>
            <PromptThumbnail
              title={prompt.title}
              category={prompt.category?.name || "Other"}
              imageUrl={prompt.imageUrl}
              className="h-24 w-24"
              placeholderType="icon"
              iconSizeClass="h-6 w-6"
              labelSizeClass="text-xs"
            />
          </Link>
        </div>

        {/* Content */}
        <div className="flex flex-1 flex-col">
          <div className="mb-1 flex items-center gap-2">
            <Link href={`/user/${prompt.user?.username}`} className="flex items-center gap-1 group/author"> {/* Added group/author */}
              <Avatar className="h-5 w-5 transition-all duration-200 group-hover/author:scale-110">
                <AvatarImage src={prompt.user?.avatarUrl || "/placeholder-user.jpg"} alt={prompt.user?.username || "User"} />
                <AvatarFallback>{prompt.user?.username?.charAt(0).toUpperCase() || "U"}</AvatarFallback>
              </Avatar>
              <span className="text-xs font-medium transition-colors duration-200 group-hover/author:text-accent-green group-hover/author:underline">{prompt.user?.username || "Anonymous"}</span>
            </Link>
            <span className="text-xs text-muted-foreground">•</span>
            <span className="text-xs text-muted-foreground">{formatDate(prompt.createdAt)}</span>

            {prompt.isPremium && (
              <>
                <span className="text-xs text-muted-foreground">•</span>
                <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500 text-xs">
                  Premium
                </Badge>
              </>
            )}
          </div>

          {/* Category and Tool badges */}
          <div className="flex flex-wrap items-center gap-2 mb-2">
            <Link href={`/category/${prompt.category?.slug}`}>
              <Badge variant="outline" className={`text-xs hover:scale-105 transition-all duration-200`} style={{ color: getIconColorHex(prompt.category?.slug || "other"), borderColor: getIconColorHex(prompt.category?.slug || "other") }}>
                {prompt.category?.name || "Other"}
              </Badge>
            </Link>

            {prompt.tool && (
              <Link href={`/tool/${prompt.tool.slug}`}>
                <Badge variant="outline" className="text-xs border-gray-300 hover:bg-gray-100 hover:text-gray-900 hover:scale-105 transition-all duration-200 font-medium">
                  {prompt.tool.name}
                </Badge>
              </Link>
            )}
          </div>

          {/* Tags in header - Option C */}
          {prompt.tags && prompt.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {prompt.tags.slice(0, 1).map((tag) => {
                // Use tag.slug if it exists, otherwise create a slug from tag.name
                const tagSlug = tag.slug || slugify(tag.name, { lower: true, strict: true })
                return (
                  <Link
                    key={tag.slug || `tag-${tag.name}`}
                    href={`/tag/${tagSlug}`}
                  >
                    <Badge className="text-xs bg-gray-800 hover:bg-gray-700 hover:scale-105 text-white rounded-full px-2 py-0.5 transition-all duration-200">
                      #{tag.name}
                    </Badge>
                  </Link>
                )
              })}
            </div>
          )}

          <Link href={promptUrl} className="block">
            <h3 className="mb-1 font-semibold transition-all duration-200 group-hover:text-accent-green">{prompt.title}</h3>
            <p className="line-clamp-2 text-sm text-muted-foreground">{prompt.description}</p>
          </Link>

          {/* Enhanced Stats with better visual hierarchy and hover effects - matching main page cards */}
          <div className="mt-2 flex items-center justify-between text-xs">
            <div className="flex items-center gap-4">
              <div
                className="flex items-center gap-1 text-muted-foreground hover:text-red-500 transition-colors duration-200 cursor-pointer group/stat"
                title="Number of likes this prompt has received"
              >
                <Heart className="h-3.5 w-3.5 group-hover/stat:scale-110 transition-transform duration-200" />
                <span className="font-medium">{formatNumber(prompt.likeCount)}</span>
              </div>
              <div
                className="flex items-center gap-1 text-muted-foreground hover:text-blue-500 transition-colors duration-200 cursor-pointer group/stat"
                title="Number of times this prompt has been viewed"
              >
                <Eye className="h-3.5 w-3.5 group-hover/stat:scale-110 transition-transform duration-200" />
                <span className="font-medium">{formatNumber(prompt.viewCount)}</span>
              </div>
              <div
                className="flex items-center gap-1 text-muted-foreground hover:text-green-500 transition-colors duration-200 cursor-pointer group/stat"
                title="Number of comments on this prompt"
              >
                <MessageSquare className="h-3.5 w-3.5 group-hover/stat:scale-110 transition-transform duration-200" />
                <span className="font-medium">{formatNumber(prompt.commentCount)}</span>
              </div>
              {/* Add remix count if available and greater than 0 */}
              {prompt.remixCount !== undefined && prompt.remixCount !== null && prompt.remixCount > 0 && (
                <div
                  className="flex items-center gap-1 text-muted-foreground hover:text-purple-500 transition-colors duration-200 cursor-pointer group/stat"
                  title="Number of times this prompt has been remixed by other users"
                >
                  <GitBranch className="h-3.5 w-3.5 group-hover/stat:scale-110 transition-transform duration-200" />
                  <span className="font-medium">{formatNumber(prompt.remixCount)}</span>
                </div>
              )}
            </div>
            <span className="text-muted-foreground font-medium">{formatDate(prompt.createdAt)}</span>
          </div>
        </div>
      </CardContent>
      {/* Add to Collection Dialog */}
      <AddToCollectionDialog
        isOpen={isAddToCollectionDialogOpen}
        onClose={() => setIsAddToCollectionDialogOpen(false)}
        promptId={prompt.id}
        promptTitle={prompt.title}
        onSuccess={(isSaved: boolean) => {
          // Update the saved status based on the final state from the dialog
          setLocalIsSaved(isSaved);
        }}
      />
    </Card>
  )
}
