"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox"; // Added Checkbox
import { Loader2 } from "lucide-react";
import type { PromptCard as PromptCardType } from "@/lib/types"; // Import PromptCardType

interface AddPromptsToCollectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  collectionId: string; // The ID of the collection to add prompts to
  onAddPrompts: (collectionId: string, promptIds: string[]) => Promise<void>;
}

export default function AddPromptsToCollectionDialog({
  isOpen,
  onClose,
  collectionId,
  onAddPrompts,
}: AddPromptsToCollectionD<PERSON>ogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPromptIds, setSelectedPromptIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  // Placeholder for fetched prompts (e.g., user's prompts, saved, search results)
  const [prompts, setPrompts] = useState<PromptCardType[]>([]); // Placeholder state

  // TODO: Implement fetching prompts based on tabs/search term
  // useEffect(() => {
  //   // Fetch prompts when dialog opens or search term changes
  //   if (isOpen) {
  //     // Fetch user's prompts, saved prompts, or search results
  //     // setPrompts(...)
  //   }
  // }, [isOpen, searchTerm]);


  const handleCheckboxChange = (promptId: string, isChecked: boolean) => {
    setSelectedPromptIds((prev) =>
      isChecked ? [...prev, promptId] : prev.filter((id) => id !== promptId)
    );
  };

  const handleAddSelected = async () => {
    setIsLoading(true);
    try {
      await onAddPrompts(collectionId, selectedPromptIds);
      // Reset state and close on success
      setSelectedPromptIds([]);
      setSearchTerm("");
      setPrompts([]); // Clear prompts after adding
      onClose();
    } catch (error) {
      console.error(`Failed to add prompts to collection ${collectionId}:`, error);
      // TODO: Show error message to user
    } finally {
      setIsLoading(false);
    }
  };

  // Mock prompts for display during development
  const mockPrompts: PromptCardType[] = [
      {
        id: "mock-prompt-1",
        shortId: "mock1",
        slug: "mock-prompt-one",
        title: "Mock Prompt One",
        description: "This is a mock prompt for testing the dialog.",
        category: { name: "Creative Writing", slug: "creative-writing" },
        tool: { name: "ChatGPT", slug: "chatgpt" },
        user: { id: "user1", username: "mockuser", avatarUrl: null },
        tags: [{ name: "mock", slug: "mock" }],
        remixCount: 0,
        likeCount: 0,
        commentCount: 0,
        viewCount: 0,
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
       {
        id: "mock-prompt-2",
        shortId: "mock2",
        slug: "mock-prompt-two",
        title: "Mock Prompt Two",
        description: "Another mock prompt.",
        category: { name: "Code Generation", slug: "code-generation" },
        tool: { name: "GitHub Copilot", slug: "github-copilot" },
        user: { id: "user1", username: "mockuser", avatarUrl: null },
        tags: [{ name: "mock", slug: "mock" }],
        remixCount: 0,
        likeCount: 0,
        commentCount: 0,
        viewCount: 0,
        isPublic: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
  ];


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]"> {/* Adjusted max-width */}
        <DialogHeader>
          <DialogTitle>Add Prompts to Collection</DialogTitle>
          <DialogDescription>
            Select prompts to add to this collection.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
            {/* Search Input */}
            <Input
                placeholder="Search prompts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
            />

            {/* Tabs/Filters Placeholder */}
            {/* TODO: Implement tabs for "My Prompts", "Saved Prompts", "Search All Public" */}

            {/* Prompts List (Placeholder) */}
            <div className="max-h-[300px] overflow-y-auto border rounded-md p-2"> {/* Added scroll */}
                {mockPrompts.length === 0 && <p className="text-center text-muted-foreground">No prompts found.</p>}
                {mockPrompts.map((prompt) => (
                    <div key={prompt.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                        <Label htmlFor={`prompt-${prompt.id}`} className="flex items-center gap-2 cursor-pointer flex-1 pr-4">
                            {/* Consider a mini prompt card view here */}
                            <span className="font-medium line-clamp-1">{prompt.title}</span>
                            <span className="text-sm text-muted-foreground line-clamp-1">{prompt.description}</span>
                        </Label>
                        <Checkbox
                            id={`prompt-${prompt.id}`}
                            checked={selectedPromptIds.includes(prompt.id)}
                            onCheckedChange={(isChecked: boolean) => handleCheckboxChange(prompt.id, isChecked)}
                            disabled={isLoading}
                        />
                    </div>
                ))}
            </div>
        </div>
        <DialogFooter>
          <Button
            type="button" // Changed to type="button" to prevent form submission
            onClick={handleAddSelected}
            disabled={isLoading || selectedPromptIds.length === 0}
          >
            {isLoading ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Adding...</>
            ) : (
              `Add Selected (${selectedPromptIds.length})`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}