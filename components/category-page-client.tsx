"use client"

import { useEffect, useState, useCallback, useRef } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { getCategoryBySlug } from "lib/api-services"
import PromptGrid from "components/prompt-grid"
import CategoryFilters from "components/category-filters"
import { useFilteredPrompts } from "hooks/use-filtered-prompts"
import type { Category } from "lib/types"
import { Loader2 } from "lucide-react"
import { Button } from "components/ui/button"

interface CategoryPageClientProps {
  initialCategorySlug: string
}

export default function CategoryPageClient({ initialCategorySlug }: CategoryPageClientProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [category, setCategory] = useState<Category | null>(null)
  const [isLoadingCategory, setIsLoadingCategory] = useState(true)
  const [errorCategory, setErrorCategory] = useState<Error | null>(null)

  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [selectedTools, setSelectedTools] = useState<string[]>([])
  const [selectedModels, setSelectedModels] = useState<string[]>([])
  const [filterSearchTerm, setFilterSearchTerm] = useState<string>("")
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>("")

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(filterSearchTerm)
    }, 300)

    return () => {
      clearTimeout(handler)
    }
  }, [filterSearchTerm])

  useEffect(() => {
    const categories = searchParams.get("categories")?.split(",") || []
    const tags = searchParams.get("tags")?.split(",") || []
    const tools = searchParams.get("tools")?.split(",") || []
    const models = searchParams.get("models")?.split(",") || []

    setSelectedCategories(categories.length > 0 ? categories : initialCategorySlug ? [initialCategorySlug] : [])
    setSelectedTags(tags)
    setSelectedTools(tools)
    setSelectedModels(models)
  }, [searchParams, initialCategorySlug])

  useEffect(() => {
    if (!initialCategorySlug) return

    const fetchCategory = async () => {
      try {
        setIsLoadingCategory(true)
        const data = await getCategoryBySlug(initialCategorySlug)
        setCategory(data)
      } catch (err) {
        setErrorCategory(err instanceof Error ? err : new Error("Unknown error occurred"))
      } finally {
        setIsLoadingCategory(false)
      }
    }
    fetchCategory()
  }, [initialCategorySlug])

  useEffect(() => {
    if (!initialCategorySlug) return

    const params = new URLSearchParams(searchParams.toString())

    if (selectedCategories.length === 1 && selectedCategories[0] === initialCategorySlug) {
        params.delete("categories")
    } else if (selectedCategories.length > 0) {
        params.set("categories", selectedCategories.join(","))
    } else {
        params.delete("categories") // Ensure category is removed if empty
    }

    if (selectedTags.length > 0) {
      params.set("tags", selectedTags.join(","))
    } else {
      params.delete("tags")
    }

    if (selectedTools.length > 0) {
      params.set("tools", selectedTools.join(","))
    } else {
      params.delete("tools")
    }

    if (selectedModels.length > 0) {
      params.set("models", selectedModels.join(","))
    } else {
      params.delete("models")
    }

    // Also update search term in URL if needed (optional)
    if (debouncedSearchTerm) {
        params.set("q", debouncedSearchTerm)
    } else {
        params.delete("q")
    }

    const currentSearch = searchParams.toString();
    const newSearch = params.toString();
    if (currentSearch !== newSearch) {
        // Use push instead of replace if you want history, replace is usually better for filters
        router.replace(`/category/${initialCategorySlug}?${params.toString()}`, { scroll: false })
    }

  }, [selectedCategories, selectedTags, selectedTools, selectedModels, debouncedSearchTerm, initialCategorySlug, router, searchParams]) // Added debouncedSearchTerm and selectedModels dependencies

  const {
    prompts,
    isLoading: isLoadingPrompts,
    error: errorPrompts,
    hasMore,
    loadMore: loadMorePrompts
  } = useFilteredPrompts({
    categorySlugs: selectedCategories,
    tagSlugs: selectedTags,
    toolSlugs: selectedTools,
    aiModelSlugs: selectedModels,
    searchQuery: debouncedSearchTerm,
  });

  // Function to clear all filters
  const handleClearAllFilters = () => {
    // Keep the initial category selected if it exists
    setSelectedCategories(initialCategorySlug ? [initialCategorySlug] : []);
    setSelectedTags([]);
    setSelectedModels([]);
    setSelectedTools([]);
    setFilterSearchTerm(""); // Clear the search term as well
  };

  const observer = useRef<IntersectionObserver | null>(null)
  const lastPromptElementRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (observer.current) observer.current.disconnect()

      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && !isLoadingPrompts && hasMore) {
          console.log("Infinite scroll triggered")
          loadMorePrompts()
        }
      })

      if (node) observer.current.observe(node)
    },
    [isLoadingPrompts, hasMore, loadMorePrompts] // Added loadMorePrompts dependency
  )

  if (isLoadingCategory) {
    return (
      <div className="container mx-auto flex min-h-[60vh] items-center justify-center px-4 py-8">
        <Loader2 className="h-12 w-12 animate-spin text-accent-green" />
      </div>
    )
  }

  if (errorCategory) {
    return (
      <div className="container mx-auto px-4 py-8 text-red-500">
        Error loading category: {errorCategory.message}
      </div>
    )
  }

  if (!category) {
    return <div className="container mx-auto px-4 py-8">Category not found.</div>
  }

  // Determine if any filters (besides the initial category and search) are active
  const areFiltersActive =
    selectedTags.length > 0 ||
    selectedTools.length > 0 ||
    selectedModels.length > 0 ||
    (selectedCategories.length > 1 || (selectedCategories.length === 1 && selectedCategories[0] !== initialCategorySlug)) ||
    filterSearchTerm !== "";


  return (
    <main className="container mx-auto px-4 py-8">
      <div className="flex flex-col space-y-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">{category.name} Prompts</h1>
          {category.description && <p className="text-muted-foreground">{category.description}</p>}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-20 rounded-lg border p-4">
              <CategoryFilters
                selectedCategories={selectedCategories}
                onSelectCategories={setSelectedCategories}
                selectedTags={selectedTags}
                onSelectTags={setSelectedTags}
                selectedTools={selectedTools}
                onSelectTools={setSelectedTools}
                selectedModels={selectedModels}
                onSelectModels={setSelectedModels}
                searchTerm={filterSearchTerm}
                onSearchChange={setFilterSearchTerm}
                onClearAll={handleClearAllFilters} // Pass down the clear function
                showClearButton={areFiltersActive} // Conditionally show clear button
              />
            </div>
          </div>

          {/* Prompt Grid Area */}
          <div className="lg:col-span-3">
            {errorPrompts ? (
              <div className="text-red-500">Error loading prompts: {errorPrompts.message}</div>
            ) : (
              <>
                <PromptGrid
                  prompts={prompts}
                  emptyMessage={
                    debouncedSearchTerm
                      ? `No prompts found matching "${debouncedSearchTerm}" within the selected filters.`
                      : `No prompts found matching your criteria in the ${category.name} category.`
                  }
                  maxTags={1}
                  viewMode="grid"
                />
                <div ref={lastPromptElementRef} style={{ height: "1px" }} />

                {isLoadingPrompts && prompts.length > 0 && (
                   <div className="mt-8 flex justify-center">
                     <Loader2 className="h-8 w-8 animate-spin text-accent-green" />
                   </div>
                )}

                {!hasMore && prompts.length > 0 && (
                    <div className="mt-8 text-center text-muted-foreground">
                      You've reached the end.
                    </div>
                )}
              </>
            )}
             {isLoadingPrompts && prompts.length === 0 && !errorPrompts && (
                <div className="flex h-64 items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-accent-green" />
                    <span className="ml-2">Loading prompts...</span>
                </div>
             )}
          </div>
        </div>
      </div>
    </main>
  )
}
