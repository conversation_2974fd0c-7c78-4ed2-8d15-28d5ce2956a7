"use client"

import { useState, useEffect, use<PERSON><PERSON>back, useMem<PERSON> } from "react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Bell, Loader2 } from "lucide-react"
import Link from "next/link"
import { createBrowserClient } from "@supabase/ssr"
import type { Notification as NotificationType, PromptCard as PromptCardType } from "@/lib/types"
import { generatePromptUrl } from "@/lib/utils/url-helpers"

// Local interface for component use
interface Notification {
  id: string
  type: "like" | "comment" | "follow" | "mention" | "system" | "comment_prompt" | "reply_comment"
  message: string
  user?: {
    username: string
    id: string
    avatarUrl?: string
  }
  promptId?: string
  promptTitle?: string
  createdAt: string
  read: boolean
  // New fields from notification table for link generation
  prompt_short_id?: string
  prompt_category_slug?: string
  prompt_tool_slug?: string
  prompt_primary_tag_slug?: string
  prompt_title_slug?: string
  entity_title?: string
}

export default function NotificationsDropdown() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)
  const [isOpen, setIsOpen] = useState(false)
const supabase = useMemo(
  () =>
    createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    ),
  []          // stable instance
)

  // Helper function to get notification message based on type
  const getNotificationMessage = (type: string): string => {
    switch (type) {
      case "like_prompt":
        return "liked your prompt"
      case "comment_prompt":
        return "commented on your prompt"
      case "reply_comment":
        return "replied to your comment"
      case "new_follower":
        return "started following you"
      case "mention":
        return "mentioned you in a comment"
      case "system":
        return "System notification"
      default:
        return "interacted with your content"
    }
  }

  // Load notifications from Supabase
  const loadNotifications = useCallback(async () => {
    setIsLoading(true)

    try {
      // Get current user
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        setNotifications([])
        setUnreadCount(0)
        setIsLoading(false)
        return
      }

      const user = session.user

      // Fetch notifications from the database
      const { data, error } = await supabase
        .from("notifications")
        .select("*")
        .eq("recipient_user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(20)

      if (error) {
        console.error("Error fetching notifications:", error.message)
        setNotifications([])
        setUnreadCount(0)
      } else {
        // Transform the data to match our Notification interface
        const transformedNotifications: Notification[] = data.map((n: any) => ({
          id: n.id,
          type: n.type as "like" | "comment" | "follow" | "mention" | "system" | "comment_prompt" | "reply_comment",
          message: getNotificationMessage(n.type),
          user: n.actor ? {
            username: n.actor.username,
            id: n.actor_user_id,
            avatarUrl: n.actor.avatar_url || "/placeholder-user.jpg",
          } : undefined,
          promptId: n.entity_type === "prompt" ? n.entity_id : undefined,
          promptTitle: n.entity_title || undefined,
          createdAt: n.created_at,
          read: n.is_read,
          // Add new fields for link generation
          prompt_short_id: n.prompt_short_id,
          prompt_category_slug: n.prompt_category_slug,
          prompt_tool_slug: n.prompt_tool_slug,
          prompt_primary_tag_slug: n.prompt_primary_tag_slug,
          prompt_title_slug: n.prompt_title_slug,
          entity_title: n.entity_title,
        }))

        setNotifications(transformedNotifications)
        setUnreadCount(transformedNotifications.filter(n => !n.read).length)
      }
    } catch (err) {
      console.error("Unexpected error fetching notifications:", err)
      setNotifications([])
      setUnreadCount(0)
    } finally {
      setIsLoading(false)
    }
  }, [supabase])

  // Load just the unread count for the badge (more efficient than loading all notifications)
  const loadUnreadCount = useCallback(async () => {
    try {
      // Get current user
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        setUnreadCount(0)
        return
      }

      const user = session.user

      // Fetch only the count of unread notifications
      const { count, error } = await supabase
        .from("notifications")
        .select("*", { count: 'exact', head: true })
        .eq("recipient_user_id", user.id)
        .eq("is_read", false)

      if (error) {
        console.error("Error fetching unread count:", error.message)
        setUnreadCount(0)
      } else {
        setUnreadCount(count || 0)
      }
    } catch (err) {
      console.error("Unexpected error loading unread count:", err)
      setUnreadCount(0)
    }
  }, [supabase])

  // Load notifications on mount and when opened
  useEffect(() => {
    if (isOpen) {
      loadNotifications()
    }
  }, [isOpen, loadNotifications])

  // Load unread count on component mount
  useEffect(() => {
    loadUnreadCount()
  }, [loadUnreadCount])

  // Set up real-time subscription for notifications
  useEffect(() => {
    let subscription: any = null

    const setupRealtimeSubscription = async () => {
      try {
        // Get current user
        const { data: { session } } = await supabase.auth.getSession()
        
        if (!session?.user) {
          // If no user, ensure any existing subscription is cleaned up
          if (subscription) {
            await subscription.unsubscribe()
            subscription = null
          }
          return
        }

        const user = session.user

        // Subscribe to real-time changes for this user's notifications
        subscription = supabase
          .channel(`notifications-${user.id}`) // Use unique channel name per user
          .on(
            "postgres_changes",
            {
              event: "*",
              schema: "public",
              table: "notifications",
              filter: `recipient_user_id=eq.${user.id}`,
            },
            (payload) => {
              console.log("Notification change:", payload)
              // Refetch notifications when there's a change
              // You could optimize this by handling the specific change
              loadNotifications()
            }
          )
          .subscribe()
      } catch (err) {
        console.error("Error setting up realtime subscription:", err)
      }
    }

    setupRealtimeSubscription()

    // Return cleanup function to unsubscribe when component unmounts or dependencies change
    return () => {
      if (subscription) {
        subscription.unsubscribe()
        subscription = null
      }
    }
  }, [loadNotifications, supabase])

  // Helper function to generate notification link
  const generateNotificationLink = (notification: Notification): string => {
    // For prompt-related notifications, use generatePromptUrl
    if ((notification.type === 'comment_prompt' || notification.type === 'reply_comment') && notification.prompt_short_id) {
      try {
        // Construct minimal PromptCardType-like object from notification data
        const promptForUrl: Partial<PromptCardType> = {
          shortId: notification.prompt_short_id,
          title: notification.entity_title || 'Untitled Prompt',
          category: {
            slug: notification.prompt_category_slug || 'uncategorized',
            name: notification.prompt_category_slug || 'Uncategorized'
          },
          tool: notification.prompt_tool_slug ? {
            slug: notification.prompt_tool_slug,
            name: notification.prompt_tool_slug
          } : undefined,
          primary_tag_slug: notification.prompt_primary_tag_slug || 'misc'
        }
        
        return generatePromptUrl(promptForUrl as PromptCardType)
      } catch (error) {
        console.error(`Error generating prompt URL for notification ${notification.id}:`, error)
        return "#"
      }
    }
    
    // For user-related notifications
    if (notification.user) {
      return `/user/${notification.user.username}`
    }
    
    // Fallback for notifications missing prompt_short_id
    if (notification.type === 'comment_prompt' || notification.type === 'reply_comment') {
      console.warn(`Notification ID ${notification.id} of type ${notification.type} is missing prompt_short_id. Cannot generate link.`)
    }
    
    return "#"
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        console.error('User not authenticated')
        return
      }

      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('recipient_user_id', session.user.id)
        .eq('is_read', false)

      if (error) {
        console.error('Error marking all notifications as read:', error)
        return
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notif => ({ ...notif, read: true }))
      )
      setUnreadCount(0)
    } catch (error) {
      console.error('Error in markAllAsRead:', error)
    }
  }

  // Mark a single notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        console.error('User not authenticated')
        return
      }

      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .eq('recipient_user_id', session.user.id)

      if (error) {
        console.error('Error marking notification as read:', error)
        return
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === notificationId 
            ? { ...notif, read: true }
            : notif
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error('Error in markAsRead:', error)
    }
  }

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return "just now"
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes}m ago`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `${hours}h ago`
    } else {
      const days = Math.floor(diffInSeconds / 86400)
      return `${days}d ago`
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        console.error('User not authenticated')
        return
      }

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .eq('recipient_user_id', session.user.id)

      if (error) {
        console.error('Error deleting notification:', error)
        return
      }

      // Update local state
      setNotifications(prev => {
        const deletedNotification = prev.find(notif => notif.id === notificationId)
        const filtered = prev.filter(notif => notif.id !== notificationId)
        
        // Update unread count if the deleted notification was unread
        if (deletedNotification && !deletedNotification.read) {
          setUnreadCount(prevCount => Math.max(0, prevCount - 1))
        }
        
        return filtered
      })
    } catch (error) {
      console.error('Error in deleteNotification:', error)
    }
  }

  const clearAllNotifications = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        console.error('User not authenticated')
        return
      }

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('recipient_user_id', session.user.id)

      if (error) {
        console.error('Error clearing all notifications:', error)
        return
      }

      // Update local state
      setNotifications([])
      setUnreadCount(0)
    } catch (error) {
      console.error('Error in clearAllNotifications:', error)
    }
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute right-1 top-1 flex h-4 w-4 items-center justify-center rounded-full bg-accent-green text-[10px] font-medium text-black">
              {unreadCount}
            </span>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" className="h-auto p-0 text-xs" onClick={markAllAsRead}>
              Mark all as read
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {isLoading ? (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Loading notifications...</span>
          </div>
        ) : notifications.length > 0 ? (
          <DropdownMenuGroup>
            {notifications.map((notification) => (
              <DropdownMenuItem key={notification.id} asChild>
                <Link
                  href={generateNotificationLink(notification)}
                  className={`flex cursor-pointer gap-3 p-3 ${!notification.read ? "bg-accent-green/5" : ""}`}
                  onClick={() => markAsRead(notification.id)}
                >
                  {notification.user ? (
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={notification.user.avatarUrl || "/placeholder-user.jpg"}
                        alt={notification.user.username}
                      />
                      <AvatarFallback>{notification.user.username.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                  ) : (
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-accent-green/10">
                      <Bell className="h-4 w-4 text-accent-green" />
                    </div>
                  )}

                  <div className="flex-1">
                    <div className="text-sm">
                      {notification.user && <span className="font-medium">{notification.user.username}</span>}{" "}
                      {notification.message}
                      {notification.promptTitle && (
                        <>
                          {" "}
                          <span className="font-medium">"{notification.promptTitle}"</span>
                        </>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">{formatRelativeTime(notification.createdAt)}</div>
                  </div>
                </Link>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        ) : (
          <div className="flex items-center justify-center py-4">
            <span className="text-sm text-muted-foreground">No notifications yet</span>
          </div>
        )}

        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/notifications" className="flex w-full justify-center">
            <span className="text-sm font-medium">View all notifications</span>
          </Link>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
