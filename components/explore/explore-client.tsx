"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import CategoryList from "@/components/category-list"
import ToolList from "@/components/tool-list"
import TagList from "@/components/tag-list"
import CollectionCard from "@/components/collection-card"
import { Skeleton } from "@/components/ui/skeleton"
import { Search, FolderOpen } from "lucide-react"
import { getCategories, getTools, getTags, getPublicCollections, getPublicCollectionsCount } from "@/lib/api-services"
import type { Collection } from "@/lib/types"

interface ExploreClientProps {
  initialCategories?: any[]
  initialTools?: any[]
  initialTags?: any[]
}

export default function ExploreClient({ 
  initialCategories = [], 
  initialTools = [], 
  initialTags = [] 
}: ExploreClientProps) {
  // State for data with proper typing
  const [categories, setCategories] = useState<any[]>(initialCategories);
  const [tools, setTools] = useState<any[]>(initialTools);
  const [tags, setTags] = useState<any[]>(initialTags);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [collectionsCount, setCollectionsCount] = useState(0);
  const [loading, setLoading] = useState(initialCategories.length === 0);
  
  // State for active tab
  const [activeTab, setActiveTab] = useState("categories");

  // State for search within tabs
  const [searchQuery, setSearchQuery] = useState("");
  
  // State for collections search and sorting
  const [collectionsSearchQuery, setCollectionsSearchQuery] = useState("");
  const [collectionsSortBy, setCollectionsSortBy] = useState<"newest" | "popular" | "most_items">("newest");
  const [collectionsLoading, setCollectionsLoading] = useState(false);
  
  // Handle hash change and initial tab selection
  useEffect(() => {
    // Get hash from URL (e.g., #tags)
    const hash = window.location.hash.replace('#', '');
    
    // Set initial tab based on hash
    if (hash && ['categories', 'tools', 'tags', 'collections'].includes(hash)) {
      setActiveTab(hash);
    }
    
    // Add hash change listener
    const handleHashChange = () => {
      const newHash = window.location.hash.replace('#', '');
      if (newHash && ['categories', 'tools', 'tags', 'collections'].includes(newHash)) {
        setActiveTab(newHash);
      }
    };
    
    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);
  
  // Fetch data if not provided
  useEffect(() => {
    async function fetchData() {
      if (categories.length > 0 && tools.length > 0 && tags.length > 0) {
        return;
      }
      
      setLoading(true);
      try {
        // Fetch all data in parallel
        const [categoriesData, toolsData, tagsData] = await Promise.all([
          categories.length === 0 ? getCategories() : Promise.resolve(categories),
          tools.length === 0 ? getTools() : Promise.resolve(tools),
          tags.length === 0 ? getTags() : Promise.resolve(tags)
        ]);
        
        // Use the data as is - prompt counts should already be included
        setCategories(categoriesData);
        setTools(toolsData);
        setTags(tagsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, [categories, tools, tags]);

  // Fetch collections count on component mount
  useEffect(() => {
    const fetchCollectionsCount = async () => {
      try {
        const count = await getPublicCollectionsCount();
        setCollectionsCount(count);
      } catch (error) {
        console.error('Error fetching collections count:', error);
      }
    };
    
    fetchCollectionsCount();
  }, []);

  // Fetch collections when collections tab is active
  useEffect(() => {
    if (activeTab === "collections") {
      fetchCollections();
    }
  }, [activeTab, collectionsSearchQuery, collectionsSortBy]);

  const fetchCollections = async () => {
    setCollectionsLoading(true);
    try {
      const collectionsData = await getPublicCollections({
        searchQuery: collectionsSearchQuery.trim() || undefined,
        sortBy: collectionsSortBy,
        limit: 50, // Fetch more collections for better browsing
      });
      setCollections(collectionsData);
    } catch (error) {
      console.error('Error fetching collections:', error);
      setCollections([]);
    } finally {
      setCollectionsLoading(false);
    }
  };

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1).replace(/\.0$/, "")}M`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
    } else {
      return count.toString()
    }
  }

  // Filter tags based on search query (only used for tags tab)
  const filteredTags = tags.filter(tag =>
    tag.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const tabs = [
    { id: "categories", label: "Categories", count: categories.length },
    { id: "tools", label: "Tools", count: tools.length },
    { id: "tags", label: "Tags", count: tags.length },
    { id: "collections", label: "Collections", count: collectionsCount },
  ]

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    // Clear search when switching away from tags tab
    if (activeTab === "tags" || tabId !== "tags") {
      setSearchQuery("");
    }
    // Update URL hash when tab changes
    window.history.pushState(null, '', `#${tabId}`);
  }

  return (
    <div className="w-full max-w-7xl mx-auto px-2 sm:px-0">
      {/* Enhanced Tab Navigation with improved responsive design */}
      <div className="border-b border-border/50">
        <div className="flex bg-muted/30 rounded-lg p-1 mb-4 max-w-4xl mx-auto">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant="ghost"
              className={`flex-1 min-w-0 rounded-md py-2 sm:py-3 px-2 sm:px-4 font-medium transition-all duration-300 ease-in-out ${
                activeTab === tab.id
                  ? "bg-primary text-primary-foreground shadow-md border border-primary/20 transform scale-[1.02]"
                  : "text-muted-foreground hover:text-foreground hover:bg-background/80 hover:shadow-sm"
              }`}
              onClick={() => handleTabChange(tab.id)}
            >
              <span className="flex items-center justify-center gap-1 sm:gap-2 min-w-0">
                <span className={`transition-all duration-300 truncate text-xs sm:text-sm ${
                  activeTab === tab.id ? "font-semibold" : "font-medium"
                }`}>
                  {tab.label}
                </span>
                <Badge
                  variant="secondary"
                  className={`text-xs transition-all duration-300 flex-shrink-0 ${
                    activeTab === tab.id
                      ? "bg-primary-foreground/20 text-primary-foreground border border-primary-foreground/30"
                      : "bg-muted text-muted-foreground hover:bg-background/80"
                  }`}
                >
                  {formatCount(tab.count)}
                </Badge>
              </span>
            </Button>
          ))}
        </div>
      </div>

      {/* Search Input for Tags only - Responsive */}
      {activeTab === "tags" && (
        <div className="mb-4 sm:mb-6 px-2 sm:px-0">
          <div className="relative max-w-md mx-auto sm:mx-0">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search tags..."
              className="pl-10 text-sm sm:text-base transition-all duration-300 focus:ring-2 focus:ring-primary/20"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {searchQuery && (
            <p className="mt-2 text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
              Found {filteredTags.length} tags
            </p>
          )}
        </div>
      )}

      {/* Tab Content with responsive design */}
      <div className="mt-4 sm:mt-6 min-h-[300px] sm:min-h-[400px] w-full px-2 sm:px-0">
        {activeTab === "categories" && (
          <div className="w-full">
            {loading ? (
              <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 w-full">
                {Array(10).fill(0).map((_, i) => (
                  <Skeleton key={i} className="h-32 sm:h-40 w-full rounded-lg" />
                ))}
              </div>
            ) : categories.length > 0 ? (
              <div className="w-full">
                <CategoryList categories={categories} />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center w-full">
                <Search className="mb-3 sm:mb-4 h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground" />
                <h3 className="mb-2 text-lg sm:text-xl font-semibold">No Categories Found</h3>
                <p className="text-sm sm:text-base text-muted-foreground">
                  No categories available.
                </p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === "tools" && (
          <div className="w-full">
            {loading ? (
              <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 w-full">
                {Array(12).fill(0).map((_, i) => (
                  <Skeleton key={i} className="h-24 sm:h-32 w-full rounded-lg" />
                ))}
              </div>
            ) : tools.length > 0 ? (
              <div className="w-full">
                <ToolList tools={tools} />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center w-full">
                <Search className="mb-3 sm:mb-4 h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground" />
                <h3 className="mb-2 text-lg sm:text-xl font-semibold">No Tools Found</h3>
                <p className="text-sm sm:text-base text-muted-foreground">
                  No tools available.
                </p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === "tags" && (
          <div className="w-full">
            {loading ? (
              <div className="grid grid-cols-3 gap-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 2xl:grid-cols-12 w-full">
                {Array(24).fill(0).map((_, i) => (
                  <Skeleton key={i} className="h-16 sm:h-20 w-full rounded-lg" />
                ))}
              </div>
            ) : filteredTags.length > 0 ? (
              <div className="w-full">
                <TagList tags={filteredTags} />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center w-full">
                <Search className="mb-3 sm:mb-4 h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground" />
                <h3 className="mb-2 text-lg sm:text-xl font-semibold">No Tags Found</h3>
                <p className="text-sm sm:text-base text-muted-foreground">
                  {searchQuery
                    ? `No tags match "${searchQuery}"`
                    : "No tags available."
                  }
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === "collections" && (
          <div className="w-full">
            {/* Collections Search and Sort Controls - Responsive */}
            <div className="mb-4 sm:mb-6 flex flex-col gap-3 sm:gap-4 sm:flex-row sm:items-center sm:justify-between w-full">
              <div className="relative flex-1 max-w-md mx-auto sm:mx-0">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search collections..."
                  className="pl-10 text-sm sm:text-base"
                  value={collectionsSearchQuery}
                  onChange={(e) => setCollectionsSearchQuery(e.target.value)}
                />
              </div>
              <Select value={collectionsSortBy} onValueChange={(value: "newest" | "popular" | "most_items") => setCollectionsSortBy(value)}>
                <SelectTrigger className="w-full sm:w-48 text-sm sm:text-base">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest</SelectItem>
                  <SelectItem value="popular">Popular</SelectItem>
                  <SelectItem value="most_items">Most Items</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Collections Grid - Responsive */}
            {collectionsLoading ? (
              <div className="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-full">
                {Array(6).fill(0).map((_, i) => (
                  <Skeleton key={i} className="h-48 sm:h-64 w-full rounded-lg" />
                ))}
              </div>
            ) : collections.length > 0 ? (
              <div className="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-full">
                {collections.map((collection) => (
                  <CollectionCard
                    key={collection.id}
                    collection={collection}
                    isOwner={false}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 sm:py-12 text-center w-full">
                <FolderOpen className="mb-3 sm:mb-4 h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground" />
                <h3 className="mb-2 text-lg sm:text-xl font-semibold">No Collections Found</h3>
                <p className="text-sm sm:text-base text-muted-foreground">
                  {collectionsSearchQuery
                    ? `No collections match "${collectionsSearchQuery}"`
                    : "No public collections available yet."
                  }
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
