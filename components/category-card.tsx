import Link from "next/link"
import Image from "next/image"
import { getCategoryTheme } from "./category-icon-mapper"
import { getCategoryColorByName } from "@/lib/data/category-colors"

interface CategoryCardProps {
  category: {
    name: string
    slug: string
    description?: string
    imagePath?: string
  }
}

export default function CategoryCard({ category }: CategoryCardProps) {
  const { icon: Icon, darkGradient, darkTextColor } = getCategoryTheme(category.name)
  const colors = getCategoryColorByName(category.name)

  return (
    <Link
      href={`/category/${category.slug}`}
      className="group flex flex-col rounded-xl border border-border bg-card transition-all hover:border-accent-green/30 hover:shadow-sm hover:shadow-accent-green/10"
    >
      <div className="overflow-hidden h-32 rounded-t-xl relative">
        {category.imagePath ? (
          <Image
            src={category.imagePath || "/placeholder.svg"}
            alt={category.name}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
            quality={85}
          />
        ) : (
          <div className={`flex h-full w-full items-center justify-center bg-gradient-to-br ${colors.gradient}`}>
            <Icon className="h-12 w-12 text-white" />
          </div>
        )}
      </div>
      <div className="p-4">
        <div className="mb-2">
          <h3 className="font-medium">{category.name}</h3>
        </div>
        {category.description && <p className="line-clamp-2 text-sm text-muted-foreground">{category.description}</p>}
      </div>
    </Link>
  )
}
