import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { Folder, Bookmark, Lock, Save, Edit, Globe } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { useState } from "react"

import type { Collection } from "@/lib/types";

interface CollectionCardProps {
  collection: Collection & {
    promptCount?: number; // Aliased from prompt_count
  };
  isOwner?: boolean;
  onEdit?: (collection: Collection) => void;
  onSave?: (collection: Collection) => void;
  priority?: boolean; // Add priority prop for above-the-fold images
}

export default function CollectionCard({ collection, isOwner, onEdit, onSave, priority = false }: CollectionCardProps) {
  const [imageError, setImageError] = useState(false);
  
  // Generate a gradient background for collections without images
  const getGradient = () => {
    const gradients = [
      "bg-gradient-to-br from-accent-green/20 to-accent-blue/20",
      "bg-gradient-to-tr from-accent-blue/20 to-accent-purple/20",
      "bg-gradient-to-bl from-accent-purple/20 to-accent-pink/20",
      "bg-gradient-to-tl from-accent-pink/20 to-accent-teal/20",
      "bg-gradient-to-r from-accent-teal/20 to-accent-green/20",
    ]
    // Use collection ID length for a consistent color based on the ID string
    const index = collection.id.length % gradients.length;
    return gradients[index];
  }
  
  // Get the appropriate icon for default collections
  const getDefaultCollectionIcon = () => {
    if (collection.isDefault && collection.defaultType) {
      if (collection.defaultType === 'saved_prompts') {
        return '/images/collection-Saved-Prompts.png';
      } else if (collection.defaultType === 'my_prompts') {
        return '/images/collection-my-prompt.png';
      }
    }
    return collection.icon;
  }

  // Determine the link based on ownership
  const collectionUrl = isOwner ? 
    `/collections/${collection.id}` : 
    `/profile/${collection.user?.username || 'user'}/c/${collection.id}`;

  // Prevent click propagation for action buttons
  const handleActionClick = (e: React.MouseEvent, callback: Function) => {
    e.preventDefault();
    e.stopPropagation();
    callback();
  };

  return (
    <Link href={collectionUrl}>
      <Card className="h-full overflow-hidden transition-all duration-300 hover:border-accent-green/40 hover:shadow-lg hover:shadow-accent-green/20 hover:-translate-y-1 group relative bg-gradient-to-br from-background to-background/95">
        <div className="relative aspect-video overflow-hidden">
          {getDefaultCollectionIcon() && !imageError ? (
            <Image
              src={getDefaultCollectionIcon() || ''}
              alt={`${collection.name} collection thumbnail`}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-110"
              onError={() => setImageError(true)}
              priority={priority}
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 300px"
              quality={85}
            />
          ) : (
            <div className={`flex h-full w-full items-center justify-center ${getGradient()} transition-all duration-300 group-hover:brightness-110`}>
              <div className="flex flex-col items-center justify-center transition-transform duration-300 group-hover:scale-110">
                <Folder className="h-12 w-12 text-foreground/70 transition-colors duration-300 group-hover:text-foreground/90" />
                <Bookmark className="absolute h-16 w-16 text-foreground/10 transition-colors duration-300 group-hover:text-foreground/20" />
              </div>
            </div>
          )}

          {/* Hover overlay for better visual feedback */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          {/* Badges */}
          <div className="absolute top-2 right-2 flex flex-col gap-2 z-10">
            {/* Only show public/private badges for owners, since explore page only shows public collections */}
            {isOwner && (
              collection.isPublic ? (
                <Badge className="border-none px-2 py-1 font-medium text-xs text-white bg-accent-green/90 backdrop-blur-sm rounded-md flex items-center gap-1 shadow-sm transition-all duration-300 group-hover:bg-accent-green group-hover:shadow-md">
                  <Globe className="h-3 w-3" /> Public
                </Badge>
              ) : (
                <Badge className="border-none px-2 py-1 font-medium text-xs text-white bg-gray-600/90 backdrop-blur-sm rounded-md flex items-center gap-1 shadow-sm transition-all duration-300 group-hover:bg-gray-600 group-hover:shadow-md">
                  <Lock className="h-3 w-3" /> Private
                </Badge>
              )
            )}
            {collection.isDefault && (
              <Badge className="border-none px-2 py-1 font-medium text-xs text-white bg-accent-blue/90 backdrop-blur-sm rounded-md flex items-center gap-1 shadow-sm transition-all duration-300 group-hover:bg-accent-blue group-hover:shadow-md">
                <Bookmark className="h-3 w-3" /> Default
              </Badge>
            )}
          </div>
          
          {/* Action buttons */}
          <div className="absolute top-2 left-2 flex gap-2">
            {/* Save button for non-owners */}
            {!isOwner && onSave && (
              <Button 
                variant="secondary" 
                size="sm" 
                className="bg-white/80 hover:bg-white"
                onClick={(e) => handleActionClick(e, () => onSave(collection))}
              >
                <Save className="h-4 w-4 mr-1" /> Save
              </Button>
            )}
          </div>
        </div>
        <CardHeader className="p-4 pb-3 space-y-2">
          <div className="flex items-start justify-between gap-2">
            <h3 className="line-clamp-1 text-lg font-semibold leading-tight group-hover:text-accent-green transition-colors duration-300">{collection.name}</h3>
          </div>
          <div className="text-sm text-muted-foreground">
            by <span className="font-medium text-foreground/80 group-hover:text-accent-green transition-colors duration-300">@{collection.user?.username || 'user'}</span>
          </div>
          {collection.description && (
            <p className="mt-2 line-clamp-2 text-sm text-muted-foreground leading-relaxed">{collection.description}</p>
          )}
        </CardHeader>
        <CardContent className="p-4 pt-0 flex items-center justify-between">
          <Badge variant="outline" className="bg-accent-green/10 border-accent-green/20 text-accent-green font-medium transition-all duration-300 group-hover:bg-accent-green/20 group-hover:border-accent-green/40">
            {collection.promptCount} {collection.promptCount === 1 ? 'prompt' : 'prompts'}
          </Badge>
          {collection.isDefault && (
            <div className="text-xs text-muted-foreground font-medium">
              {collection.defaultType === 'my_prompts' ? '✨ My Creations' : '📚 Saved Items'}
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  )
}
