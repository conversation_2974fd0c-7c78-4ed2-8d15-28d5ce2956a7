"use client"

import type React from "react"
import { useState, useEffect, use<PERSON><PERSON>back, useRef, useMemo } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Sheet, SheetContent, SheetTrigger, SheetTitle } from "@/components/ui/sheet"
import { Popover, PopoverContent, PopoverAnchor } from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandList,
  CommandSeparator,
  CommandItem,
} from "@/components/ui/command"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Search, Menu, FileText, Tag, Settings, Boxes, Library, Loader2, FolderOpen, Bookmark, User as UserIcon, ChevronDown, MoreHorizontal } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import CommunityJoinModal from "@/components/community-join-modal"
import NotificationsDropdown from "@/components/notifications-dropdown"

import { useCategories } from "@/hooks/use-categories"
import { useTags } from "@/hooks/use-tags"
import { useTools } from "@/hooks/use-tools"
import { useUniversalSearch } from "@/hooks/use-search"
import type { PromptCard, Category, Tool, Profile } from "@/lib/types"
import { createBrowserClient } from "@supabase/ssr"

export default function Header() {
  const [user, setUser] = useState<any>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [debouncedQuery, setDebouncedQuery] = useState("")
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const [lastKeyPressed, setLastKeyPressed] = useState<string | null>(null)
  const [showCommunityModal, setShowCommunityModal] = useState(false)

  const router = useRouter()
  const inputRef = useRef<HTMLInputElement>(null)
  const popoverRef = useRef<HTMLDivElement>(null)
  const supabase = useMemo(
    () =>
      createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      ),
    []
  )
  const isFetchingRef = useRef(false)

  useEffect(() => {
    const fetchUserAndProfile = async () => {
      if (isFetchingRef.current) return
      isFetchingRef.current = true

      try {
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.warn("Error fetching session in header:", sessionError.message)
          setUser(null)
          setProfile(null)
          return
        }

        const user = session?.user || null
        setUser(user)

        if (user) {
          try {
            const { data: profileData, error: profileError } = await supabase
              .from("profiles")
              .select("*")
              .eq("id", user.id)
              .single()

            if (profileError) {
              console.warn("Error fetching profile in header:", profileError.message)
              setProfile(null)
            } else {
              setProfile(profileData)
            }
          } catch (profileFetchError) {
            console.warn("Network error fetching profile:", profileFetchError)
            setProfile(null)
          }
        } else {
          setProfile(null)
        }
      } catch (error) {
        console.warn("Network error in fetchUserAndProfile:", error)
        setUser(null)
        setProfile(null)
      } finally {
        isFetchingRef.current = false
      }
    }

    fetchUserAndProfile().catch(error => {
      console.warn("Failed to fetch user and profile:", error)
      isFetchingRef.current = false
    })

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log("Auth state changed:", event, session)
      setUser(session?.user || null)
      if (session?.user) {
         fetchUserAndProfile().catch(error => {
           console.warn("Failed to re-fetch profile on auth change:", error)
         })
      } else {
         setProfile(null)
      }
    })

    const handleWindowFocus = () => {
      if (user && !isFetchingRef.current) {
        fetchUserAndProfile().catch(error => {
          console.warn("Failed to refresh profile on window focus:", error)
        })
      }
    }

    window.addEventListener('focus', handleWindowFocus)

    return () => {
      subscription.unsubscribe()
      window.removeEventListener('focus', handleWindowFocus)
    }
  }, [supabase])

  // Fetch static data for filtering
  const { categories, isLoading: isLoadingCategories } = useCategories()
  const { tags, isLoading: isLoadingTags } = useTags()
  const { tools, isLoading: isLoadingTools } = useTools()

  // Fetch dynamic search suggestions
  const { results: searchResults, isLoading: isLoadingSearch, error } = useUniversalSearch(debouncedQuery, { limit: 7 })

  // Optimized debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery)
    }, 150)
    return () => clearTimeout(timer)
  }, [searchQuery])

  // Enhanced popover visibility logic
  const showPopover = (isFocused || isPopoverOpen) && searchQuery.trim().length > 0 && !error

  // Monitor search performance
  useEffect(() => {
    if (isLoadingSearch) {
      const timeout = setTimeout(() => {
        console.warn('Search taking longer than expected:', searchQuery)
      }, 2000)

      return () => clearTimeout(timeout)
    }
  }, [isLoadingSearch, searchQuery])

  // Handle search form submission (optional, mainly for pressing Enter)
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
      setIsPopoverOpen(false) // Close popover on submit
      setIsFocused(false) // Clear focus state
      inputRef.current?.blur() // Blur input on submit
    }
  }

  // Handle selecting a suggestion item
  const handleSelect = (url: string, itemType?: "prompt" | "collection" | "profile") => {
    router.push(url)
    setIsPopoverOpen(false)
    setIsFocused(false) // Clear focus state
    setSearchQuery("") // Clear search after selection
    inputRef.current?.blur() // Blur input after selection
  }

  // Handle search query changes with immediate popover opening - FIXED: Remove isFocused dependency
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setSearchQuery(newValue)
    
    // Open popover immediately when typing (check focus state directly)
    if (newValue.trim().length > 0) {
      setIsPopoverOpen(true)
    } else if (newValue.trim().length === 0) {
      setIsPopoverOpen(false)
    }
  }, []) // Remove isFocused dependency to prevent infinite re-renders

  // Enhanced focus handling - FIXED: Remove searchQuery dependency
  const handleInputFocus = useCallback(() => {
    setIsFocused(true)
    // Check if there's existing content and open popover
    setTimeout(() => {
      if (inputRef.current && inputRef.current.value.trim().length > 0) {
        setIsPopoverOpen(true)
      }
    }, 0)
  }, []) // Remove searchQuery dependency to prevent infinite re-renders

  // Enhanced blur handling with click detection
  const handleInputBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    // Check if the blur is due to clicking inside the popover
    if (popoverRef.current?.contains(e.relatedTarget as Node)) {
      return // Don't close if clicking inside popover
    }
    
    // Delay to allow for popover clicks
    setTimeout(() => {
      setIsFocused(false)
      setIsPopoverOpen(false)
    }, 150)
  }, [])

  // Handle key down events for search input
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      if (lastKeyPressed === 'Escape') {
        setSearchQuery(''); // Clear search query
        setIsPopoverOpen(false); // Close popover
        setIsFocused(false); // Clear focus state
        inputRef.current?.blur(); // Blur input
        setLastKeyPressed(null); // Reset key tracking
      } else {
        setLastKeyPressed('Escape'); // Record first escape press
      }
    } else {
      setLastKeyPressed(null); // Reset if any other key is pressed
    }
  };

  // Filter static lists based on search query
  const filteredCategories = searchQuery
    ? (categories || []).filter((cat) => cat.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : []
  const filteredTags = searchQuery
    ? (tags || []).filter((tag) => tag.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : []
  const filteredTools = searchQuery
    ? (tools || []).filter((tool) => tool.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : []

  // Navigation items with progressive disclosure
  const primaryNavItems = [
    { id: "explore", label: "Explore", href: "/explore", icon: Library, description: "Discover prompts by category, tool, or tag" },
  ]

  const secondaryNavItems = [
    { id: "search", label: "Advanced Search", href: "/search", icon: Search, description: "Powerful search with filters" },
    { id: "categories", label: "Categories", href: "/explore#categories", icon: Boxes, description: "Browse by topic" },
    { id: "tools", label: "Tools", href: "/explore#tools", icon: Settings, description: "Find prompts for specific AI tools" },
    { id: "tags", label: "Tags", href: "/explore#tags", icon: Tag, description: "Explore by keywords" },
  ]

  return (
    <TooltipProvider>
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
        <Sheet>
          {/* Mobile hamburger menu */}
          <SheetTrigger asChild>
             <Button variant="ghost" size="icon" className="md:hidden">
               <Menu className="h-5 w-5" />
               <span className="sr-only">Toggle menu</span>
             </Button>
           </SheetTrigger>
           <SheetContent side="left">
             <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
             <Link href="/" className="flex items-center space-x-2">
               <Image
                 src="/images/mascot.png"
                 alt="PromptHQ Mascot"
                 width={50}
                 height={50}
                 className="w-12 h-12 rounded-full"
               />
               <span className="text-xl font-bold">
                 Prompt<span className="text-accent-green font-extrabold">HQ</span>
               </span>
             </Link>
             <div className="mt-8 flex flex-col gap-4">
               {/* Simplified mobile navigation */}
               <div className="space-y-2">
                 <div className="text-lg font-medium text-foreground">EXPLORE</div>
                 <div className="ml-4 space-y-3">
                   <Link href="/explore" className="flex items-center gap-2 text-base text-muted-foreground hover:text-foreground transition-colors">
                     <Library className="h-4 w-4" />
                     <span>Browse All</span>
                   </Link>
                   <Link href="/search" className="flex items-center gap-2 text-base text-muted-foreground hover:text-foreground transition-colors">
                     <Search className="h-4 w-4" />
                     <span>Search</span>
                   </Link>
                 </div>
               </div>

               {user ? (
                 <>
                   <Link href="/collections" className="text-lg font-medium">
                     My Collections
                   </Link>
                   <Link href="/saved" className="text-lg font-medium">Saved Prompts</Link>
                   <Link href={`/user/${profile?.username}`} className="text-lg font-medium">
                     Profile
                   </Link>
                   <Link href="/settings" className="text-lg font-medium">
                     Settings
                   </Link>
                   <Button variant="destructive" onClick={async () => {
                      const { error } = await supabase.auth.signOut();
                      if (error) console.error("Sign out error:", error.message);
                   }}>
                     Sign Out
                   </Button>
                 </>
               ) : (
                 <>
                   <Link href="/sign-in" className="text-lg font-medium">Sign In</Link>
                   <Link href="/sign-up" className="text-lg font-medium">Sign Up</Link>
                 </>
               )}
             </div>
           </SheetContent>
        </Sheet>

        {/* Logo - responsive sizing */}
        <div className="flex items-center gap-2 md:gap-4">
          <Link href="/" className="flex items-center space-x-2">
            {/* Mascot image - visible on all screen sizes */}
            <Image
              src="/images/mascot.png"
              alt="PromptHQ Mascot"
              width={50}
              height={50}
              className="w-12 h-12 rounded-full"
            />
            {/* Desktop logo - full PromptHQ - hidden on mobile */}
            <span className="text-xl font-bold hidden md:block">
              Prompt<span className="text-accent-green font-extrabold">HQ</span>
            </span>
          </Link>
          {/* New Prompt button - only show on larger screens */}
          {user ? (
            <Button 
              className="hidden lg:inline-flex text-white font-medium transition-all duration-200" 
              style={{ 
                borderRadius: "6px",
                background: "linear-gradient(145deg, #4ade80, #22c55e)",
                boxShadow: "inset 0 -3px 0 0 rgba(0, 0, 0, 0.2), 0 3px 10px rgba(0, 0, 0, 0.1)"
              }}
              asChild
            >
              <Link 
                href="/prompt/submit"
                className="transition-all duration-200 hover:brightness-110 hover:shadow-lg"
                style={{
                  textShadow: "0 1px 0 rgba(0, 0, 0, 0.1)"
                }}
              >
                + New Prompt
              </Link>
            </Button>
          ) : (
            <Button 
              className="hidden lg:inline-flex text-white font-medium transition-all duration-200" 
              style={{ 
                borderRadius: "6px",
                background: "linear-gradient(145deg, #4ade80, #22c55e)",
                boxShadow: "inset 0 -3px 0 0 rgba(0, 0, 0, 0.2), 0 3px 10px rgba(0, 0, 0, 0.1)",
                textShadow: "0 1px 0 rgba(0, 0, 0, 0.1)"
              }}
              onClick={() => setShowCommunityModal(true)}
            >
              + New Prompt
            </Button>
          )}
        </div>

        {/* Enhanced Search Bar Area - More Prominent */}
        <div className="flex-1 mx-4 md:mx-6 lg:mx-8 max-w-2xl">
          <Popover open={showPopover} onOpenChange={setIsPopoverOpen}>
            <PopoverAnchor asChild>
              <form onSubmit={handleSearchSubmit} className="relative w-full">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    ref={inputRef}
                    type="search"
                    placeholder="Search prompts, collections, users..."
                    className="w-full pl-12 pr-4 py-3 rounded-xl bg-white text-gray-900 text-base border-2 border-border/50 focus:border-primary/50 transition-all duration-200 shadow-sm hover:shadow-md focus:shadow-lg"
                    value={searchQuery}
                    onChange={handleSearchChange}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    onKeyDown={handleKeyDown}
                    aria-autocomplete="list"
                    aria-controls="search-suggestions"
                  />
                </div>
              </form>
            </PopoverAnchor>

            {/* Enhanced Suggestions Popover */}
            <PopoverContent
                ref={popoverRef}
                id="search-suggestions"
                className="w-[--radix-popover-trigger-width] p-0 mt-2 max-h-[70vh] overflow-auto border-2 border-border/50 shadow-xl"
                align="start"
                side="bottom"
                onOpenAutoFocus={(e) => e.preventDefault()}
                onCloseAutoFocus={(e) => e.preventDefault()}
             >
              <Command shouldFilter={false} className="text-gray-900"> {/* Disable cmdk internal filtering */}
                {/* We don't need CommandInput here as the main Input handles typing */}
                <CommandList>
                  {isLoadingSearch && debouncedQuery && searchResults.length === 0 && (
                    <div className="p-4 text-sm text-center text-muted-foreground flex items-center justify-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" /> Searching...
                    </div>
                  )}

                  {!isLoadingSearch && debouncedQuery && searchResults.length === 0 && filteredCategories.length === 0 && filteredTags.length === 0 && filteredTools.length === 0 && (
                    <CommandEmpty>No results found for "{debouncedQuery}".</CommandEmpty>
                  )}

                  {/* Universal Search Results (Prompts + Collections + Profiles) */}
                  {searchResults.length > 0 && (
                    <CommandGroup heading="Quick Results">
                      {searchResults.map((result) => (
                        <CommandItem
                          key={`${result.itemType}-${result.id}`}
                          value={`${result.itemType}-${result.title}-${result.id}`} // Ensure unique value for CMDK
                          onSelect={() => {
                            if (result.itemType === "prompt") {
                              handleSelect(`/prompt/${result.shortId}`, "prompt")
                            } else if (result.itemType === "collection") {
                              handleSelect(`/profile/${result.username}/c/${result.id}`, "collection")
                            } else if (result.itemType === "profile") {
                              handleSelect(`/user/${result.username}`, "profile")
                            }
                          }}
                          className="cursor-pointer hover:bg-green-400/20 hover:text-green-400 transition-colors duration-200 py-3"
                        >
                          {result.itemType === "prompt" ? (
                            <FileText className="mr-3 h-4 w-4 flex-shrink-0" />
                          ) : result.itemType === "collection" ? (
                            <FolderOpen className="mr-3 h-4 w-4 flex-shrink-0" />
                          ) : (
                            <UserIcon className="mr-3 h-4 w-4 flex-shrink-0" />
                          )}
                          <span className="flex-1 truncate">
                            {result.title}
                            {result.itemType === 'profile' && result.username !== result.title && (
                              <span className="text-xs text-muted-foreground ml-1">({result.username})</span>
                            )}
                          </span>
                          <Badge variant="outline" className="ml-auto text-xs capitalize flex-shrink-0">
                            {result.itemType}
                          </Badge>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}

                  {/* Category, Tool, and Tag Results */}
                  {filteredCategories.length > 0 && (
                    <CommandGroup heading="Categories">
                      {filteredCategories.slice(0, 3).map((category) => (
                        <CommandItem
                          key={`category-${category.id}`}
                          value={`category-${category.name}`}
                          onSelect={() => handleSelect(`/category/${category.slug}`)}
                          className="cursor-pointer hover:bg-green-400/20 hover:text-green-400 transition-colors duration-200 py-3"
                        >
                          <Boxes className="mr-3 h-4 w-4 flex-shrink-0" />
                          <span className="flex-1 truncate">{category.name}</span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}

                  {filteredTools.length > 0 && (
                    <CommandGroup heading="Tools">
                      {filteredTools.slice(0, 3).map((tool) => (
                        <CommandItem
                          key={`tool-${tool.id}`}
                          value={`tool-${tool.name}`}
                          onSelect={() => handleSelect(`/tool/${tool.slug}`)}
                          className="cursor-pointer hover:bg-green-400/20 hover:text-green-400 transition-colors duration-200 py-3"
                        >
                          <Settings className="mr-3 h-4 w-4 flex-shrink-0" />
                          <span className="flex-1 truncate">{tool.name}</span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}

                  {filteredTags.length > 0 && (
                    <CommandGroup heading="Tags">
                      {filteredTags.slice(0, 3).map((tag) => (
                        <CommandItem
                          key={`tag-${tag.id}`}
                          value={`tag-${tag.name}`}
                          onSelect={() => handleSelect(`/tag/${tag.slug}`)}
                          className="cursor-pointer hover:bg-green-400/20 hover:text-green-400 transition-colors duration-200 py-3"
                        >
                          <Tag className="mr-3 h-4 w-4 flex-shrink-0" />
                          <span className="flex-1 truncate">{tag.name}</span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}

                 {/* See all results link */}
                 {(searchResults.length > 0 || filteredCategories.length > 0 || filteredTools.length > 0 || filteredTags.length > 0) && (
                   <>
                    <CommandSeparator />
                    <CommandGroup>
                         <CommandItem
                          key="search-all"
                           value={`search-all-${searchQuery}`}
                          onSelect={() => handleSelect(`/search?q=${encodeURIComponent(searchQuery.trim())}`)}
                          className="cursor-pointer italic text-muted-foreground hover:bg-green-400/15 hover:text-green-400 transition-colors duration-200 py-3 md:py-2"
                         >
                          <Search className="mr-2 h-4 w-4 flex-shrink-0" />
                          <span className="text-sm md:text-base">See all results for "{searchQuery}"</span>
                         </CommandItem>
                    </CommandGroup>
                    </>
                 )}

                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
        {/* --- End Enhanced Search Bar Area --- */}

        {/* Right side - Simplified Navigation with Dropdown */}
        <div className="flex items-center gap-2 md:gap-4">
          {/* Primary Navigation - Always visible */}
          <div className="hidden lg:flex items-center">
            {/* Tab-style navigation matching explore page */}
            <div className="flex bg-muted/30 rounded-lg p-1">
              {primaryNavItems.map((item) => (
                <Tooltip key={item.id}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      className="rounded-md px-3 py-2 font-medium transition-all duration-300 ease-in-out text-muted-foreground hover:text-foreground hover:bg-background/80 hover:shadow-sm"
                      asChild
                    >
                      <Link href={item.href} className="flex items-center gap-2">
                        <item.icon className="h-4 w-4" />
                        <span className="text-sm">{item.label}</span>
                      </Link>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p className="text-sm">{item.description}</p>
                  </TooltipContent>
                </Tooltip>
              ))}

              {/* More dropdown menu */}
              <DropdownMenu>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="rounded-md px-3 py-2 font-medium transition-all duration-300 ease-in-out text-muted-foreground hover:text-foreground hover:bg-background/80 hover:shadow-sm"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="text-sm ml-2">More</span>
                        <ChevronDown className="h-3 w-3 ml-1" />
                      </Button>
                    </DropdownMenuTrigger>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    <p className="text-sm">More navigation options</p>
                  </TooltipContent>
                </Tooltip>
                <DropdownMenuContent className="w-56" align="end">
                  <DropdownMenuGroup>
                    {secondaryNavItems.map((item) => (
                      <DropdownMenuItem key={item.id} asChild>
                        <Link href={item.href} className="flex items-center gap-2">
                          <item.icon className="h-4 w-4" />
                          <div className="flex flex-col">
                            <span>{item.label}</span>
                            <span className="text-xs text-muted-foreground">{item.description}</span>
                          </div>
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {user ? (
            <div className="flex items-center gap-1 md:gap-2">
              {/* Collections Icon - Hidden on small mobile */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="hidden sm:flex" asChild>
                    <Link href="/collections">
                      <FolderOpen className="h-4 w-4 md:h-5 md:w-5" />
                      <span className="sr-only">My Collections</span>
                    </Link>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>My Collections</p>
                </TooltipContent>
              </Tooltip>

              {/* Saved Prompts Icon - Hidden on small mobile */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="hidden sm:flex" asChild>
                    <Link href="/saved">
                      <Bookmark className="h-4 w-4 md:h-5 md:w-5" />
                      <span className="sr-only">Saved Prompts</span>
                    </Link>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Saved Prompts</p>
                </TooltipContent>
              </Tooltip>

              {/* Notifications - Always visible but smaller on mobile */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <NotificationsDropdown />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Notifications</p>
                </TooltipContent>
              </Tooltip>

              {/* User Avatar with enhanced styling */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="rounded-full relative">
                        <Avatar className="h-7 w-7 md:h-8 md:w-8 ring-2 ring-transparent hover:ring-primary/20 transition-all duration-200">
                          <AvatarImage src={profile?.avatar_url || undefined} alt={profile?.username || "Your profile"} />
                          <AvatarFallback className="text-xs md:text-sm bg-gradient-to-br from-primary/20 to-primary/10">
                            {profile?.username?.substring(0, 2).toUpperCase() || "YP"}
                          </AvatarFallback>
                        </Avatar>
                      </Button>
                    </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end">
                  <DropdownMenuLabel>
                    {profile?.username ? `@${profile.username}` : "My Account"}
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    {profile?.username && (
                      <DropdownMenuItem asChild>
                        <Link href={`/user/${profile.username}`} className="flex items-center gap-2">
                          <UserIcon className="h-4 w-4" />
                          Profile
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem asChild>
                      <Link href="/collections" className="flex items-center gap-2">
                        <FolderOpen className="h-4 w-4" />
                        My Collections
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/saved" className="flex items-center gap-2">
                        <Bookmark className="h-4 w-4" />
                        Saved Prompts
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/settings" className="flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Settings
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={async () => {
                     const { error } = await supabase.auth.signOut()
                     if (error) console.error("Sign out error:", error.message)
                  }}>
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
                  </DropdownMenu>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Account Menu</p>
                </TooltipContent>
              </Tooltip>
            </div>
          ) : (
            <div className="hidden md:flex items-center gap-1 md:gap-2">
              <Link href="/sign-in" passHref>
                <Button variant="ghost" size="sm" className="rounded-md text-sm px-2 md:px-4">Sign In</Button>
              </Link>
              <Link href="/sign-up" passHref>
                <Button size="sm" className="btn-accent-blue rounded-md text-sm px-2 md:px-4">Sign Up</Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </header>

    {/* Community Join Modal */}
    <CommunityJoinModal
      isOpen={showCommunityModal}
      onClose={() => setShowCommunityModal(false)}
      redirectPath="/prompt/submit"
    />
    </TooltipProvider>
  )
}
