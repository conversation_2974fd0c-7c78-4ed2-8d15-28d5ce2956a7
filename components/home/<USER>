"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import CategoryCard from "@/components/category-card"
import MiniCategoryCard from "@/components/mini-category-card"
import { ChevronDown, ChevronUp } from "lucide-react"
import type { Category } from "@/lib/types" // Import Category type from lib/types

// Define types for component props
interface CategoriesSectionProps {
  featuredCategories: Category[]
  additionalCategories: Category[]
}

export default function CategoriesSection({ featuredCategories, additionalCategories }: CategoriesSectionProps) {
  // Set showMoreCategories to true by default to expand the grid
  const [showMoreCategories, setShowMoreCategories] = useState(true)

  return (
    <section className="mb-12">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Popular Categories</h2>
        {/* Remove the toggle button */}
      </div>

      {/* Main featured categories */}
      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 mb-6">
        {featuredCategories.map((category) => (
          <CategoryCard key={category.id} category={category} />
        ))}
      </div>

      {/* Additional categories (always shown since showMoreCategories is true by default) */}
      {showMoreCategories && (
        <div id="more-categories" className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
          {additionalCategories.map((category) => (
            <MiniCategoryCard key={category.id} category={category} />
          ))}
        </div>
      )}
    </section>
  )
}
