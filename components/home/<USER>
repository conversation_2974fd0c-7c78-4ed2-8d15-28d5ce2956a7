"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// Import components that will be children
import ToolsToggleButton from "@/components/home/<USER>"
import MoreTools from "@/components/home/<USER>"
import type { Tool } from "@/lib/types" // Import Tool type from lib/types

// Define types for component props
interface PopularToolsSectionProps {
  popularTools: Tool[]
  additionalTools: Tool[]
}

export default function PopularToolsSection({ popularTools, additionalTools }: PopularToolsSectionProps) {
  // Set showMoreTools to true by default to expand the grid
  const [showMoreTools, setShowMoreTools] = useState(true);

  return (
    <section className="mb-12">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Popular Tools</h2>
        {/* Remove the toggle button */}
      </div>

      {/* Display popular tools */}
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
        {popularTools.map((tool) => (
          <Link key={tool.id} href={`/tool/${tool.slug}`}>
            <Card className="h-full transition-all hover:border-accent-green/30 hover:shadow-sm hover:shadow-accent-green/10">
              <CardContent className="flex flex-col items-center justify-center p-4">
                <div className="mb-2 text-2xl">{tool.icon}</div>
                <h3 className="text-center font-medium">{tool.name}</h3>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Additional tools (always shown since showMoreTools is true by default) */}
      <MoreTools additionalTools={additionalTools} showMoreTools={showMoreTools} />
    </section>
  )
}
