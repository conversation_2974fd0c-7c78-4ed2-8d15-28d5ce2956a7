"use client"

export default function FeaturedPromptsSkeleton() {
  return (
    <section>
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Featured Prompts</h2>
        <div className="h-10 w-32 animate-pulse rounded-md bg-muted"></div>
      </div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div key={i} className="h-64 animate-pulse rounded-lg bg-muted"></div>
        ))}
      </div>
    </section>
  )
}
