"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverAnchor } from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandList,
  CommandSeparator,
  CommandItem,
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { Search, FileText, FolderOpen, User as UserIcon, Boxes, Settings, Tag, Loader2 } from "lucide-react"
import { useCategories } from "@/hooks/use-categories"
import { useTags } from "@/hooks/use-tags"
import { useTools } from "@/hooks/use-tools"
import { useUniversalSearch } from "@/hooks/use-search"

export default function HeroSearchWithDropdown() {
  const router = useRouter()
  const inputRef = useRef<HTMLInputElement>(null)
  const popoverRef = useRef<HTMLDivElement>(null)
  
  // Search state
  const [searchQuery, setSearchQuery] = useState("")
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const [lastKeyPressed, setLastKeyPressed] = useState<string | null>(null)

  // Get static data
  const { categories } = useCategories()
  const { tags } = useTags()
  const { tools } = useTools()

  // Universal search hook with debouncing
  const { results: searchResults, isLoading: isLoadingSearch } = useUniversalSearch(searchQuery, { limit: 5 })

  // Computed property for showing popover
  const showPopover = isPopoverOpen && isFocused && searchQuery.trim().length > 0

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
      setIsPopoverOpen(false)
      setIsFocused(false)
      inputRef.current?.blur()
    }
  }

  // Handle selecting a suggestion item
  const handleSelect = (url: string) => {
    router.push(url)
    setIsPopoverOpen(false)
    setIsFocused(false)
    setSearchQuery("")
    inputRef.current?.blur()
  }

  // Handle search query changes
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setSearchQuery(newValue)
    
    // Open popover immediately when typing
    if (newValue.trim().length > 0) {
      setIsPopoverOpen(true)
    } else if (newValue.trim().length === 0) {
      setIsPopoverOpen(false)
    }
  }, [])

  // Enhanced focus handling
  const handleInputFocus = useCallback(() => {
    setIsFocused(true)
    // Check if there's existing content and open popover
    setTimeout(() => {
      if (inputRef.current && inputRef.current.value.trim().length > 0) {
        setIsPopoverOpen(true)
      }
    }, 0)
  }, [])

  // Enhanced blur handling with click detection
  const handleInputBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    // Check if the blur is due to clicking inside the popover
    if (popoverRef.current?.contains(e.relatedTarget as Node)) {
      return // Don't close if clicking inside popover
    }
    
    // Delay to allow for popover clicks
    setTimeout(() => {
      setIsFocused(false)
      setIsPopoverOpen(false)
    }, 150)
  }, [])

  // Handle key down events for search input
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      if (lastKeyPressed === 'Escape') {
        setSearchQuery('')
        setIsPopoverOpen(false)
        setIsFocused(false)
        inputRef.current?.blur()
        setLastKeyPressed(null)
      } else {
        setLastKeyPressed('Escape')
      }
    } else {
      setLastKeyPressed(null)
    }
  }

  // Filter static lists based on the current searchQuery
  const filteredCategories = searchQuery
    ? categories.filter((cat) => cat.name.toLowerCase().includes(searchQuery.toLowerCase())).slice(0, 3)
    : []
  const filteredTags = searchQuery
    ? tags.filter((tag) => tag.name.toLowerCase().includes(searchQuery.toLowerCase())).slice(0, 3)
    : []
  const filteredTools = searchQuery
    ? tools.filter((tool) => tool.name.toLowerCase().includes(searchQuery.toLowerCase())).slice(0, 3)
    : []

  return (
    <Popover open={showPopover} onOpenChange={setIsPopoverOpen}>
      <PopoverAnchor asChild>
        <form onSubmit={handleSearchSubmit} className="mx-auto max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
            <Input
              ref={inputRef}
              type="search"
              name="q"
              placeholder="Search prompts, categories, tools..."
              className="w-full rounded-lg pl-10 pr-4 text-base md:text-lg"
              style={{ borderRadius: "8px" }}
              aria-label="Search Prompts"
              value={searchQuery}
              onChange={handleSearchChange}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              onKeyDown={handleKeyDown}
              aria-autocomplete="list"
              aria-controls="hero-search-suggestions"
            />
          </div>
        </form>
      </PopoverAnchor>

      {/* Search Suggestions Popover */}
      <PopoverContent
        ref={popoverRef}
        id="hero-search-suggestions"
        className="w-[--radix-popover-trigger-width] p-0 mt-1 max-h-[70vh] overflow-auto"
        align="start"
        side="bottom"
        onOpenAutoFocus={(e) => e.preventDefault()}
        onCloseAutoFocus={(e) => e.preventDefault()}
      >
        <Command shouldFilter={false} className="text-gray-900">
          <CommandList>
            {/* Show loading only if search is taking longer than expected and we have a query */}
            {isLoadingSearch && searchQuery && searchResults.length === 0 && (
              <div className="p-3 text-sm text-center text-muted-foreground flex items-center justify-center gap-2">
                <Loader2 className="h-3 w-3 animate-spin" /> Searching...
              </div>
            )}
            
            {/* Only show empty if not loading and query exists */}
            {!isLoadingSearch && searchQuery && searchResults.length === 0 && filteredCategories.length === 0 && filteredTags.length === 0 && filteredTools.length === 0 && (
              <CommandEmpty>No results found for "{searchQuery}".</CommandEmpty>
            )}

            {/* Universal Search Results (Prompts + Collections + Profiles) */}
            {searchResults.length > 0 && (
              <CommandGroup heading="Quick Results">
                {searchResults.map((result) => (
                  <CommandItem
                    key={`${result.itemType}-${result.id}`}
                    value={`${result.itemType}-${result.title}-${result.id}`}
                    onSelect={() => {
                      if (result.itemType === "prompt") {
                        handleSelect(`/prompt/${result.shortId}`)
                      } else if (result.itemType === "collection") {
                        handleSelect(`/profile/${result.username}/c/${result.id}`)
                      } else if (result.itemType === "profile") {
                        handleSelect(`/user/${result.username}`)
                      }
                    }}
                    className="cursor-pointer hover:bg-green-400/20 hover:text-green-400 transition-colors duration-200 py-2"
                  >
                    {result.itemType === "prompt" ? (
                      <FileText className="mr-2 h-4 w-4 flex-shrink-0" />
                    ) : result.itemType === "collection" ? (
                      <FolderOpen className="mr-2 h-4 w-4 flex-shrink-0" />
                    ) : (
                      <UserIcon className="mr-2 h-4 w-4 flex-shrink-0" />
                    )}
                    <span className="flex-1 truncate text-sm">
                      {result.title}
                      {result.itemType === 'profile' && result.username !== result.title && (
                        <span className="text-xs text-muted-foreground ml-1">({result.username})</span>
                      )}
                    </span>
                    <Badge variant="outline" className="ml-auto text-xs capitalize flex-shrink-0">
                      {result.itemType}
                    </Badge>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {/* Category Results */}
            {filteredCategories.length > 0 && (
              <CommandGroup heading="Categories">
                {filteredCategories.map((category) => (
                  <CommandItem
                    key={`category-${category.id}`}
                    value={`category-${category.name}`}
                    onSelect={() => handleSelect(`/category/${category.slug}`)}
                    className="cursor-pointer hover:bg-green-400/20 hover:text-green-400 transition-colors duration-200 py-2"
                  >
                    <Boxes className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="flex-1 truncate text-sm">{category.name}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {/* Tool Results */}
            {filteredTools.length > 0 && (
              <CommandGroup heading="Tools">
                {filteredTools.map((tool) => (
                  <CommandItem
                    key={`tool-${tool.id}`}
                    value={`tool-${tool.name}`}
                    onSelect={() => handleSelect(`/tool/${tool.slug}`)}
                    className="cursor-pointer hover:bg-green-400/20 hover:text-green-400 transition-colors duration-200 py-2"
                  >
                    <Settings className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="flex-1 truncate text-sm">{tool.name}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {/* Tag Results */}
            {filteredTags.length > 0 && (
              <CommandGroup heading="Tags">
                {filteredTags.map((tag) => (
                  <CommandItem
                    key={`tag-${tag.id}`}
                    value={`tag-${tag.name}`}
                    onSelect={() => handleSelect(`/tag/${tag.slug}`)}
                    className="cursor-pointer hover:bg-green-400/20 hover:text-green-400 transition-colors duration-200 py-2"
                  >
                    <Tag className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="flex-1 truncate text-sm">{tag.name}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {/* See all results link */}
            {(searchResults.length > 0 || filteredCategories.length > 0 || filteredTools.length > 0 || filteredTags.length > 0) && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    key="search-all"
                    value={`search-all-${searchQuery}`}
                    onSelect={() => handleSelect(`/search?q=${encodeURIComponent(searchQuery.trim())}`)}
                    className="cursor-pointer italic text-muted-foreground hover:bg-green-400/15 hover:text-green-400 transition-colors duration-200 py-2"
                  >
                    <Search className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="text-sm">See all results for "{searchQuery}"</span>
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
} 