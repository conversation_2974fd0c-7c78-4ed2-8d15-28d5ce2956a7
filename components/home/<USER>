"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { createBrowserClient } from "@supabase/ssr"

const VISITED_KEY = "phq_has_visited"

export default function GettingStartedSection() {
  const [shouldShow, setShouldShow] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkVisitorStatus = async () => {
      try {
        // Check if required environment variables are available
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
        const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        
        if (!supabaseUrl || !supabaseAnonKey) {
          console.error("Missing required Supabase environment variables:", {
            NEXT_PUBLIC_SUPABASE_URL: !!supabaseUrl,
            NEXT_PUBLIC_SUPABASE_ANON_KEY: !!supabaseAnonKey
          })
          // Fallback to showing the section for anonymous users when Supabase is not configured
          const hasVisited = localStorage.getItem(VISITED_KEY)
          if (!hasVisited) {
            setShouldShow(true)
            localStorage.setItem(VISITED_KEY, "true")
          } else {
            setShouldShow(false)
          }
          setIsLoading(false)
          return
        }
        
        // Check if user is logged in
        const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)
        
        const { data: { session } } = await supabase.auth.getSession()
        
        // If user is logged in, don't show the section
        if (session?.user) {
          setShouldShow(false)
          setIsLoading(false)
          return
        }
        
        // Check if user has visited before (for anonymous users)
        const hasVisited = localStorage.getItem(VISITED_KEY)
        
        if (!hasVisited) {
          // First time visitor - show the section and mark as visited
          setShouldShow(true)
          localStorage.setItem(VISITED_KEY, "true")
        } else {
          // Returning visitor - don't show the section
          setShouldShow(false)
        }
      } catch (error) {
        console.error("Error checking visitor status:", error)
        // On error, default to not showing the section
        setShouldShow(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkVisitorStatus()
  }, [])

  // Don't render anything while loading or if shouldn't show
  if (isLoading || !shouldShow) {
    return null
  }

  return (
    <section className="mb-12">
      <h2 className="mb-4 text-2xl font-bold">Getting Started</h2>
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-6 items-center">
            <div className="flex-1">
              <p className="text-muted-foreground">
                Welcome to PromptHQ! Here's how to get started:
              </p>
              <ul className="mt-3 list-disc space-y-1 pl-5 text-muted-foreground">
                <li>Use the search bar above or <Link href="/explore" className="text-accent-green hover:underline">explore</Link> to find prompts.</li>
                <li>Click on a prompt to see its details and copy it for use.</li>
                <li><Link href="/sign-up" className="text-accent-green hover:underline">Join the community</Link> to save, share, and remix prompts.</li>
              </ul>
            </div>
            <div className="flex-shrink-0">
              <Image
                src="/images/welcome-screen.png"
                alt="Welcome to PromptHQ - Discover and share awesome prompts"
                width={400}
                height={300}
                className="rounded-lg shadow-md"
                priority
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </section>
  )
} 