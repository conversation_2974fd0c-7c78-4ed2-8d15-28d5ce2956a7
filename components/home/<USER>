"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"
import type { Tool } from "@/components/types" // Import Tool type
import type { Dispatch, SetStateAction } from "react" // Import Dispatch and SetStateAction

// Define types for ToolsToggleButton props
interface ToolsToggleButtonProps {
  showMoreTools: boolean;
  setShowMoreTools: Dispatch<SetStateAction<boolean>>;
}

export default function ToolsToggleButton({ showMoreTools, setShowMoreTools }: ToolsToggleButtonProps) {
  return (
    <Button
      variant="ghost"
      size="sm"
      className="flex items-center gap-1 text-accent-green"
      onClick={() => setShowMoreTools(!showMoreTools)}
      aria-expanded={showMoreTools}
      aria-controls="more-tools"
    >
      {showMoreTools ? (
        <>
          Less Tools <ChevronUp className="h-4 w-4" />
        </>
      ) : (
        <>
          More Tools <ChevronDown className="h-4 w-4" />
        </>
      )}
    </Button>
  )
}
