"use client";

import dynamic from "next/dynamic";
import type { Tool } from "@/lib/types"; // Use the type from lib/types

// Dynamically import PopularToolsSection to prevent SSR issues
const PopularToolsSection = dynamic(() => import("./popular-tools-section"), { ssr: false });

interface PopularToolsSectionClientProps {
  popularTools: Tool[];
  additionalTools: Tool[];
}

export default function PopularToolsSectionClient({ popularTools, additionalTools }: PopularToolsSectionClientProps) {
  return (
    <PopularToolsSection popularTools={popularTools} additionalTools={additionalTools} />
  );
}