"use client"

import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import type { Tool } from "@/lib/types" // Import Tool type from lib/types

// Define types for component props
interface MoreToolsProps {
  additionalTools: Tool[]
  showMoreTools: boolean // showMoreTools prop is passed down
}

export default function MoreTools({ additionalTools, showMoreTools }: MoreToolsProps) {
  // State is now managed in the parent (Home)
  // Removed useEffect that synced with global state

  if (!showMoreTools) return null

  return (
    <div id="more-tools" className="mt-6 grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
      {additionalTools.map((tool) => (
        <Link key={tool.id} href={`/tool/${tool.slug}`}>
          <Card className="h-full transition-all hover:border-accent-green/30 hover:shadow-sm hover:shadow-accent-green/10">
            <CardContent className="flex flex-col items-center justify-center p-3">
              <div className="mb-1 text-xl">{tool.icon}</div>
              <h3 className="text-center text-sm font-medium">{tool.name}</h3>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  )
}
