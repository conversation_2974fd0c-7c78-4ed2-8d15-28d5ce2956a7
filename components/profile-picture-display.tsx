"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { X, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface ProfilePictureDisplayProps {
  avatarUrl?: string | null;
  username: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showDeleteButton?: boolean;
  onDelete?: () => void;
  isDeleting?: boolean;
  className?: string;
}

const sizeClasses = {
  sm: "h-8 w-8",
  md: "h-12 w-12", 
  lg: "h-16 w-16",
  xl: "h-32 w-32"
}

const fallbackSizeClasses = {
  sm: "text-xs",
  md: "text-sm",
  lg: "text-lg", 
  xl: "text-4xl"
}

const deleteBtnSizeClasses = {
  sm: "h-5 w-5 -top-1 -right-1",
  md: "h-6 w-6 -top-1 -right-1",
  lg: "h-7 w-7 -top-2 -right-2",
  xl: "h-8 w-8 -top-2 -right-2"
}

const deleteBtnIconSizes = {
  sm: "h-3 w-3",
  md: "h-3 w-3", 
  lg: "h-4 w-4",
  xl: "h-4 w-4"
}

export function ProfilePictureDisplay({
  avatarUrl,
  username,
  size = 'md',
  showDeleteButton = false,
  onDelete,
  isDeleting = false,
  className
}: ProfilePictureDisplayProps) {
  const initials = username?.substring(0, 2).toUpperCase() || "YP"
  
  return (
    <div className={cn("relative", className)}>
      <Avatar className={cn(
        sizeClasses[size],
        "border-2 border-background shadow-sm",
        className
      )}>
        <AvatarImage 
          src={avatarUrl || undefined} 
          alt={`${username}'s profile picture`} 
        />
        <AvatarFallback className={fallbackSizeClasses[size]}>
          {initials}
        </AvatarFallback>
      </Avatar>
      
      {showDeleteButton && avatarUrl && onDelete && (
        <Button
          variant="destructive"
          size="sm"
          className={cn(
            "absolute rounded-full p-0",
            deleteBtnSizeClasses[size]
          )}
          onClick={onDelete}
          disabled={isDeleting}
        >
          {isDeleting ? (
            <Loader2 className={cn("animate-spin", deleteBtnIconSizes[size])} />
          ) : (
            <X className={deleteBtnIconSizes[size]} />
          )}
        </Button>
      )}
    </div>
  )
} 