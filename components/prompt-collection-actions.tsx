"use client";

import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreH<PERSON>zon<PERSON>, FolderUp, Trash, AlertCircle } from "lucide-react";
import MovePromptDialog from "@/components/move-prompt-dialog";
import { removePromptFromCollection, addPromptToCollection } from "@/lib/api-services";
import { useToast } from "@/components/ui/use-toast";
import type { Collection } from "@/lib/types";

interface PromptCollectionActionsProps {
  userId: string;
  promptId: string;
  collectionId: string;
  isOwnPrompt: boolean;
  collections: Collection[];
  onPromptMoved?: () => void;
  onPromptRemoved?: () => void;
}

export default function PromptCollectionActions({
  userId,
  promptId,
  collectionId,
  isOwnPrompt,
  collections,
  onPromptMoved,
  onPromptRemoved,
}: PromptCollectionActionsProps) {
  const [isMoveDialogOpen, setIsMoveDialogOpen] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const { toast } = useToast();

  const handleMovePrompt = async (promptId: string, fromCollectionId: string, toCollectionId: string) => {
    try {
      // First add to the new collection
      await addPromptToCollection(userId, promptId, toCollectionId);
      
      // Then remove from the current collection
      await removePromptFromCollection(userId, promptId, fromCollectionId);
      
      toast({
        title: "Prompt moved",
        description: "The prompt has been moved to the selected collection.",
      });
      
      // Notify parent component
      if (onPromptMoved) {
        onPromptMoved();
      }
    } catch (error) {
      console.error("Error moving prompt:", error);
      toast({
        title: "Error",
        description: "Failed to move the prompt. Please try again.",
        variant: "destructive",
      });
      throw error; // Re-throw for the dialog to handle
    }
  };

  const handleRemovePrompt = async () => {
    if (!confirm("Are you sure you want to remove this prompt from the collection?")) {
      return;
    }
    
    setIsRemoving(true);
    try {
      await removePromptFromCollection(userId, promptId, collectionId);
      
      toast({
        title: "Prompt removed",
        description: "The prompt has been removed from this collection.",
      });
      
      // Notify parent component
      if (onPromptRemoved) {
        onPromptRemoved();
      }
    } catch (error) {
      console.error("Error removing prompt:", error);
      toast({
        title: "Error",
        description: "Failed to remove the prompt. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRemoving(false);
    }
  };

  // Check if this is a default collection
  const currentCollection = collections.find(c => c.id === collectionId);
  const isDefaultCollection = currentCollection?.isDefault || false;

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setIsMoveDialogOpen(true)}>
            <FolderUp className="mr-2 h-4 w-4" />
            Move to another collection
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleRemovePrompt} disabled={isRemoving}>
            <Trash className="mr-2 h-4 w-4" />
            Remove from collection
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <MovePromptDialog
        isOpen={isMoveDialogOpen}
        onClose={() => setIsMoveDialogOpen(false)}
        promptId={promptId}
        isOwnPrompt={isOwnPrompt}
        currentCollectionId={collectionId}
        collections={collections}
        onMovePrompt={handleMovePrompt}
      />
    </>
  );
}
