"use client"

import React, { useState, useEffect, useRef } from "react" // Added React hooks imports

import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { useToast } from "hooks/use-toast"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "components/ui/avatar"
import { Badge } from "components/ui/badge"
import { Button } from "components/ui/button"
import { Separator } from "components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "components/ui/select"
import { Textarea } from "components/ui/textarea" // Added Textarea import
import {
  Copy,
  Share2,
  FolderPlus,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Sparkles,
  Eye,
  Loader2,
  Check,
  User as UserIcon, // For Author icon fallback
  Settings as SettingsIcon, // For Tool icon
  Grid as GridIcon, // For Category icon
  Calendar as CalendarIcon, // For Date icon
  Send, // For post comment button
  Pencil, // For edit comment button
  Trash2, // For delete comment button
  X, // For cancel edit button
  AlertCircle, // For error messages
  GitFork, // For remixed from icon
  Users, // For community modal icon
} from "lucide-react"
import type { Prompt, Comment, Tag, Profile } from "lib/types" // Added Tag and Profile to import
import { formatDistanceToNow } from "date-fns"
import { supabase } from "lib/supabase/client"
// Using only direct voting API for improved reliability
import { voteOnPromptDirect, getUserVoteOnPromptDirect } from "lib/api-voting-direct";
import { highlightDoubleBracketedText } from "lib/utils/text-highlighting"
import { getViewerHash } from "lib/utils/viewer-tracking"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from "components/ui/dialog"
import AddToCollectionDialog from "components/add-to-collection-dialog"
import { getCategoryColorBySlug } from "lib/data/category-colors"
import type { User } from "@supabase/supabase-js" // Import Supabase User type
import type { Database } from "lib/database.types"
import { containsProfanity, recordProfanityAttempt, isUserOnTimeout } from "@/lib/utils/profanity-filter"
import { getRelatedPromptsForDisplay } from "@/lib/api-services"
import type { PromptCard as PromptCardType } from "@/lib/types"
import PromptGrid from "@/components/prompt-grid"
import CommunityJoinModal from "@/components/community-join-modal"
import slugify from 'slugify'

interface PromptDetailViewProps {
  prompt: Prompt
  initialComments?: Comment[]
}

// Use the generated type from database.types.ts
type CommentDisplayDetails = Database['public']['Views']['comment_display_details']['Row'];

// Helper function to convert from CommentDisplayDetails to Comment
function convertToComment(data: CommentDisplayDetails): Comment {
  // Ensure we have a valid ID, throw error if null
  if (!data.id) {
    throw new Error('Comment ID cannot be null');
  }
  
  return {
    id: data.id,
    prompt_id: data.prompt_id || '',
    parent_comment_id: data.parent_comment_id,
    text: data.text || '',
    created_at: data.created_at || new Date().toISOString(),
    updated_at: data.updated_at || new Date().toISOString(),
    user: {
      id: data.user_id || '',
      username: data.author_username || "User",
      avatar_url: data.author_avatar_url,
    } as Profile,
    likes: data.like_count || 0,
    dislikes: 0, // Default to 0 as this field isn't in the view
    liked_by_user: false,
    replies: [],
  };
}

export default function PromptDetailView({ prompt, initialComments = [] }: PromptDetailViewProps) {
  const router = useRouter()
  const { toast } = useToast()
  const searchParams = useSearchParams()

  // Helper function to format large numbers
  const formatStatNumber = (num: number | undefined | null): string => {
    if (num === undefined || num === null) return "0"
    const value = typeof num === "string" ? Number.parseInt(num, 10) : num
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1).replace(/\.0$/, "")}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1).replace(/\.0$/, "")}K`
    } else {
      return value.toString()
    }
  }

  const [activeTab, setActiveTab] = useState<"about" | "comments" | "related">("about")
  const [voteCount, setVoteCount] = useState(prompt.likeCount || 0)
  const [userVote, setUserVote] = useState<"up" | "down" | null>(null)
  const [isAddToCollectionDialogOpen, setIsAddToCollectionDialogOpen] = useState(false)
  const [showAllComments, setShowAllComments] = useState(false)
  const [commentSortOption, setCommentSortOption] = useState<"top" | "newest" | "oldest">("top")
  const [isCopying, setIsCopying] = useState(false)
  const [newlyAddedCommentId, setNewlyAddedCommentId] = useState<string | null>(null)
  const [profanityError, setProfanityError] = useState<string | null>(null)
  const [isOnTimeout, setIsOnTimeout] = useState(false);
  const [timeoutRemaining, setTimeoutRemaining] = useState(0);
  
  // For tracking consecutive comments
  const [lastCommenterUserId, setLastCommenterUserId] = useState<string | null>(null);
  
  // For tracking consecutive replies by parent comment ID
  const [consecutiveRepliesByParent, setConsecutiveRepliesByParent] = useState<Record<string, {userId: string, count: number}>>({});

  const [comments, setComments] = useState<Comment[]>(initialComments)
  const [isLoadingComments, setIsLoadingComments] = useState(false)
  const [commentsLoaded, setCommentsLoaded] = useState(false)

  // State for new comment
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [newCommentText, setNewCommentText] = useState("")
  const [isPostingComment, setIsPostingComment] = useState(false)

  // State for replies
  const [replyingToCommentId, setReplyingToCommentId] = useState<string | null>(null)
  const [replyText, setReplyText] = useState("")
  const [isPostingReply, setIsPostingReply] = useState(false)

  // State for editing comments
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null)
  const [editText, setEditText] = useState("")
  const [isEditingComment, setIsEditingComment] = useState(false)

  // State for related prompts
  const [relatedPrompts, setRelatedPrompts] = useState<PromptCardType[]>([])
  const [isLoadingRelated, setIsLoadingRelated] = useState(false)
  const [relatedPromptsLoaded, setRelatedPromptsLoaded] = useState(false)
  const [errorRelated, setErrorRelated] = useState<string | null>(null)

  // State for community join modal and share animation
  const [showCommunityModal, setShowCommunityModal] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const [shareConfirmed, setShareConfirmed] = useState(false)

  // State for related prompts save functionality
  const [selectedRelatedPromptId, setSelectedRelatedPromptId] = useState<string | null>(null)
  const [selectedRelatedPromptTitle, setSelectedRelatedPromptTitle] = useState<string>("")
  const [isRelatedPromptSaveDialogOpen, setIsRelatedPromptSaveDialogOpen] = useState(false)

  // Use original prompt data from the prompt prop instead of fetching separately
  const originalPrompt = prompt.originalPrompt

  // Add ref to prevent multiple view recordings in the same session
  const viewRecordedRef = useRef(false)
  
  // Add ref to track share timeout for cleanup
  const shareTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Shared style configuration for all CodeEditor instances with enhanced styling
  const codeEditorStyle = {
    fontSize: '14px',
    backgroundColor: 'hsl(220 13% 18%)', // Slightly different from page background
    color: 'hsl(210 40% 98%)',
    fontFamily: 'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace',
    minHeight: '120px',
    border: 'none',
    padding: '24px', // Increased padding for better readability
    '--color-prettylights-syntax-comment': '#8B949E',
    '--color-prettylights-syntax-constant': '#F97583',
    '--color-prettylights-syntax-entity': '#58A6FF',
    '--color-prettylights-syntax-storage-modifier-import': '#F97583',
    '--color-prettylights-syntax-entity-tag': '#C9D1D9',
    '--color-prettylights-syntax-keyword': '#F97583',
    '--color-prettylights-syntax-string': '#A5D6FF',
    '--color-prettylights-syntax-variable': '#79C0FF',
    '--color-prettylights-syntax-brackethighlighter-unmatched': '#F87171',
    '--color-prettylights-syntax-invalid-illegal-text': '#F87171',
    '--color-prettylights-syntax-invalid-illegal-bg': 'rgba(248, 113, 113, 0.1)',
    '--color-prettylights-syntax-carriage-return-text': 'hsl(210 40% 98%)',
    '--color-prettylights-syntax-carriage-return-bg': 'rgba(248, 113, 113, 0.2)',
    '--color-prettylights-syntax-string-regexp': '#F97583',
    '--color-prettylights-syntax-markup-list': '#C9D1D9',
    '--color-prettylights-syntax-markup-heading': '#C9D1D9',
    '--color-prettylights-syntax-markup-italic': '#F0F6FC',
    '--color-prettylights-syntax-markup-bold': '#F0F6FC',
    '--color-prettylights-syntax-markup-deleted-text': '#F87171',
    '--color-prettylights-syntax-markup-deleted-bg': 'rgba(248, 113, 113, 0.1)',
    '--color-prettylights-syntax-markup-inserted-text': '#4ADE80',
    '--color-prettylights-syntax-markup-inserted-bg': 'rgba(74, 222, 128, 0.1)',
    '--color-prettylights-syntax-markup-changed-text': '#FACC15',
    '--color-prettylights-syntax-markup-changed-bg': 'rgba(250, 204, 21, 0.1)',
    '--color-prettylights-syntax-markup-ignored-text': '#8B949E',
    '--color-prettylights-syntax-markup-ignored-bg': 'rgba(148, 163, 184, 0.1)',
    '--color-prettylights-syntax-meta-diff-range': '#A78BFA',
    '--color-prettylights-syntax-brackethighlighter-angle': '#C9D1D9',
    '--color-prettylights-syntax-sublimelinter-gutter-mark': '#F87171',
  } as const

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      setCurrentUser(session?.user || null)
    }
    fetchUser()
  }, [])

  useEffect(() => {
    let isMounted = true; // Track if component is still mounted
    
    const recordView = async () => {
      // Prevent multiple calls in the same session
      if (viewRecordedRef.current) {
        console.log(`[ViewTracking] View already recorded for this session, skipping`);
        return;
      }

      // Small delay to prevent React Strict Mode double execution
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Check if component is still mounted after delay
      if (!isMounted) {
        console.log(`[ViewTracking] Component unmounted, skipping view recording`);
        return;
      }

      if (prompt?.id) { // Ensure prompt and its ID are available
        console.log(`[ViewTracking] Starting view recording for prompt: ${prompt.id}`);
        try {
          const viewerHash = await getViewerHash(); // Use the new utility
          console.log(`[ViewTracking] Generated viewer hash: ${viewerHash}`);

          if (viewerHash) {
            // Check if this viewer has already viewed this prompt (client-side unique tracking)
            const viewKey = `prompt_viewed_${prompt.id}_${viewerHash}`;
            const hasViewed = localStorage.getItem(viewKey);
            console.log(`[ViewTracking] Checking localStorage key: ${viewKey}, hasViewed: ${hasViewed}`);

            if (!hasViewed) {
              console.log(`[ViewTracking] Recording new view for prompt ${prompt.id}`);
              
              // Mark as recording to prevent duplicate calls
              viewRecordedRef.current = true;
              
              // Record view for this prompt (simple increment)
              const { error: rpcError } = await supabase.rpc('record_prompt_view', {
                p_prompt_id: prompt.id,
                p_viewer_hash: viewerHash
              });

              if (rpcError) {
                console.error("[ViewTracking] Error recording prompt view:", rpcError.message);
                console.error("[ViewTracking] Full error details:", rpcError);
                // Reset the flag on error so it can be retried
                viewRecordedRef.current = false;
                // Non-critical error since view count is hidden from UI
              } else {
                console.log(`[ViewTracking] Successfully recorded view for prompt ${prompt.id}`);
                // Mark this prompt as viewed by this viewer (client-side tracking)
                try {
                  localStorage.setItem(viewKey, 'true');
                  console.log(`[ViewTracking] Marked as viewed in localStorage: ${viewKey}`);
                } catch (e) {
                  console.warn("[ViewTracking] Failed to set view tracking in localStorage:", e);
                  // Continue anyway, view was still recorded on server
                }
              }
            } else {
              console.log(`[ViewTracking] Skipping view recording - already viewed by this viewer`);
            }
          } else {
            console.warn("[ViewTracking] No viewer hash generated, skipping view recording");
          }
        } catch (e) {
          console.error("[ViewTracking] Exception while trying to record prompt view:", e);
          // Reset the flag on error so it can be retried
          viewRecordedRef.current = false;
        }
      } else {
        console.warn("[ViewTracking] No prompt ID available, skipping view recording");
      }
    };

    recordView();

    // Cleanup function to reset the ref when the component unmounts or prompt changes
    return () => {
      isMounted = false;
      viewRecordedRef.current = false;
    };
  }, [prompt?.id]); // Dependency: prompt.id. Runs when prompt data is loaded.

  // This updates our comment tracking state for consecutive comments/replies
  useEffect(() => {
    if (comments.length > 0) {
      // Get the most recent top-level comment to track the last commenter
      const topLevelComments = comments.filter(c => !c.parent_comment_id);
      if (topLevelComments.length > 0) {
        const lastComment = topLevelComments.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];
        setLastCommenterUserId(lastComment.user.id);
      }
      
      // Build a tracking record for consecutive replies by parent
      const replyTracking: Record<string, {userId: string, count: number}> = {};
      comments.forEach(comment => {
        // Get replies for each comment and sort by date
        if (comment.replies && comment.replies.length > 0) {
          const sortedReplies = [...comment.replies].sort((a, b) => 
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
          
          // Check for consecutive replies from the same user
          let lastReplyUserId: string | null = null;
          let consecutiveCount = 0;
          
          sortedReplies.forEach(reply => {
            if (lastReplyUserId === reply.user.id) {
              consecutiveCount++;
            } else {
              lastReplyUserId = reply.user.id;
              consecutiveCount = 1;
            }
          });
          
          // If the most recent replies are from the same user, add to tracking
          if (consecutiveCount > 0 && lastReplyUserId) {
            replyTracking[comment.id] = {
              userId: lastReplyUserId,
              count: consecutiveCount
            };
          }
        }
      });
      
      setConsecutiveRepliesByParent(replyTracking);
    }
  }, [comments]);

  useEffect(() => {
    const fetchComments = async () => {
      if (activeTab === "comments" && !commentsLoaded) {
        setIsLoadingComments(true)
        try {
          const { data: commentsData, error: commentsError } = await supabase
            .from("comment_display_details")
            .select("*")
            .eq("prompt_id", prompt.id)
            .is("parent_comment_id", null)
            .order(commentSortOption === "top" ? "like_count" : "created_at", {
              ascending: commentSortOption === "oldest",
            })

          if (commentsError) {
            console.error("Supabase comments error:", commentsError);
            throw commentsError;
          }
          const topLevelComments: CommentDisplayDetails[] = (commentsData || []) as unknown as CommentDisplayDetails[];

          const { data: repliesData, error: repliesError } = await supabase
            .from("comment_display_details")
            .select("*")
            .eq("prompt_id", prompt.id)
            .not("parent_comment_id", "is", null)
            .order("created_at", { ascending: true })

          if (repliesError) {
            console.error("Supabase replies error:", repliesError);
            throw repliesError;
          }
          const replies: CommentDisplayDetails[] = (repliesData || []) as unknown as CommentDisplayDetails[];

          const { data: { session } } = await supabase.auth.getSession()
          const userId = session?.user?.id
          let userLikedCommentIds = new Set<string>()

          if (userId && (topLevelComments.length > 0 || replies.length > 0)) {
            // Filter out null IDs and create a clean array of string IDs
            const commentIds = [
              ...topLevelComments.map((c) => c.id || '').filter(id => id !== ''),
              ...replies.map((r) => r.id || '').filter(id => id !== '')
            ];
            
            if (commentIds.length > 0) {
              const { data: likesData, error: likesError } = await supabase
                .from("comment_votes")
                .select("comment_id")
                .eq("user_id", userId)
                .eq("vote_type", 1)
                .in("comment_id", commentIds)

              if (likesError) {
                console.error("Supabase likes error:", likesError);
                throw likesError;
              }
              if (likesData) userLikedCommentIds = new Set(likesData.map((like: any) => like.comment_id as string))
            }
          }

          const repliesByParentId = replies.reduce(
            (acc, reply) => {
              const parentId = reply.parent_comment_id;
              if (parentId) {
                if (!acc[parentId]) acc[parentId] = [];
                acc[parentId].push(reply);
              }
              return acc;
            },
            {} as Record<string, CommentDisplayDetails[]>
          );

          // Use our helper function to convert top-level comments
          const processedComments: Comment[] = topLevelComments.map((commentData) => {
            // First convert the base comment
            const baseComment = convertToComment(commentData);
            
            // Then add user-specific data and replies
            return {
              ...baseComment,
              liked_by_user: userLikedCommentIds.has(commentData.id || ''),
              replies: (repliesByParentId[commentData.id || ''] || []).map(replyData => {
                // Convert each reply using the helper function
                const baseReply = convertToComment(replyData);
                
                // Add the user-specific data
                return {
                  ...baseReply,
                  liked_by_user: userLikedCommentIds.has(replyData.id || ''),
                };
              }),
            };
          });
          setComments(processedComments)
          setCommentsLoaded(true)
        } catch (error) {
          console.error("Error fetching comments:", error)
          toast({ title: "Error", description: "Failed to load comments. Please try again.", variant: "destructive" })
        } finally {
          setIsLoadingComments(false)
        }
      }
    }
    fetchComments()
  }, [activeTab, commentsLoaded, commentSortOption, prompt.id, toast])

  useEffect(() => {
    const fetchUserVote = async () => {
      console.log('[PromptDetail] Initializing vote state');
      if (currentUser?.id) {
        console.log(`[PromptDetail] Fetching vote for user ${currentUser.id} on prompt ${prompt.id}`);
        const { voteType } = await getUserVoteOnPromptDirect(currentUser.id, prompt.id);
        console.log(`[PromptDetail] Initial vote state received: ${voteType}`);
        
        // Convert numeric vote type to string representation for UI
        if (voteType === 1) {
          console.log('[PromptDetail] Setting initial vote state to "up"');
          setUserVote("up");
        } else if (voteType === -1) {
          console.log('[PromptDetail] Setting initial vote state to "down"');
          setUserVote("down");
        } else {
          console.log('[PromptDetail] Setting initial vote state to null (no vote)');
          setUserVote(null);
        }
      } else {
        console.log('[PromptDetail] No user logged in, vote state will remain null');
      }
    };
    
    fetchUserVote();
  }, [currentUser, prompt.id]);

  useEffect(() => {
    const success = searchParams.get('success');
    if (success) {
      toast({ title: "Success", description: success, variant: "default" });
    }
  }, [searchParams, toast]);

  // Fetch related prompts when the related tab is activated
  useEffect(() => {
    const fetchRelatedData = async () => {
      if (activeTab === "related" && !relatedPromptsLoaded && !isLoadingRelated) {
        if (!prompt.shortId) {
          console.error("[PromptDetailView] Source prompt shortId is missing. Cannot fetch related prompts.");
          setErrorRelated("Cannot load related prompts: Source information is missing.");
          setRelatedPromptsLoaded(true); // Prevent retrying if source ID is missing
          return;
        }
        setIsLoadingRelated(true);
        setErrorRelated(null);
        try {
          const data = await getRelatedPromptsForDisplay(prompt.shortId, 6);
          setRelatedPrompts(data);
          setRelatedPromptsLoaded(true);
        } catch (error: any) {
          console.error("Error fetching related prompts in component:", error);
          setErrorRelated(error.message || "Failed to load related prompts.");
          setRelatedPrompts([]);
        } finally {
          setIsLoadingRelated(false);
        }
      }
    };
    fetchRelatedData();
  }, [activeTab, prompt.shortId, relatedPromptsLoaded, isLoadingRelated]);

  const handleTabChange = (value: string) => setActiveTab(value as "about" | "comments" | "related")

  const handleUpvote = async () => {
    console.log('[PromptDetail] Upvote button clicked');
    if (!currentUser) {
      console.log('[PromptDetail] Upvote rejected - user not logged in');
      setShowCommunityModal(true);
      return;
    }
    console.log(`[PromptDetail] Processing upvote for prompt ${prompt.id} by user ${currentUser.id}`);

    // Optimistically update UI
    const previousVote = userVote;
    const previousCount = voteCount;
    
    if (userVote === "up") {
      console.log('[PromptDetail] Removing existing upvote');
      // Remove upvote
      setVoteCount(voteCount - 1);
      setUserVote(null);
      
      // Call API to update vote in database (0 = remove vote)
      console.log('[PromptDetail] Calling API to remove vote');
      const { success, updatedVoteCount, error } = await voteOnPromptDirect(currentUser.id, prompt.id, 0);
      console.log(`[PromptDetail] API response: success=${success}, updatedVoteCount=${updatedVoteCount}, error=${error || 'none'}`);
      
      if (!success) {
        // Revert to previous state if API call fails
        console.log('[PromptDetail] Vote removal failed, reverting UI state');
        setVoteCount(previousCount);
        setUserVote(previousVote);
        toast({
          title: "Vote Failed",
          description: error || "Failed to update vote. Please try again.",
          variant: "destructive"
        });
      } else if (updatedVoteCount !== undefined) {
        // Update with accurate count from server
        console.log(`[PromptDetail] Updating vote count to server value: ${updatedVoteCount}`);
        setVoteCount(updatedVoteCount);
      }
    } else {
      // Add upvote or change from downvote to upvote
      console.log(`[PromptDetail] ${userVote === "down" ? "Changing downvote to upvote" : "Adding new upvote"}`);
      setVoteCount(voteCount + (userVote === "down" ? 2 : 1));
      setUserVote("up");
      
      // Call API to update vote in database (1 = upvote)
      console.log('[PromptDetail] Calling API to add upvote');
      const { success, updatedVoteCount, error } = await voteOnPromptDirect(currentUser.id, prompt.id, 1);
      console.log(`[PromptDetail] API response: success=${success}, updatedVoteCount=${updatedVoteCount}, error=${error || 'none'}`);
      
      if (!success) {
        // Revert to previous state if API call fails
        console.log('[PromptDetail] Upvote failed, reverting UI state');
        setVoteCount(previousCount);
        setUserVote(previousVote);
        toast({
          title: "Vote Failed",
          description: error || "Failed to update vote. Please try again.",
          variant: "destructive"
        });
      } else if (updatedVoteCount !== undefined) {
        // Update with accurate count from server
        console.log(`[PromptDetail] Updating vote count to server value: ${updatedVoteCount}`);
        setVoteCount(updatedVoteCount);
      }
    }
  }

  const handleDownvote = async () => {
    console.log('[PromptDetail] Downvote button clicked');
    if (!currentUser) {
      console.log('[PromptDetail] Downvote rejected - user not logged in');
      setShowCommunityModal(true);
      return;
    }
    console.log(`[PromptDetail] Processing downvote for prompt ${prompt.id} by user ${currentUser.id}`);

    // Optimistically update UI
    const previousVote = userVote;
    const previousCount = voteCount;
    
    if (userVote === "down") {
      console.log('[PromptDetail] Removing existing downvote');
      // Remove downvote
      setVoteCount(voteCount + 1);
      setUserVote(null);
      
      // Call API to update vote in database (0 = remove vote)
      console.log('[PromptDetail] Calling API to remove vote');
      const { success, updatedVoteCount, error } = await voteOnPromptDirect(currentUser.id, prompt.id, 0);
      console.log(`[PromptDetail] API response: success=${success}, updatedVoteCount=${updatedVoteCount}, error=${error || 'none'}`);
      
      if (!success) {
        // Revert to previous state if API call fails
        console.log('[PromptDetail] Vote removal failed, reverting UI state');
        setVoteCount(previousCount);
        setUserVote(previousVote);
        toast({
          title: "Vote Failed",
          description: error || "Failed to update vote. Please try again.",
          variant: "destructive"
        });
      } else if (updatedVoteCount !== undefined) {
        // Update with accurate count from server
        console.log(`[PromptDetail] Updating vote count to server value: ${updatedVoteCount}`);
        setVoteCount(updatedVoteCount);
      }
    } else {
      // Add downvote or change from upvote to downvote
      console.log(`[PromptDetail] ${userVote === "up" ? "Changing upvote to downvote" : "Adding new downvote"}`);
      setVoteCount(voteCount - (userVote === "up" ? 2 : 1));
      setUserVote("down");
      
      // Call API to update vote in database (-1 = downvote)
      console.log('[PromptDetail] Calling API to add downvote');
      const { success, updatedVoteCount, error } = await voteOnPromptDirect(currentUser.id, prompt.id, -1);
      console.log(`[PromptDetail] API response: success=${success}, updatedVoteCount=${updatedVoteCount}, error=${error || 'none'}`);
      
      if (!success) {
        // Revert to previous state if API call fails
        console.log('[PromptDetail] Downvote failed, reverting UI state');
        setVoteCount(previousCount);
        setUserVote(previousVote);
        toast({
          title: "Vote Failed",
          description: error || "Failed to update vote. Please try again.",
          variant: "destructive"
        });
      } else if (updatedVoteCount !== undefined) {
        // Update with accurate count from server
        console.log(`[PromptDetail] Updating vote count to server value: ${updatedVoteCount}`);
        setVoteCount(updatedVoteCount);
      }
    }
  }

  const handleCopyPrompt = () => {
    if (!prompt) return
    navigator.clipboard.writeText(prompt.text || "")
    setIsCopying(true)
    setTimeout(() => setIsCopying(false), 2000)
    toast({ title: "Prompt copied", description: "Prompt text copied to clipboard" })
  }

  const handleShare = () => {
    // Prevent multiple concurrent executions
    if (isSharing) {
      return
    }
    
    // Clear any existing timeout to avoid overlapping timers
    if (shareTimeoutRef.current) {
      clearTimeout(shareTimeoutRef.current)
      shareTimeoutRef.current = null
    }
    
    setIsSharing(true)
    
    try {
      navigator.clipboard.writeText(window.location.href)
      toast({ title: "Link copied to clipboard", description: "Share this amazing prompt with others!" })
      setShareConfirmed(true)
    } catch (error) {
      console.error("Failed to copy to clipboard:", error)
      toast({ 
        title: "Copy failed", 
        description: "Unable to copy link to clipboard. Please copy the URL manually.", 
        variant: "destructive" 
      })
      setIsSharing(false)
      return
    }
    
    shareTimeoutRef.current = setTimeout(() => {
      setIsSharing(false)
      setShareConfirmed(false)
      shareTimeoutRef.current = null
    }, 2000)
  }

  // Handle community modal actions
  const handleCommunityModalLogin = () => {
    setShowCommunityModal(false)
    router.push("/sign-in")
  }

  const handleCommunityModalSignup = () => {
    setShowCommunityModal(false)
    router.push("/sign-up")
  }

  // Check if user is logged in for various actions
  const requireAuth = (action: () => void) => {
    if (!currentUser) {
      setShowCommunityModal(true)
    } else {
      action()
    }
  }

  const safeFormatDate = (dateString: string | Date | undefined | null) => {
    if (!dateString) return "Unknown date"
    try {
      const date = typeof dateString === "string" ? new Date(dateString) : dateString
      if (isNaN(date.getTime())) return "Unknown date"
      return formatDistanceToNow(date, { addSuffix: true })
    } catch (error) {
      console.error("Error formatting date:", error)
      return "Unknown date"
    }
  }

  const handleCommentLike = async (commentId: string) => {
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.user) {
      setShowCommunityModal(true)
      return
    }

    const user = session.user

    let commentIndex = -1, isReply = false, parentIndex = -1, replyIndex = -1;
    commentIndex = comments.findIndex((c) => c.id === commentId)
    if (commentIndex === -1) {
      for (let i = 0; i < comments.length; i++) {
        const comment = comments[i];
        if (comment.replies && Array.isArray(comment.replies)) {
          replyIndex = comment.replies.findIndex((r) => r.id === commentId);
          if (replyIndex !== -1) {
            isReply = true; parentIndex = i; break;
          }
        }
      }
    }
    if (commentIndex === -1 && !isReply) return

    let currentComment: Comment | undefined;
    let isCurrentlyLiked: boolean | undefined;

    if (isReply) {
      if (comments[parentIndex].replies && Array.isArray(comments[parentIndex].replies)) {
        currentComment = comments[parentIndex].replies![replyIndex]
        isCurrentlyLiked = currentComment.liked_by_user
      } else { return; }
    } else {
      currentComment = comments[commentIndex]
      isCurrentlyLiked = currentComment.liked_by_user
    }
    if (!currentComment) return;

    const originalComments = [...comments]; // Keep a copy for potential revert
    const updatedComments = JSON.parse(JSON.stringify(comments)); // Deep copy for modification


    if (isReply) {
      if (updatedComments[parentIndex].replies && Array.isArray(updatedComments[parentIndex].replies)) {
        updatedComments[parentIndex].replies[replyIndex] = {
          ...currentComment,
          liked_by_user: !isCurrentlyLiked,
          likes: isCurrentlyLiked ? (currentComment.likes || 1) - 1 : (currentComment.likes || 0) + 1,
        }
      } else { return; }
    } else {
      updatedComments[commentIndex] = {
        ...currentComment,
        liked_by_user: !isCurrentlyLiked,
        likes: isCurrentlyLiked ? (currentComment.likes || 1) - 1 : (currentComment.likes || 0) + 1,
      }
    }
    setComments(updatedComments)

    try {
      if (isCurrentlyLiked) {
        await supabase.from("comment_votes").delete().eq("user_id", user.id).eq("comment_id", commentId)
      } else {
        await supabase.from("comment_votes").upsert({
          user_id: user.id, comment_id: commentId, vote_type: 1, created_at: new Date().toISOString(),
        })
      }
    } catch (error) {
      console.error("Error updating comment like:", error)
      setComments(originalComments) // Revert optimistic update
      toast({ title: "Error", description: "Failed to update like status. Please try again.", variant: "destructive" })
    }
  };

  // ...

  const handlePostComment = async () => {
    if (!currentUser || !newCommentText.trim()) return;
    
    // Check if the last comment was from the current user (can't comment twice in a row)
    if (lastCommenterUserId === currentUser.id) {
      setProfanityError("You can't post two comments in a row. Please wait for someone else to respond.");
      return;
    }

    // Check for profanity
    if (containsProfanity(newCommentText.trim())) {
      // Record the profanity attempt
      const result = recordProfanityAttempt(currentUser.id);
      if (result.onTimeout) {
        setIsOnTimeout(true);
        setTimeoutRemaining(10 * 60); // 10 minutes in seconds
        setProfanityError(`Your comment wasn't appropriate. You won't be able to comment for a while.`);
        
        // Set up timer to check when timeout is over
        const timer = setInterval(() => {
          if (!isUserOnTimeout(currentUser.id)) {
            clearInterval(timer);
            setIsOnTimeout(false);
            setProfanityError(""); // Set to empty string instead of null
            setTimeoutRemaining(0);
          }
        }, 30000); // Check every 30 seconds
      } else {
        setProfanityError(`Your comment contains inappropriate language. Please revise your wording.`);
      }

      setIsPostingComment(false);
      return;
    }

    // Clear any previous profanity errors
    setProfanityError(null);

    setIsPostingComment(true);
    try {
      // Use the add_comment RPC function instead of direct table insert
      const { data: commentId, error } = await supabase.rpc('add_comment', {
        p_user_id: currentUser.id,
        p_prompt_id: prompt.id,
        p_text: newCommentText.trim()
      }) as { data: string | null, error: any };

      if (error) {
        // Handle specific error codes from the add_comment function
        let errorMessage = "Could not post your comment due to a server error. Please try again later.";
        
        if (error.code === 'P0001' || error.message?.includes('Prompt not found or not public')) {
          errorMessage = "This prompt is not available for commenting. It may have been removed or made private.";
        } else if (error.code === 'P0002' || error.message?.includes('Parent comment not found')) {
          errorMessage = "The comment you are trying to reply to is no longer available or has been moved.";
        }
        
        toast({
          variant: "destructive",
          title: "Error",
          description: errorMessage,
        });
        return;
      }

      if (!commentId) {
        throw new Error("Failed to create comment - no ID returned");
      }

      // Create a temporary comment object to add to the UI immediately
      const newComment: Comment = {
        id: commentId,
        prompt_id: typeof prompt.id === 'string' ? prompt.id : '',
        text: newCommentText.trim(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user: {
          id: currentUser.id,
          username: currentUser.user_metadata?.username || 'User',
          avatar_url: currentUser.user_metadata?.avatar_url,
        } as Profile,
        likes: 0,
        dislikes: 0,
        liked_by_user: false,
        replies: [],
      };

      // Add the new comment to the list and mark it as newly added
      setComments(prev => [newComment, ...prev]);
      setNewlyAddedCommentId(commentId);
      
      // Update the last commenter tracking
      setLastCommenterUserId(currentUser.id);

      // Clear the input
      setNewCommentText('');

      toast({
        title: "Comment posted",
        description: "Your comment has been posted successfully.",
      });
    } catch (error) {
      console.error("Error posting comment:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to post your comment. Please try again.",
      });
    } finally {
      setIsPostingComment(false);
    }
  };

  // Function to check if a comment has been edited
  const hasBeenEdited = (createdAt: string, updatedAt: string) => {
    // If the timestamps are the same, it hasn't been edited
    if (createdAt === updatedAt) return false;
    
    // Parse the dates
    const created = new Date(createdAt);
    const updated = new Date(updatedAt);
    
    // Calculate difference in seconds
    const diffSeconds = Math.floor((updated.getTime() - created.getTime()) / 1000);
    
    // If the difference is very small (less than 5 seconds), it's likely just the initial creation
    // This handles cases where the database might set updated_at automatically on creation
    if (diffSeconds < 5) return false;
    
    // Otherwise, it has been edited
    return true;
  }
  
  // Function to check if a comment is within the 5-minute edit window
  const isWithinEditWindow = (createdAt: string) => {
    const commentDate = new Date(createdAt);
    const now = new Date();
    // Calculate the difference in milliseconds
    const diffMs = now.getTime() - commentDate.getTime();
    // Convert to minutes
    const diffMins = Math.floor(diffMs / 60000);
    // Return true if less than 5 minutes
    return diffMins < 5;
  }
  
  // Function to handle starting comment edit
  const handleStartEditComment = (commentId: string, currentText: string) => {
    setEditingCommentId(commentId);
    setEditText(currentText);
  }
  
  // Function to handle canceling comment edit
  const handleCancelEditComment = () => {
    setEditingCommentId(null);
    setEditText("");
  }
  
  // Function to handle saving comment edit
  const handleSaveEditComment = async (commentId: string, isReply: boolean = false, parentId?: string) => {
    if (!currentUser || !editText.trim()) return;
    
    setIsEditingComment(true);
    
    try {
      // Optimistic UI update
      if (isReply && parentId) {
        // For replies, update the reply text
        setComments(prevComments => 
          prevComments.map(comment => 
            comment.id === parentId 
              ? { 
                  ...comment, 
                  replies: comment.replies?.map(reply => 
                    reply.id === commentId 
                      ? { ...reply, text: editText.trim(), updated_at: new Date().toISOString() } 
                      : reply
                  ) 
                } 
              : comment
          )
        );
      } else {
        // For top-level comments, update the comment text
        setComments(prevComments => 
          prevComments.map(comment => 
            comment.id === commentId 
              ? { ...comment, text: editText.trim(), updated_at: new Date().toISOString() } 
              : comment
          )
        );
      }

      // Update in database
      const { error } = await supabase
        .from("comments")
        .update({ 
          text: editText.trim(),
          updated_at: new Date().toISOString()
        })
        .eq("id", commentId)
        .eq("user_id", currentUser.id); // Ensure only the owner can edit

      if (error) throw error;
      
      setEditingCommentId(null);
      setEditText("");
      toast({ title: "Comment updated", description: "Your comment has been updated." });
    } catch (error) {
      console.error("Error updating comment:", error);
      toast({ title: "Error", description: "Failed to update comment. Please try again.", variant: "destructive" });
      // Revert optimistic update by refreshing comments
      setCommentsLoaded(false);
    } finally {
      setIsEditingComment(false);
    }
  }
  
  // Function to handle comment deletion
  const handleDeleteComment = async (commentId: string, isReply: boolean = false, parentId?: string) => {
    if (!currentUser) return;

    try {
      // Optimistic UI update
      if (isReply && parentId) {
        // For replies, filter them out from the parent comment
        setComments(prevComments => 
          prevComments.map(comment => 
            comment.id === parentId 
              ? { 
                  ...comment, 
                  replies: comment.replies?.filter(reply => reply.id !== commentId)
                } 
              : comment
          )
        );
      } else {
        // For top-level comments, remove them completely
        setComments(prevComments => prevComments.filter(comment => comment.id !== commentId));
      }

      // Update comment count in UI
      if (prompt.commentCount) {
        const newCount = prompt.commentCount - 1;
        prompt.commentCount = newCount > 0 ? newCount : 0;
      }

      // Delete from database
      const { error } = await supabase
        .from("comments")
        .delete()
        .eq("id", commentId)
        .eq("user_id", currentUser.id); // Ensure only the owner can delete

      if (error) throw error;
      
      toast({ title: "Comment deleted", description: "Your comment has been deleted." });
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast({ title: "Error", description: "Failed to delete comment. Please try again.", variant: "destructive" });
      // Revert optimistic update by refreshing comments
      setCommentsLoaded(false);
    }
  }

  const handlePostReply = async (parentId: string) => {
    if (!currentUser || !replyText.trim()) return;
    
    // Check if user has already replied twice in a row to this comment
    const replyTracking = consecutiveRepliesByParent[parentId];
    if (replyTracking && replyTracking.userId === currentUser.id && replyTracking.count >= 2) {
      setProfanityError("You can't reply more than twice in a row to the same comment. Please wait for someone else to respond.");
      return;
    }
    
    setIsPostingReply(true);
    try {
      // Check for profanity
      if (containsProfanity(replyText.trim())) {
        // Record the profanity attempt
        const result = recordProfanityAttempt(currentUser.id);
        if (result.onTimeout) {
          setIsOnTimeout(true);
          setTimeoutRemaining(10 * 60); // 10 minutes in seconds
          setProfanityError(`Your comment wasn't appropriate. You won't be able to comment for a while.`);
        
          // Set up timer to check when timeout is over
          const timer = setInterval(() => {
            if (!isUserOnTimeout(currentUser.id)) {
              clearInterval(timer);
              setIsOnTimeout(false);
              setProfanityError(""); // Set to empty string instead of null
              setTimeoutRemaining(0);
            }
          }, 30000); // Check every 30 seconds
        } else {
          setProfanityError(`Your reply contains inappropriate language. Please revise your wording.`);
        }

        setIsPostingReply(false);
        return;
      }

      // Clear any previous profanity errors
      setProfanityError(null);

      // Use the add_comment RPC function for replies
      const { data: replyId, error } = await supabase.rpc('add_comment', {
        p_user_id: currentUser.id,
        p_prompt_id: prompt.id,
        p_text: replyText.trim(),
        p_parent_comment_id: parentId
      }) as { data: string | null, error: any };

      if (error) {
        // Handle specific error codes from the add_comment function
        let errorMessage = "Could not post your reply due to a server error. Please try again later.";
        
        if (error.code === 'P0001' || error.message?.includes('Prompt not found or not public')) {
          errorMessage = "This prompt is not available for commenting. It may have been removed or made private.";
        } else if (error.code === 'P0002' || error.message?.includes('Parent comment not found')) {
          errorMessage = "The comment you are trying to reply to is no longer available or has been moved.";
        }
        
        toast({
          variant: "destructive",
          title: "Error",
          description: errorMessage,
        });
        return;
      }

      if (!replyId) {
        throw new Error("Failed to create reply - no ID returned");
      }

      // Fetch the newly created reply with full details using the view
      const { data: newReplyData, error: fetchError } = await supabase
        .from("comment_display_details")
        .select("*")
        .eq("id", replyId)
        .single();

      if (fetchError) throw fetchError;

      if (newReplyData) {
        // Use the helper function to convert to Comment type
        // Ensure proper typing for newReplyData by using a type assertion
        // This is safe because we know the structure from the database schema
        const typedReplyData = newReplyData as Record<string, any>;
        const newReplyEntry = convertToComment(typedReplyData as CommentDisplayDetails);

        setComments(prevComments =>
          prevComments.map(comment =>
            comment.id === parentId
              ? { ...comment, replies: [...(comment.replies || []), newReplyEntry] }
              : comment
          )
        );
        
        // Update consecutive reply tracking for this parent
        setConsecutiveRepliesByParent(prev => {
          const current = prev[parentId];
          if (current && current.userId === currentUser.id) {
            return {
              ...prev,
              [parentId]: { userId: currentUser.id, count: current.count + 1 }
            };
          } else {
            return {
              ...prev,
              [parentId]: { userId: currentUser.id, count: 1 }
            };
          }
        });
        
        setReplyText("");
        setReplyingToCommentId(null);
        toast({ title: "Reply posted", description: "Your reply has been added." });
      }
    } catch (error) {
      console.error("Error posting reply:", error);
      toast({ title: "Error", description: "Failed to post reply. Please try again.", variant: "destructive" });
    } finally {
      setIsPostingReply(false);
    }
  };

  // ...

  const sortedComments = [...comments].sort((a, b) => {
    // Always show newly added comment at the top
    if (a.id === newlyAddedCommentId) return -1;
    if (b.id === newlyAddedCommentId) return 1;

    // Then apply the selected sort option
    if (commentSortOption === "top") return (b.likes || 0) - (a.likes || 0);
    const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
    const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
    if (commentSortOption === "newest") return dateB - dateA;
    return dateA - dateB;
  });

  // Limit to 5 top-level comments unless showing all
  const displayedComments = showAllComments ? sortedComments : sortedComments.slice(0, 5)

  const categoryName = typeof prompt.category === "string" ? prompt.category : prompt.category?.name ?? "Other"
  const categorySlug = typeof prompt.category === "string" ? prompt.category.toLowerCase() : prompt.category?.slug ?? "other"
  const toolName = prompt.tool?.name ?? "Other"
  const toolSlug = prompt.tool?.slug ?? "other"
  const formattedDate = prompt.createdAt ? new Date(prompt.createdAt).toLocaleDateString("en-US", { year: "numeric", month: "long", day: "numeric" }) : "Unknown date"
  const username = prompt.user?.username || prompt.author || "anonymous"
  const categoryDisplayColor = getCategoryColorBySlug(categorySlug)?.primary || 'var(--accent-purple)';

  // Handle save toggle for related prompts
  const handleRelatedPromptToggleSave = async (promptId: string, currentSaveStatus: boolean) => {
    requireAuth(() => {
      // Find the prompt to get its title
      const relatedPrompt = relatedPrompts.find(p => p.id === promptId)
      if (relatedPrompt) {
        setSelectedRelatedPromptId(promptId)
        setSelectedRelatedPromptTitle(relatedPrompt.title)
        setIsRelatedPromptSaveDialogOpen(true)
      }
    })
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4">
        <div className="flex items-center gap-2 py-4 text-sm text-muted-foreground">
          <Link href="/" className="hover:text-primary">Home</Link>
          <span>{'>'}</span>
          <Link href={`/category/${categorySlug}`} className="hover:text-primary">{categoryName}</Link>
        </div>

        <div className="mb-6">
          <h1 className="text-3xl font-bold text-foreground">{prompt.title}</h1>
        </div>

        <div className="grid grid-cols-1 gap-6 pb-8 lg:grid-cols-3"> {/* Changed to lg:grid-cols-3 for main content and sidebar */}
          {/* Left column - Main content */}
          <div className="lg:col-span-2"> {/* Main content takes 2/3 */}
            {prompt.imageUrl && (
              <Dialog>
                <DialogTrigger asChild>
                  <div className="relative mb-4 h-[300px] w-full cursor-pointer overflow-hidden rounded-md">
                    <Image 
                      src={prompt.imageUrl || "/placeholder.svg"} 
                      alt={`${prompt.title} - Example image`}
                      fill 
                      className="object-cover" 
                      priority={true}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 66vw, 600px"
                      quality={90}
                    />
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-4xl border-border bg-background">
                  <DialogHeader>
                    <DialogTitle>{prompt.title}</DialogTitle>
                    <DialogDescription>Full-size image preview.</DialogDescription>
                  </DialogHeader>
                  <div className="relative h-[80vh] w-full">
                    <Image 
                      src={prompt.imageUrl || "/placeholder.svg"} 
                      alt={`${prompt.title} - Full size image`}
                      fill 
                      className="object-contain"
                      sizes="90vw"
                      quality={95}
                    />
                  </div>
                </DialogContent>
              </Dialog>
            )}

            {prompt.description && <p className="mb-4 text-sm text-muted-foreground">{prompt.description}</p>}

            <div className="mb-6">
              <div className="border-b border-border">
                <div className="flex">
                  {[
                    { id: "about", label: "About This Prompt", count: null },
                    { id: "comments", label: "Comments", count: prompt.commentCount },
                    { id: "related", label: "Related Prompts", count: null },
                  ].map((tab) => (
                    <Button
                      key={tab.id}
                      variant="ghost"
                      className={`flex-1 rounded-none border-b-2 py-4 font-medium transition-colors ${
                        activeTab === tab.id
                          ? "border-primary text-primary bg-primary/5"
                          : "border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/50"
                      }`}
                      onClick={() => handleTabChange(tab.id)}
                    >
                      <span className="flex items-center gap-2">
                        {tab.label}
                        {tab.count !== null && (
                          <Badge 
                            variant="secondary" 
                            className={`text-xs ${
                              activeTab === tab.id 
                                ? "bg-primary/20 text-primary" 
                                : "bg-muted text-muted-foreground"
                            }`}
                          >
                            {formatStatNumber(tab.count)}
                          </Badge>
                        )}
                      </span>
                    </Button>
                  ))}
                </div>
              </div>

              <div className="mt-4 rounded-md bg-card p-6">
                {activeTab === 'about' && (
                  <>
                    <div className="mb-6">
                      <div className="mb-2 flex items-center justify-between">
                        <h2 className="text-lg font-semibold text-foreground">Prompt</h2>
                        <div className="flex gap-2">
                          {currentUser && currentUser.id === prompt.user?.id && (
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-green-500/50 text-foreground hover:bg-green-500/20"
                                onClick={() => router.push(`/prompt/edit/${prompt.shortId}`)}
                              >
                                <Pencil className="mr-2 inline h-4 w-4" />
                                Edit
                              </Button>
                            </>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-blue-500/50 text-foreground hover:bg-blue-500/20"
                            onClick={handleCopyPrompt}
                            disabled={isCopying}
                          >
                            {isCopying ? <Check className="mr-2 inline h-4 w-4" /> : <Copy className="mr-2 inline h-4 w-4" />}
                            {isCopying ? "Copied" : "Copy"}
                          </Button>
                        </div>
                      </div>
                      <p className="mb-2 text-sm text-muted-foreground">Copy and use this prompt with {toolName}</p>
                      <div className="rounded-md bg-muted/30 p-6 border">
                        <pre className="whitespace-pre-wrap font-mono text-sm text-muted-foreground">
                          {highlightDoubleBracketedText(prompt.text)}
                        </pre>
                      </div>
                    </div>
                    {prompt.instructions && (
                      <div className="mb-6">
                        <h2 className="mb-2 text-lg font-semibold text-foreground">Instructions</h2>
                        <div className="rounded-md bg-muted/30 p-6 border">
                          <pre className="whitespace-pre-wrap font-mono text-sm text-muted-foreground">
                            {highlightDoubleBracketedText(prompt.instructions)}
                          </pre>
                        </div>
                      </div>
                    )}
                    {prompt.exampleInput && (
                      <div className="mb-6">
                        <h2 className="mb-2 text-lg font-semibold text-foreground">Example Input</h2>
                        <div className="rounded-md bg-muted/30 p-6 border">
                          <pre className="whitespace-pre-wrap font-mono text-sm text-muted-foreground">
                            {highlightDoubleBracketedText(prompt.exampleInput)}
                          </pre>
                        </div>
                      </div>
                    )}
                    {prompt.exampleOutput && (
                      <div className="mb-6">
                        <h2 className="mb-2 text-lg font-semibold text-foreground">Example Output</h2>
                        <div className="rounded-md bg-muted/30 p-6 border">
                          <pre className="whitespace-pre-wrap font-mono text-sm text-muted-foreground">
                            {highlightDoubleBracketedText(prompt.exampleOutput)}
                          </pre>
                        </div>
                      </div>
                    )}
                  </>
                )}
                {activeTab === 'comments' && (
                  <>
                    {isLoadingComments ? (
                      <div className="flex h-32 items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center justify-between">
                          <h2 className="text-lg font-semibold text-foreground">Comments ({formatStatNumber(prompt.commentCount)})</h2>
                          <Select value={commentSortOption} onValueChange={(value: "top" | "newest" | "oldest") => { setCommentSortOption(value); setCommentsLoaded(false); }}>
                            <SelectTrigger className="w-[180px]"><SelectValue placeholder="Sort by" /></SelectTrigger>
                            <SelectContent>
                              <SelectItem value="top">Top</SelectItem>
                              <SelectItem value="newest">Newest</SelectItem>
                              <SelectItem value="oldest">Oldest</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <Separator className="my-4" />
                        {comments.length > 0 ? (
                          <div className="space-y-6">
                            {displayedComments.map((comment) => (
                              <div key={comment.id} className={`flex gap-4 rounded-md bg-muted/50 p-4 ${comment.id === newlyAddedCommentId ? 'border-2 border-primary/50' : ''}`}>
                                <Link href={`/user/${comment.user?.username || 'anonymous'}`} className="flex-shrink-0">
                                  <Avatar className="h-10 w-10 bg-card text-foreground">
                                    <AvatarImage src={comment.user?.avatar_url || "/placeholder-user.jpg"} />
                                    <AvatarFallback>{comment.user?.username?.charAt(0).toUpperCase() || "A"}</AvatarFallback>
                                  </Avatar>
                                </Link>
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    <Link href={`/user/${comment.user?.username || 'anonymous'}`} className="font-semibold text-foreground hover:underline">
                                      {comment.user?.username || "Anonymous"}
                                    </Link>
                                    <span className="text-sm text-muted-foreground">{safeFormatDate(comment.created_at)}</span>
                                    {hasBeenEdited(comment.created_at, comment.updated_at) && (
                                      <span className="text-xs text-muted-foreground">(edited)</span>
                                    )}
                                  </div>
                                  {editingCommentId === comment.id ? (
                                    <div className="mt-2">
                                      <Textarea
                                        value={editText}
                                        onChange={(e) => setEditText(e.target.value)}
                                        rows={3}
                                        className="mb-2"
                                        disabled={isEditingComment}
                                      />
                                      <div className="flex justify-end gap-2">
                                        <Button variant="ghost" size="sm" onClick={handleCancelEditComment} disabled={isEditingComment}>
                                          <X className="mr-1 h-4 w-4" /> Cancel
                                        </Button>
                                        <Button size="sm" onClick={() => handleSaveEditComment(comment.id)} disabled={isEditingComment || !editText.trim()}>
                                          {isEditingComment ? <Loader2 className="mr-1 h-4 w-4 animate-spin" /> : <Check className="mr-1 h-4 w-4" />}
                                          Save
                                        </Button>
                                      </div>
                                    </div>
                                  ) : (
                                    <p className="mt-1 text-muted-foreground">{comment.text}</p>
                                  )}
                                  
                                  <div className="mt-3 flex items-center gap-2">
                                    <Button variant="ghost" size="sm" className={`flex items-center gap-1 ${comment.liked_by_user ? "text-primary" : "text-muted-foreground hover:text-primary/80"}`} onClick={() => handleCommentLike(comment.id)}>
                                      <ThumbsUp className={`h-4 w-4 ${comment.liked_by_user ? "fill-primary" : ""}`} />
                                      <span>{comment.likes || 0}</span>
                                    </Button>
                                    <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-primary/80" onClick={() => requireAuth(() => { setReplyingToCommentId(replyingToCommentId === comment.id ? null : comment.id); setReplyText(""); })}>
                                      <MessageSquare className="mr-1 h-4 w-4" /> Reply
                                    </Button>
                                    
                                    {/* Edit button - only show if user is the author and within 5 minute window */}
                                    {currentUser && currentUser.id === comment.user?.id && isWithinEditWindow(comment.created_at) && (
                                      <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-amber-500" onClick={() => handleStartEditComment(comment.id, comment.text)}>
                                        <Pencil className="mr-1 h-4 w-4" /> Edit
                                      </Button>
                                    )}
                                    
                                    {/* Delete button - only show if user is the author */}
                                    {currentUser && currentUser.id === comment.user?.id && (
                                      <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-red-500" onClick={() => handleDeleteComment(comment.id)}>
                                        <Trash2 className="mr-1 h-4 w-4" /> Delete
                                      </Button>
                                    )}
                                  </div>
                                  {/* Reply Input */}
                                  {replyingToCommentId === comment.id && currentUser && (
                                    <div className="mt-3 flex items-start gap-3 pl-0">
                                      <Link href={`/user/${currentUser.user_metadata?.username || currentUser.email?.split('@')[0] || 'anonymous'}`} className="flex-shrink-0">
                                        <Avatar className="h-8 w-8 bg-card text-foreground">
                                          <AvatarImage src={currentUser.user_metadata?.avatar_url || "/placeholder-user.jpg"} />
                                          <AvatarFallback>{currentUser.user_metadata?.username?.charAt(0).toUpperCase() || currentUser.email?.charAt(0).toUpperCase() || "U"}</AvatarFallback>
                                        </Avatar>
                                      </Link>
                                      <div className="flex-1">
                                        <Textarea
                                          placeholder={`Replying to ${comment.user?.username || "Anonymous"}...`}
                                          value={replyText}
                                          onChange={(e) => setReplyText(e.target.value)}
                                          rows={2}
                                          className="mb-2"
                                          disabled={isPostingReply}
                                        />
                                        <div className="flex justify-end gap-2">
                                          <Button variant="ghost" size="sm" onClick={() => setReplyingToCommentId(null)} disabled={isPostingReply}>Cancel</Button>
                                          <Button size="sm" onClick={() => handlePostReply(comment.id)} disabled={isPostingReply || !replyText.trim()}>
                                            {isPostingReply ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Send className="mr-2 h-4 w-4" />}
                                            Post Reply
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  )}

                                  {comment.replies && comment.replies.length > 0 && (
                                    <div className="mt-4 space-y-4">
                                      {/* Only show up to 3 replies per comment */}
                                      {comment.replies.slice(0, 3).map((reply) => (
                                        <div key={reply.id} className="flex gap-4 rounded-md bg-muted/30 p-3">
                                          <Link href={`/user/${reply.user?.username || 'anonymous'}`} className="flex-shrink-0">
                                            <Avatar className="h-8 w-8 bg-card text-foreground">
                                              <AvatarImage src={reply.user?.avatar_url || "/placeholder-user.jpg"} />
                                              <AvatarFallback>{reply.user?.username?.charAt(0).toUpperCase() || "A"}</AvatarFallback>
                                            </Avatar>
                                          </Link>
                                          <div className="flex-1">
                                            <div className="flex items-center gap-2">
                                              <Link href={`/user/${reply.user?.username || 'anonymous'}`} className="font-semibold text-foreground hover:underline">
                                                {reply.user?.username || "Anonymous"}
                                              </Link>
                                              <span className="text-sm text-muted-foreground">{safeFormatDate(reply.created_at)}</span>
                                              {hasBeenEdited(reply.created_at, reply.updated_at) && (
                                                <span className="text-xs text-muted-foreground">(edited)</span>
                                              )}
                                            </div>
                                            {editingCommentId === reply.id ? (
                                              <div className="mt-2">
                                                <Textarea
                                                  value={editText}
                                                  onChange={(e) => setEditText(e.target.value)}
                                                  rows={2}
                                                  className="mb-2"
                                                  disabled={isEditingComment}
                                                />
                                                <div className="flex justify-end gap-2">
                                                  <Button variant="ghost" size="sm" onClick={handleCancelEditComment} disabled={isEditingComment}>
                                                    <X className="mr-1 h-4 w-4" /> Cancel
                                                  </Button>
                                                  <Button size="sm" onClick={() => handleSaveEditComment(reply.id, true, comment.id)} disabled={isEditingComment || !editText.trim()}>
                                                    {isEditingComment ? <Loader2 className="mr-1 h-4 w-4 animate-spin" /> : <Check className="mr-1 h-4 w-4" />}
                                                    Save
                                                  </Button>
                                                </div>
                                              </div>
                                            ) : (
                                              <p className="mt-1 text-muted-foreground">{reply.text}</p>
                                            )}
                                            
                                            <div className="mt-3 flex items-center gap-2">
                                              <Button variant="ghost" size="sm" className={`flex items-center gap-1 ${reply.liked_by_user ? "text-primary" : "text-muted-foreground hover:text-primary/80"}`} onClick={() => handleCommentLike(reply.id)}>
                                                <ThumbsUp className={`h-4 w-4 ${reply.liked_by_user ? "fill-primary" : ""}`} />
                                                <span>{reply.likes || 0}</span>
                                              </Button>
                                              
                                              {/* Edit button - only show if user is the author and within 5 minute window */}
                                              {currentUser && currentUser.id === reply.user?.id && isWithinEditWindow(reply.created_at) && (
                                                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-amber-500" onClick={() => handleStartEditComment(reply.id, reply.text)}>
                                                  <Pencil className="mr-1 h-4 w-4" /> Edit
                                                </Button>
                                              )}
                                              
                                              {/* Delete button - only show if user is the author */}
                                              {currentUser && currentUser.id === reply.user?.id && (
                                                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-red-500" onClick={() => handleDeleteComment(reply.id, true, comment.id)}>
                                                  <Trash2 className="mr-1 h-4 w-4" /> Delete
                                                </Button>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                      ))}
                                      {comment.replies.length > 3 && (
                                        <Button variant="ghost" size="sm" className="mt-2 w-full text-xs" onClick={() => setShowAllComments(true)}>
                                          Show {comment.replies.length - 3} more {comment.replies.length - 3 === 1 ? 'reply' : 'replies'}
                                        </Button>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                            {comments.length > 5 && !showAllComments && (
                              <Button variant="ghost" className="mt-4 w-full" onClick={() => setShowAllComments(true)}>Show All {comments.length} Comments</Button>
                            )}
                          </div>
                        ) : ( <p className="text-muted-foreground">No comments yet.</p> )}
                        
                        {/* Comment Input Section - Moved below displayed comments */}
                        <div className="mt-8 space-y-6">
                          <Separator className="mb-6" />
                          
                          {/* Profanity error message */}
                          {profanityError && (
                            <div className="mb-6 rounded-md border border-destructive bg-destructive/10 p-4 text-destructive">
                              <div className="flex items-center gap-2">
                                <AlertCircle className="h-5 w-5" />
                                <p>{profanityError}</p>
                              </div>
                            </div>
                          )}
                          
                          {/* Comment input for logged in users who are not on timeout */}
                          {currentUser && !isOnTimeout ? (
                            <div className="mb-8">
                              <div className="flex gap-4">
                                <Link href={`/user/${currentUser.user_metadata?.username || 'user'}`} className="flex-shrink-0">
                                  <Avatar className="h-10 w-10 bg-card text-foreground">
                                    <AvatarImage src={currentUser.user_metadata?.avatar_url || "/placeholder-user.jpg"} />
                                    <AvatarFallback className="bg-primary/10 text-primary">
                                      {currentUser.user_metadata?.username ? currentUser.user_metadata.username.charAt(0).toUpperCase() : <UserIcon className="h-5 w-5" />}
                                    </AvatarFallback>
                                  </Avatar>
                                </Link>
                                <div className="flex-1">
                                  <Textarea
                                    placeholder="Add a comment..."
                                    value={newCommentText}
                                    onChange={(e) => {
                                      setNewCommentText(e.target.value)
                                      // Clear error when user starts typing
                                      if (profanityError && !isOnTimeout) setProfanityError(null)
                                    }}
                                    className="mb-2"
                                    disabled={isPostingComment}
                                  />
                                  <div className="flex justify-end">
                                    <Button 
                                      onClick={handlePostComment} 
                                      disabled={!newCommentText.trim() || isPostingComment}
                                      className="gap-2"
                                    >
                                      {isPostingComment ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
                                      Post Comment
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : !currentUser ? (
                            <div className="rounded-md border border-muted p-4 text-center">
                              <p className="mb-2 text-muted-foreground">Sign in to join the conversation</p>
                              <Button onClick={() => setShowCommunityModal(true)}>Sign In</Button>
                            </div>
                          ) : null}
                        </div>
                      </>
                    )}
                  </>
                )}
                {activeTab === 'related' && (
                  <>
                    <h2 className="mb-4 text-lg font-semibold text-foreground">Related Prompts</h2>
                    {isLoadingRelated ? (
                      <div className="flex h-32 items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : errorRelated ? (
                      <div className="text-center py-4">
                        <p className="text-destructive">{errorRelated}</p>
                      </div>
                    ) : relatedPrompts.length > 0 ? (
                      <PromptGrid
                        prompts={relatedPrompts}
                        viewMode="grid"
                        maxTags={1}
                        emptyMessage="No related prompts found."
                        onToggleSave={handleRelatedPromptToggleSave}
                      />
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-muted-foreground">No related prompts found for this prompt.</p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Right column - Sidebar */}
          <div className="lg:col-span-1">
            {/* "Use This Prompt" Card */}
            <div className="mb-6 overflow-hidden rounded-md bg-card">
              <div className="bg-gradient-to-r from-slate-700 to-slate-800 p-4"> {/* Updated gradient */}
                <h2 className="text-lg font-semibold text-foreground">Use This Prompt</h2>
              </div>
              <div className="p-4">
                <div className="mb-6 text-center">
                  <div className="text-3xl font-bold">{formatStatNumber(voteCount)}</div>
                  <div className="text-sm text-muted-foreground">Rating</div>
                </div>
                <div className="mb-4 flex gap-3">
                  <Button 
                    variant="outline" 
                    className={`flex-1 border-green-500 text-green-500 ${userVote === 'up' ? 'bg-green-500/10 hover:bg-green-500/30' : 'hover:bg-green-500/20'} transition-colors duration-200`} 
                    onClick={handleUpvote}
                  >
                    <ThumbsUp className={`mr-2 h-4 w-4 ${userVote === 'up' ? 'fill-green-500' : ''}`} /> Upvote
                  </Button>
                  <Button 
                    variant="outline" 
                    className={`flex-1 border-red-500 text-red-500 ${userVote === 'down' ? 'bg-red-500/10 hover:bg-red-500/30' : 'hover:bg-red-500/20'} transition-colors duration-200`} 
                    onClick={handleDownvote}
                  >
                    <ThumbsDown className={`mr-2 h-4 w-4 ${userVote === 'down' ? 'fill-red-500' : ''}`} /> Downvote
                  </Button>
                </div>
                <Separator className="my-4" />
                <div className="space-y-3">
                  <Button 
                    className="w-full bg-purple-600 text-white hover:bg-purple-700"
                    onClick={() => requireAuth(() => router.push(`/prompt/remix/${prompt.shortId}`))}
                  >
                    <Sparkles className="mr-2 h-4 w-4" /> Remix Prompt
                  </Button>
                  <Button 
                    className="w-full bg-teal-600 text-white hover:bg-teal-700"
                    onClick={() => requireAuth(() => setIsAddToCollectionDialogOpen(true))}
                  >
                    <FolderPlus className="mr-2 h-4 w-4" /> Add to Collection
                  </Button>
                  
                  {/* Add to Collection Dialog */}
                  <AddToCollectionDialog
                    isOpen={isAddToCollectionDialogOpen}
                    onClose={() => setIsAddToCollectionDialogOpen(false)}
                    promptId={prompt.id}
                    promptTitle={prompt.title}
                    onSuccess={() => {
                      toast({
                        title: "Success",
                        description: `"${prompt.title}" has been added to your collection(s).`
                      })
                    }}
                  />
                  <Button 
                    variant="ghost" 
                    className={`w-full text-foreground hover:bg-muted transition-all duration-200 ${isSharing ? 'animate-pulse' : ''}`} 
                    onClick={handleShare}
                    disabled={isSharing}
                  >
                    {shareConfirmed ? <Check className="mr-2 h-4 w-4" /> : <Share2 className="mr-2 h-4 w-4" />}
                    {shareConfirmed ? "Copied link to clipboard" : "Share"}
                  </Button>
                </div>
              </div>
            </div>

            {/* "Prompt Details" Card */}
            <div className="mb-6 overflow-hidden rounded-md bg-card">
              <div className="bg-gradient-to-r from-slate-700 to-slate-800 p-4"> {/* Updated gradient */}
                <h2 className="text-lg font-semibold text-foreground">Prompt Details</h2>
              </div>
              <div className="p-4">
                <div className="mb-4 flex items-center gap-3">
                  <Avatar className="h-10 w-10 bg-muted text-foreground">
                    <AvatarImage src={prompt.user?.avatar_url || "/placeholder-user.jpg"} />
                    <AvatarFallback>{username.charAt(0).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="text-sm text-muted-foreground">Author</div>
                    <Link href={`/user/${username}`} className="font-medium text-foreground hover:underline">{username}</Link>
                  </div>
                </div>
                <Separator className="my-3" />
                <div className="mb-4 flex items-center gap-3">
                   <Avatar className="h-10 w-10 flex items-center justify-center bg-muted text-foreground">
                    <SettingsIcon className="h-5 w-5" />
                  </Avatar>
                  <div>
                    <div className="text-sm text-muted-foreground">Tool</div>
                    <Link href={`/tool/${toolSlug}`} className="font-medium text-foreground hover:underline">{toolName}</Link>
                  </div>
                </div>
                <Separator className="my-3" />
                <div className="mb-4 flex items-center gap-3">
                   <Avatar className="h-10 w-10 flex items-center justify-center bg-muted text-foreground">
                    <GridIcon className="h-5 w-5" />
                  </Avatar>
                  <div>
                    <div className="text-sm text-muted-foreground">Category</div>
                    <Link href={`/category/${categorySlug}`} className="font-medium hover:underline" style={{ color: categoryDisplayColor }}>{categoryName}</Link>
                  </div>
                </div>
                <Separator className="my-3" />
                {prompt.ai_model && (
                  <>
                    <div className="mb-4 flex items-center gap-3">
                      <Avatar className="h-10 w-10 flex items-center justify-center bg-muted text-foreground">
                        <Sparkles className="h-5 w-5" />
                      </Avatar>
                      <div>
                        <div className="text-sm text-muted-foreground">AI Model</div>
                        <Link 
                          href={`/search?q=${encodeURIComponent(prompt.ai_model.provider + ' ' + prompt.ai_model.name)}`} 
                          className="font-medium text-foreground hover:underline"
                        >
                          {prompt.ai_model.provider} - {prompt.ai_model.name}
                        </Link>
                      </div>
                    </div>
                    <Separator className="my-3" />
                  </>
                )}
                <div className="mb-4 flex items-center gap-3">
                   <Avatar className="h-10 w-10 flex items-center justify-center bg-muted text-foreground">
                    <CalendarIcon className="h-5 w-5" />
                  </Avatar>
                  <div>
                    <div className="text-sm text-muted-foreground">Post Date</div>
                    <div className="font-medium text-foreground">{formattedDate}</div>
                  </div>
                </div>
                {/* Remixed from section - only show if original prompt exists and is public */}
                {originalPrompt && (
                  <>
                    <Separator className="my-3" />
                    <div className="mb-4 flex items-center gap-3">
                      <Avatar className="h-10 w-10 flex items-center justify-center bg-muted text-foreground">
                        <GitFork className="h-5 w-5" />
                      </Avatar>
                      <div>
                        <div className="text-sm text-muted-foreground">Remixed from:</div>
                        <Link 
                          href={`/prompt/${originalPrompt.shortId}`} 
                          className="font-medium text-foreground hover:underline"
                        >
                          {originalPrompt.title}
                        </Link>
                      </div>
                    </div>
                  </>
                )}
                {prompt.tags && prompt.tags.length > 0 && (
                  <>
                    <Separator className="my-3" />
                    <div>
                      <div className="mb-2 text-sm text-muted-foreground">Tags</div>
                      <div className="flex flex-wrap gap-2">
                        {(prompt.tags as Tag[]).map((tag) => { // Cast to Tag[]
                          // Use tag.slug if it exists, otherwise create a slug from tag.name
                          const tagSlug = tag.slug || slugify(tag.name, { lower: true, strict: true })
                          return (
                            <Badge key={tag.id || tag.slug} variant="secondary" className="bg-teal-500/10 text-teal-400">
                              <Link href={`/tag/${tagSlug}`} className="hover:underline">
                                #{tag.name}
                              </Link>
                            </Badge>
                          )
                        })}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* "Stats" Card */}
            <div className="overflow-hidden rounded-md bg-card">
              <div className="bg-gradient-to-r from-slate-700 to-slate-800 p-4"> {/* Updated gradient */}
                <h2 className="text-lg font-semibold text-foreground">Stats</h2>
              </div>
              <div className="space-y-3 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-muted-foreground"><ThumbsUp className="h-4 w-4" /> Upvotes</div>
                  <div className="font-medium text-foreground">{formatStatNumber(prompt.likeCount)}</div>
                </div>
                {/* VIEWS DISPLAY REMOVED - viewCount now tracks unique views but is hidden from UI */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-muted-foreground"><MessageSquare className="h-4 w-4" /> Comments</div>
                  <div className="font-medium text-foreground">{formatStatNumber(prompt.commentCount)}</div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-muted-foreground"><Sparkles className="h-4 w-4" /> Remixes</div>
                  <div className="font-medium text-foreground">{formatStatNumber(prompt.remixCount)}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Community Join Modal */}
      <CommunityJoinModal
        isOpen={showCommunityModal}
        onClose={() => setShowCommunityModal(false)}
      />

      {/* Add to Collection Dialog for Related Prompts */}
      {selectedRelatedPromptId && (
        <AddToCollectionDialog
          isOpen={isRelatedPromptSaveDialogOpen}
          onClose={() => {
            setIsRelatedPromptSaveDialogOpen(false)
            setSelectedRelatedPromptId(null)
            setSelectedRelatedPromptTitle("")
          }}
          promptId={selectedRelatedPromptId}
          promptTitle={selectedRelatedPromptTitle}
          onSuccess={() => {
            // Optimistically update the related prompt as saved
            setRelatedPrompts(prev => 
              prev.map(p => 
                p.id === selectedRelatedPromptId ? { ...p, isSaved: true } : p
              )
            )
            setIsRelatedPromptSaveDialogOpen(false)
            setSelectedRelatedPromptId(null)
            setSelectedRelatedPromptTitle("")
            toast({
              title: "Success",
              description: `"${selectedRelatedPromptTitle}" has been added to your collection(s).`
            })
          }}
        />
      )}
    </div>
  )
}
