"use client"

import React from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "components/ui/dialog"
import { But<PERSON> } from "components/ui/button"
import { ThumbsUp, MessageSquare, Sparkles, FolderPlus } from "lucide-react"

interface CommunityJoinModalProps {
  isOpen: boolean
  onClose: () => void
  redirectPath?: string
}

export default function CommunityJoinModal({ isOpen, onClose, redirectPath }: CommunityJoinModalProps) {
  const router = useRouter()

  const handleLogin = () => {
    onClose()
    const loginPath = redirectPath ? `/sign-in?redirect=${encodeURIComponent(redirectPath)}` : "/sign-in"
    router.push(loginPath)
  }

  const handleSignup = () => {
    onClose()
    const signupPath = redirectPath ? `/sign-up?redirect=${encodeURIComponent(redirectPath)}` : "/sign-up"
    router.push(signupPath)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md border-border bg-background p-0 overflow-hidden">
        <div className="relative w-full h-40">
          <Image 
            src="/images/prompthq-community.png" 
            alt="PromptHQ Community" 
            fill 
            className="object-cover"
          />
          <button
            onClick={onClose}
            className="absolute top-2 right-2 z-10 flex h-8 w-8 items-center justify-center rounded-full bg-black/60 backdrop-blur-sm text-white hover:bg-black/80 transition-all duration-200"
            aria-label="Close modal"
          >
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div className="p-6">
          <DialogHeader className="mb-4">
            <DialogTitle className="text-center text-xl font-bold text-foreground">
              Join the PromptHQ Community
            </DialogTitle>
          </DialogHeader>
          <div className="flex flex-col items-center space-y-4">
            <p className="text-center text-muted-foreground leading-relaxed">
              Join our growing community of prompt creators and discover amazing possibilities! 
            </p>
            <div className="bg-card/50 rounded-lg p-4 space-y-3 w-full">
              <div className="flex items-center gap-3">
                <ThumbsUp className="h-5 w-5 text-green-500" />
                <span className="text-sm">Vote on prompts</span>
              </div>
              <div className="flex items-center gap-3">
                <MessageSquare className="h-5 w-5 text-blue-500" />
                <span className="text-sm">Comment and engage</span>
              </div>
              <div className="flex items-center gap-3">
                <Sparkles className="h-5 w-5 text-purple-500" />
                <span className="text-sm">Create and remix prompts</span>
              </div>
              <div className="flex items-center gap-3">
                <FolderPlus className="h-5 w-5 text-teal-500" />
                <span className="text-sm">Organize public & private collections</span>
              </div>
            </div>
            <div className="flex gap-3 w-full pt-2">
              <Button 
                variant="outline" 
                className="flex-1" 
                onClick={handleLogin}
              >
                Log In
              </Button>
              <Button 
                className="flex-1 bg-primary hover:bg-primary/90" 
                onClick={handleSignup}
              >
                Sign Up
              </Button>
            </div>
            <p className="text-xs text-muted-foreground text-center">
              All features are completely free
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 