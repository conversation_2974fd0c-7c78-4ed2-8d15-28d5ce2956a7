"use client"

import * as React from "react"
import { X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"

export interface TagOption {
  value: string
  label: string
}

interface TagInputProps {
  options: TagOption[]
  selectedTags: string[]
  onTagsChange: (tags: string[]) => void
  placeholder?: string
  maxTags?: number
  className?: string
  showSelectedInField?: boolean
}

export function TagInput({
  options,
  selectedTags,
  onTagsChange,
  placeholder = "Search tags...",
  maxTags = 5,
  className,
  showSelectedInField = false,
}: TagInputProps) {
  const [open, setOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState("")

  const filteredOptions = React.useMemo(() => {
    return options.filter(
      (option) => 
        !selectedTags.includes(option.value) && 
        option.label.toLowerCase().includes(inputValue.toLowerCase())
    )
  }, [options, selectedTags, inputValue])

  const handleSelect = (value: string) => {
    if (selectedTags.length < maxTags) {
      onTagsChange([...selectedTags, value])
      setInputValue("")
    }
  }

  const handleRemove = (tag: string) => {
    onTagsChange(selectedTags.filter((t) => t !== tag))
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Backspace" && !inputValue && selectedTags.length > 0) {
      handleRemove(selectedTags[selectedTags.length - 1])
    }
  }

  // Get the labels for the selected tags
  const selectedTagLabels = selectedTags.map(
    (tag) => options.find((opt) => opt.value === tag)?.label || tag
  )

  return (
    <div className={cn("w-full", className)}>
      {!showSelectedInField && (
        <div className="flex flex-wrap gap-2 mb-2">
          {selectedTagLabels.map((tag, index) => (
            <Badge key={index} variant="secondary" className="gap-1 pr-1 py-0.5">
              {tag}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={() => handleRemove(selectedTags[index])}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">Remove {tag}</span>
              </Button>
            </Badge>
          ))}
        </div>
      )}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-10"
            onClick={() => setOpen(true)}
            disabled={selectedTags.length >= maxTags}
          >
            {selectedTags.length >= maxTags ? (
              `Maximum ${maxTags} tags reached`
            ) : showSelectedInField && selectedTags.length > 0 ? (
              <div className="flex flex-wrap gap-1 items-center overflow-hidden">
                {selectedTagLabels.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="gap-1 pr-1 py-0.5 text-xs">
                    {tag}
                    <span
                      role="button"
                      tabIndex={0}
                      className="ml-1 rounded-full outline-none cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleRemove(selectedTags[index])
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.stopPropagation()
                          handleRemove(selectedTags[index])
                        }
                      }}
                    >
                      <X className="h-2 w-2" />
                      <span className="sr-only">Remove {tag}</span>
                    </span>
                  </Badge>
                ))}
              </div>
            ) : (
              placeholder
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
          <Command>
            <CommandInput 
              placeholder={placeholder} 
              value={inputValue}
              onValueChange={setInputValue}
              onKeyDown={handleKeyDown}
              className="border-none focus:ring-0"
            />
            <CommandList>
              <CommandEmpty>No matching tags found</CommandEmpty>
              <CommandGroup>
                {filteredOptions.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.label}
                    onSelect={() => {
                      handleSelect(option.value)
                      setOpen(false)
                    }}
                  >
                    {option.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {selectedTags.length > 0 && (
        <p className="text-xs text-muted-foreground mt-1">
          {selectedTags.length}/{maxTags} tags selected
        </p>
      )}
    </div>
  )
}
