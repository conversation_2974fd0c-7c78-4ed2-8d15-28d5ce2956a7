"use client"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Upload, X, CheckCircle, AlertCircle } from "lucide-react"

interface DragDropImageUploadProps {
  imageFile: File | null
  imagePreview: string | null
  onImageChange: (file: File | null, preview: string | null) => void
  disabled?: boolean
  maxSizeMB?: number
  acceptedTypes?: string[]
  className?: string
  placeholder?: {
    title?: string
    subtitle?: string
    formats?: string
  }
}

export function DragDropImageUpload({
  imageFile,
  imagePreview,
  onImageChange,
  disabled = false,
  maxSizeMB = 1.5,
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  className = "",
  placeholder = {
    title: "Click to upload image or drag and drop",
    subtitle: "PNG, JPG, GIF up to 1.5MB",
    formats: "PNG, JPG, GIF up to 1.5MB"
  }
}: DragDropImageUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // File validation
  const validateFile = (file: File): string | null => {
    // Check file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024
    if (file.size > maxSizeBytes) {
      return `File size must be less than ${maxSizeMB}MB (current: ${(file.size / 1024 / 1024).toFixed(2)}MB)`
    }

    // Check file type
    if (!acceptedTypes.includes(file.type)) {
      return `File must be one of: ${acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}`
    }

    return null
  }

  // Process file (shared between file input and drag & drop)
  const processFile = (file: File) => {
    const validationError = validateFile(file)
    if (validationError) {
      setValidationError(validationError)
      // Don't process the file if validation fails
      return
    }

    // Clear any previous validation errors
    setValidationError(null)

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      const preview = e.target?.result as string
      onImageChange(file, preview)
    }
    reader.readAsDataURL(file)
  }

  // Handle file selection from input
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    processFile(file)
  }

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    if (!disabled) {
      setIsDragOver(true)
    }
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    if (disabled) return
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      processFile(files[0])
    }
  }

  // Handle remove image
  const handleRemoveImage = () => {
    setValidationError(null) // Clear any validation errors
    onImageChange(null, null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Handle click to open file dialog
  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click()
    }
  }

  // Check if we have any image (new file or existing preview)
  const hasImage = imageFile || imagePreview

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center space-x-2">
        {!hasImage ? (
          <div className="flex-1">
            <input
              ref={fileInputRef}
              type="file"
              accept={acceptedTypes.join(',')}
              onChange={handleFileSelect}
              disabled={disabled}
              className="hidden"
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleClick}
              disabled={disabled}
              className={`w-full h-20 border-2 border-dashed transition-colors ${
                isDragOver
                  ? 'border-accent-green bg-accent-green/10'
                  : 'border-muted-foreground/25 hover:border-muted-foreground/50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <div className="flex flex-col items-center gap-2">
                <Upload className="w-6 h-6 text-muted-foreground" />
                <span className="text-sm font-medium">{placeholder.title}</span>
                <span className="text-xs text-muted-foreground">{placeholder.subtitle}</span>
              </div>
            </Button>
          </div>
        ) : (
          <div className="flex-1 flex items-center space-x-2">
            <div className="flex items-center space-x-2 flex-1 p-2 border rounded-md bg-muted/20">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium truncate">
                {imageFile ? imageFile.name : 'Current image'}
              </span>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleRemoveImage}
              disabled={disabled}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        )}
      </div>
      
      {/* Validation Error Display */}
      {validationError && (
        <div className="flex items-center space-x-2 p-2 bg-red-50 border border-red-200 rounded-md">
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <span className="text-sm text-red-700">{validationError}</span>
        </div>
      )}
      
      {/* Image Preview */}
      {imagePreview && (
        <div className="mt-2">
          <img
            src={imagePreview}
            alt="Preview"
            className="max-w-xs max-h-48 rounded-md border"
          />
        </div>
      )}
    </div>
  )
} 