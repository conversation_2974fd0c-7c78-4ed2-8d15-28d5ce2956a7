"use client";

import React from "react";
import CodeMirror from "@uiw/react-codemirror";
import { markdown, markdownLanguage } from "@codemirror/lang-markdown";
import { languages } from "@codemirror/language-data";
import { Extension } from "@codemirror/state";
import { 
  ViewPlugin, 
  Decoration, 
  DecorationSet, 
  ViewUpdate, 
  EditorView,
  keymap
} from "@codemirror/view";
import { defaultKeymap, insertNewlineAndIndent } from "@codemirror/commands";
import { syntaxHighlighting, defaultHighlightStyle, HighlightStyle } from "@codemirror/language";
import { tags } from "@lezer/highlight";

interface MarkdownPromptEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  minHeight?: string;
  className?: string;
}

// Custom highlight style for markdown syntax
const markdownHighlightStyle = HighlightStyle.define([
  { tag: tags.heading1, color: 'hsl(142.1 70.6% 45.3%)', fontSize: '1.25em', fontWeight: '700' },
  { tag: tags.heading2, color: 'hsl(142.1 70.6% 45.3%)', fontSize: '1.15em', fontWeight: '700' },
  { tag: tags.heading3, color: 'hsl(142.1 70.6% 45.3%)', fontSize: '1.1em', fontWeight: '700' },
  { tag: tags.heading, color: 'hsl(142.1 70.6% 45.3%)', fontWeight: '700' },
  { tag: tags.strong, color: 'hsl(38 92% 50%)', fontWeight: '700' },
  { tag: tags.emphasis, color: 'hsl(168 76% 42%)', fontStyle: 'italic' },
  { tag: tags.strikethrough, color: 'hsl(215 20.2% 65.1%)', textDecoration: 'line-through' },
  { tag: tags.monospace, color: 'hsl(266 85% 58%)', backgroundColor: 'hsl(217.2 32.6% 17.5%)', fontFamily: 'ui-monospace, SFMono-Regular, SF Mono, Consolas, Liberation Mono, Menlo, monospace' },
  { tag: tags.link, color: 'hsl(213 94% 68%)', textDecoration: 'underline' },
  { tag: tags.url, color: 'hsl(213 94% 68%)', textDecoration: 'underline' },
  { tag: tags.quote, color: 'hsl(215 20.2% 65.1%)', fontStyle: 'italic' },
  { tag: tags.list, color: 'hsl(210 40% 98%)' },
  { tag: tags.meta, color: 'hsl(215 20.2% 65.1%)' },
  { tag: tags.comment, color: 'hsl(215 20.2% 65.1%)', fontStyle: 'italic' },
  { tag: tags.atom, color: 'hsl(31 81% 61%)' },
  { tag: tags.number, color: 'hsl(31 81% 61%)' },
  { tag: tags.propertyName, color: 'hsl(213 94% 68%)' },
  { tag: tags.keyword, color: 'hsl(316 73% 69%)' },
  { tag: tags.tagName, color: 'hsl(142.1 70.6% 45.3%)' },
  { tag: tags.attributeName, color: 'hsl(213 94% 68%)' },
  { tag: tags.string, color: 'hsl(168 76% 42%)' },
]);

// Create a custom theme using EditorView.theme for better control
const appTheme = EditorView.theme({
  '&': {
    color: 'hsl(210 40% 98%)', // --foreground color
    backgroundColor: 'hsl(222.2 84% 4.9%)', // --background color
  },
  '.cm-content': {
    padding: '12px',
    caretColor: 'hsl(142.1 70.6% 45.3%)', // green caret
    fontFamily: 'ui-monospace, SFMono-Regular, SF Mono, Consolas, Liberation Mono, Menlo, monospace',
    color: 'hsl(210 40% 98%)', // Ensure text color is set
    backgroundColor: 'hsl(222.2 84% 4.9%)', // Ensure background is set
    lineHeight: '1.5',
    fontSize: '14px',
  },
  '.cm-focused': {
    outline: 'none',
  },
  '.cm-editor.cm-focused': {
    outline: 'none',
  },
  '.cm-selectionBackground, ::selection': {
    backgroundColor: 'hsl(217.2 32.6% 17.5%)',
  },
  '.cm-cursor, .cm-dropCursor': {
    borderLeftColor: 'hsl(142.1 70.6% 45.3%)',
  },
  // Hide gutters completely for a cleaner look
  '.cm-gutters': {
    display: 'none',
  },
  // Variable highlighting style
  '.cm-variable-highlight': {
    backgroundColor: 'hsl(142.1 70.6% 45.3% / 0.2)', // bg-accent-green/20
    color: 'hsl(142.1 70.6% 45.3%) !important', // text-accent-green with !important
    padding: '2px 4px', // px-1 py-0.5
    borderRadius: '4px', // rounded
    fontFamily: 'ui-monospace, SFMono-Regular, SF Mono, Consolas, Liberation Mono, Menlo, monospace', // font-mono
    fontWeight: 'normal',
  },
  // Override any nested syntax highlighting within variables
  '.cm-variable-highlight *': {
    color: 'hsl(142.1 70.6% 45.3%) !important',
  },
  // Placeholder styling
  '.cm-placeholder': {
    color: 'hsl(215 20.2% 65.1%)', // muted-foreground color for placeholder
  },
  // Ensure the editor background is always dark
  '.cm-editor': {
    backgroundColor: 'hsl(222.2 84% 4.9%)',
  },
  // Line numbers (hidden but ensure proper spacing)
  '.cm-lineNumbers': {
    display: 'none',
  },
}, { dark: true });

// Variable highlighting plugin for [[variable]] patterns
const variableHighlightPlugin = ViewPlugin.fromClass(
  class {
    decorations: DecorationSet;

    constructor(view: EditorView) {
      this.decorations = this.getDecorations(view);
    }

    update(update: ViewUpdate) {
      if (update.docChanged || update.viewportChanged) {
        this.decorations = this.getDecorations(update.view);
      }
    }

    getDecorations(view: EditorView): DecorationSet {
      const widgets: Array<{from: number, to: number, decoration: Decoration}> = [];
      const variableRegex = /\[\[([^\]]+)\]\]/g;
      
      for (const { from, to } of view.visibleRanges) {
        const text = view.state.doc.sliceString(from, to);
        let match;
        
        while ((match = variableRegex.exec(text))) {
          const start = from + match.index;
          const end = start + match[0].length;
          
          widgets.push({
            from: start,
            to: end,
            decoration: Decoration.mark({
              class: "cm-variable-highlight",
            })
          });
        }
        
        // Reset regex lastIndex for next iteration
        variableRegex.lastIndex = 0;
      }
      
      return Decoration.set(widgets.map(w => w.decoration.range(w.from, w.to)));
    }
  },
  {
    decorations: (v) => v.decorations,
  }
);

const MarkdownPromptEditor: React.FC<MarkdownPromptEditorProps> = ({
  value,
  onChange,
  placeholder,
  disabled = false,
  minHeight = "200px",
  className = "",
}) => {
  const extensions: Extension[] = [
    // Enable basic functionality including line breaks
    keymap.of([
      ...defaultKeymap,
      { key: "Enter", run: insertNewlineAndIndent },
    ]),
    // Markdown language support
    markdown({ 
      base: markdownLanguage, 
      codeLanguages: languages 
    }),
    // Syntax highlighting
    syntaxHighlighting(markdownHighlightStyle, { fallback: true }),
    // Custom variable highlighting
    variableHighlightPlugin,
    // Line wrapping
    EditorView.lineWrapping,
    // Custom theme
    appTheme,
    // Additional theme for proper sizing
    EditorView.theme({
      '.cm-editor': {
        fontSize: '14px',
      },
      '.cm-content': {
        minHeight: minHeight,
        fontSize: '14px',
      },
    }),
  ];

  return (
    <div className={`relative rounded-md border border-input overflow-hidden ${className}`} style={{ backgroundColor: 'hsl(222.2 84% 4.9%)' }}>
      <CodeMirror
        value={value}
        onChange={onChange}
        placeholder={placeholder || "Enter your prompt text here. Use [[variables]] for placeholders..."}
        editable={!disabled}
        extensions={extensions}
        basicSetup={false} // We're manually setting up what we need
        style={{
          minHeight: minHeight,
          backgroundColor: 'hsl(222.2 84% 4.9%)',
          color: 'hsl(210 40% 98%)',
        }}
      />
    </div>
  );
};

export default MarkdownPromptEditor; 