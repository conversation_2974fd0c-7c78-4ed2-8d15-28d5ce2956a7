import Link from "next/link"
import { getCategoryTheme } from "./category-icon-mapper"
import { getCategoryColorByName } from "@/lib/data/category-colors"
import { getLinearGradientStyle, getIconColorHex } from "@/lib/utils/category-styles" // Import utility functions


interface MiniCategoryCardProps {
  category: {
    name: string
    slug: string
  }
}

export default function MiniCategoryCard({ category }: MiniCategoryCardProps) {
  const { icon: Icon, darkGradient, darkTextColor } = getCategoryTheme(category.name)
  const colors = getCategoryColorByName(category.name)

  return (
    <Link
      href={`/category/${category.slug}`}
      className="group flex items-center gap-3 rounded-xl border border-border bg-card p-3 transition-all hover:border-accent-green/30 hover:shadow-sm hover:shadow-accent-green/10"
    >
      <div
        className={`flex h-10 w-10 items-center justify-center rounded-md`} // Removed bg-gradient-to-br and darkGradient class
        style={{ backgroundImage: getLinearGradientStyle(category.slug) }} // Apply inline gradient style
      >
        <Icon className={`h-5 w-5`} color={getIconColorHex(category.slug)} /> {/* Removed darkTextColor class and added color prop */}
      </div>
      <div className="flex-1">
        <h3 className="text-sm font-medium">{category.name}</h3>
      </div>
    </Link>
  )
}
