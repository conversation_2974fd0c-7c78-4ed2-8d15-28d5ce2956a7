"use client"

import { useState, useRef, useCallback, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import PromptCard from "@/components/prompt-card"
import { Loader2, ChevronDown, Check } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { getPrompts, addPromptToCollection } from "@/lib/api-services"
import type { PromptCard as PromptCardType } from "@/lib/types"
import { logPromptShortIdIssues } from "@/lib/utils/debug-helpers"
import { useUser } from "@/lib/hooks/use-user"
import { toast } from "@/components/ui/use-toast"

// Define the sort options type
type SortOption = "newest" | "popular" | "trending"

export default function FeaturedPrompts() {
  const { user, isLoading: isUserLoading } = useUser();
  
  // State for displayed prompts
  const [displayedPrompts, setDisplayedPrompts] = useState<PromptCardType[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const PROMPTS_PER_PAGE = 6

  // Sort options state
  const [sortOption, setSortOption] = useState<SortOption>("popular")

  // Helper function to get sort parameters for the API
  const getSortByOption = (option: SortOption): { field: string; order: "asc" | "desc" } => {
    switch (option) {
      case "newest":
        return { field: "created_at", order: "desc" }
      case "popular":
        return { field: "rating", order: "desc" }
      case "trending":
        return { field: "trending_score", order: "desc" }
      default:
        return { field: "created_at", order: "desc" }
    }
  }

  // Function to load more prompts
  const loadMorePrompts = async () => {
    if (isLoading) return

    setIsLoading(true)

    try {
      // Get sorted prompts from the database
      const sortBy = getSortByOption(sortOption)
      const offset = page * PROMPTS_PER_PAGE

      const newPrompts = await getPrompts({
        limit: PROMPTS_PER_PAGE,
        offset: offset,
        sortBy: sortBy.field,
        sortOrder: sortBy.order,
        currentUserId: user?.id,
      })

      console.log(`Loaded ${newPrompts.length} additional prompts, page ${page}`)

      // Check for missing shortIds
      logPromptShortIdIssues(newPrompts, "FeaturedPrompts-loadMore")

      setDisplayedPrompts((prevPrompts) => [...prevPrompts, ...newPrompts])
      setPage((prevPage) => prevPage + 1)

      // Assume there are more prompts if we got a full page
      setHasMore(newPrompts.length === PROMPTS_PER_PAGE)
    } catch (error) {
      console.error("Error loading more prompts:", error)
      setHasMore(false)
    } finally {
      setIsLoading(false)
    }
  }

  // Reference for the intersection observer
  const observerRef = useRef<IntersectionObserver | null>(null)
  const lastPromptRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (isLoading) return

      if (observerRef.current) observerRef.current.disconnect()

      observerRef.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          loadMorePrompts()
        }
      })

      if (node) observerRef.current.observe(node)
    },
    [isLoading, hasMore, loadMorePrompts],
  )

  // Function to load initial prompts - only call when user loading is complete
  useEffect(() => {
    if (!isUserLoading) {
      loadInitialPrompts(sortOption)
    }
  }, [sortOption, isUserLoading])

  // Function to load initial prompts with a specific sort option
  const loadInitialPrompts = async (option: SortOption) => {
    setIsLoading(true)

    try {
      // Get sorted prompts from the database
      const sortBy = getSortByOption(option)
      const prompts = await getPrompts({
        limit: PROMPTS_PER_PAGE,
        offset: 0,
        sortBy: sortBy.field,
        sortOrder: sortBy.order,
        currentUserId: user?.id,
      })

      // Check for missing shortIds
      logPromptShortIdIssues(prompts, "FeaturedPrompts-initial")

      setDisplayedPrompts(prompts)
      setPage(1)

      // Assume there are more prompts if we got a full page
      setHasMore(prompts.length === PROMPTS_PER_PAGE)
    } catch (error) {
      console.error("Error loading prompts:", error)
      setDisplayedPrompts([])
      setHasMore(false)
    } finally {
      setIsLoading(false)
    }
  }

  // Function to handle sort option change
  const handleSortChange = (option: SortOption) => {
    setSortOption(option)
  }

  // Function to get the display name for a sort option
  const getSortOptionDisplayName = (option: SortOption): string => {
    switch (option) {
      case "newest":
        return "Newest"
      case "popular":
        return "Popular"
      case "trending":
        return "Trending"
      default:
        return "Popular"
    }
  }

  // Handle optimistic save updates
  const handlePromptSave = async (promptId: string) => {
    if (!user?.id) {
      toast({
        title: "Sign in required",
        description: "Please sign in to save prompts.",
        variant: "destructive",
      })
      return
    }

    // Check if this is user's own prompt
    const prompt = displayedPrompts.find(p => p.id === promptId)
    if (prompt?.user.id === user.id) {
      toast({
        title: "Can't save own prompt",
        description: "You can't save your own prompts.",
        variant: "default",
      })
      return
    }

    // Optimistically update the UI
    setDisplayedPrompts(prev => 
      prev.map(p => 
        p.id === promptId ? { ...p, isSaved: true } : p
      )
    )

    try {
      // Call the save API - save to default collection (null)
      const result = await addPromptToCollection(user.id, promptId, null)
      
      if (!result.success) {
        throw new Error(result.error?.message || "Failed to save prompt")
      }

      // Show success toast
      toast({
        title: "Prompt saved",
        description: "The prompt has been saved to your collection.",
      })
    } catch (error) {
      console.error("Error saving prompt:", error)
      
      // Revert the optimistic UI update
      setDisplayedPrompts(prev => 
        prev.map(p => 
          p.id === promptId ? { ...p, isSaved: false } : p
        )
      )

      // Show error toast
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save prompt. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <section>
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Featured Prompts</h2>

        {/* Sort Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="flex items-center gap-2 rounded-md"
              style={{ borderRadius: "6px" }}
            >
              <span>Sort: {getSortOptionDisplayName(sortOption)}</span>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-48 rounded-lg"
            style={{ borderRadius: "8px" }}
          >
            <DropdownMenuItem className="flex items-center justify-between rounded-sm" onClick={() => handleSortChange("newest")}>
              Newest
              {sortOption === "newest" && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
            <DropdownMenuItem className="flex items-center justify-between rounded-sm" onClick={() => handleSortChange("popular")}>
              Popular
              {sortOption === "popular" && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
            <DropdownMenuItem
              className="flex items-center justify-between rounded-sm"
              onClick={() => handleSortChange("trending")}
            >
              Trending
              {sortOption === "trending" && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {displayedPrompts.map((prompt, index) => {
          // Add ref to the last prompt element
          if (index === displayedPrompts.length - 1) {
            return (
              <div key={`${prompt.id}-${index}`} ref={lastPromptRef}>
                <PromptCard 
                  prompt={prompt} 
                  placeholderType="icon" 
                  onToggleSave={handlePromptSave}
                />
              </div>
            )
          } else {
            return (
              <PromptCard 
                key={`${prompt.id}-${index}`} 
                prompt={prompt} 
                placeholderType="icon" 
                onToggleSave={handlePromptSave}
              />
            )
          }
        })}
      </div>

      {/* Loading indicator */}
      {isLoading && (
        <div className="mt-8 flex justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-accent-green" />
        </div>
      )}

      {/* End of content message */}
      {!hasMore && displayedPrompts.length > 0 && (
        <div className="mt-8 text-center text-muted-foreground">
          <p>You've reached the end of the prompts</p>
        </div>
      )}
    </section>
  )
} 