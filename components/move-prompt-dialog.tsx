"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, MoveRight, Info } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import type { Collection } from "@/lib/types";

interface MovePromptDialogProps {
  isOpen: boolean;
  onClose: () => void;
  promptId: string;
  isOwnPrompt: boolean;
  currentCollectionId: string;
  collections: Collection[];
  onMovePrompt: (promptId: string, fromCollectionId: string, toCollectionId: string) => Promise<void>;
}

export default function MovePromptDialog({
  isO<PERSON>,
  onClose,
  promptId,
  isOwnPrompt,
  currentCollectionId,
  collections,
  onMovePrompt,
}: MovePromptDialogProps) {
  const [selectedCollectionId, setSelectedCollectionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset state when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedCollectionId(null);
      setError(null);
    }
  }, [isOpen]);

  // Helper function to determine if a collection is valid for this prompt
  const isValidCollection = (collection: Collection): boolean => {
    // Don't show the current collection
    if (collection.id === currentCollectionId) {
      return false;
    }
    
    // Content rule: My Prompts can only contain user's own prompts
    if (collection.defaultType === "my_prompts" && !isOwnPrompt) {
      return false;
    }
    
    // Content rule: Saved Prompts can only contain other users' prompts
    if (collection.defaultType === "saved_prompts" && isOwnPrompt) {
      return false;
    }
    
    // Custom collections can contain any prompts
    return true;
  };

  // Get current collection name
  const currentCollection = collections.find(c => c.id === currentCollectionId);
  
  // Filter collections to only show valid target collections
  const validTargetCollections = collections.filter(isValidCollection);

  const handleMovePrompt = async () => {
    if (!selectedCollectionId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      await onMovePrompt(promptId, currentCollectionId, selectedCollectionId);
      onClose();
    } catch (err) {
      console.error("Error moving prompt:", err);
      setError("Failed to move prompt. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Move Prompt to Another Collection</DialogTitle>
          <DialogDescription>
            Select a collection to move this prompt to.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="mb-4 flex items-center gap-2 text-sm">
            <span>From:</span>
            <span className="font-medium">{currentCollection?.name || "Current Collection"}</span>
          </div>
          
          <MoveRight className="mx-auto my-2 h-5 w-5 text-muted-foreground" />
          
          <div className="mb-2 flex items-center gap-2 text-sm">
            <span>To:</span>
          </div>
          
          {validTargetCollections.length === 0 ? (
            <div className="rounded-md bg-muted p-4 text-sm">
              There are no valid collections to move this prompt to. 
              {!isOwnPrompt ? (
                <span className="block mt-2 text-muted-foreground">
                  Note: Other users' prompts cannot be moved to "My Prompts".
                </span>
              ) : (
                <span className="block mt-2 text-muted-foreground">
                  Note: Your own prompts cannot be moved to "Saved Prompts".
                </span>
              )}
            </div>
          ) : (
            <RadioGroup value={selectedCollectionId || ""} onValueChange={setSelectedCollectionId}>
              {validTargetCollections.map(collection => (
                <div key={collection.id} className="flex items-center space-x-2 mb-2 p-2 rounded-md hover:bg-accent/10">
                  <RadioGroupItem value={collection.id} id={`collection-${collection.id}`} />
                  <Label htmlFor={`collection-${collection.id}`} className="flex items-center gap-2 cursor-pointer">
                    {collection.name}
                    {collection.isDefault && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="h-3 w-3 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Default collection</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}
          
          {error && (
            <div className="mt-2 text-sm text-red-500">
              {error}
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            type="button" 
            onClick={handleMovePrompt}
            disabled={isLoading || !selectedCollectionId || validTargetCollections.length === 0}
          >
            {isLoading ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Moving...</>
            ) : (
              "Move Prompt"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
