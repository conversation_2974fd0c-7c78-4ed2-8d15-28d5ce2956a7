"use client";

import { useState, useEffect, useMemo, useRef } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, PlusCircle, Search, Info, Bookmark, Globe, Lock } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Collection } from "@/lib/types";
import { toast } from "sonner";
import { getUserCollectionsForDialog, createCollection, getPromptCollectionMembership } from "@/lib/api-services";
import { updatePromptCollections } from "@/lib/api-services/updatePromptCollections";
import { createBrowserClient } from "@supabase/ssr"

interface AddToCollectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  promptId: string;
  promptTitle: string;
  onSuccess?: (isSaved: boolean) => void; // Pass the final saved status
  currentCollectionId?: string; // Optional: if we're viewing from a specific collection page
}

export default function AddToCollectionDialog({
  isOpen,
  onClose,
  promptId,
  promptTitle,
  onSuccess,
  currentCollectionId
}: AddToCollectionDialogProps) {
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
  const [user, setUser] = useState<any>(null);
  const userRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCollectionIds, setSelectedCollectionIds] = useState<string[]>([]);
  // Track which collections the prompt was originally in for comparison
  const [initialCollectionIds, setInitialCollectionIds] = useState<string[]>([]);
  const [newCollectionName, setNewCollectionName] = useState("");
  const [isCreatingCollection, setIsCreatingCollection] = useState(false);
  const [existingMemberships, setExistingMemberships] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Fetch current user when component mounts
  useEffect(() => {
    const fetchUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
      userRef.current = session?.user || null;
    };
    fetchUser();
  }, []); // Remove supabase.auth dependency to prevent unnecessary re-renders

  // Fetch user collections and memberships when dialog opens
  // NOTE: This is for dialog functionality only and does NOT affect bookmark display
  // Bookmark display uses the isSaved status from prompt data fetched with the main query
  useEffect(() => {
    const fetchUserAndCollections = async () => {
      if (!isOpen) return;

      setIsLoading(true);
      setError(null);

      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session?.user) {
          setError('User not authenticated');
          setIsLoading(false);
          return;
        }

        const currentUser = session.user;
        userRef.current = currentUser;

        // Create an AbortController for this request
        const abortController = new AbortController();
        abortControllerRef.current = abortController;

        // Fetch collections and membership status in parallel
        const [collections, membership] = await Promise.all([
          getUserCollectionsForDialog(userRef.current.id, abortController.signal),
          getPromptCollectionMembership(userRef.current.id, promptId, abortController.signal)
        ]);

        // Check if the request was aborted
        if (abortController.signal.aborted) {
          return;
        }

        setCollections(collections);
        setExistingMemberships(membership.collectionIds);
        setSelectedCollectionIds(membership.collectionIds);
        setInitialCollectionIds(membership.collectionIds);
      } catch (error: any) {
        if (error.name === 'AbortError') {
          console.log('Request was aborted');
          return;
        }
        console.error('Error fetching collections:', error);
        setError('Failed to load collections');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAndCollections();

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [isOpen, promptId]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm("");
      setNewCollectionName("");
      setIsCreatingCollection(false);
    }
  }, [isOpen]);

  // Removed separate fetch functions - now using a single combined fetch in useEffect

  const handleToggleCollection = (collectionId: string) => {
    setSelectedCollectionIds(prev => {
      if (prev.includes(collectionId)) {
        return prev.filter(id => id !== collectionId);
      } else {
        return [...prev, collectionId];
      }
    });
  };

  const handleCreateAndAddToCollection = async () => {
    if (!user || !newCollectionName.trim() || newCollectionName.trim().length < 3) {
      toast.error("Collection name must be at least 3 characters");
      return;
    }
    
    setIsCreatingCollection(true);
    try {
      // Create new collection
      const newCollection = await createCollection(user.id, {
        name: newCollectionName.trim(),
        is_public: false // Default to private
      });
      
      // Validate the new collection has required fields
      if (!newCollection || !newCollection.id || !newCollection.name) {
        throw new Error("Created collection is missing required fields");
      }

      // Add the new collection to the local state without refetching
      const newCollectionWithDefaults = {
        ...newCollection,
        // Ensure all required properties exist
        id: newCollection.id,
        name: newCollection.name,
        userId: newCollection.userId || user.id,
        description: newCollection.description || "",
        icon: newCollection.icon || "",
        isPublic: newCollection.isPublic || false,
        isDefault: newCollection.isDefault || false,
        promptCount: newCollection.promptCount || 0,
        viewCount: newCollection.viewCount || 0,
        createdAt: newCollection.createdAt || new Date().toISOString(),
        updatedAt: newCollection.updatedAt || new Date().toISOString()
      };
      
      // Update collections state with the new collection at the top
      setCollections(prev => [newCollectionWithDefaults, ...prev]);
      
      // Select the new collection
      setSelectedCollectionIds(prev => [...prev, newCollectionWithDefaults.id]);
      
      // Clear the input
      setNewCollectionName("");
      
      toast.success(`Created new collection "${newCollectionWithDefaults.name}"`);
    } catch (error) {
      console.error("[AddToCollectionDialog] Error creating collection:", error);
      toast.error("Failed to create new collection. Please try again.");
    } finally {
      setIsCreatingCollection(false);
    }
  };

  const handleUpdateCollections = async () => {
    if (!user) return;
    
    setIsLoading(true);
    
    try {
      // Determine which collections to add to and which to remove from
      const collectionsToAddTo = selectedCollectionIds.filter(id => !initialCollectionIds.includes(id));
      const collectionsToRemoveFrom = initialCollectionIds.filter(id => !selectedCollectionIds.includes(id));
      
      // Only proceed if there are changes to make
      if (collectionsToAddTo.length === 0 && collectionsToRemoveFrom.length === 0) {
        onClose();
        return;
      }
      
      const result = await updatePromptCollections(
        user.id,
        promptId,
        {
          addToCollectionIds: collectionsToAddTo,
          removeFromCollectionIds: collectionsToRemoveFrom
        }
      );
      
      if (result.success) {
        // Construct appropriate success message
        let successMessage = '';
        if (collectionsToAddTo.length > 0 && collectionsToRemoveFrom.length > 0) {
          successMessage = `Updated collections (${collectionsToAddTo.length} added, ${collectionsToRemoveFrom.length} removed)`;
        } else if (collectionsToAddTo.length > 0) {
          successMessage = `Added to ${collectionsToAddTo.length} collection${collectionsToAddTo.length > 1 ? 's' : ''}`;
        } else {
          successMessage = `Removed from ${collectionsToRemoveFrom.length} collection${collectionsToRemoveFrom.length > 1 ? 's' : ''}`;
        }
        
        toast.success(successMessage);

        // Determine if the prompt is still saved (in any collection)
        const finalIsSaved = selectedCollectionIds.length > 0;
        if (onSuccess) onSuccess(finalIsSaved);
        onClose();
      } else {
        toast.error("Failed to update collections");
        console.error("Error updating collections:", result.error);
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error("Unexpected error in handleUpdateCollections:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Compute filtered collections based on search term
  const filteredCollections = useMemo(() => {
    // Filter out any collections with missing name
    const validCollections = collections.filter(collection => {
      if (!collection || !collection.name) {
        return false;
      }
      return true;
    });
    
    // Then filter by search term
    return validCollections.filter(collection => 
      collection.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [collections, searchTerm]);

  // Check if search term matches an existing collection name exactly
  const searchTermMatchesExisting = useMemo(() => {
    return collections.some(
      collection => collection?.name?.toLowerCase() === searchTerm.toLowerCase()
    );
  }, [collections, searchTerm]);

  // Check if new collection name is valid for creation
  const isValidNewCollection = useMemo(() => {
    return newCollectionName.trim().length >= 3 && 
      !collections.some(c => c?.name?.toLowerCase() === newCollectionName.toLowerCase().trim());
  }, [collections, newCollectionName]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add to Collection</DialogTitle>
          <DialogDescription>
            Select one or more collections to save this prompt to, or create a new one.
          </DialogDescription>
        </DialogHeader>
        
        {/* Search input */}
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search collections..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        {/* Collections list */}
        <ScrollArea className="h-[300px] pr-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : filteredCollections.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No collections found.
            </div>
          ) : (
            <div className="space-y-2">
              {/* Show a header if there are existing memberships */}
              {existingMemberships.length > 0 && (
                <div className="mb-3 pb-2 border-b border-border">
                  <p className="text-sm font-medium text-muted-foreground">
                    Currently saved in {existingMemberships.length} collection{existingMemberships.length > 1 ? 's' : ''}
                  </p>
                </div>
              )}
              
              {filteredCollections.map((collection, index) => {
                // Ensure collection has an id, use index as fallback for key
                const collectionId = collection?.id || `temp-${index}`;
                const isInCollection = existingMemberships.includes(collectionId);
                
                return (
                  <div 
                    key={collectionId} 
                    className={`flex items-center justify-between p-3 rounded-md transition-colors ${
                      isInCollection 
                        ? "bg-accent-green/10 border border-accent-green/20 hover:bg-accent-green/15" 
                        : "hover:bg-accent/50"
                    }`}
                  >
                    <div className="flex items-center gap-2 flex-1">
                      <Label 
                        htmlFor={`col-${collection.id}`} 
                        className="flex items-center gap-2 cursor-pointer flex-1"
                      >
                        {/* Show public/private icon first */}
                        {collection.isPublic ? (
                          <Globe className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Lock className="h-4 w-4 text-muted-foreground" />
                        )}
                        {/* Then show default collection bookmark icon */}
                        {collection.isDefault && (
                          <Bookmark className="h-3 w-3 text-accent-blue" />
                        )}
                        {/* Collection name with saved indicator */}
                        <span className={`flex-1 ${isInCollection ? 'font-medium' : ''}`}>
                          {collection.name}
                        </span>

                      </Label>
                    </div>
                    <Checkbox
                      id={`col-${collection.id}`}
                      checked={selectedCollectionIds.includes(collection.id)}
                      onCheckedChange={() => handleToggleCollection(collection.id)}
                    />
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
        
        {/* Create new collection */}
        <div className="space-y-2">
          <Label htmlFor="new-collection">Create new collection</Label>
          <div className="flex gap-2">
            <Input
              id="new-collection"
              placeholder="New collection name..."
              value={newCollectionName}
              onChange={(e) => setNewCollectionName(e.target.value)}
              disabled={isCreatingCollection}
            />
            <Button
              variant="outline"
              onClick={handleCreateAndAddToCollection}
              disabled={!isValidNewCollection || isCreatingCollection}
            >
              {isCreatingCollection ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <PlusCircle className="h-4 w-4" />
              )}
            </Button>
          </div>
          {newCollectionName.trim().length > 0 && newCollectionName.trim().length < 3 && (
            <p className="text-xs text-destructive">Collection name must be at least 3 characters</p>
          )}
        </div>
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpdateCollections}
            disabled={isLoading}
          >
            {isLoading ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Updating...</>
            ) : (
              `Update Collections`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
