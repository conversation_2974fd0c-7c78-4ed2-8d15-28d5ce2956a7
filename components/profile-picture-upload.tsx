"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Loader2, Upload, X } from "lucide-react"
import { createBrowserClient } from "@supabase/ssr"

interface ProfilePictureUploadProps {
  currentAvatarUrl?: string | null;
  username: string;
  onUploadSuccess: (newAvatarUrl: string) => void;
  onDeleteSuccess: () => void;
  onError: (error: string) => void;
}

export function ProfilePictureUpload({
  currentAvatarUrl,
  username,
  onUploadSuccess,
  onDeleteSuccess,
  onError
}: ProfilePictureUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  // File validation
  const validateFile = (file: File): string | null => {
    // Check file size (1MB = 1048576 bytes)
    if (file.size > 1048576) {
      return "File size must be less than 1MB"
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
      return "File must be a JPEG, PNG, WebP, or GIF image"
    }

    return null
  }

  // Process file (shared between file input and drag & drop)
  const processFile = (file: File) => {
    const validationError = validateFile(file)
    if (validationError) {
      onError(validationError)
      return
    }

    setSelectedFile(file)
    
    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    processFile(file)
  }

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      processFile(files[0])
    }
  }

  // Upload profile picture
  const handleUpload = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    try {
      // Get upload path from backend function
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase() || 'jpg'
      const { data: uploadPath, error: pathError } = await supabase
        .rpc('get_profile_picture_upload_path', { file_extension: fileExtension })

      if (pathError) {
        throw new Error(`Failed to get upload path: ${pathError.message}`)
      }

      // Upload file to storage
      const { error: uploadError } = await supabase.storage
        .from('profile-pictures')
        .upload(uploadPath, selectedFile, {
          upsert: true,
        })

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`)
      }

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('profile-pictures')
        .getPublicUrl(uploadPath)

      // Reset state
      setSelectedFile(null)
      setPreviewUrl(null)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }

      onUploadSuccess(publicUrl)
    } catch (error) {
      console.error('Upload error:', error)
      onError(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setIsUploading(false)
    }
  }

  // Delete profile picture
  const handleDelete = async () => {
    if (!currentAvatarUrl) return

    const confirmed = window.confirm('Are you sure you want to delete your profile picture?')
    if (!confirmed) return

    setIsDeleting(true)
    try {
      const { data: deleted, error } = await supabase
        .rpc('delete_current_profile_picture')

      if (error) {
        throw new Error(`Delete failed: ${error.message}`)
      }

      if (deleted) {
        onDeleteSuccess()
      } else {
        onError('No profile picture found to delete')
      }
    } catch (error) {
      console.error('Delete error:', error)
      onError(error instanceof Error ? error.message : 'Delete failed')
    } finally {
      setIsDeleting(false)
    }
  }

  // Cancel file selection
  const handleCancel = () => {
    setSelectedFile(null)
    setPreviewUrl(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const displayUrl = previewUrl || currentAvatarUrl
  const hasCurrentPicture = currentAvatarUrl && !selectedFile

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Picture</CardTitle>
        <CardDescription>
          Upload a profile picture to personalize your account. Maximum size: 1MB.
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center justify-center space-y-4">
        {/* Avatar Display */}
        <div className="relative">
          <Avatar className="h-32 w-32 border-4 border-background shadow-md">
            <AvatarImage 
              src={displayUrl || undefined} 
              alt={`${username}'s profile picture`} 
            />
            <AvatarFallback className="text-4xl">
              {username?.substring(0, 2).toUpperCase() || "YP"}
            </AvatarFallback>
          </Avatar>
          
          {/* Delete button for current picture */}
          {hasCurrentPicture && (
            <Button
              variant="destructive"
              size="sm"
              className="absolute -top-2 -right-2 h-8 w-8 rounded-full p-0"
              onClick={handleDelete}
              disabled={isDeleting || isUploading}
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <X className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>

        {/* File Selection with Drag & Drop */}
        {!selectedFile ? (
          <div className="flex w-full flex-col space-y-2">
            <div
              className={`relative rounded-lg border-2 border-dashed p-6 text-center transition-colors ${
                isDragOver
                  ? 'border-accent-green bg-accent-green/10'
                  : 'border-input hover:border-accent-green/50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <Upload className="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
              <div className="mb-2">
                <Label htmlFor="profile-picture" className="cursor-pointer font-medium text-accent-green hover:text-accent-green/80">
                  {currentAvatarUrl ? 'Click to change picture' : 'Click to upload picture'}
                </Label>
                <p className="text-sm text-muted-foreground">
                  or drag and drop here
                </p>
              </div>
              <p className="text-xs text-muted-foreground">
                JPEG, PNG, WebP, or GIF. Max size 1MB.
              </p>
              <Input
                ref={fileInputRef}
                id="profile-picture"
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
                className="hidden"
                onChange={handleFileSelect}
                disabled={isUploading || isDeleting}
              />
            </div>
          </div>
        ) : (
          /* Upload/Cancel Actions */
          <div className="flex w-full flex-col space-y-2">
            <div className="text-sm text-center">
              <p className="font-medium">{selectedFile.name}</p>
              <p className="text-muted-foreground">
                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={handleUpload}
                disabled={isUploading}
                className="flex-1 bg-accent-green hover:bg-accent-green/90"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={isUploading}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 