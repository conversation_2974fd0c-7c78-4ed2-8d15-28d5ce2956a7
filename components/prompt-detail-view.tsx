"use client"

import React, { useState, useEffect, useRef, useMemo } from "react"
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { useToast } from "hooks/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Copy,
  Share2,
  FolderPlus,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Sparkles,
  Eye,
  Loader2,
  Check,
  User as UserIcon,
  Settings as SettingsIcon,
  Grid as GridIcon,
  Calendar as CalendarIcon,
  Send,
  Pencil,
  Trash2,
  X,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  GitFor<PERSON>,
} from "lucide-react"
import type { Prompt, Comment, Tag, Profile } from "@/lib/types"
import { formatDistanceToNow } from "date-fns"
import { supabase } from "@/lib/supabase/client"
import { voteOnPromptDirect, getUserVoteOnPromptDirect } from "@/lib/api-voting-direct";
import { highlightDoubleBracketedText } from "@/lib/utils/text-highlighting"
import { getViewerHash } from "@/lib/utils/viewer-tracking"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription } from "@/components/ui/dialog"
import AddToCollectionDialog from "@/components/add-to-collection-dialog"
import { getCategoryColorBySlug } from "@/lib/data/category-colors"
import type { User } from "@supabase/supabase-js"

type UserWithProfile = User & {
  profile?: Profile | null;
};
import type { Database } from "@/lib/database.types"
import { containsProfanity, recordProfanityAttempt, isUserOnTimeout } from "@/lib/utils/profanity-filter"
import { getRelatedPromptsForDisplay } from "@/lib/api-services"
import type { PromptCard as PromptCardType } from "@/lib/types"
import PromptGrid from "@/components/prompt-grid"
import CommunityJoinModal from "@/components/community-join-modal"
import slugify from 'slugify'

interface PromptDetailViewProps {
  prompt: Prompt
  initialComments?: Comment[]
}

type CommentDisplayDetails = Database['public']['Views']['comment_display_details']['Row'];

function convertToComment(data: CommentDisplayDetails): Comment {
  if (!data.id) {
    throw new Error('Comment ID cannot be null');
  }
  return {
    id: data.id,
    prompt_id: data.prompt_id || '',
    parent_comment_id: data.parent_comment_id,
    text: data.text || '',
    created_at: data.created_at || new Date().toISOString(),
    updated_at: data.updated_at || new Date().toISOString(),
    user: {
      id: data.user_id || '',
      username: data.author_username || "User",
      avatar_url: data.author_avatar_url,
    } as Profile,
    likes: data.like_count || 0,
    dislikes: 0,
    liked_by_user: false,
    replies: [],
  };
}

// Add configurable timeout constant near the top of the component
const PROFANITY_TIMEOUT_MINUTES = 10;
const TIMEOUT_CHECK_INTERVAL_MS = 30000; // 30 seconds

export default function PromptDetailView({ prompt, initialComments = [] }: PromptDetailViewProps) {
  const router = useRouter()
  const { toast } = useToast()
  const searchParams = useSearchParams()

  const formatStatNumber = (num: number | undefined | null): string => {
    if (num === undefined || num === null) return "0"
    const value = typeof num === "string" ? Number.parseInt(num, 10) : num
    if (value >= 1000000) return `${(value / 1000000).toFixed(1).replace(/\.0$/, "")}M`
    if (value >= 1000) return `${(value / 1000).toFixed(1).replace(/\.0$/, "")}K`
    return value.toString()
  }

  const [activeTab, setActiveTab] = useState<"about" | "comments" | "related">("about")
  const [voteCount, setVoteCount] = useState(prompt.likeCount || 0)
  const [userVote, setUserVote] = useState<"up" | "down" | null>(null)
  const [isAddToCollectionDialogOpen, setIsAddToCollectionDialogOpen] = useState(false)
  const [showAllComments, setShowAllComments] = useState(false)
  const [commentSortOption, setCommentSortOption] = useState<"top" | "newest" | "oldest">("top")
  const [isCopying, setIsCopying] = useState(false)
  const [newlyAddedCommentId, setNewlyAddedCommentId] = useState<string | null>(null)
  const [profanityError, setProfanityError] = useState<string | null>(null)
  const [isOnTimeout, setIsOnTimeout] = useState(false);
  const [timeoutRemaining, setTimeoutRemaining] = useState(0);
  const [lastCommenterUserId, setLastCommenterUserId] = useState<string | null>(null);
  const [consecutiveRepliesByParent, setConsecutiveRepliesByParent] = useState<Record<string, {userId: string, count: number}>>({});
  const [comments, setComments] = useState<Comment[]>(initialComments)
  const [isLoadingComments, setIsLoadingComments] = useState(false)
  const [commentsLoaded, setCommentsLoaded] = useState(false)
  const [currentUser, setCurrentUser] = useState<UserWithProfile | null>(null)
  const [newCommentText, setNewCommentText] = useState("")
  const [isPostingComment, setIsPostingComment] = useState(false)
  const [replyingToCommentId, setReplyingToCommentId] = useState<string | null>(null)
  const [replyText, setReplyText] = useState("")
  const [isPostingReply, setIsPostingReply] = useState(false)
  const [replyingToUsername, setReplyingToUsername] = useState<string | null>(null)
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null)
  const [editText, setEditText] = useState("")
  const [isEditingComment, setIsEditingComment] = useState(false)
  const [relatedPrompts, setRelatedPrompts] = useState<PromptCardType[]>([])
  const [isLoadingRelated, setIsLoadingRelated] = useState(false)
  const [relatedPromptsLoaded, setRelatedPromptsLoaded] = useState(false)
  const [errorRelated, setErrorRelated] = useState<string | null>(null)
  const [showCommunityModal, setShowCommunityModal] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const [shareConfirmed, setShareConfirmed] = useState(false)
  const originalPrompt = prompt.originalPrompt
  const viewRecordedRef = useRef(false)
  const shareTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  
  // Add ref to store the profanity timeout check interval
  const profanityTimeoutIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // State for related prompts save functionality
  const [selectedRelatedPromptId, setSelectedRelatedPromptId] = useState<string | null>(null)
  const [selectedRelatedPromptTitle, setSelectedRelatedPromptTitle] = useState<string>("")
  const [isRelatedPromptSaveDialogOpen, setIsRelatedPromptSaveDialogOpen] = useState(false)

  // Enhanced styling for code blocks to make them more distinct and readable with mobile optimization
  const codeBlockStyle = "rounded-lg bg-muted/50 dark:bg-muted/20 p-3 sm:p-4 md:p-6 border border-border text-sm font-mono whitespace-pre-wrap overflow-x-auto shadow-inner";

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user) {
        // Fetch the user's profile data to get the correct username and avatar
        const { data: profileData } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", session.user.id)
          .single()

        // Combine auth user with profile data
        const userWithProfile = {
          ...session.user,
          profile: profileData
        }
        setCurrentUser(userWithProfile)
      } else {
        setCurrentUser(null)
      }
    }
    fetchUser()
  }, [])

  useEffect(() => {
    let isMounted = true;
    const recordView = async () => {
      if (viewRecordedRef.current || !prompt?.id) return;
      await new Promise(resolve => setTimeout(resolve, 100));
      if (!isMounted) return;
      try {
        const viewerHash = await getViewerHash();
        if (viewerHash) {
          const viewKey = `prompt_viewed_${prompt.id}_${viewerHash}`;
          let hasViewed = false;
          try {
            hasViewed = !!localStorage.getItem(viewKey);
          } catch (storageError) {
            // Handle SecurityError in private browsing modes or restricted environments
            console.warn("[ViewTracking] Could not access localStorage:", storageError);
            // Continue without localStorage check - will record view in database
          }
          
          if (!hasViewed) {
            viewRecordedRef.current = true;
            const { error: rpcError } = await supabase.rpc('record_prompt_view', { p_prompt_id: prompt.id, p_viewer_hash: viewerHash });
            if (rpcError) {
              console.error("[ViewTracking] Error recording prompt view:", rpcError.message);
              viewRecordedRef.current = false;
            } else {
              try {
                localStorage.setItem(viewKey, 'true');
              } catch (storageError) {
                // Handle SecurityError in private browsing modes or restricted environments
                console.warn("[ViewTracking] Could not save to localStorage:", storageError);
                // Continue execution without localStorage - the view was still recorded in the database
              }
            }
          }
        }
      } catch (e) {
        console.error("[ViewTracking] Exception:", e);
        viewRecordedRef.current = false;
      }
    };
    recordView();
    return () => { isMounted = false; viewRecordedRef.current = false; };
  }, [prompt?.id]);

  useEffect(() => {
    if (comments.length > 0) {
      const topLevelComments = comments.filter(c => !c.parent_comment_id);
      if (topLevelComments.length > 0) {
        const lastComment = topLevelComments.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];
        setLastCommenterUserId(lastComment.user.id);
      }
      const replyTracking: Record<string, {userId: string, count: number}> = {};
      comments.forEach(comment => {
        if (comment.replies && comment.replies.length > 0) {
          const sortedReplies = [...comment.replies].sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
          let lastReplyUserId: string | null = null;
          let consecutiveCount = 0;
          sortedReplies.forEach(reply => {
            if (lastReplyUserId === reply.user.id) consecutiveCount++;
            else { lastReplyUserId = reply.user.id; consecutiveCount = 1; }
          });
          if (consecutiveCount > 0 && lastReplyUserId) replyTracking[comment.id] = { userId: lastReplyUserId, count: consecutiveCount };
        }
      });
      setConsecutiveRepliesByParent(replyTracking);
    }
  }, [comments]);

  useEffect(() => {
    const fetchComments = async () => {
      if (activeTab === "comments" && !commentsLoaded) {
        setIsLoadingComments(true)
        try {
          const { data: commentsData, error: commentsError } = await supabase.from("comment_display_details").select("*").eq("prompt_id", prompt.id).is("parent_comment_id", null).order(commentSortOption === "top" ? "like_count" : "created_at", { ascending: commentSortOption === "oldest" });
          if (commentsError) throw commentsError;
          const topLevelComments: CommentDisplayDetails[] = (commentsData || []) as unknown as CommentDisplayDetails[];
          const { data: repliesData, error: repliesError } = await supabase.from("comment_display_details").select("*").eq("prompt_id", prompt.id).not("parent_comment_id", "is", null).order("created_at", { ascending: true });
          if (repliesError) throw repliesError;
          const replies: CommentDisplayDetails[] = (repliesData || []) as unknown as CommentDisplayDetails[];
          const { data: { session } } = await supabase.auth.getSession();
          const userId = session?.user?.id;
          let userLikedCommentIds = new Set<string>();
          if (userId && (topLevelComments.length > 0 || replies.length > 0)) {
            const commentIds = [...topLevelComments.map((c) => c.id || '').filter(Boolean), ...replies.map((r) => r.id || '').filter(Boolean)];
            if (commentIds.length > 0) {
              const { data: likesData, error: likesError } = await supabase.from("comment_votes").select("comment_id").eq("user_id", userId).eq("vote_type", 1).in("comment_id", commentIds);
              if (likesError) throw likesError;
              if (likesData) userLikedCommentIds = new Set(likesData.map((like: any) => like.comment_id as string));
            }
          }
          const repliesByParentId = replies.reduce((acc, reply) => {
            const parentId = reply.parent_comment_id;
            if (parentId) {
              if (!acc[parentId]) acc[parentId] = [];
              acc[parentId].push(reply);
            }
            return acc;
          }, {} as Record<string, CommentDisplayDetails[]>);
          const processedComments: Comment[] = topLevelComments.map((commentData) => {
            const baseComment = convertToComment(commentData);
            return { ...baseComment, liked_by_user: userLikedCommentIds.has(commentData.id || ''), replies: (repliesByParentId[commentData.id || ''] || []).map(replyData => ({ ...convertToComment(replyData), liked_by_user: userLikedCommentIds.has(replyData.id || '') })) };
          });
          setComments(processedComments);
          setCommentsLoaded(true);
        } catch (error) {
          console.error("Error fetching comments:", error);
          toast({ title: "Error", description: "Failed to load comments. Please try again.", variant: "destructive" });
        } finally {
          setIsLoadingComments(false);
        }
      }
    }
    fetchComments()
  }, [activeTab, commentsLoaded, commentSortOption, prompt.id, supabase, toast])

  useEffect(() => {
    const fetchUserVote = async () => {
      if (currentUser?.id) {
        const { voteType } = await getUserVoteOnPromptDirect(currentUser.id, prompt.id);
        if (voteType === 1) setUserVote("up");
        else if (voteType === -1) setUserVote("down");
        else setUserVote(null);
      }
    };
    fetchUserVote();
  }, [currentUser, prompt.id]);

  useEffect(() => {
    const success = searchParams.get('success');
    if (success) toast({ title: "Success", description: success, variant: "default" });
  }, [searchParams, toast]);

  useEffect(() => {
    const fetchRelatedData = async () => {
      if (activeTab === "related" && !relatedPromptsLoaded && !isLoadingRelated) {
        if (!prompt.shortId) {
          setErrorRelated("Cannot load related prompts: Source information is missing.");
          setRelatedPromptsLoaded(true);
          return;
        }
        setIsLoadingRelated(true);
        setErrorRelated(null);
        try {
          const data = await getRelatedPromptsForDisplay(prompt.shortId, 6);
          setRelatedPrompts(data);
          setRelatedPromptsLoaded(true);
        } catch (error: any) {
          setErrorRelated(error.message || "Failed to load related prompts.");
          setRelatedPrompts([]);
        } finally {
          setIsLoadingRelated(false);
        }
      }
    };
    fetchRelatedData();
  }, [activeTab, prompt.shortId, relatedPromptsLoaded, isLoadingRelated]);

  // Add cleanup effect for profanity timeout interval
  useEffect(() => {
    return () => {
      // Clear the profanity timeout interval on component unmount or prompt change
      if (profanityTimeoutIntervalRef.current) {
        clearInterval(profanityTimeoutIntervalRef.current);
        profanityTimeoutIntervalRef.current = null;
      }
    };
  }, [prompt.id]);

  // Helper function to handle profanity timeout logic
  const handleProfanityTimeout = (userId: string) => {
    setIsOnTimeout(true);
    setTimeoutRemaining(PROFANITY_TIMEOUT_MINUTES * 60);
    setProfanityError(`Your content wasn't appropriate. You won't be able to comment for a while.`);
    
    // Only create a new interval if one doesn't already exist
    if (!profanityTimeoutIntervalRef.current) {
      profanityTimeoutIntervalRef.current = setInterval(() => {
        if (!isUserOnTimeout(userId)) {
          // Clear the interval and reset timeout state
          if (profanityTimeoutIntervalRef.current) {
            clearInterval(profanityTimeoutIntervalRef.current);
            profanityTimeoutIntervalRef.current = null;
          }
          setIsOnTimeout(false);
          setProfanityError("");
          setTimeoutRemaining(0);
        }
      }, TIMEOUT_CHECK_INTERVAL_MS);
    }
  };

  const handleTabChange = (value: string) => setActiveTab(value as "about" | "comments" | "related")

  // Simplified handlers for brevity - in a real implementation these would be fully implemented
  const handleUpvote = async () => {
    if (!currentUser) {
      toast({ title: "Authentication required", description: "Please sign in to vote on prompts.", variant: "destructive" });
      return;
    }
    try {
      const newVote = userVote === "up" ? null : "up";
      await voteOnPromptDirect(currentUser.id, prompt.id, newVote === "up" ? 1 : 0);
      setUserVote(newVote);
      
      // Calculate vote count adjustment based on previous and new vote states
      setVoteCount(prev => {
        if (newVote === "up") {
          // Adding an upvote
          if (userVote === "down") {
            // Switching from downvote to upvote: +2 (remove -1, add +1)
            return prev + 2;
          } else {
            // Adding upvote from no vote: +1
            return prev + 1;
          }
        } else {
          // Removing upvote (newVote is null)
          return prev - 1;
        }
      });
      
      toast({ title: "Vote recorded", description: newVote === "up" ? "Thanks for your upvote!" : "Vote removed.", variant: "default" });
    } catch (error) {
      console.error("Error voting:", error);
      toast({ title: "Error", description: "Failed to record vote. Please try again.", variant: "destructive" });
    }
  };

  const handleDownvote = async () => {
    if (!currentUser) {
      toast({ title: "Authentication required", description: "Please sign in to vote on prompts.", variant: "destructive" });
      return;
    }
    try {
      const newVote = userVote === "down" ? null : "down";
      await voteOnPromptDirect(currentUser.id, prompt.id, newVote === "down" ? -1 : 0);
      setUserVote(newVote);
      
      // Calculate vote count adjustment based on previous and new vote states
      setVoteCount(prev => {
        if (newVote === "down") {
          // Adding a downvote
          if (userVote === "up") {
            // Switching from upvote to downvote: -2 (remove +1, add -1)
            return prev - 2;
          } else {
            // Adding downvote from no vote: -1
            return prev - 1;
          }
        } else {
          // Removing downvote (newVote is null)
          return prev + 1;
        }
      });
      
      toast({ title: "Vote recorded", description: newVote === "down" ? "Thanks for your feedback!" : "Vote removed.", variant: "default" });
    } catch (error) {
      console.error("Error voting:", error);
      toast({ title: "Error", description: "Failed to record vote. Please try again.", variant: "destructive" });
    }
  };

  const handleCopyPrompt = async () => {
    setIsCopying(true);
    try {
      await navigator.clipboard.writeText(prompt.text);
      toast({ title: "Copied!", description: "Prompt copied to clipboard.", variant: "default" });
    } catch (error) {
      toast({ title: "Error", description: "Failed to copy prompt.", variant: "destructive" });
    } finally {
      setTimeout(() => setIsCopying(false), 1000);
    }
  };

  const handleShare = async () => {
    setIsSharing(true);
    try {
      const url = window.location.href;
      await navigator.clipboard.writeText(url);
      setShareConfirmed(true);
      toast({ title: "Link copied!", description: "Prompt link copied to clipboard.", variant: "default" });
      setTimeout(() => {
        setShareConfirmed(false);
        setIsSharing(false);
      }, 2000);
    } catch (error) {
      toast({ title: "Error", description: "Failed to copy link.", variant: "destructive" });
      setIsSharing(false);
    }
  };

  const requireAuth = (action: () => void) => {
    if (!currentUser) {
      setShowCommunityModal(true);
    } else {
      action();
    }
  }

  const safeFormatDate = (dateString: string | Date | undefined | null) => {
    if (!dateString) return "Unknown date"
    try {
      const date = typeof dateString === "string" ? new Date(dateString) : dateString
      if (isNaN(date.getTime())) return "Unknown date"
      return formatDistanceToNow(date, { addSuffix: true })
    } catch (error) {
      console.error("Error formatting date:", error)
      return "Unknown date"
    }
  }

  const handleCommentLike = async (commentId: string) => {
    const { data: { session } } = await supabase.auth.getSession()
    if (!session?.user) {
      router.push('/sign-in')
      return
    }

    const user = session.user

    let commentIndex = -1, isReply = false, parentIndex = -1, replyIndex = -1;
    commentIndex = comments.findIndex((c) => c.id === commentId)
    if (commentIndex === -1) {
      for (let i = 0; i < comments.length; i++) {
        const comment = comments[i];
        if (comment.replies && Array.isArray(comment.replies)) {
          replyIndex = comment.replies.findIndex((r) => r.id === commentId);
          if (replyIndex !== -1) {
            isReply = true; parentIndex = i; break;
          }
        }
      }
    }
    if (commentIndex === -1 && !isReply) return

    let currentComment: Comment | undefined;
    let isCurrentlyLiked: boolean | undefined;

    if (isReply) {
      if (comments[parentIndex].replies && Array.isArray(comments[parentIndex].replies)) {
        currentComment = comments[parentIndex].replies![replyIndex]
        isCurrentlyLiked = currentComment.liked_by_user
      } else { return; }
    } else {
      currentComment = comments[commentIndex]
      isCurrentlyLiked = currentComment.liked_by_user
    }
    if (!currentComment) return;

    const originalComments = [...comments];
    const updatedComments = JSON.parse(JSON.stringify(comments));

    if (isReply) {
      if (updatedComments[parentIndex].replies && Array.isArray(updatedComments[parentIndex].replies)) {
        updatedComments[parentIndex].replies[replyIndex] = {
          ...currentComment,
          liked_by_user: !isCurrentlyLiked,
          likes: isCurrentlyLiked ? (currentComment.likes || 1) - 1 : (currentComment.likes || 0) + 1,
        }
      } else { return; }
    } else {
      updatedComments[commentIndex] = {
        ...currentComment,
        liked_by_user: !isCurrentlyLiked,
        likes: isCurrentlyLiked ? (currentComment.likes || 1) - 1 : (currentComment.likes || 0) + 1,
      }
    }
    setComments(updatedComments)

    try {
      if (isCurrentlyLiked) {
        await supabase.from("comment_votes").delete().eq("user_id", user.id).eq("comment_id", commentId)
      } else {
        await supabase.from("comment_votes").upsert({
          user_id: user.id, comment_id: commentId, vote_type: 1, created_at: new Date().toISOString(),
        })
      }
    } catch (error) {
      console.error("Error updating comment like:", error)
      setComments(originalComments)
      toast({ title: "Error", description: "Failed to update like status. Please try again.", variant: "destructive" })
    }
  };

  const handlePostComment = async () => {
    if (!currentUser || !newCommentText.trim()) return;
    
    if (lastCommenterUserId === currentUser.id) {
      setProfanityError("You can't post two comments in a row. Please wait for someone else to respond.");
      return;
    }

    if (containsProfanity(newCommentText.trim())) {
      const result = recordProfanityAttempt(currentUser.id);
      if (result.onTimeout) {
        handleProfanityTimeout(currentUser.id);
      } else {
        setProfanityError(`Your comment contains inappropriate language. Please revise your wording.`);
      }

      setIsPostingComment(false);
      return;
    }

    setProfanityError(null);

    setIsPostingComment(true);
    try {
      const { data: commentId, error } = await supabase.rpc('add_comment', {
        p_user_id: currentUser.id,
        p_prompt_id: prompt.id,
        p_text: newCommentText.trim()
      }) as { data: string | null, error: any };

      if (error) {
        let errorMessage = "Could not post your comment due to a server error. Please try again later.";
        
        if (error.code === 'P0001' || error.message?.includes('Prompt not found or not public')) {
          errorMessage = "This prompt is not available for commenting. It may have been removed or made private.";
        } else if (error.code === 'P0002' || error.message?.includes('Parent comment not found')) {
          errorMessage = "The comment you are trying to reply to is no longer available or has been moved.";
        }
        
        toast({
          variant: "destructive",
          title: "Error",
          description: errorMessage,
        });
        return;
      }

      if (!commentId) {
        throw new Error("Failed to create comment - no ID returned");
      }

      const newComment: Comment = {
        id: commentId,
        prompt_id: typeof prompt.id === 'string' ? prompt.id : '',
        text: newCommentText.trim(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user: {
          id: currentUser.id,
          username: currentUser.profile?.username || 'User',
          avatar_url: currentUser.profile?.avatar_url,
        } as Profile,
        likes: 0,
        dislikes: 0,
        liked_by_user: false,
        replies: [],
      };

      setComments(prev => [newComment, ...prev]);
      setNewlyAddedCommentId(commentId);
      setLastCommenterUserId(currentUser.id);
      setNewCommentText('');

      toast({
        title: "Comment posted",
        description: "Your comment has been posted successfully.",
      });
    } catch (error) {
      console.error("Error posting comment:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to post your comment. Please try again.",
      });
    } finally {
      setIsPostingComment(false);
    }
  };

  const hasBeenEdited = (createdAt: string, updatedAt: string) => {
    if (createdAt === updatedAt) return false;
    const created = new Date(createdAt);
    const updated = new Date(updatedAt);
    const diffSeconds = Math.floor((updated.getTime() - created.getTime()) / 1000);
    if (diffSeconds < 5) return false;
    return true;
  }
  
  const isWithinEditWindow = (createdAt: string) => {
    const commentDate = new Date(createdAt);
    const now = new Date();
    const diffMs = now.getTime() - commentDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    return diffMins < 5;
  }
  
  const handleStartEditComment = (commentId: string, currentText: string) => {
    setEditingCommentId(commentId);
    setEditText(currentText);
  };

  const handleCancelEditComment = () => {
    setEditingCommentId(null);
    setEditText("");
  };

  const handleSaveEditComment = async (commentId: string, isReply: boolean = false, parentId?: string) => {
    if (!currentUser || !editText.trim()) return;

    if (containsProfanity(editText.trim())) {
      setProfanityError("Your edit contains inappropriate language. Please revise your wording.");
      return;
    }

    setIsEditingComment(true);
    try {
      const { error } = await supabase
        .from("comments")
        .update({ text: editText.trim(), updated_at: new Date().toISOString() })
        .eq("id", commentId);

      if (error) throw error;

      const updatedComments = [...comments];
      if (isReply && parentId) {
        const parentIndex = updatedComments.findIndex(c => c.id === parentId);
        if (parentIndex !== -1 && updatedComments[parentIndex].replies) {
          const replyIndex = updatedComments[parentIndex].replies!.findIndex(r => r.id === commentId);
          if (replyIndex !== -1) {
            updatedComments[parentIndex].replies![replyIndex] = {
              ...updatedComments[parentIndex].replies![replyIndex],
              text: editText.trim(),
              updated_at: new Date().toISOString()
            };
          }
        }
      } else {
        const commentIndex = updatedComments.findIndex(c => c.id === commentId);
        if (commentIndex !== -1) {
          updatedComments[commentIndex] = {
            ...updatedComments[commentIndex],
            text: editText.trim(),
            updated_at: new Date().toISOString()
          };
        }
      }

      setComments(updatedComments);
      setEditingCommentId(null);
      setEditText("");
      toast({ title: "Comment updated", description: "Your comment has been updated successfully." });
    } catch (error) {
      console.error("Error updating comment:", error);
      toast({ variant: "destructive", title: "Error", description: "Failed to update comment. Please try again." });
    } finally {
      setIsEditingComment(false);
    }
  };

  const handleDeleteComment = async (commentId: string, isReply: boolean = false, parentId?: string) => {
    if (!currentUser) return;

    try {
      const { error } = await supabase.from("comments").delete().eq("id", commentId);
      if (error) throw error;

      const updatedComments = [...comments];
      if (isReply && parentId) {
        const parentIndex = updatedComments.findIndex(c => c.id === parentId);
        if (parentIndex !== -1 && updatedComments[parentIndex].replies) {
          updatedComments[parentIndex].replies = updatedComments[parentIndex].replies!.filter(r => r.id !== commentId);
        }
      } else {
        const filteredComments = updatedComments.filter(c => c.id !== commentId);
        setComments(filteredComments);
        return;
      }

      setComments(updatedComments);
      toast({ title: "Comment deleted", description: "Your comment has been deleted successfully." });
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast({ variant: "destructive", title: "Error", description: "Failed to delete comment. Please try again." });
    }
  };

  const handlePostReply = async (parentId: string) => {
    if (!currentUser || !replyText.trim()) return;

    const parentComment = comments.find(c => c.id === parentId);
    if (!parentComment) return;

    const consecutiveInfo = consecutiveRepliesByParent[parentId];
    if (consecutiveInfo && consecutiveInfo.userId === currentUser.id && consecutiveInfo.count >= 3) {
      setProfanityError("You've replied to this comment several times in a row. Please wait for others to join the conversation.");
      return;
    }

    if (containsProfanity(replyText.trim())) {
      const result = recordProfanityAttempt(currentUser.id);
      if (result.onTimeout) {
        handleProfanityTimeout(currentUser.id);
      } else {
        setProfanityError(`Your reply contains inappropriate language. Please revise your wording.`);
      }
      return;
    }

    setProfanityError(null);
    setIsPostingReply(true);

    try {
      const { data: replyId, error } = await supabase.rpc('add_comment', {
        p_user_id: currentUser.id,
        p_prompt_id: prompt.id,
        p_text: replyText.trim(),
        p_parent_comment_id: parentId
      }) as { data: string | null, error: any };

      if (error) throw error;
      if (!replyId) throw new Error("Failed to create reply - no ID returned");

      const newReply: Comment = {
        id: replyId,
        prompt_id: typeof prompt.id === 'string' ? prompt.id : '',
        parent_comment_id: parentId,
        text: replyText.trim(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user: {
          id: currentUser.id,
          username: currentUser.profile?.username || 'User',
          avatar_url: currentUser.profile?.avatar_url,
        } as Profile,
        likes: 0,
        dislikes: 0,
        liked_by_user: false,
        replies: [],
      };

      const updatedComments = [...comments];
      const parentIndex = updatedComments.findIndex(c => c.id === parentId);
      if (parentIndex !== -1) {
        if (!updatedComments[parentIndex].replies) {
          updatedComments[parentIndex].replies = [];
        }
        updatedComments[parentIndex].replies!.push(newReply);
      }

      setComments(updatedComments);
      setReplyingToCommentId(null);
      setReplyText('');
      setReplyingToUsername(null);

      toast({ title: "Reply posted", description: "Your reply has been posted successfully." });
    } catch (error) {
      console.error("Error posting reply:", error);
      toast({ variant: "destructive", title: "Error", description: "Failed to post your reply. Please try again." });
    } finally {
      setIsPostingReply(false);
    }
  };

  const sortedComments = useMemo(() => {
    return [...comments].sort((a, b) => {
      if (a.id === newlyAddedCommentId) return -1;
      if (b.id === newlyAddedCommentId) return 1;
      if (commentSortOption === "top") return (b.likes || 0) - (a.likes || 0);
      const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
      const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
      if (commentSortOption === "newest") return dateB - dateA;
      return dateA - dateB;
    });
  }, [comments, newlyAddedCommentId, commentSortOption]);

  const displayedComments = showAllComments ? sortedComments : sortedComments.slice(0, 5);
  const categoryName = typeof prompt.category === "string" ? prompt.category : prompt.category?.name ?? "Other";
  const categorySlug = typeof prompt.category === "string" ? prompt.category.toLowerCase() : prompt.category?.slug ?? "other";
  const toolName = prompt.tool?.name ?? "Other";
  const toolSlug = prompt.tool?.slug ?? "other";
  const formattedDate = prompt.createdAt ? new Date(prompt.createdAt).toLocaleDateString("en-US", { year: "numeric", month: "long", day: "numeric" }) : "Unknown date";
  const username = prompt.user?.username || prompt.author || "anonymous";
  const categoryDisplayColor = getCategoryColorBySlug(categorySlug)?.primary || 'var(--accent-purple)';

  // Handle save toggle for related prompts
  const handleRelatedPromptToggleSave = async (promptId: string, currentSaveStatus: boolean) => {
    requireAuth(() => {
      // Find the prompt to get its title
      const relatedPrompt = relatedPrompts.find(p => p.id === promptId)
      if (relatedPrompt) {
        setSelectedRelatedPromptId(promptId)
        setSelectedRelatedPromptTitle(relatedPrompt.title)
        setIsRelatedPromptSaveDialogOpen(true)
      }
    })
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumbs - slightly more subtle */}
        <div className="flex items-center gap-1 py-4 text-xs text-muted-foreground/80">
          <Link href="/" className="hover:text-primary hover:underline">Home</Link>
          <span>/</span>
          <Link href={`/category/${categorySlug}`} className="hover:text-primary hover:underline">{categoryName}</Link>
        </div>

        <div className="mb-6">
          <h1 className="text-3xl sm:text-4xl font-extrabold tracking-tight text-foreground lg:text-5xl">{prompt.title}</h1>
          {prompt.description && <p className="mt-2 text-base sm:text-lg text-muted-foreground">{prompt.description}</p>}
        </div>

        <div className="flex flex-col lg:grid lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Main content area */}
          <div className="lg:col-span-2 space-y-6 lg:space-y-8">
            {prompt.imageUrl && (
              <Dialog>
                <DialogTrigger asChild>
                  <div className="relative h-[250px] sm:h-[300px] lg:h-[350px] w-full cursor-pointer overflow-hidden rounded-xl shadow-lg group">
                    <Image
                      src={prompt.imageUrl || "/placeholder.svg"}
                      alt={`${prompt.title} - Example image`}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                      priority={true}
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 66vw, 700px"
                      quality={90}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-4xl border-border bg-background">
                  <DialogHeader><DialogTitle>{prompt.title}</DialogTitle><DialogDescription>Full-size image preview.</DialogDescription></DialogHeader>
                  <div className="relative h-[80vh] w-full"><Image src={prompt.imageUrl || "/placeholder.svg"} alt={`${prompt.title} - Full size image`} fill className="object-contain" sizes="90vw" quality={95} /></div>
                </DialogContent>
              </Dialog>
            )}

            {/* Enhanced Tab Navigation with collections page styling */}
            <div className="rounded-xl border bg-card shadow-sm">
              <div className="border-b border-border/50">
                <div className="flex bg-muted/30 rounded-lg p-1 m-4 sm:m-4">
                  {[
                    { id: "about", label: "About", icon: <UserIcon className="h-4 w-4"/> },
                    { id: "comments", label: "Comments", count: prompt.commentCount, icon: <MessageSquare className="h-4 w-4"/> },
                    { id: "related", label: "Related", icon: <GitFork className="h-4 w-4" /> },
                  ].map((tab) => (
                    <Button
                      key={tab.id}
                      variant="ghost"
                      className={`flex-1 min-w-0 rounded-md py-3 sm:py-4 px-3 sm:px-4 font-medium transition-all duration-300 ease-in-out min-h-[44px] ${
                        activeTab === tab.id
                          ? "bg-primary text-primary-foreground shadow-md border border-primary/20 transform scale-[1.02]"
                          : "text-muted-foreground hover:text-foreground hover:bg-background/80 hover:shadow-sm"
                      }`}
                      onClick={() => handleTabChange(tab.id)}
                    >
                      <span className="flex items-center justify-center gap-1 sm:gap-2 min-w-0">
                        {tab.icon}
                        <span className={`transition-all duration-300 truncate text-sm sm:text-base ${
                          activeTab === tab.id ? "font-semibold" : "font-medium"
                        }`}>
                          {tab.label}
                        </span>
                        {tab.count !== undefined && tab.count > 0 && (
                          <Badge
                            variant="secondary"
                            className={`text-xs sm:text-sm transition-all duration-300 flex-shrink-0 px-2 py-1 ${
                              activeTab === tab.id
                                ? "bg-primary-foreground/20 text-primary-foreground border border-primary-foreground/30"
                                : "bg-muted text-muted-foreground hover:bg-background/80"
                            }`}
                          >
                            {formatStatNumber(tab.count)}
                          </Badge>
                        )}
                      </span>
                    </Button>
                  ))}
                </div>
              </div>

              <div className="p-4 sm:p-6 space-y-6">
                {activeTab === 'about' && (
                  <>
                    {/* Prompt Text */}
                    <div className="space-y-2">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <h2 className="text-xl font-semibold text-foreground">Prompt</h2>
                        <div className="flex flex-col sm:flex-row gap-2">
                          {currentUser && currentUser.id === prompt.user?.id && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-green-500/50 text-foreground hover:bg-green-500/20 md:size-default"
                              onClick={() => router.push(`/prompt/edit/${prompt.shortId}`)}
                            >
                              <Pencil className="mr-2 h-4 w-4" />
                              Edit
                            </Button>
                          )}
                          <Button variant="outline" size="sm" className="border-blue-500/70 text-blue-500 hover:bg-blue-500/10 md:size-default" onClick={handleCopyPrompt} disabled={isCopying}>
                            {isCopying ? <Check className="mr-2 h-4 w-4" /> : <Copy className="mr-2 h-4 w-4" />}
                            {isCopying ? "Copied" : "Copy"}
                          </Button>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">Copy and use this prompt with {toolName}.</p>
                      <div className={codeBlockStyle}>
                        {highlightDoubleBracketedText(prompt.text)}
                      </div>
                    </div>
                    {/* Instructions */}
                    {prompt.instructions && (
                      <div className="space-y-2">
                        <h2 className="text-xl font-semibold text-foreground">Instructions</h2>
                        <div className={codeBlockStyle}>
                          {highlightDoubleBracketedText(prompt.instructions)}
                        </div>
                      </div>
                    )}
                    {/* Example Input */}
                    {prompt.exampleInput && (
                      <div className="space-y-2">
                        <h2 className="text-xl font-semibold text-foreground">Example Input</h2>
                         <div className={codeBlockStyle}>
                          {highlightDoubleBracketedText(prompt.exampleInput)}
                        </div>
                      </div>
                    )}
                    {/* Example Output */}
                    {prompt.exampleOutput && (
                       <div className="space-y-2">
                        <h2 className="text-xl font-semibold text-foreground">Example Output</h2>
                        <div className={codeBlockStyle}>
                          {highlightDoubleBracketedText(prompt.exampleOutput)}
                        </div>
                      </div>
                    )}
                  </>
                )}
                {activeTab === 'comments' && (
                    <div className="space-y-6">
                        <div className="flex items-center justify-between">
                            <h2 className="text-xl font-semibold text-foreground">Comments</h2>
                            {comments.length > 0 && (
                                <Select value={commentSortOption} onValueChange={(value: "top" | "newest" | "oldest") => setCommentSortOption(value)}>
                                    <SelectTrigger className="w-32">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="top">Top</SelectItem>
                                        <SelectItem value="newest">Newest</SelectItem>
                                        <SelectItem value="oldest">Oldest</SelectItem>
                                    </SelectContent>
                                </Select>
                            )}
                        </div>

                        {/* Comment Input */}
                        {currentUser ? (
                            <div className="space-y-3">
                                <div className="flex items-start gap-3">
                                    <Avatar className="h-8 w-8 flex-shrink-0">
                                        <AvatarImage src={currentUser.profile?.avatar_url || "/placeholder-user.jpg"} alt={currentUser.profile?.username || "User"} />
                                        <AvatarFallback>{(currentUser.profile?.username || "U").charAt(0).toUpperCase()}</AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1 space-y-2">
                                        <Textarea
                                            placeholder="Share your thoughts about this prompt..."
                                            value={newCommentText}
                                            onChange={(e) => setNewCommentText(e.target.value)}
                                            className="min-h-[80px] resize-none"
                                            disabled={isPostingComment || isOnTimeout}
                                        />
                                        {profanityError && (
                                            <div className="flex items-center gap-2 text-sm text-destructive">
                                                <AlertCircle className="h-4 w-4" />
                                                {profanityError}
                                            </div>
                                        )}
                                        <div className="flex justify-end">
                                            <Button 
                                                onClick={handlePostComment} 
                                                disabled={!newCommentText.trim() || isPostingComment || isOnTimeout}
                                                size="sm"
                                            >
                                                {isPostingComment ? (
                                                    <>
                                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                        Posting...
                                                    </>
                                                ) : (
                                                    <>
                                                        <Send className="mr-2 h-4 w-4" />
                                                        Post Comment
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <div className="text-center py-4 border rounded-lg bg-muted/20">
                                <p className="text-muted-foreground mb-3">Join the conversation! Sign in to share your thoughts.</p>
                                <Button onClick={() => router.push('/sign-in')} variant="outline">
                                    Sign In to Comment
                                </Button>
                            </div>
                        )}

                        {/* Comments List */}
                        {isLoadingComments ? (
                            <div className="flex h-32 items-center justify-center">
                                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            </div>
                        ) : comments.length > 0 ? (
                            <div className="space-y-4">
                                {displayedComments.map((comment) => (
                                    <div key={comment.id} className={`border rounded-lg p-4 bg-card ${comment.id === newlyAddedCommentId ? 'ring-2 ring-primary/50' : ''}`}>
                                        <div className="flex items-start gap-3">
                                            <Link href={`/user/${comment.user.username}`} className="flex-shrink-0">
                                                <Avatar className="h-8 w-8">
                                                    <AvatarImage src={comment.user.avatar_url || "/placeholder-user.jpg"} alt={comment.user.username} />
                                                    <AvatarFallback>{comment.user.username.charAt(0).toUpperCase()}</AvatarFallback>
                                                </Avatar>
                                            </Link>
                                            <div className="flex-1">
                                                <div className="flex items-center gap-2 mb-1">
                                                    <Link href={`/user/${comment.user.username}`} className="font-medium text-sm hover:underline">{comment.user.username}</Link>
                                                    <span className="text-xs text-muted-foreground">
                                                        {safeFormatDate(comment.created_at)}
                                                    </span>
                                                    {hasBeenEdited(comment.created_at, comment.updated_at) && (
                                                        <span className="text-xs text-muted-foreground">(edited)</span>
                                                    )}
                                                </div>
                                                
                                                {editingCommentId === comment.id ? (
                                                    <div className="space-y-2">
                                                        <Textarea
                                                            value={editText}
                                                            onChange={(e) => setEditText(e.target.value)}
                                                            className="min-h-[60px] resize-none"
                                                            disabled={isEditingComment}
                                                        />
                                                        <div className="flex gap-2">
                                                            <Button size="sm" onClick={() => handleSaveEditComment(comment.id)} disabled={isEditingComment || !editText.trim()}>
                                                                {isEditingComment ? <Loader2 className="mr-2 h-3 w-3 animate-spin" /> : <Check className="mr-2 h-3 w-3" />}
                                                                Save
                                                            </Button>
                                                            <Button size="sm" variant="outline" onClick={handleCancelEditComment} disabled={isEditingComment}>
                                                                <X className="mr-2 h-3 w-3" />
                                                                Cancel
                                                            </Button>
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <>
                                                        <p className="text-base sm:text-sm text-foreground mb-2">{comment.text}</p>
                                                        <div className="flex items-center gap-2">
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                className={`h-8 px-3 sm:h-6 sm:px-2 text-xs ${comment.liked_by_user ? 'text-primary' : 'text-muted-foreground'}`}
                                                                onClick={() => handleCommentLike(comment.id)}
                                                            >
                                                                <ThumbsUp className={`h-3 w-3 mr-1 ${comment.liked_by_user ? 'fill-current' : ''}`} />
                                                                {comment.likes}
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                className="h-8 px-3 sm:h-6 sm:px-2 text-xs text-muted-foreground"
                                                                onClick={() => {
                                                                    if (replyingToCommentId === comment.id) {
                                                                        setReplyingToCommentId(null)
                                                                        setReplyText("")
                                                                        setReplyingToUsername(null)
                                                                    } else {
                                                                        setReplyingToCommentId(comment.id)
                                                                        setReplyText(`@${comment.user.username} `)
                                                                        setReplyingToUsername(comment.user.username)
                                                                    }
                                                                }}
                                                            >
                                                                Reply
                                                            </Button>
                                                            {currentUser?.id === comment.user.id && (
                                                                <>
                                                                    {isWithinEditWindow(comment.created_at) && (
                                                                        <Button
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            className="h-8 px-3 sm:h-6 sm:px-2 text-xs text-muted-foreground"
                                                                            onClick={() => handleStartEditComment(comment.id, comment.text)}
                                                                        >
                                                                            <Pencil className="h-3 w-3 mr-1" />
                                                                            Edit
                                                                        </Button>
                                                                    )}
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        className="h-8 px-3 sm:h-6 sm:px-2 text-xs text-destructive"
                                                                        onClick={() => handleDeleteComment(comment.id)}
                                                                    >
                                                                        <Trash2 className="h-3 w-3 mr-1" />
                                                                        Delete
                                                                    </Button>
                                                                </>
                                                            )}
                                                        </div>
                                                    </>
                                                )}

                                                {/* Reply Input */}
                                                {replyingToCommentId === comment.id && currentUser && (
                                                    <div className="mt-3 pl-4 border-l-2 border-muted">
                                                        <div className="flex items-start gap-3">
                                                            <Avatar className="h-6 w-6 flex-shrink-0">
                                                                <AvatarImage src={currentUser.profile?.avatar_url || "/placeholder-user.jpg"} alt={currentUser.profile?.username || "User"} />
                                                                <AvatarFallback className="text-xs">{(currentUser.profile?.username || "U").charAt(0).toUpperCase()}</AvatarFallback>
                                                            </Avatar>
                                                            <div className="flex-1 space-y-2">
                                                                <Textarea
                                                                    placeholder={`Reply to ${comment.user.username}...`}
                                                                    value={replyText}
                                                                    onChange={(e) => setReplyText(e.target.value)}
                                                                    className="min-h-[60px] resize-none text-sm"
                                                                    disabled={isPostingReply}
                                                                />
                                                                <div className="flex gap-2">
                                                                    <Button size="sm" onClick={() => handlePostReply(comment.id)} disabled={!replyText.trim() || isPostingReply}>
                                                                        {isPostingReply ? <Loader2 className="mr-2 h-3 w-3 animate-spin" /> : <Send className="mr-2 h-3 w-3" />}
                                                                        Reply
                                                                    </Button>
                                                                    <Button size="sm" variant="outline" onClick={() => {
                                                                        setReplyingToCommentId(null)
                                                                        setReplyText("")
                                                                        setReplyingToUsername(null)
                                                                    }} disabled={isPostingReply}>
                                                                        Cancel
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}

                                                {/* Replies */}
                                                {comment.replies && comment.replies.length > 0 && (
                                                    <div className="mt-3 pl-4 border-l-2 border-muted space-y-3">
                                                        {comment.replies.map((reply) => (
                                                            <div key={reply.id} className="flex items-start gap-3">
                                                                <Link href={`/user/${reply.user.username}`} className="flex-shrink-0">
                                                                    <Avatar className="h-6 w-6">
                                                                        <AvatarImage src={reply.user.avatar_url || "/placeholder-user.jpg"} alt={reply.user.username} />
                                                                        <AvatarFallback className="text-xs">{reply.user.username.charAt(0).toUpperCase()}</AvatarFallback>
                                                                    </Avatar>
                                                                </Link>
                                                                <div className="flex-1">
                                                                    <div className="flex items-center gap-2 mb-1">
                                                                        <Link href={`/user/${reply.user.username}`} className="font-medium text-xs hover:underline">{reply.user.username}</Link>
                                                                        <span className="text-xs text-muted-foreground">
                                                                            {safeFormatDate(reply.created_at)}
                                                                        </span>
                                                                        {hasBeenEdited(reply.created_at, reply.updated_at) && (
                                                                            <span className="text-xs text-muted-foreground">(edited)</span>
                                                                        )}
                                                                    </div>
                                                                    
                                                                    {editingCommentId === reply.id ? (
                                                                        <div className="space-y-2">
                                                                            <Textarea
                                                                                value={editText}
                                                                                onChange={(e) => setEditText(e.target.value)}
                                                                                className="min-h-[50px] resize-none text-sm"
                                                                                disabled={isEditingComment}
                                                                            />
                                                                            <div className="flex gap-2">
                                                                                <Button size="sm" onClick={() => handleSaveEditComment(reply.id, true, comment.id)} disabled={isEditingComment || !editText.trim()}>
                                                                                    {isEditingComment ? <Loader2 className="mr-2 h-3 w-3 animate-spin" /> : <Check className="mr-2 h-3 w-3" />}
                                                                                    Save
                                                                                </Button>
                                                                                <Button size="sm" variant="outline" onClick={handleCancelEditComment} disabled={isEditingComment}>
                                                                                    <X className="mr-2 h-3 w-3" />
                                                                                    Cancel
                                                                                </Button>
                                                                            </div>
                                                                        </div>
                                                                    ) : (
                                                                        <>
                                                                            <p className="text-sm sm:text-xs text-foreground mb-1">{reply.text}</p>
                                                                            <div className="flex items-center gap-2">
                                                                                <Button
                                                                                    variant="ghost"
                                                                                    size="sm"
                                                                                    className={`h-7 px-2 sm:h-5 sm:px-1 text-xs ${reply.liked_by_user ? 'text-primary' : 'text-muted-foreground'}`}
                                                                                    onClick={() => handleCommentLike(reply.id)}
                                                                                >
                                                                                    <ThumbsUp className={`h-3 w-3 mr-1 ${reply.liked_by_user ? 'fill-current' : ''}`} />
                                                                                    {reply.likes}
                                                                                </Button>
                                                                                <Button
                                                                                    variant="ghost"
                                                                                    size="sm"
                                                                                    className="h-7 px-2 sm:h-5 sm:px-1 text-xs text-muted-foreground"
                                                                                    onClick={() => {
                                                                                        if (replyingToCommentId === comment.id && replyingToUsername === reply.user.username) {
                                                                                            setReplyingToCommentId(null)
                                                                                            setReplyText("")
                                                                                            setReplyingToUsername(null)
                                                                                        } else {
                                                                                            setReplyingToCommentId(comment.id)
                                                                                            setReplyText(`@${reply.user.username} `)
                                                                                            setReplyingToUsername(reply.user.username)
                                                                                        }
                                                                                    }}
                                                                                >
                                                                                    Reply
                                                                                </Button>
                                                                                {currentUser?.id === reply.user.id && (
                                                                                    <>
                                                                                        {isWithinEditWindow(reply.created_at) && (
                                                                                            <Button
                                                                                                variant="ghost"
                                                                                                size="sm"
                                                                                                className="h-7 px-2 sm:h-5 sm:px-1 text-xs text-muted-foreground"
                                                                                                onClick={() => handleStartEditComment(reply.id, reply.text)}
                                                                                            >
                                                                                                <Pencil className="h-3 w-3 mr-1" />
                                                                                                Edit
                                                                                            </Button>
                                                                                        )}
                                                                                        <Button
                                                                                            variant="ghost"
                                                                                            size="sm"
                                                                                            className="h-7 px-2 sm:h-5 sm:px-1 text-xs text-destructive"
                                                                                            onClick={() => handleDeleteComment(reply.id, true, comment.id)}
                                                                                        >
                                                                                            <Trash2 className="h-3 w-3 mr-1" />
                                                                                            Delete
                                                                                        </Button>
                                                                                    </>
                                                                                )}
                                                                            </div>
                                                                        </>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                                {!showAllComments && sortedComments.length > 5 && (
                                    <Button variant="outline" onClick={() => setShowAllComments(true)} className="w-full">
                                        Show {sortedComments.length - 5} more comments
                                    </Button>
                                )}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                <p className="text-muted-foreground mb-4">No comments yet. Be the first to share your thoughts!</p>
                                {!currentUser && (
                                    <Button onClick={() => router.push('/sign-in')} variant="outline">
                                        Sign In to Comment
                                    </Button>
                                )}
                            </div>
                        )}
                    </div>
                )}
                {activeTab === 'related' && (
                     <div className="space-y-6">
                        <h2 className="text-xl font-semibold text-foreground">Related Prompts</h2>
                        {isLoadingRelated ? (
                            <div className="flex h-32 items-center justify-center">
                                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            </div>
                        ) : errorRelated ? (
                            <div className="text-center py-4"><p className="text-destructive">{errorRelated}</p></div>
                        ) : relatedPrompts.length > 0 ? (
                            <PromptGrid prompts={relatedPrompts} viewMode="grid" maxTags={1} onToggleSave={handleRelatedPromptToggleSave} />
                        ) : (
                            <div className="text-center py-4"><p className="text-muted-foreground">No related prompts found.</p></div>
                        )}
                    </div>
                )}
              </div>
            </div>
          </div>

          {/* Right column - Sidebar */}
          <aside className="lg:col-span-1 space-y-4 lg:space-y-6">
            {/* "Use This Prompt" Card - Redesigned */}
            <Card className="overflow-hidden rounded-xl border shadow-sm">
              <div className="bg-gradient-to-br from-primary/10 to-primary/5 p-4 border-b">
                <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  Use This Prompt
                </h2>
              </div>
              <CardContent className="p-4 space-y-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-primary">{formatStatNumber(voteCount)}</div>
                  <div className="text-sm text-muted-foreground">Rating</div>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className={`flex-1 group hover:border-green-500 hover:bg-green-500/10 ${userVote === 'up' ? 'border-green-500 bg-green-500/10 text-green-600' : 'border-border'}`} onClick={handleUpvote}>
                    <ThumbsUp className={`mr-2 h-4 w-4 group-hover:text-green-500 ${userVote === 'up' ? 'fill-green-500 text-green-600' : 'text-muted-foreground'}`} /> Upvote
                  </Button>
                  <Button variant="outline" className={`flex-1 group hover:border-red-500 hover:bg-red-500/10 ${userVote === 'down' ? 'border-red-500 bg-red-500/10 text-red-600' : 'border-border'}`} onClick={handleDownvote}>
                    <ThumbsDown className={`mr-2 h-4 w-4 group-hover:text-red-500 ${userVote === 'down' ? 'fill-red-500 text-red-600' : 'text-muted-foreground'}`} /> Downvote
                  </Button>
                </div>
                <Separator />
                <div className="space-y-2">
                  <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground text-base sm:text-sm py-3 sm:py-2" onClick={() => requireAuth(() => router.push(`/prompt/remix/${prompt.shortId}`))}>
                    <Sparkles className="mr-2 h-4 w-4" /> Remix Prompt
                  </Button>
                  <Button className="w-full bg-secondary hover:bg-secondary/80 text-secondary-foreground" onClick={() => requireAuth(() => setIsAddToCollectionDialogOpen(true))}>
                    <FolderPlus className="mr-2 h-4 w-4" /> Add to Collection
                  </Button>
                  <AddToCollectionDialog isOpen={isAddToCollectionDialogOpen} onClose={() => setIsAddToCollectionDialogOpen(false)} promptId={prompt.id} promptTitle={prompt.title} onSuccess={() => toast({ title: "Success", description: `"${prompt.title}" added to collection(s).`}) } />
                  <Button variant="ghost" className={`w-full hover:bg-muted/50 transition-all duration-200 ${isSharing ? 'text-primary' : 'text-muted-foreground'}`} onClick={handleShare} disabled={isSharing}>
                    {shareConfirmed ? <Check className="mr-2 h-4 w-4 text-green-500" /> : <Share2 className="mr-2 h-4 w-4" />}
                    {shareConfirmed ? "Link Copied!" : "Share Prompt"}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* "Prompt Details" Card - Redesigned */}
            <Card className="overflow-hidden rounded-xl border shadow-sm">
              <div className="bg-gradient-to-br from-muted/50 to-muted/20 p-4 border-b">
                <h2 className="text-lg font-semibold text-foreground">Prompt Details</h2>
              </div>
              <CardContent className="p-4 space-y-3">
                {/* Author */}
                <div className="flex items-center gap-3">
                  <Link href={`/user/${username}`} className="flex-shrink-0">
                    <Avatar className="h-10 w-10 border">
                      <AvatarImage src={prompt.user?.avatar_url || "/placeholder-user.jpg"} alt={username} />
                      <AvatarFallback>{username.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </Link>
                  <div>
                    <div className="text-xs text-muted-foreground">Author</div>
                    <Link href={`/user/${username}`} className="font-medium text-foreground hover:underline">{username}</Link>
                  </div>
                </div>
                <Separator/>
                {/* Tool */}
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted/50 border text-muted-foreground"><SettingsIcon className="h-5 w-5" /></div>
                  <div>
                    <div className="text-xs text-muted-foreground">Tool</div>
                    <Link href={`/tool/${toolSlug}`} className="font-medium text-foreground hover:underline">{toolName}</Link>
                  </div>
                </div>
                 <Separator/>
                {/* Category */}
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted/50 border text-muted-foreground"><GridIcon className="h-5 w-5" /></div>
                  <div>
                    <div className="text-xs text-muted-foreground">Category</div>
                    <Link href={`/category/${categorySlug}`} className="font-medium hover:underline" style={{ color: categoryDisplayColor }}>{categoryName}</Link>
                  </div>
                </div>
                <Separator/>
                {/* AI Model (if exists) */}
                {prompt.ai_model && (
                  <>
                    <div className="flex items-center gap-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted/50 border text-muted-foreground"><Sparkles className="h-5 w-5" /></div>
                      <div>
                        <div className="text-xs text-muted-foreground">AI Model</div>
                        <Link href={`/search?q=${encodeURIComponent(prompt.ai_model.provider + ' ' + prompt.ai_model.name)}`} className="font-medium text-foreground hover:underline">
                          {prompt.ai_model.provider} - {prompt.ai_model.name}
                        </Link>
                      </div>
                    </div>
                    <Separator/>
                  </>
                )}
                {/* Post Date */}
                <div className="flex items-center gap-3">
                   <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted/50 border text-muted-foreground"><CalendarIcon className="h-5 w-5" /></div>
                  <div>
                    <div className="text-xs text-muted-foreground">Post Date</div>
                    <div className="font-medium text-foreground">{formattedDate}</div>
                  </div>
                </div>
                {/* Remixed From (if exists and public) */}
                {originalPrompt && (
                  <>
                    <Separator />
                    <div className="flex items-center gap-3">
                      <div className="flex h-10 w-10 items-center justify-center rounded-md bg-muted/50 border text-muted-foreground"><GitFork className="h-5 w-5" /></div>
                      <div>
                        <div className="text-xs text-muted-foreground">Remixed from</div>
                        <Link href={`/prompt/${originalPrompt.shortId}`} className="font-medium text-foreground hover:underline">{originalPrompt.title}</Link>
                      </div>
                    </div>
                  </>
                )}
                {/* Tags */}
                {prompt.tags && prompt.tags.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <div className="mb-1 text-xs text-muted-foreground">Tags</div>
                      <div className="flex flex-wrap gap-1.5">
                        {(prompt.tags as Tag[]).map((tag) => {
                          // Use tag.slug if it exists, otherwise create a slug from tag.name
                          const tagSlug = tag.slug || slugify(tag.name, { lower: true, strict: true })
                          return (
                            <Badge key={tag.id || tag.slug} variant="secondary" className="px-2 py-0.5 text-xs bg-teal-500/10 border-teal-500/30 text-teal-400 hover:bg-teal-500/20">
                              <Link href={`/tag/${tagSlug}`} className="hover:underline">#{tag.name}</Link>
                            </Badge>
                          )
                        })}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Stats Card - Redesigned */}
            <Card className="overflow-hidden rounded-xl border shadow-sm">
              <div className="bg-gradient-to-br from-muted/50 to-muted/20 p-4 border-b">
                 <h2 className="text-lg font-semibold text-foreground">Stats</h2>
              </div>
              <CardContent className="p-4 space-y-3 sm:space-y-2.5">
                <div className="flex items-center justify-between text-base sm:text-sm">
                  <div className="flex items-center gap-2 text-muted-foreground"><ThumbsUp className="h-4 w-4" /> Upvotes</div>
                  <div className="font-medium text-foreground">{formatStatNumber(prompt.likeCount)}</div>
                </div>
                 <div className="flex items-center justify-between text-base sm:text-sm">
                  <div className="flex items-center gap-2 text-muted-foreground"><Eye className="h-4 w-4" /> Views</div>
                  <div className="font-medium text-foreground">{formatStatNumber(prompt.viewCount)}</div>
                </div>
                <div className="flex items-center justify-between text-base sm:text-sm">
                  <div className="flex items-center gap-2 text-muted-foreground"><MessageSquare className="h-4 w-4" /> Comments</div>
                  <div className="font-medium text-foreground">{formatStatNumber(prompt.commentCount)}</div>
                </div>
                <div className="flex items-center justify-between text-base sm:text-sm">
                  <div className="flex items-center gap-2 text-muted-foreground"><Sparkles className="h-4 w-4" /> Remixes</div>
                  <div className="font-medium text-foreground">{formatStatNumber(prompt.remixCount)}</div>
                </div>
              </CardContent>
            </Card>
          </aside>
        </div>
      </div>
      <CommunityJoinModal isOpen={showCommunityModal} onClose={() => setShowCommunityModal(false)} />

      {/* Add to Collection Dialog for Related Prompts */}
      {selectedRelatedPromptId && (
        <AddToCollectionDialog
          isOpen={isRelatedPromptSaveDialogOpen}
          onClose={() => {
            setIsRelatedPromptSaveDialogOpen(false)
            setSelectedRelatedPromptId(null)
            setSelectedRelatedPromptTitle("")
          }}
          promptId={selectedRelatedPromptId}
          promptTitle={selectedRelatedPromptTitle}
          onSuccess={() => {
            // Optimistically update the related prompt as saved
            setRelatedPrompts(prev => 
              prev.map(p => 
                p.id === selectedRelatedPromptId ? { ...p, isSaved: true } : p
              )
            )
            setIsRelatedPromptSaveDialogOpen(false)
            setSelectedRelatedPromptId(null)
            setSelectedRelatedPromptTitle("")
            toast({
              title: "Success",
              description: `"${selectedRelatedPromptTitle}" has been added to your collection(s).`
            })
          }}
        />
      )}
    </div>
  )
} 