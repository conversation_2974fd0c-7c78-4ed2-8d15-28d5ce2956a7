"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card"
import { Heart, MessageSquare, AlertCircle } from "lucide-react"
import Link from "next/link"
import { supabase } from "@/lib/supabase/client"
import { Alert, AlertDescription } from "@/components/ui/alert"
// Import the profanity filter directly
import { containsProfanity, recordProfanityAttempt, isUserOnTimeout, getRemainingTimeoutSeconds } from "@/lib/utils/profanity-filter"

interface Comment {
  id: string
  text: string
  user: {
    id: string
    username: string
  }
  likeCount: number
  createdAt: string
  parentId: string | null
  replies: Comment[]
}

interface CommentSectionProps {
  promptId: string
}

export default function CommentSection({ promptId }: CommentSectionProps) {
  const [commentText, setCommentText] = useState("")
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyText, setReplyText] = useState("")
  const [sortBy, setSortBy] = useState<"newest" | "mostLiked">("mostLiked")
  const [currentUser, setCurrentUser] = useState<{ id: string; username: string } | null>(null)
  const [profanityFilter, setProfanityFilter] = useState<any>(null)
  const [profanityError, setProfanityError] = useState<string | null>(null)
  const [isOnTimeout, setIsOnTimeout] = useState(false)
  const [timeoutRemaining, setTimeoutRemaining] = useState(0)
  const [profanityAttempts, setProfanityAttempts] = useState(0)
  const [consecutiveCommentCount, setConsecutiveCommentCount] = useState(0)
  const [lastCommenterUserId, setLastCommenterUserId] = useState<string | null>(null)
  const [consecutiveReplyCount, setConsecutiveReplyCount] = useState<Record<string, number>>({}) // Track consecutive replies by parent comment ID
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Check if user is logged in and initialize timeout status if needed
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        setCurrentUser({
          id: session.user.id,
          username: session.user.user_metadata?.username || 'User'
        })
        
        // Check if user is on timeout
        if (isUserOnTimeout(session.user.id)) {
          setIsOnTimeout(true)
          const remainingTime = getRemainingTimeoutSeconds(session.user.id)
          setTimeoutRemaining(remainingTime)
          
          // Set up countdown timer
          const timer = setInterval(() => {
            setTimeoutRemaining(prev => {
              if (prev <= 1) {
                clearInterval(timer)
                setIsOnTimeout(false)
                return 0
              }
              return prev - 1
            })
          }, 1000)
          
          return () => clearInterval(timer)
        }
      }
    }
    
    checkAuth()
  }, [])
  
  // Mock comments data
  const [comments, setComments] = useState<Comment[]>([
    {
      id: "comment1",
      text: "This prompt is incredibly useful! I've been using it to refactor some legacy code at work and it's been a game changer. The suggestions are always spot on.",
      user: {
        id: "user2",
        username: "devguru",
      },
      likeCount: 12,
      createdAt: "2023-06-15T00:00:00Z",
      parentId: null,
      replies: [
        {
          id: "comment3",
          text: "I agree! Have you tried combining it with the test case generator prompt? They work really well together.",
          user: {
            id: "user3",
            username: "testmaster",
          },
          likeCount: 5,
          createdAt: "2023-06-16T00:00:00Z",
          parentId: "comment1",
          replies: [],
        },
      ],
    },
    {
      id: "comment2",
      text: "I found a small issue when using this with TypeScript generics. The AI sometimes gets confused with complex type definitions. Otherwise, it's perfect!",
      user: {
        id: "user4",
        username: "typescript_fan",
      },
      likeCount: 8,
      createdAt: "2023-06-17T00:00:00Z",
      parentId: null,
      replies: [],
    },
  ])

  const formatTimeRemaining = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const checkForProfanity = (text: string): boolean => {
    if (!currentUser) return false;
    
    // Use the imported containsProfanity function directly
    const hasProfanity = containsProfanity(text);
    
    if (hasProfanity) {
      // Use the imported recordProfanityAttempt function directly
      const result = recordProfanityAttempt(currentUser.id);
      setProfanityAttempts(result.attemptsCount);
      
      if (result.onTimeout) {
        setIsOnTimeout(true);
        setTimeoutRemaining(10 * 60); // 10 minutes in seconds
        setProfanityError(`You've been placed on a 10-minute timeout due to multiple attempts to post inappropriate content.`);
        
        // Set up countdown timer
        const timer = setInterval(() => {
          setTimeoutRemaining(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              setIsOnTimeout(false);
              setProfanityError(null);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setProfanityError(`Your comment contains inappropriate language. Please revise your wording. Warning ${result.attemptsCount}/${5}`);
      }
    }
    
    return hasProfanity;
  }

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!commentText.trim()) return
    if (isOnTimeout) return
    
    const userId = currentUser?.id || "anonymous"
    
    // Check if the last commenter was the current user (can't comment twice in a row)
    if (lastCommenterUserId === userId) {
      setProfanityError("You can't post two comments in a row. Please wait for someone else to respond.")
      return
    }
    
    // Check for profanity if user is logged in
    if (currentUser && checkForProfanity(commentText)) {
      return
    }

    setIsSubmitting(true)
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        console.error('User not authenticated')
        setIsSubmitting(false)
        return
      }

      const user = session.user

      // Insert the new comment
      const { data, error } = await supabase
        .from('comments')
        .insert({
          prompt_id: promptId,
          user_id: user.id,
          content: commentText.trim(),
        })
        .select(`
          *,
          user:profiles!comments_user_id_fkey (
            username,
            avatar_url
          )
        `)
        .single()

      if (error) {
        console.error('Error submitting comment:', error)
        return
      }

      // Update consecutive comment tracking
      if (lastCommenterUserId === userId) {
        setConsecutiveCommentCount(prev => prev + 1)
      } else {
        setLastCommenterUserId(userId)
        setConsecutiveCommentCount(1)
      }

      // Add the new comment to the list
      setComments(prev => [data, ...prev])
      setCommentText("")
      setProfanityError(null)
    } catch (error) {
      console.error('Error in handleSubmitComment:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmitReply = (parentId: string) => {
    if (!replyText.trim()) return
    if (isOnTimeout) return
    
    const userId = currentUser?.id || "anonymous"
    
    // Check if user has already replied twice in a row to this comment
    const userConsecutiveReplies = consecutiveReplyCount[parentId] || 0
    const lastReplier = comments.find(c => c.id === parentId)?.replies.slice(-1)[0]?.user.id
    
    if (lastReplier === userId && userConsecutiveReplies >= 2) {
      setProfanityError("You can't reply more than twice in a row to the same comment. Please wait for someone else to respond.")
      return
    }
    
    // Check for profanity if user is logged in
    if (currentUser && checkForProfanity(replyText)) {
      return
    }

    const newReply: Comment = {
      id: `comment${Date.now()}`,
      text: replyText,
      user: {
        id: userId,
        username: currentUser?.username || "Anonymous",
      },
      likeCount: 0,
      createdAt: new Date().toISOString(),
      parentId,
      replies: [],
    }

    // Update consecutive reply tracking for this parent comment
    if (lastReplier === userId) {
      setConsecutiveReplyCount(prev => ({
        ...prev,
        [parentId]: (prev[parentId] || 1) + 1
      }))
    } else {
      setConsecutiveReplyCount(prev => ({
        ...prev,
        [parentId]: 1
      }))
    }

    const updatedComments = comments.map((comment) => {
      if (comment.id === parentId) {
        return {
          ...comment,
          replies: [...comment.replies, newReply],
        }
      }
      return comment
    })

    setComments(updatedComments)
    setReplyText("")
    setReplyingTo(null)
    setProfanityError(null)
  }

  const handleLikeComment = (commentId: string) => {
    const updateComment = (comments: Comment[]): Comment[] => {
      return comments.map((comment) => {
        if (comment.id === commentId) {
          return {
            ...comment,
            likeCount: comment.likeCount + 1,
          }
        }
        if (comment.replies.length > 0) {
          return {
            ...comment,
            replies: updateComment(comment.replies),
          }
        }
        return comment
      })
    }

    setComments(updateComment(comments))
  }

  const sortedComments = [...comments].sort((a, b) => {
    if (sortBy === "newest") {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    }
    return b.likeCount - a.likeCount
  })

  const renderComment = (comment: Comment, isReply = false) => {
    const formattedDate = new Date(comment.createdAt).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })

    return (
      <Card key={comment.id} className={`mb-4 ${isReply ? "ml-8" : ""}`}>
        <CardHeader className="flex flex-row items-start gap-4 space-y-0 p-4">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/placeholder-user.jpg" alt={comment.user.username} />
            <AvatarFallback>{comment.user.username.charAt(0).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <Link href={`/user/${comment.user.username}`} className="font-medium">
                {comment.user.username}
              </Link>
              <span className="text-xs text-muted-foreground">{formattedDate}</span>
            </div>
            <p className="mt-2 text-sm">{comment.text}</p>
          </div>
        </CardHeader>
        <CardFooter className="flex justify-between p-4 pt-0">
          <Button variant="ghost" size="sm" className="gap-1 px-2" onClick={() => handleLikeComment(comment.id)}>
            <Heart className="h-4 w-4" />
            <span>{comment.likeCount}</span>
          </Button>
          {!isReply && (
            <Button
              variant="ghost"
              size="sm"
              className="gap-1 px-2"
              onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
              disabled={!!currentUser && comment.replies.length >= 2 && 
                comment.replies.slice(-2).every(reply => reply.user.id === currentUser.id)}
            >
              <MessageSquare className="h-4 w-4" />
              <span>Reply</span>
            </Button>
          )}
        </CardFooter>

        {replyingTo === comment.id && (
          <CardContent className="p-4 pt-0">
            <div className="flex gap-4">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder-user.jpg" alt="You" />
                <AvatarFallback>Y</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <Textarea
                  placeholder="Write a reply..."
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  className="mb-2"
                />
                <div className="flex justify-end gap-2">
                  <Button variant="outline" size="sm" onClick={() => setReplyingTo(null)}>
                    Cancel
                  </Button>
                  <Button size="sm" onClick={() => handleSubmitReply(comment.id)}>
                    Reply
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        )}

        {comment.replies.map((reply) => renderComment(reply, true))}
      </Card>
    )
  }

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Comments</h2>
        <div className="flex gap-2">
          <Button
            variant={sortBy === "mostLiked" ? "default" : "outline"}
            size="sm"
            onClick={() => setSortBy("mostLiked")}
            className="rounded-md"
            style={{ borderRadius: "6px" }}
          >
            Most Liked
          </Button>
          <Button
            variant={sortBy === "newest" ? "default" : "outline"}
            size="sm"
            onClick={() => setSortBy("newest")}
            className="rounded-md"
            style={{ borderRadius: "6px" }}
          >
            Newest
          </Button>
        </div>
      </div>

      {profanityError && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{profanityError}</AlertDescription>
        </Alert>
      )}
      
      {isOnTimeout && (
        <Alert className="mb-4">
          <AlertDescription>
            You can post again in {formatTimeRemaining(timeoutRemaining)}
          </AlertDescription>
        </Alert>
      )}
      
      <div className="mb-8 flex gap-4">
        <Avatar className="h-10 w-10">
          <AvatarImage 
            src={currentUser?.username ? "/placeholder-user.jpg" : "/placeholder-anonymous.jpg"} 
            alt={currentUser?.username || "Anonymous"} 
          />
          <AvatarFallback>{currentUser?.username ? currentUser.username.charAt(0).toUpperCase() : "A"}</AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <Textarea
            placeholder="Write a comment..."
            value={commentText}
            onChange={(e) => {
              setCommentText(e.target.value)
              // Clear error when user starts typing
              if (profanityError) setProfanityError(null)
            }}
            className="mb-2"
            disabled={isOnTimeout}
          />
          <div className="flex justify-end">
            <Button 
              onClick={handleSubmitComment} 
              disabled={isOnTimeout || !commentText.trim() || 
                (!!currentUser && lastCommenterUserId === currentUser.id)}
            >
              Post Comment
            </Button>
          </div>
        </div>
      </div>

      <div>{sortedComments.map((comment) => renderComment(comment))}</div>
    </div>
  )
}
