"use client";

import { useState, useEffect } from "react";
import { performSupabaseHealthCheck, type HealthCheckResult } from "@/lib/utils/supabase-health-check";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, CheckCircle, XCircle, AlertCircle } from "lucide-react";

/**
 * Debug component to help diagnose Supabase connection issues
 * This should only be used during development/debugging
 */
export default function SupabaseDebug() {
  const [healthCheck, setHealthCheck] = useState<HealthCheckResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const runHealthCheck = async () => {
    setIsLoading(true);
    try {
      const result = await performSupabaseHealthCheck();
      setHealthCheck(result);
    } catch (error) {
      setHealthCheck({
        isHealthy: false,
        error: error instanceof Error ? error.message : "Unknown error",
        details: {
          environmentVariables: { url: false, anonKey: false },
          clientInitialization: false,
          databaseConnection: false,
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    runHealthCheck();
  }, []);

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusBadge = (status: boolean) => {
    return (
      <Badge variant={status ? "default" : "destructive"}>
        {status ? "OK" : "FAIL"}
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          Supabase Connection Debug
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Button onClick={runHealthCheck} disabled={isLoading}>
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              "Run Health Check"
            )}
          </Button>
          {healthCheck && (
            <Badge variant={healthCheck.isHealthy ? "default" : "destructive"}>
              {healthCheck.isHealthy ? "HEALTHY" : "UNHEALTHY"}
            </Badge>
          )}
        </div>

        {healthCheck && (
          <div className="space-y-3">
            {healthCheck.error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-800 font-medium">Error:</p>
                <p className="text-red-700">{healthCheck.error}</p>
              </div>
            )}

            <div className="space-y-2">
              <h4 className="font-medium">Environment Variables</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center gap-2">
                  {getStatusIcon(healthCheck.details.environmentVariables.url)}
                  <span>NEXT_PUBLIC_SUPABASE_URL</span>
                  {getStatusBadge(healthCheck.details.environmentVariables.url)}
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(healthCheck.details.environmentVariables.anonKey)}
                  <span>NEXT_PUBLIC_SUPABASE_ANON_KEY</span>
                  {getStatusBadge(healthCheck.details.environmentVariables.anonKey)}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Client Status</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  {getStatusIcon(healthCheck.details.clientInitialization)}
                  <span>Client Initialization</span>
                  {getStatusBadge(healthCheck.details.clientInitialization)}
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(healthCheck.details.databaseConnection)}
                  <span>Database Connection</span>
                  {getStatusBadge(healthCheck.details.databaseConnection)}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Environment Info</h4>
              <div className="text-sm space-y-1">
                <p>
                  <strong>NODE_ENV:</strong> {process.env.NODE_ENV || "undefined"}
                </p>
                <p>
                  <strong>Timestamp:</strong> {new Date().toISOString()}
                </p>
                <p>
                  <strong>User Agent:</strong>{" "}
                  {typeof window !== "undefined"
                    ? window.navigator?.userAgent?.substring(0, 50) + "..."
                    : "Server-side"}
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
