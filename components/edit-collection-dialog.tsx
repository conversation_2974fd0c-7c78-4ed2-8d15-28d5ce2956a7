"use client";

import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Loader2, Trash, Upload, X } from "lucide-react";
import Image from "next/image";
import type { Collection } from "@/lib/types"; // Import Collection type
import { DragDropImageUpload } from "@/components/ui/drag-drop-image-upload";

interface EditCollectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  collection: Collection; // Pass the collection data to edit
  onUpdate: (
    collectionId: string,
    collectionData: {
      name?: string;
      description?: string | null;
      imageFile?: File | null;
      removeCurrentImage?: boolean;
      icon?: string | null;
      isPublic?: boolean;
    }
  ) => Promise<void>;
  onDelete?: (collectionId: string) => Promise<void>;
}

export default function EditCollectionDialog({
  isOpen,
  onClose,
  collection,
  onUpdate,
  onDelete,
}: EditCollectionDialogProps) {
  const [name, setName] = useState(collection.name);
  const [description, setDescription] = useState(collection.description || "");
  const [currentIcon, setCurrentIcon] = useState(collection.icon);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [removeImage, setRemoveImage] = useState(false);
  // Handle potential data format inconsistencies between API and frontend
  const [isPublic, setIsPublic] = useState(Boolean(collection.isPublic));
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update state when the collection prop changes (e.g., if dialog is reused)
  useEffect(() => {
    if (collection) {
      setName(collection.name);
      setDescription(collection.description || "");
      setCurrentIcon(collection.icon);
      setImageFile(null);
      setImagePreview(null);
      setRemoveImage(false);
      setIsPublic(Boolean(collection.isPublic));
    }
  }, [collection]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('[EditCollectionDialog] Image input change event triggered');
    const file = e.target.files?.[0];
    if (!file) {
      console.log('[EditCollectionDialog] No file selected');
      return;
    }

    console.log('[EditCollectionDialog] File selected:', {
      name: file.name,
      type: file.type,
      size: file.size,
      lastModified: new Date(file.lastModified).toISOString()
    });

    setImageFile(file);
    setRemoveImage(false);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      console.log('[EditCollectionDialog] FileReader completed, preview generated');
      setImagePreview(reader.result as string);
    };
    reader.onerror = (error) => {
      console.error('[EditCollectionDialog] FileReader error:', error);
    };
    console.log('[EditCollectionDialog] Starting FileReader.readAsDataURL');
    reader.readAsDataURL(file);
  };

  const handleRemoveImage = () => {
    console.log('[EditCollectionDialog] Remove image requested');
    setImageFile(null);
    setImagePreview(null);
    setRemoveImage(true);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
      console.log('[EditCollectionDialog] File input value cleared');
    }
    console.log('[EditCollectionDialog] Image removal state updated: removeImage=true, imageFile=null');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('[EditCollectionDialog] Form submitted for collection:', collection.id);
    setIsLoading(true);
    try {
      // Only send fields that have changed (optional optimization)
      const updatedData: {
        name?: string;
        description?: string | null;
        imageFile?: File | null;
        removeCurrentImage?: boolean;
        icon?: string | null;
        isPublic?: boolean;
      } = {};
      
      console.log('[EditCollectionDialog] Current collection state:', {
        id: collection.id,
        name: collection.name,
        description: collection.description,
        icon: collection.icon,
        isDefault: collection.isDefault,
        isPublic: collection.isPublic
      });
      
      console.log('[EditCollectionDialog] Form state:', {
        name,
        description,
        imageFile: imageFile ? { name: imageFile.name, type: imageFile.type, size: imageFile.size } : null,
        removeImage,
        currentIcon,
        isPublic
      });
      
      // For default collection, don't allow changing anything except image
      if (!collection.isDefault) {
        if (name !== collection.name) {
          updatedData.name = name;
          console.log('[EditCollectionDialog] Name changed:', { from: collection.name, to: name });
        }
        if (description !== (collection.description || "")) {
          updatedData.description = description;
          console.log('[EditCollectionDialog] Description changed:', { from: collection.description, to: description });
        }
        if (isPublic !== collection.isPublic) {
          updatedData.isPublic = isPublic;
          console.log('[EditCollectionDialog] Public status changed:', { from: collection.isPublic, to: isPublic });
        }
      }
      
      // Handle image changes
      if (imageFile) {
        updatedData.imageFile = imageFile;
        console.log('[EditCollectionDialog] New image file to upload:', {
          name: imageFile.name,
          type: imageFile.type,
          size: imageFile.size,
          lastModified: new Date(imageFile.lastModified).toISOString()
        });
      } else if (removeImage) {
        updatedData.removeCurrentImage = true;
        updatedData.icon = null;
        console.log('[EditCollectionDialog] Removing current image');
      }

      console.log('[EditCollectionDialog] Final update data being sent:', updatedData);
      
      await onUpdate(collection.id, updatedData);
      console.log('[EditCollectionDialog] Update successful');
      // Close on success
      onClose();
    } catch (error) {
      console.error(`[EditCollectionDialog] Failed to update collection ${collection.id}:`, error);
      // TODO: Show error message to user
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (collection.isDefault) {
      alert("The default collection cannot be deleted.");
      return;
    }

    if (!confirm("Are you sure you want to delete this collection? This action cannot be undone.")) {
      return;
    }

    if (onDelete) {
      setIsDeleting(true);
      try {
        await onDelete(collection.id);
        onClose();
      } catch (error) {
        console.error(`Failed to delete collection ${collection.id}:`, error);
        // TODO: Show error message to user
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Collection</DialogTitle>
          <DialogDescription>
            Update the details for your collection. Use a unique name.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              {collection.isDefault ? (
                <div className="col-span-3 flex items-center">
                  <Input
                    id="name"
                    value={name}
                    className="col-span-3 bg-gray-100"
                    disabled
                  />
                  <span className="ml-2 text-xs text-muted-foreground">(Cannot be changed)</span>
                </div>
              ) : (
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="col-span-3"
                  required
                />
              )}
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              {collection.isDefault ? (
                <div className="col-span-3 flex items-center">
                  <Textarea
                    id="description"
                    value={description}
                    className="col-span-3 bg-gray-100"
                    disabled
                  />
                  <span className="ml-2 text-xs text-muted-foreground">(Cannot be changed)</span>
                </div>
              ) : (
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="col-span-3"
                />
              )}
            </div>
            {/* Image Upload - Hide for default collections */}
            {!collection.isDefault && (
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="image" className="text-right">
                  Image
                </Label>
                <div className="col-span-3">
                  <DragDropImageUpload
                    imageFile={imageFile}
                    imagePreview={imagePreview}
                    onImageChange={(file, preview) => {
                      setImageFile(file)
                      setImagePreview(preview)
                      setRemoveImage(!file) // Set removal flag based on whether file exists
                      if (file) {
                        console.log('[EditCollectionDialog] Image selected:', { name: file.name, size: file.size })
                      } else {
                        console.log('[EditCollectionDialog] Image removed')
                      }
                    }}
                    disabled={isLoading}
                    maxSizeMB={1}
                    placeholder={{
                      title: "Click to upload or drag and drop",
                      subtitle: "PNG, JPG, GIF up to 1MB"
                    }}
                    className="w-full"
                  />
                  {/* Show current image if no preview and no new file */}
                  {!imagePreview && !imageFile && currentIcon && (
                    <div className="mt-2 relative h-32 w-32">
                      <Image 
                        src={currentIcon} 
                        alt="Current collection image" 
                        fill
                        className="object-cover rounded-md border" 
                        sizes="128px"
                        quality={85}
                      />
                    </div>
                  )}
                </div>
              </div>
            )}
            
            {/* For default collections, show the fixed image */}
            {collection.isDefault && collection.icon && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="defaultImage" className="text-right">
                  Image
                </Label>
                <div className="col-span-3">
                  <div className="relative h-32 w-32">
                    <Image 
                      src={collection.icon} 
                      alt={collection.name} 
                      fill
                      className="object-cover rounded-md" 
                      sizes="128px"
                      quality={85}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">(Default collection image cannot be changed)</p>
                </div>
              </div>
            )}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="public" className="text-right">
                Public
              </Label>
              {collection.isDefault ? (
                <div className="col-span-3 flex items-center">
                  <Switch
                    id="public"
                    checked={false}
                    className="col-span-3"
                    disabled
                  />
                  <span className="ml-2 text-xs text-muted-foreground">(Default collection must be private)</span>
                </div>
              ) : (
                <Switch
                  id="public"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                  className="col-span-3"
                />
              )}
            </div>
          </div>
          <DialogFooter className="flex justify-between w-full">
            {onDelete && !collection.isDefault && (
              <Button 
                type="button" 
                variant="outline"
                className="text-red-600 hover:bg-red-600/10 hover:text-red-600"
                onClick={handleDelete}
                disabled={isDeleting || isLoading}
              >
                {isDeleting ? (
                  <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Deleting...</>
                ) : (
                  <><Trash className="mr-2 h-4 w-4" /> Delete Collection</>
                )}
              </Button>
            )}
            <Button type="submit" disabled={isLoading || !name || isDeleting}>
              {isLoading ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...</>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}