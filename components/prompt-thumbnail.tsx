import Image from "next/image"
import { getCategoryTheme } from "./category-icon-mapper"
import { getCategoryColorByName } from "@/lib/data/category-colors"
import MinimalistIconPlaceholder from "./placeholders/minimalist-icon-placeholder"
import GradientLetterPlaceholder from "./placeholders/gradient-letter-placeholder"
import AbstractPatternPlaceholder from "./placeholders/abstract-pattern-placeholder"
import { getLinearGradientStyle, getIconColorHex } from "@/lib/utils/category-styles" // Import utility functions

// Tailwind JIT compilation hint:
// Ensure these classes are included in the build even if dynamically generated
// iconTextColor classes: text-blue-200, text-green-200, text-pink-200, text-purple-200, text-teal-200, text-sky-200, text-lime-200, text-violet-200, text-cyan-200, text-pink-100, text-orange-200, text-emerald-200, text-fuchsia-200, text-violet-200, text-indigo-200, text-purple-200, text-red-200, text-purple-200, text-slate-200

interface PromptThumbnailProps {
  title: string
  category: string
  imageUrl?: string | null
  className?: string
  placeholderType?: "icon" | "letter" | "pattern"
  iconSizeClass?: string // Added icon size prop
  labelSizeClass?: string // Added label size prop
  priority?: boolean // Add priority prop for above-the-fold images
}

/**
 * PromptThumbnail component
 *
 * Renders a thumbnail for a prompt. If an image URL is provided, it displays the image.
 * Otherwise, it generates a placeholder based on the specified type:
 * - "icon": Shows a category-specific icon on a themed background (default)
 * - "letter": Shows the first letter of the title on a gradient background
 * - "pattern": Shows an abstract pattern generated from the title
 *
 * The PromptHQ platform employs a sophisticated system for generating visually distinct
 * thumbnails when images are absent. This ensures that each prompt card maintains a unique
 * and recognizable visual identity aligned with its category.
 */
export default function PromptThumbnail({
  title,
  category,
  imageUrl,
  className = "",
  placeholderType = "icon",
  iconSizeClass, // Destructure iconSizeClass
  labelSizeClass, // Destructure labelSizeClass
  priority = false, // Destructure priority prop
}: PromptThumbnailProps) {
  // Get the theme for this category
  const theme = getCategoryTheme(category)
  const { icon: Icon } = theme
  const categoryColor = theme.colors

  // If an image URL is provided, display the image
  if (imageUrl) {
    return (
      <div className={`aspect-video overflow-hidden rounded-t-lg ${className}`}>
        <Image
          src={imageUrl}
          alt={`${title} - ${category} prompt thumbnail`}
          fill
          className="object-cover transition-transform duration-300 hover:scale-105"
          priority={priority}
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 300px"
          quality={85}
        />
      </div>
    )
  }

  // If no image, use the specified placeholder type
  switch (placeholderType) {
    case "letter":
      return <GradientLetterPlaceholder title={title} category={category} className={className} />

    case "pattern":
      return <AbstractPatternPlaceholder seed={title} category={category} className={className} />

    case "icon":
    default:
      // Use the minimalist icon style (default) with category-specific styling
      // Use the minimalist icon style (default) with category-specific styling
      // Implement premium background with gradient and radial highlight
      return (
        <div className={`relative aspect-video overflow-hidden rounded-t-lg ${className}`}> {/* Added relative */}
          <div
            className={`h-full w-full flex items-center justify-center rounded-t-lg`} // Removed bg-gradient-to-br and color stops, will use inline style
            style={{ backgroundImage: getLinearGradientStyle(categoryColor.slug) }} // Apply inline gradient style
            aria-label={`${category} category thumbnail`}
          >
            {/* Radial highlight overlay */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_left,rgba(255,255,255,0.05),transparent)] pointer-events-none rounded-t-lg"></div> {/* Added radial overlay and rounded-lg */}

            <div className="flex flex-col items-center justify-center text-center z-10"> {/* Added z-10 to bring content above overlay */}
              {/* Apply icon color using the color prop with derived hex */}
              <Icon className={iconSizeClass || `h-12 w-12`} color={getIconColorHex(categoryColor.slug)} />
              <span className={`mt-2 font-medium ${labelSizeClass || 'text-sm'} ${categoryColor.darkTextColor} px-2`}>{category}</span>
            </div>
          </div>
        </div>
      )
  }
}
