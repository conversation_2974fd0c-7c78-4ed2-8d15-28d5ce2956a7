// Load and run the automated NLP test suite
// Copy this into browser console on /prompt/submit page

console.log('🚀 Loading Automated NLP Test Suite...');

// First check if the function is available
if (typeof window.analyzePromptContent !== 'function') {
  console.error('❌ analyzePromptContent function not available! Make sure you are on /prompt/submit page');
} else {
  console.log('✅ analyzePromptContent function is available');
  
  // Load the test suite from the external file
  fetch('/automated-nlp-test-suite.js')
    .then(response => response.text())
    .then(scriptContent => {
      // Execute the script content
      eval(scriptContent);
    })
    .catch(error => {
      console.error('❌ Failed to load test suite:', error);
      console.log('📋 You can copy and paste the test suite manually from automated-nlp-test-suite.js');
    });
} 