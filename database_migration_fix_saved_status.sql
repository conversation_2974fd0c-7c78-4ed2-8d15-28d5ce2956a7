-- Migration: Fix saved status to check ANY collection membership
-- This fixes the bookmark icon to show as filled when a prompt is saved in ANY collection

-- Step 1: Update the get_prompts_with_saved_status function to properly check ALL collections
CREATE OR REPLACE FUNCTION get_prompts_with_saved_status(
  p_user_id uuid DEFAULT NULL,
  p_limit integer DEFAULT 20,
  p_offset integer DEFAULT 0,
  p_category_slugs text[] DEFAULT NULL,
  p_tool_slugs text[] DEFAULT NULL,
  p_tag_slugs text[] DEFAULT NULL,
  p_ai_model_slugs text[] DEFAULT NULL,
  p_search_query text DEFAULT NULL,
  p_author_id uuid DEFAULT NULL,
  p_sort_by text DEFAULT 'created_at',
  p_sort_order text DEFAULT 'desc'
)
RETURNS TABLE (
  id uuid,
  short_id text,
  title text,
  description text,
  image_url text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  is_public boolean,
  view_count integer,
  primary_tag_id integer,
  category_id integer,
  tool_id integer,
  author_id uuid,
  search_vector tsvector,
  tag_slugs_array text[],
  category_name text,
  category_slug text,
  tool_name text,
  tool_slug text,
  author_username text,
  author_avatar_url text,
  primary_tag_slug text,
  tags jsonb,
  rating bigint,
  comment_count bigint,
  trending_score double precision,
  ai_model_id integer,
  ai_model_provider text,
  ai_model_name text,
  ai_model_slug text,
  ai_model_deprecated boolean,
  is_saved_by_user boolean
) AS $$
DECLARE
  search_ts_query tsquery;
  exact_username_match boolean := false;
BEGIN
  -- Check if search query exactly matches a username (case-insensitive)
  IF p_search_query IS NOT NULL THEN
    SELECT EXISTS(
      SELECT 1 FROM profiles 
      WHERE LOWER(username) = LOWER(p_search_query)
    ) INTO exact_username_match;
    
    -- Prepare the tsquery for full-text search
    search_ts_query := plainto_tsquery('english', p_search_query);
  END IF;

  RETURN QUERY
  WITH user_saved_prompts AS (
    SELECT DISTINCT cp.prompt_id
    FROM collection_prompts cp
    JOIN collections c ON cp.collection_id = c.id
    WHERE c.user_id = p_user_id 
      AND p_user_id IS NOT NULL
  ),
  search_results AS (
    SELECT 
      pcd.id,
      pcd.short_id,
      pcd.title,
      pcd.description,
      pcd.image_url,
      pcd.created_at,
      pcd.updated_at,
      pcd.is_public,
      pcd.view_count,
      pcd.primary_tag_id,
      pcd.category_id,
      pcd.tool_id,
      pcd.author_id,
      pcd.search_vector,
      pcd.tag_slugs_array,
      pcd.category_name,
      pcd.category_slug,
      pcd.tool_name,
      pcd.tool_slug,
      pcd.author_username,
      pcd.author_avatar_url,
      pcd.primary_tag_slug,
      pcd.tags,
      pcd.rating,
      pcd.comment_count,
      pcd.trending_score,
      pcd.ai_model_id,
      pcd.ai_model_provider,
      pcd.ai_model_name,
      pcd.ai_model_slug,
      pcd.ai_model_deprecated,
      CASE WHEN usp.prompt_id IS NOT NULL THEN true ELSE false END AS is_saved_by_user,
      -- Calculate relevance score for search queries
      CASE 
        WHEN p_search_query IS NULL THEN 0
        WHEN exact_username_match AND LOWER(pcd.author_username) = LOWER(p_search_query) THEN 100 -- High boost for exact username match
        WHEN pcd.author_username ILIKE '%' || p_search_query || '%' THEN 20 -- Username contains query
        ELSE 0
      END +
      CASE 
        WHEN p_search_query IS NULL THEN 0
        WHEN pcd.search_vector @@ search_ts_query THEN ts_rank_cd(pcd.search_vector, search_ts_query) * 100
        ELSE 0
      END AS relevance_score
    FROM prompt_card_details pcd
    LEFT JOIN user_saved_prompts usp ON pcd.id = usp.prompt_id
    WHERE pcd.is_public = true
      AND (p_category_slugs IS NULL OR pcd.category_slug = ANY(p_category_slugs))
      AND (p_tool_slugs IS NULL OR pcd.tool_slug = ANY(p_tool_slugs))
      AND (p_tag_slugs IS NULL OR pcd.tag_slugs_array && p_tag_slugs)
      AND (p_ai_model_slugs IS NULL OR pcd.ai_model_slug = ANY(p_ai_model_slugs))
      AND (p_author_id IS NULL OR pcd.author_id = p_author_id)
      AND (
        p_search_query IS NULL OR 
        pcd.search_vector @@ search_ts_query OR 
        pcd.author_username ILIKE '%' || p_search_query || '%'
      )
  )
  SELECT 
    sr.id,
    sr.short_id,
    sr.title,
    sr.description,
    sr.image_url,
    sr.created_at,
    sr.updated_at,
    sr.is_public,
    sr.view_count,
    sr.primary_tag_id,
    sr.category_id,
    sr.tool_id,
    sr.author_id,
    sr.search_vector,
    sr.tag_slugs_array,
    sr.category_name,
    sr.category_slug,
    sr.tool_name,
    sr.tool_slug,
    sr.author_username,
    sr.author_avatar_url,
    sr.primary_tag_slug,
    sr.tags,
    sr.rating,
    sr.comment_count,
    sr.trending_score,
    sr.ai_model_id,
    sr.ai_model_provider,
    sr.ai_model_name,
    sr.ai_model_slug,
    sr.ai_model_deprecated,
    sr.is_saved_by_user
  FROM search_results sr
  ORDER BY 
    -- If there's a search query, order by relevance first
    CASE 
      WHEN p_search_query IS NOT NULL THEN sr.relevance_score
      ELSE 0
    END DESC,
    -- Then apply the requested sorting
    CASE 
      WHEN p_sort_by = 'created_at' AND p_sort_order = 'desc' THEN sr.created_at
    END DESC,
    CASE 
      WHEN p_sort_by = 'created_at' AND p_sort_order = 'asc' THEN sr.created_at
    END ASC,
    CASE 
      WHEN p_sort_by = 'rating' AND p_sort_order = 'desc' THEN sr.rating
    END DESC,
    CASE 
      WHEN p_sort_by = 'rating' AND p_sort_order = 'asc' THEN sr.rating
    END ASC,
    CASE 
      WHEN p_sort_by = 'trending_score' AND p_sort_order = 'desc' THEN sr.trending_score
    END DESC,
    CASE 
      WHEN p_sort_by = 'trending_score' AND p_sort_order = 'asc' THEN sr.trending_score
    END ASC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Step 2: Update the prompt_card_details_with_saved view to check ALL collections
CREATE OR REPLACE VIEW prompt_card_details_with_saved AS
 WITH aggregatedtagsfordisplay AS (
         SELECT pt.prompt_id,
            jsonb_agg(jsonb_build_object('id', t.id, 'name', t.name, 'slug', t.slug) ORDER BY t.name) FILTER (WHERE (t.id IS NOT NULL)) AS tags_jsonb
           FROM (public.prompt_tags pt
             JOIN public.tags t ON ((pt.tag_id = t.id)))
          GROUP BY pt.prompt_id
        ), user_saved_prompts AS (
         SELECT cp.prompt_id,
            cp.collection_id,
            c.user_id,
            cp.added_at AS saved_at
           FROM (public.collection_prompts cp
             JOIN public.collections c ON ((cp.collection_id = c.id)))
          -- REMOVED: WHERE (c.default_type = 'saved_prompts'::text)
          -- Now checks ALL collections, not just default saved_prompts
        )
 SELECT p.id,
    p.short_id,
    p.title,
    p.description,
    p.image_url,
    p.created_at,
    p.updated_at,
    p.is_public,
    p.view_count,
    p.primary_tag_id,
    p.category_id,
    p.tool_id,
    p.user_id AS author_id,
    p.search_vector,
    COALESCE(p.tag_slugs, '{}'::text[]) AS tag_slugs_array,
    cat.name AS category_name,
    cat.slug AS category_slug,
    tool.name AS tool_name,
    tool.slug AS tool_slug,
    prof.username AS author_username,
    prof.avatar_url AS author_avatar_url,
    ptag.slug AS primary_tag_slug,
    COALESCE(atd.tags_jsonb, '[]'::jsonb) AS tags,
    COALESCE(stats.rating, (0)::bigint) AS rating,
    COALESCE(stats.comment_count, (0)::bigint) AS comment_count,
    COALESCE(trend.trending_score, (0)::double precision) AS trending_score,
    p.ai_model_id,
    am.provider AS ai_model_provider,
    am.tool_name AS ai_model_name,
    am.slug AS ai_model_slug,
    am.deprecated AS ai_model_deprecated,
    usp.user_id AS saved_by_user_id,
    usp.saved_at,
        CASE
            WHEN (usp.user_id IS NOT NULL) THEN true
            ELSE false
        END AS is_saved_by_user
   FROM (((((((((public.prompts p
     LEFT JOIN public.categories cat ON ((p.category_id = cat.id)))
     LEFT JOIN public.tools tool ON ((p.tool_id = tool.id)))
     LEFT JOIN public.profiles prof ON ((p.user_id = prof.id)))
     LEFT JOIN public.tags ptag ON ((p.primary_tag_id = ptag.id)))
     LEFT JOIN aggregatedtagsfordisplay atd ON ((p.id = atd.prompt_id)))
     LEFT JOIN public.prompt_statistics stats ON ((p.id = stats.id)))
     LEFT JOIN public.trending_prompts trend ON ((p.id = trend.id)))
     LEFT JOIN public.ai_models am ON ((p.ai_model_id = am.id)))
     LEFT JOIN user_saved_prompts usp ON ((p.id = usp.prompt_id)));

-- Step 3: Grant permissions
GRANT EXECUTE ON FUNCTION get_prompts_with_saved_status TO authenticated;
GRANT EXECUTE ON FUNCTION get_prompts_with_saved_status TO anon;

-- Step 4: Add comment explaining the change
COMMENT ON FUNCTION get_prompts_with_saved_status IS 'Returns prompts with saved status based on ANY collection membership, not just default saved_prompts collection';
COMMENT ON VIEW prompt_card_details_with_saved IS 'Shows prompt details with saved status based on ANY collection membership'; 