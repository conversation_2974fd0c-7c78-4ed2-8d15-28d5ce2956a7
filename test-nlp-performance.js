// Test Script for Submit Page NLP Performance
// Run this in browser console on /prompt/submit page

(function() {
  'use strict';
  
  console.log('🧪 Starting Submit Page Performance Tests...');

  // Test Data
  const testCases = [
    {
      name: 'Creative Writing',
      input: 'Write a compelling short story about time travel and paradoxes',
      expectedCategory: 'creative-writing',
      expectedTool: 'chatgpt'
    },
    {
      name: 'Code Generation',
      input: 'Create a Python function to sort an array using quicksort algorithm with comments',
      expectedCategory: 'code-generation',
      expectedTool: 'chatgpt'
    },
    {
      name: 'Image Generation',
      input: 'Generate a photorealistic portrait of a warrior, epic lighting, unreal engine, 4k',
      expectedCategory: 'image-generation',
      expectedTool: 'midjourney'
    },
    {
      name: 'Midjourney Pattern',
      input: '/imagine a cyberpunk cityscape --ar 16:9 --v 6 --style raw',
      expectedCategory: 'image-generation',
      expectedTool: 'midjourney'
    },
    {
      name: 'Marketing Copy',
      input: 'Write persuasive ad copy for a new fitness app with strong call to action',
      expectedCategory: 'marketing',
      expectedTool: 'chatgpt'
    }
  ];

  // Performance measurement utilities
  function measurePageLoad() {
    try {
      const navigation = performance.getEntriesByType('navigation')[0];
      console.log('📊 Page Load Performance:');
      console.log(`  • DOM Content Loaded: ${Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart)}ms`);
      console.log(`  • Load Complete: ${Math.round(navigation.loadEventEnd - navigation.loadEventStart)}ms`);
      console.log(`  • Total Load Time: ${Math.round(navigation.loadEventEnd - navigation.fetchStart)}ms`);
    } catch (error) {
      console.log('📊 Page Load Performance: Not available');
    }
  }

  // Bundle size analysis
  function analyzeBundleSize() {
    try {
      const resources = performance.getEntriesByType('resource');
      const jsResources = resources.filter(r => r.name && r.name.includes('.js'));
      
      console.log('📦 JavaScript Bundle Analysis:');
      let foundMainPage = false;
      let foundNLPModules = false;
      
      jsResources.forEach(resource => {
        const size = resource.transferSize || resource.encodedBodySize || 'unknown';
        const name = resource.name.split('/').pop() || resource.name;
        
        if (name.includes('nlp') || name.includes('keyword') || name.includes('tokenise')) {
          console.log(`  • ${name}: ${size} bytes (NLP module)`);
          foundNLPModules = true;
        } else if (name.includes('submit') || name.includes('page')) {
          console.log(`  • ${name}: ${size} bytes (main page)`);
          foundMainPage = true;
        }
      });
      
      if (!foundMainPage) console.log('  • Main page bundle not identified');
      if (!foundNLPModules) console.log('  • No NLP modules loaded yet (good - lazy loading working!)');
      
    } catch (error) {
      console.log('📦 Bundle analysis failed:', error.message);
    }
  }

  // Test NLP analysis with timing
  async function testNLPAnalysis(testCase) {
    console.log(`\n🧠 Testing: ${testCase.name}`);
    console.log(`Input: "${testCase.input}"`);
    
    const startTime = performance.now();
    
    try {
      // Find form elements with multiple selectors
      const titleInput = document.getElementById('title') || document.querySelector('input[placeholder*="title" i]');
      const promptTextArea = document.querySelector('[data-color-mode="dark"]') || 
                            document.querySelector('textarea') ||
                            document.querySelector('[placeholder*="prompt" i]');
      
      if (!titleInput || !promptTextArea) {
        console.log('❌ Could not find form elements');
        console.log('Available inputs:', document.querySelectorAll('input').length);
        console.log('Available textareas:', document.querySelectorAll('textarea').length);
        return;
      }
      
      // Clear previous content
      titleInput.value = '';
      promptTextArea.value = '';
      
      // Set new content
      titleInput.value = `Test: ${testCase.name}`;
      promptTextArea.value = testCase.input;
      
      // Trigger multiple types of events to ensure detection
      ['input', 'change', 'keyup'].forEach(eventType => {
        titleInput.dispatchEvent(new Event(eventType, { bubbles: true }));
        promptTextArea.dispatchEvent(new Event(eventType, { bubbles: true }));
      });
      
      console.log('⏳ Waiting for analysis (3s debounce)...');
      
      // Wait for debounce + analysis
      await new Promise(resolve => setTimeout(resolve, 4000));
      
      const endTime = performance.now();
      console.log(`⚡ Analysis completed in ${Math.round(endTime - startTime)}ms`);
      
      // Check if suggestions appeared with multiple selectors
      const categoryBox = document.querySelector('[class*="bg-blue-50"]') || 
                         document.querySelector('[class*="blue"]');
      const toolBox = document.querySelector('[class*="bg-green-50"]') || 
                     document.querySelector('[class*="green"]');
      const tagBox = document.querySelector('[class*="bg-purple-50"]') || 
                    document.querySelector('[class*="purple"]');
      
      console.log('📋 Results:');
      console.log(`  • Category suggestion: ${categoryBox ? '✅ Found' : '❌ Missing'}`);
      console.log(`  • Tool suggestion: ${toolBox ? '✅ Found' : '❌ Missing'}`);
      console.log(`  • Tag suggestions: ${tagBox ? '✅ Found' : '❌ Missing'}`);
      
      // Additional debugging
      if (!categoryBox && !toolBox && !tagBox) {
        console.log('🔍 Debugging: Looking for any suggestion boxes...');
        const allSuggestionBoxes = document.querySelectorAll('[class*="bg-"][class*="-50"]');
        console.log(`Found ${allSuggestionBoxes.length} potential suggestion boxes`);
      }
      
    } catch (error) {
      console.error('❌ Test failed:', error);
    }
  }

  // Test debouncing behavior
  async function testDebouncing() {
    console.log('\n⏱️ Testing Debouncing Behavior...');
    
    const promptTextArea = document.querySelector('[data-color-mode="dark"]') || 
                          document.querySelector('textarea');
    if (!promptTextArea) {
      console.log('❌ Could not find prompt textarea');
      return;
    }
    
    let analysisCount = 0;
    const originalLog = console.log;
    
    // Count NLP analysis calls
    console.log = function(...args) {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('[NLP] Starting analysis')) {
        analysisCount++;
        originalLog('🔍 Detected analysis call #' + analysisCount);
      }
      originalLog.apply(console, args);
    };
    
    try {
      // Rapid typing simulation
      for (let i = 0; i < 5; i++) {
        promptTextArea.value = `Test content ${i} - rapid typing test - ${Date.now()}`;
        ['input', 'change'].forEach(eventType => {
          promptTextArea.dispatchEvent(new Event(eventType, { bubbles: true }));
        });
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      // Wait for debounce
      console.log('⏳ Waiting for debounce period...');
      await new Promise(resolve => setTimeout(resolve, 4000));
      
    } finally {
      // Restore console.log
      console.log = originalLog;
    }
    
    console.log(`📊 Debouncing Test Result: ${analysisCount <= 1 ? '✅ PASS' : '❌ FAIL'} (${analysisCount} analysis calls)`);
  }

  // Memory usage monitoring
  function monitorMemory() {
    try {
      if (performance.memory) {
        const memory = performance.memory;
        console.log('💾 Memory Usage:');
        console.log(`  • Used: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`);
        console.log(`  • Total: ${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`);
        console.log(`  • Limit: ${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`);
      } else {
        console.log('💾 Memory monitoring not available (try Chrome/Edge)');
      }
    } catch (error) {
      console.log('💾 Memory monitoring failed:', error.message);
    }
  }

  // Quick page check
  function checkPageStatus() {
    console.log('🔍 Page Status Check:');
    console.log(`  • URL: ${window.location.pathname}`);
    console.log(`  • Title inputs found: ${document.querySelectorAll('input').length}`);
    console.log(`  • Textareas found: ${document.querySelectorAll('textarea').length}`);
    console.log(`  • Code editors found: ${document.querySelectorAll('[data-color-mode]').length}`);
    
    const submitButton = document.querySelector('button[type="submit"]');
    console.log(`  • Submit button: ${submitButton ? '✅ Found' : '❌ Missing'}`);
    
    // Check if we're on the right page
    if (!window.location.pathname.includes('submit')) {
      console.log('⚠️  Warning: You might not be on the submit page. Navigate to /prompt/submit first.');
    }
  }

  // Main test runner
  async function runAllTests() {
    try {
      console.log('🚀 Performance & Functionality Test Suite');
      console.log('==========================================\n');
      
      // 0. Check page status
      checkPageStatus();
      
      // 1. Performance metrics
      measurePageLoad();
      analyzeBundleSize();
      monitorMemory();
      
      // 2. Test debouncing
      await testDebouncing();
      
      // 3. Test NLP functionality
      for (const testCase of testCases) {
        await testNLPAnalysis(testCase);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Pause between tests
      }
      
      console.log('\n✅ Test Suite Complete!');
      console.log('Check the results above and verify against expected outcomes.');
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  // Export functions for manual testing
  const nlpTests = {
    runAllTests,
    testNLPAnalysis,
    testDebouncing,
    measurePageLoad,
    analyzeBundleSize,
    monitorMemory,
    checkPageStatus,
    testCases
  };

  // Attach to window
  window.nlpTests = nlpTests;
  
  console.log('📋 Test functions loaded successfully!');
  console.log('🎯 Quick start: window.nlpTests.runAllTests()');
  console.log('🔧 Individual tests: testNLPAnalysis, testDebouncing, measurePageLoad, etc.');
  console.log('📍 Page check: window.nlpTests.checkPageStatus()');
  
  // Return the object for immediate use
  return nlpTests;
  
})(); 