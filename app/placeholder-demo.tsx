"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import MinimalistIconPlaceholder from "@/components/placeholders/minimalist-icon-placeholder"
import GradientLetterPlaceholder from "@/components/placeholders/gradient-letter-placeholder"
import AbstractPatternPlaceholder from "@/components/placeholders/abstract-pattern-placeholder"

export default function PlaceholderDemo() {
  const [selectedCategory, setSelectedCategory] = useState("Code Generation")
  const [sampleTitle, setSampleTitle] = useState("Advanced Code Refactoring")

  const categories = [
    "Code Generation",
    "Creative Writing",
    "Marketing",
    "Image Generation",
    "Data Analysis",
    "Business",
    "Education",
    "Personal",
  ]

  const sampleTitles = [
    "Advanced Code Refactoring",
    "Creative Story Generator",
    "Marketing Copy Optimizer",
    "Realistic Portrait Creator",
    "Data Analysis Report",
    "Business Plan Template",
    "Learning Path Generator",
    "Personal Growth Reflection",
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-8 text-3xl font-bold">Prompt Thumbnail Placeholder Options</h1>

      <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-2">
        <div>
          <h2 className="mb-4 text-xl font-semibold">Test Parameters</h2>
          <div className="mb-4">
            <label className="block mb-2 text-sm font-medium">Category:</label>
            <select
              className="w-full p-2 rounded-md border bg-background"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {categories.map((cat) => (
                <option key={cat} value={cat}>
                  {cat}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block mb-2 text-sm font-medium">Title:</label>
            <select
              className="w-full p-2 rounded-md border bg-background"
              value={sampleTitle}
              onChange={(e) => setSampleTitle(e.target.value)}
            >
              {sampleTitles.map((title) => (
                <option key={title} value={title}>
                  {title}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <h2 className="mb-4 text-xl font-semibold">Preview</h2>
          <Tabs defaultValue="icon">
            <TabsList className="mb-4 w-full">
              <TabsTrigger value="icon">Minimalist Icons</TabsTrigger>
              <TabsTrigger value="letter">Gradient Letters</TabsTrigger>
              <TabsTrigger value="pattern">Abstract Patterns</TabsTrigger>
            </TabsList>

            <TabsContent value="icon">
              <Card>
                <CardHeader>
                  <CardTitle>Minimalist Iconography</CardTitle>
                </CardHeader>
                <CardContent>
                  <MinimalistIconPlaceholder category={selectedCategory} />
                  <p className="mt-4 text-sm text-muted-foreground">
                    Simple, recognizable icons representing common prompt categories, placed on a colored background
                    that matches the existing color scheme.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="letter">
              <Card>
                <CardHeader>
                  <CardTitle>Gradient Letters</CardTitle>
                </CardHeader>
                <CardContent>
                  <GradientLetterPlaceholder title={sampleTitle} />
                  <p className="mt-4 text-sm text-muted-foreground">
                    Gradient backgrounds using the existing color palette with the first letter of the prompt's title
                    overlaid in a contrasting color.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="pattern">
              <Card>
                <CardHeader>
                  <CardTitle>Abstract Patterns</CardTitle>
                </CardHeader>
                <CardContent>
                  <AbstractPatternPlaceholder seed={sampleTitle} />
                  <p className="mt-4 text-sm text-muted-foreground">
                    Abstract shapes and patterns using the color palette, creating visual interest without overwhelming
                    complexity.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
