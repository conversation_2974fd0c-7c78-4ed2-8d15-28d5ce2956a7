import { Suspense } from "react"
import { getCategoriesWithPromptCount, getToolsWithPromptCount, getTagsWithPromptCount } from "@/lib/api-services"
import ExploreClient from "@/components/explore/explore-client"
import { Skeleton } from "@/components/ui/skeleton"
import { metadata } from "./metadata"

export default async function ExplorePage() {
  // Fetch data for the page with actual prompt counts
  const [categories, tools, tags] = await Promise.all([
    getCategoriesWithPromptCount(),
    getToolsWithPromptCount(),
    getTagsWithPromptCount()
  ]);
  
  // The data already includes the correct prompt counts, so we don't need to modify it

  return (
    <main className="mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 max-w-[1400px] w-full">
      <div className="flex flex-col space-y-6 sm:space-y-8">
        <div className="text-center sm:text-left">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2">Explore Prompts</h1>
          <p className="text-sm sm:text-base text-muted-foreground">Discover and explore AI prompt categories, tools, and tags</p>
        </div>

        <ExploreClient
          initialCategories={categories}
          initialTools={tools}
          initialTags={tags}
        />
      </div>
    </main>
  )
}

function PromptGridSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {Array(8)
        .fill(0)
        .map((_, i) => (
          <div key={i} className="flex flex-col space-y-2">
            <Skeleton className="h-40 w-full rounded-lg" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ))}
    </div>
  )
}

function CategoryListSkeleton() {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
      {Array(10)
        .fill(0)
        .map((_, i) => (
          <Skeleton key={i} className="h-24 w-full rounded-lg" />
        ))}
    </div>
  )
}

function ToolListSkeleton() {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
      {Array(10)
        .fill(0)
        .map((_, i) => (
          <Skeleton key={i} className="h-24 w-full rounded-lg" />
        ))}
    </div>
  )
}

function TagListSkeleton() {
  return (
    <div>
      <div className="flex flex-wrap gap-2 mb-4">
        {Array(10)
          .fill(0)
          .map((_, i) => (
            <Skeleton key={i} className="h-10 w-10 rounded-md" />
          ))}
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {Array(15)
          .fill(0)
          .map((_, i) => (
            <Skeleton key={i} className="h-24 w-full rounded-lg" />
          ))}
      </div>
    </div>
  )
}
