import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UserCircle } from "lucide-react"

export default function ProfileNotFound() {
  return (
    <div className="container mx-auto flex min-h-[60vh] flex-col items-center justify-center px-4 py-8 text-center">
      <UserCircle className="mb-4 h-16 w-16 text-muted-foreground" />
      <h1 className="mb-4 text-4xl font-bold">Profile Not Found</h1>
      <p className="mb-8 text-xl text-muted-foreground">
        Sorry, we couldn't find a user with that username.
      </p>
      <div className="flex gap-4">
        <Button asChild className="bg-accent-green hover:bg-accent-green/90 rounded-md">
          <Link href="/explore">Explore Prompts</Link>
        </Button>
        <Button asChild variant="outline" className="rounded-md">
          <Link href="/">Return Home</Link>
        </Button>
      </div>
    </div>
  );
}