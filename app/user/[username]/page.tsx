"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@supabase/ssr"
import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import PromptCard from "@/components/prompt-card"
import CollectionCard from "@/components/collection-card"
import { Github, Link as LinkIcon, Settings, UserPlus, Mail, Check, ExternalLink } from "lucide-react"
import type { PromptCard as PromptCardType, Profile } from "@/lib/types"

interface UserProfilePageProps {
  params: Promise<{
    username: string;
  }>;
}

async function getUserPublicPrompts(supabase: any, userId: string): Promise<PromptCardType[]> {
  const { data, error } = await supabase
    .from("prompt_card_details")
    .select("*")
    .eq("author_id", userId)
    .eq("is_public", true)
    .order("created_at", { ascending: false })
    .limit(10);

  if (error) {
    console.error(`Error fetching prompts for user ${userId}:`, error.message);
    return [];
  }

  // Transform the data to match the PromptCardType
  return data?.map((prompt: any) => ({
    id: prompt.id,
    shortId: prompt.short_id,
    slug: prompt.slug,
    title: prompt.title,
    description: prompt.description,
    imageUrl: prompt.image_url,
    createdAt: prompt.created_at,
    updatedAt: prompt.updated_at,
    isPublic: prompt.is_public,
    viewCount: prompt.view_count,
    category: {
      name: prompt.category_name,
      slug: prompt.category_slug,
    },
    tool: prompt.tool_name ? {
      name: prompt.tool_name,
      slug: prompt.tool_slug,
    } : undefined,
    user: {
      id: prompt.author_id,
      username: prompt.author_username,
      avatarUrl: prompt.author_avatar_url,
    },
    tags: prompt.tags || [],
    primary_tag_slug: prompt.tags?.[0]?.tag?.slug || null,
    // rating field is no longer used, likeCount is used instead
    likeCount: prompt.rating,
    commentCount: prompt.comment_count,
    remixCount: prompt.remix_count,
  })) || [];
}

interface CollectionForCard {
  id: string;
  userId: string;
  name: string;
  description: string | null;
  icon: string | null;
  isPublic: boolean;
  isDefault: boolean;
  defaultType: 'saved_prompts' | 'my_prompts' | null;
  createdAt: string;
  updatedAt: string;
  promptCount?: number;
  viewCount?: number;
  user?: { username: string; id: string };
}

async function getUserPublicCollections(supabase: any, userId: string, profileUsername: string): Promise<CollectionForCard[]> {
  const { data, error } = await supabase
    .from("collections")
    .select(`
      *,
      collection_prompts(count)
    `)
    .eq("user_id", userId)
    .eq("is_public", true)
    .order("created_at", { ascending: false })
    .limit(10);

  if (error) {
    console.error(`Error fetching collections for user ${userId}:`, error.message);
    return [];
  }

  return data?.map((c: any) => {
    const collectionPrompts = c.collection_prompts as unknown as [{count: number}] | [];
    return {
        id: c.id,
        userId: c.user_id,
        name: c.name,
        description: c.description,
        icon: c.icon,
        isPublic: c.is_public,
        isDefault: c.is_default || false,
        defaultType: c.default_type || null,
        createdAt: c.created_at,
        updatedAt: c.updated_at,
        promptCount: collectionPrompts[0]?.count || 0,
        viewCount: 0, // Placeholder - implement view tracking for collections if needed
        user: { username: profileUsername, id: userId },
    };
  }) || [];
}

export default function UserProfilePage({ params }: UserProfilePageProps) {
  const [profile, setProfile] = useState<any>(null)
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const [prompts, setPrompts] = useState<PromptCardType[]>([])
  const [collections, setCollections] = useState<CollectionForCard[]>([])
  const [promptsCount, setPromptsCount] = useState(0)
  const [collectionsCount, setCollectionsCount] = useState(0)
  const [totalUpvotes, setTotalUpvotes] = useState(0)
  const [activeTab, setActiveTab] = useState<"prompts" | "collections">("prompts")
  const [loading, setLoading] = useState(true)

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  useEffect(() => {
    async function loadUserProfile() {
      try {
        const { username } = await params;
        
        // First, get the current user to check if they're trying to access their own profile
        const { data: authData } = await supabase.auth.getSession();
        const currentUserId = authData.session?.user?.id || null;
        setCurrentUserId(currentUserId);

        // If user is logged in, check if they're trying to access their own profile with an old username
        if (currentUserId) {
          const { data: currentUserProfile, error: currentUserError } = await supabase
            .from("profiles")
            .select("username")
            .eq("id", currentUserId)
            .single();

          if (!currentUserError && currentUserProfile && currentUserProfile.username !== username) {
            // Check if the requested username belongs to the current user (old username)
            const { data: requestedProfile, error: requestedError } = await supabase
              .from("profiles")
              .select("id")
              .eq("username", username)
              .single();

            // If the requested username doesn't exist but the user is logged in,
            // redirect them to their current username
            if (requestedError && requestedError.code === "PGRST116") {
              window.location.href = `/user/${currentUserProfile.username}`;
              return;
            }
          }
        }
        
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("username", username)
          .single();

        if (profileError || !profileData) {
          console.error(`Profile not found for username: ${username}`, profileError?.message);
          notFound();
          return;
        }

        setProfile(profileData);

        // Fetch actual counts from database
        const { count: promptsCountData } = await supabase
          .from("prompts")
          .select("*", { count: "exact", head: true })
          .eq("user_id", profileData.id)
          .eq("is_public", true);

        const { count: collectionsCountData } = await supabase
          .from("collections")
          .select("*", { count: "exact", head: true })
          .eq("user_id", profileData.id)
          .eq("is_public", true);

        // Fetch total upvotes (likes) received across all user's prompts
        const { data: upvotesData } = await supabase
          .from("prompt_statistics")
          .select("upvotes")
          .eq("user_id", profileData.id)
          .eq("is_public", true);

        const totalUpvotesData = upvotesData?.reduce((sum, prompt) => sum + (prompt.upvotes || 0), 0) || 0;

        setPromptsCount(promptsCountData || 0);
        setCollectionsCount(collectionsCountData || 0);
        setTotalUpvotes(totalUpvotesData);

        const promptsData = await getUserPublicPrompts(supabase, profileData.id);
        const collectionsData = await getUserPublicCollections(supabase, profileData.id, profileData.username);

        setPrompts(promptsData);
        setCollections(collectionsData);
        setLoading(false);
      } catch (error) {
        console.error("Error loading user profile:", error);
        setLoading(false);
      }
    }

    loadUserProfile();

    // Add visibility change listener to refresh data when user returns to page
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        loadUserProfile();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [params, supabase]);

  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1).replace(/\.0$/, "")}M`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
    } else {
      return count.toString()
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-64 bg-muted rounded-xl mb-8"></div>
          <div className="h-12 bg-muted rounded mb-6"></div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-muted rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!profile) {
    notFound();
    return null;
  }

  const isOwnProfile = currentUserId === profile.id;

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mb-8 shadow-lg rounded-xl overflow-hidden border border-border">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row items-center md:items-start gap-4">
            <Avatar className="h-32 w-32 md:h-40 md:w-40 border-4 border-background shadow-md bg-muted">
              <AvatarImage src={profile.avatar_url || undefined} alt={profile.username} />
              <AvatarFallback className="text-4xl">
                {profile.username?.substring(0, 2).toUpperCase() || "P"}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 pt-4 text-center md:text-left">
              <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold">{profile.username}</h1>
                  {profile.email && <p className="text-sm text-muted-foreground flex items-center justify-center md:justify-start gap-1 mt-1"><Mail className="h-3.5 w-3.5"/> {profile.email}</p>}
                </div>
                <div className="mt-3 md:mt-0 md:pt-1">
                  {isOwnProfile ? (
                    <Button asChild variant="outline" size="sm" className="rounded-md">
                      <Link href="/settings"><Settings className="mr-1.5 h-4 w-4" /> Edit Profile</Link>
                    </Button>
                  ) : (
                    <div className="flex gap-2 justify-center">
                      {/* Follow button placeholder - Requires client component & server action */}
                      {/* <Button size="sm" className="rounded-md">
                        <UserPlus className="mr-1.5 h-4 w-4" /> Follow
                      </Button>
                      <Button variant="outline" size="sm" className="rounded-md">Message</Button> */}
                    </div>
                  )}
                </div>
              </div>
              {profile.bio && <p className="mt-2 text-muted-foreground text-sm">{profile.bio}</p>}
            </div>
          </div>

          {/* Stats Bar */}
          <div className="mt-6 pt-4 border-t border-border flex flex-wrap gap-x-6 gap-y-2 text-sm items-center justify-center md:justify-start">
            <div className="text-center md:text-left"><span className="font-semibold text-foreground">{promptsCount}</span> <span className="text-muted-foreground">Prompts</span></div>
            <div className="text-center md:text-left"><span className="font-semibold text-foreground">{collectionsCount}</span> <span className="text-muted-foreground">Collections</span></div>
            <div className="text-center md:text-left"><span className="font-semibold text-foreground">{totalUpvotes}</span> <span className="text-muted-foreground">upvotes</span></div>
            {/* <div className="text-muted-foreground"><span className="font-semibold text-foreground">{followerCount}</span> Followers</div>
            <div className="text-muted-foreground"><span className="font-semibold text-foreground">150</span> Following</div> */}
          </div>

          <div className="mt-6 flex flex-wrap gap-x-6 gap-y-3 text-sm items-center justify-center md:justify-start">
            {profile.website_url && (
              <a href={profile.website_url.startsWith('http') ? profile.website_url : `https://${profile.website_url}`} target="_blank" rel="noopener noreferrer" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
                <LinkIcon className="mr-1.5 h-4 w-4" /> {profile.website_url.replace(/^https?:\/\//, '')}
              </a>
            )}
            {profile.github_url && (
              <a href={profile.github_url} target="_blank" rel="noopener noreferrer" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
                <Github className="mr-1.5 h-4 w-4" /> GitHub
              </a>
            )}
            {profile.x_url && (
              <a href={profile.x_url} target="_blank" rel="noopener noreferrer" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
                <svg viewBox="0 0 24 24" aria-hidden="true" className="mr-1.5 h-4 w-4 fill-current"><g><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path></g></svg>
                X.com
              </a>
            )}
            <div className="text-muted-foreground">Joined {new Date(profile.created_at).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}</div>
          </div>
        </CardContent>
      </Card>

      {/* Tab Navigation matching saved page design */}
      <div className="border-b border-border mb-6">
        <div className="flex">
          <Button
            variant="ghost"
            className={`flex-1 rounded-none border-b-2 py-4 font-medium transition-colors ${
              activeTab === "prompts"
                ? "border-primary text-primary bg-primary/5"
                : "border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/50"
            }`}
            onClick={() => setActiveTab("prompts")}
          >
            <span className="flex items-center gap-2">
              Prompts
              <Badge 
                variant="secondary" 
                className={`text-xs ${
                  activeTab === "prompts" 
                    ? "bg-primary/20 text-primary" 
                    : "bg-muted text-muted-foreground"
                }`}
              >
                {formatCount(prompts.length)}
              </Badge>
            </span>
          </Button>
          <Button
            variant="ghost"
            className={`flex-1 rounded-none border-b-2 py-4 font-medium transition-colors ${
              activeTab === "collections"
                ? "border-primary text-primary bg-primary/5"
                : "border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/50"
            }`}
            onClick={() => setActiveTab("collections")}
          >
            <span className="flex items-center gap-2">
              Collections
              <Badge 
                variant="secondary" 
                className={`text-xs ${
                  activeTab === "collections" 
                    ? "bg-primary/20 text-primary" 
                    : "bg-muted text-muted-foreground"
                }`}
              >
                {formatCount(collections.length)}
              </Badge>
            </span>
          </Button>
        </div>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === "prompts" && (
          <div>
            {prompts.length > 0 ? (
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3">
                {prompts.map((prompt) => (
                  <PromptCard key={prompt.id} prompt={prompt} maxTags={1} isOwner={isOwnProfile} />
                ))}
              </div>
            ) : (
              <Card className="py-12 rounded-lg">
                <CardContent className="text-center text-muted-foreground">
                  <p className="text-lg">{profile.username} hasn't published any prompts yet.</p>
                  {isOwnProfile && <Button asChild className="mt-4 btn-accent-green rounded-md"><Link href="/prompt/submit">Submit Your First Prompt</Link></Button>}
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {activeTab === "collections" && (
          <div>
            {collections.length > 0 ? (
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3">
                {collections.map((collection) => (
                  <CollectionCard key={collection.id} collection={collection} isOwner={isOwnProfile} />
                ))}
              </div>
            ) : (
              <Card className="py-12 rounded-lg">
                <CardContent className="text-center text-muted-foreground">
                  <p className="text-lg">{profile.username} hasn't created any public collections yet.</p>
                  {isOwnProfile && <Button asChild className="mt-4 btn-accent-green rounded-md"><Link href="/collections">Create a Collection</Link></Button>}
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  );
}