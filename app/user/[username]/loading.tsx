import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"

export default function ProfileLoading() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-muted h-40 rounded-lg animate-pulse mb-[-4rem] md:mb-[-5rem]"></div>
      <Card className="mb-8 shadow-lg rounded-xl overflow-hidden">
        <CardContent className="p-6 pt-0">
          <div className="flex flex-col md:flex-row items-center md:items-end gap-4 -mt-16 md:-mt-20 relative z-10">
            <div className="h-32 w-32 md:h-40 md:w-40 rounded-full bg-muted border-4 border-background animate-pulse"></div>
            <div className="flex-1 pt-4 text-center md:text-left space-y-2">
              <div className="h-8 w-48 bg-muted rounded animate-pulse mx-auto md:mx-0"></div>
              <div className="h-4 w-64 bg-muted rounded animate-pulse mx-auto md:mx-0"></div>
              <div className="h-4 w-56 bg-muted rounded animate-pulse mx-auto md:mx-0 mt-1"></div>
              <div className="flex gap-4 mt-3 justify-center md:justify-start">
                <div className="h-8 w-24 bg-muted rounded-md animate-pulse"></div>
                <div className="h-8 w-24 bg-muted rounded-md animate-pulse"></div>
              </div>
            </div>
          </div>
          <div className="mt-6 flex flex-wrap gap-x-6 gap-y-3 justify-center md:justify-start">
            <div className="h-4 w-32 bg-muted rounded animate-pulse"></div>
            <div className="h-4 w-24 bg-muted rounded animate-pulse"></div>
            <div className="h-4 w-40 bg-muted rounded animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
      <div className="h-10 w-1/2 bg-muted rounded-lg animate-pulse mb-6"></div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map(i => <div key={i} className="h-72 bg-muted rounded-xl animate-pulse"></div>)}
      </div>
    </div>
  );
}