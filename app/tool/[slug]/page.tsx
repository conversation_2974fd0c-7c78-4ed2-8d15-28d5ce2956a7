import React from 'react'
import FilteredPromptsPageClient from '@/components/filtered-prompts-page-client'
import { getToolBySlug } from "@/lib/api-services" // Import the fetch function
import { notFound } from "next/navigation"; // Import notFound

interface ToolPageProps {
  params: Promise<{
    slug: string
  }>
  // searchParams: { [key: string]: string | string[] | undefined };
}

// Accept the full props object
export default async function ToolPage(props: ToolPageProps) {
  // Access slug from props.params
  const slug = (await props.params).slug;

  // Fetch tool data on the server
  const tool = await getToolBySlug(slug);

  // Handle case where the tool is not found
  if (!tool) {
    notFound();
  }

  // Render the client component, passing the fetched data
  return (
    <FilteredPromptsPageClient
      initialSlug={slug}
      entityType="tool"
      initialEntityData={tool} // Pass the fetched tool data
      entityName="Tool"
    />
  );
}
