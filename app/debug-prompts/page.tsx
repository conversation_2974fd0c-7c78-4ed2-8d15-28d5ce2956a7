"use client"

import { useEffect, useState } from "react"
import { getPrompts } from "@/lib/api-services"
import type { PromptCard } from "@/lib/types"

export default function DebugPromptsPage() {
  const [prompts, setPrompts] = useState<PromptCard[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchPrompts() {
      try {
        console.log("🔍 Fetching prompts for creative-writing category...")
        
        const data = await getPrompts({
          categorySlugs: ['creative-writing'],
          limit: 10,
          offset: 0,
          sortBy: 'created_at',
          sortOrder: 'desc'
        })
        
        console.log("✅ Prompts fetched:", data.length)
        console.log("Sample prompt:", data[0])
        
        setPrompts(data)
      } catch (err) {
        console.error("❌ Error fetching prompts:", err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchPrompts()
  }, [])

  if (loading) {
    return <div className="p-8">Loading prompts...</div>
  }

  if (error) {
    return <div className="p-8 text-red-500">Error: {error}</div>
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Debug Prompts Page</h1>
      <p className="mb-4">Found {prompts.length} prompts for creative-writing category</p>
      
      <div className="space-y-4">
        {prompts.map((prompt) => (
          <div key={prompt.id} className="border p-4 rounded">
            <h3 className="font-semibold">{prompt.title}</h3>
            <p className="text-sm text-gray-600">{prompt.description}</p>
            <p className="text-xs text-gray-500">Category: {prompt.category?.slug}</p>
          </div>
        ))}
      </div>
    </div>
  )
} 