import { notFound } from "next/navigation"
import { getPromptByShortId } from "@/lib/api-services"
import PromptDetailView from "@/components/prompt-detail-view"

interface PageProps {
  params: Promise<{
    shortId: string
  }>
}

export default async function PromptDetailPage({ params }: PageProps) {
  try {
    // Await params as it's now a Promise in Next.js 15
    const { shortId } = await params;
    
    console.log(`[PromptDetailPage] Received params.shortId from URL: "${shortId}"`);
    console.log(`Fetching prompt with shortId: ${shortId}`)

    // Fetch the prompt data
    const prompt = await getPromptByShortId(shortId)

    if (!prompt) {
      console.error(`Prompt with shortId ${shortId} not found`)
      return notFound()
    }

    return <PromptDetailView prompt={prompt} />
  } catch (error) {
    console.error("Error fetching prompt:", error)
    return notFound()
  }
} 