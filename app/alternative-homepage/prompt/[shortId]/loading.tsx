import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"

export default function Loading() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4">
        {/* Breadcrumbs skeleton */}
        <div className="flex items-center gap-1 py-4">
          <Skeleton className="h-3 w-12" />
          <span>/</span>
          <Skeleton className="h-3 w-16" />
        </div>

        {/* Title and description skeleton */}
        <div className="mb-6">
          <Skeleton className="h-12 w-3/4 mb-2" />
          <Skeleton className="h-6 w-1/2" />
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main content area */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image skeleton */}
            <Skeleton className="h-[350px] w-full rounded-xl" />

            {/* Tabs skeleton */}
            <Card className="rounded-xl border shadow-sm">
              <div className="border-b border-border">
                <div className="flex">
                  <Skeleton className="flex-1 h-12 rounded-none" />
                  <Skeleton className="flex-1 h-12 rounded-none" />
                  <Skeleton className="flex-1 h-12 rounded-none" />
                </div>
              </div>
              <div className="p-6 space-y-6">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-32 w-full rounded-lg" />
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-24 w-full rounded-lg" />
              </div>
            </Card>
          </div>

          {/* Sidebar skeleton */}
          <aside className="lg:col-span-1 space-y-6">
            {/* Use This Prompt card skeleton */}
            <Card className="rounded-xl border shadow-sm">
              <div className="p-4 border-b">
                <Skeleton className="h-6 w-32" />
              </div>
              <CardContent className="p-4 space-y-4">
                <div className="text-center">
                  <Skeleton className="h-12 w-16 mx-auto mb-2" />
                  <Skeleton className="h-4 w-12 mx-auto" />
                </div>
                <div className="flex gap-2">
                  <Skeleton className="flex-1 h-10" />
                  <Skeleton className="flex-1 h-10" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </CardContent>
            </Card>

            {/* Details card skeleton */}
            <Card className="rounded-xl border shadow-sm">
              <div className="p-4 border-b">
                <Skeleton className="h-6 w-32" />
              </div>
              <CardContent className="p-4 space-y-3">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center gap-3">
                    <Skeleton className="h-10 w-10 rounded-md" />
                    <div className="flex-1">
                      <Skeleton className="h-3 w-16 mb-1" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Stats card skeleton */}
            <Card className="rounded-xl border shadow-sm">
              <div className="p-4 border-b">
                <Skeleton className="h-6 w-16" />
              </div>
              <CardContent className="p-4 space-y-2.5">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-8" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </aside>
        </div>
      </div>
    </div>
  )
} 