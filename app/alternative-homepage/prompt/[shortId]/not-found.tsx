import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { AlertCircle, Home, Search } from "lucide-react"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <Card className="rounded-xl border shadow-sm">
            <CardContent className="p-8">
              <div className="mb-6">
                <AlertCircle className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h1 className="text-2xl font-bold text-foreground mb-2">Prompt Not Found</h1>
                <p className="text-muted-foreground">
                  The prompt you're looking for doesn't exist or may have been removed.
                </p>
              </div>
              
              <div className="space-y-3">
                <Button asChild className="w-full">
                  <Link href="/alternative-homepage">
                    <Home className="mr-2 h-4 w-4" />
                    Back to Home
                  </Link>
                </Button>
                
                <Button variant="outline" asChild className="w-full">
                  <Link href="/search">
                    <Search className="mr-2 h-4 w-4" />
                    Search Prompts
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 