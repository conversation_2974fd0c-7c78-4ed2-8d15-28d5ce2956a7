"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Github, Link as LinkIcon, AtSign } from "lucide-react"
import { createBrowserClient } from "@supabase/ssr"
import { useRouter } from "next/navigation"
import { ProfilePictureUpload } from "@/components/profile-picture-upload"
import { toast } from "sonner"
import type { Profile } from "@/lib/types"

export default function SettingsPage() {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [activeTab, setActiveTab] = useState("profile")
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  const router = useRouter()
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  // Form fields
  const [username, setUsername] = useState("")
  const [bio, setBio] = useState("")
  const [websiteUrl, setWebsiteUrl] = useState("")
  const [githubUrl, setGithubUrl] = useState("")
  const [xUrl, setXUrl] = useState("")
  const [youtubeUrl, setYoutubeUrl] = useState("")

  // Load user profile on mount
  useEffect(() => {
    const fetchUserProfile = async () => {
      setIsLoading(true)
      setError(null)
      
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        // Not logged in, redirect to sign in
        router.push("/sign-in")
        return
      }

      const user = session.user

      // Fetch the user's profile
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single()
      
      if (profileError) {
        console.error("Error fetching profile:", profileError.message)
        setError("Failed to load your profile. Please try again.")
        setIsLoading(false)
        return
      }
      
      setProfile(profileData)
      
      // Set form fields
      setUsername(profileData.username || "")
      setBio(profileData.bio || "")
      setWebsiteUrl(profileData.website_url || "")
      setGithubUrl(profileData.github_url || "")
      setXUrl(profileData.x_url || "")
      setYoutubeUrl(profileData.youtube_url || "")
      
      setIsLoading(false)
    }
    
    fetchUserProfile()
  }, [supabase, router])

  // Profile picture handlers
  const handleProfilePictureUploadSuccess = (newAvatarUrl: string) => {
    setProfile(prev => prev ? { ...prev, avatar_url: newAvatarUrl } : null)
    toast.success("Profile picture uploaded successfully!")
  }

  const handleProfilePictureDeleteSuccess = () => {
    setProfile(prev => prev ? { ...prev, avatar_url: null } : null)
    toast.success("Profile picture deleted successfully!")
  }

  const handleProfilePictureError = (error: string) => {
    toast.error(error)
  }

  // Handle profile update
  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)
    setIsSaving(true)
    
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        setError("You must be logged in to update your profile")
        setIsSaving(false)
        return
      }
      
      const user = session.user
      
      // Basic validation
      if (!username.trim()) {
        setError("Username cannot be empty")
        setIsSaving(false)
        return
      }
      
      // Username format validation (alphanumeric, underscores, hyphens)
      if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
        setError("Username can only contain letters, numbers, underscores, and hyphens")
        setIsSaving(false)
        return
      }
      
      // Length validation
      if (username.length < 3 || username.length > 20) {
        setError("Username must be between 3 and 20 characters")
        setIsSaving(false)
        return
      }
      
      // Check if username is already taken (excluding the current user)
      if (username !== profile?.username) {
        const { data: existingUser, error: checkError } = await supabase
          .from("profiles")
          .select("id")
          .eq("username", username)
          .neq("id", user.id)
          .single()
        
        if (existingUser) {
          setError("This username is already taken. Please choose another one.")
          setIsSaving(false)
          return
        }
        
        if (checkError && checkError.code !== "PGRST116") { // PGRST116 is "no rows returned" which is what we want
          console.error("Error checking username:", checkError.message)
          setError("Failed to check username availability. Please try again.")
          setIsSaving(false)
          return
        }
      }
      
      // Avatar URL is now managed by the ProfilePictureUpload component
      const avatarUrl = profile?.avatar_url
      
      // Update profile
      const { error: updateError } = await supabase
        .from("profiles")
        .update({
          username,
          bio,
          website_url: websiteUrl,
          github_url: githubUrl,
          x_url: xUrl,
          youtube_url: youtubeUrl,
          avatar_url: avatarUrl,
          is_username_customized: true, // Ensure this is set to true
          updated_at: new Date().toISOString()
        })
        .eq("id", user.id)
      
      if (updateError) {
        console.error("Error updating profile:", updateError.message)
        setError("Failed to update profile. Please try again.")
        setIsSaving(false)
        return
      }
      
      // Success!
      setSuccess("Profile updated successfully!")
      
      // Update local profile state
      setProfile({
        ...profile!,
        username,
        bio,
        website_url: websiteUrl,
        github_url: githubUrl,
        x_url: xUrl,
        youtube_url: youtubeUrl,
        avatar_url: avatarUrl,
        is_username_customized: true,
        updated_at: new Date().toISOString()
      })
      
      // Profile picture state is managed by the ProfilePictureUpload component
      
      // If username was changed, redirect to the new profile page
      if (username !== profile?.username) {
        // Use window.location.href to trigger a full page refresh and update the header
        setTimeout(() => {
          window.location.href = `/user/${username}`;
        }, 1000); // Give user time to see the success message
      } else {
        // Refresh the page to show updated data
        router.refresh()
      }
    } catch (err) {
      console.error("Unexpected error:", err)
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-3xl font-bold">Settings</h1>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6 grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="account">Account</TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile">
          {isLoading ? (
            <div className="flex h-40 items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-accent-green" />
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2">
              <ProfilePictureUpload
                currentAvatarUrl={profile?.avatar_url}
                username={profile?.username || ""}
                onUploadSuccess={handleProfilePictureUploadSuccess}
                onDeleteSuccess={handleProfilePictureDeleteSuccess}
                onError={handleProfilePictureError}
              />
              
              <Card>
                <form onSubmit={handleProfileUpdate}>
                  <CardHeader>
                    <CardTitle>Profile Information</CardTitle>
                    <CardDescription>
                      Update your profile information visible to other users.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="username">Username</Label>
                      <div className="relative">
                        <AtSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                          id="username"
                          placeholder="your-username"
                          value={username}
                          onChange={(e) => setUsername(e.target.value)}
                          disabled={isSaving}
                          className="pl-10"
                        />
                      </div>
                      <p className="text-xs text-muted-foreground">
                        This will be your public username visible to everyone.
                      </p>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="bio">Bio</Label>
                      <Textarea
                        id="bio"
                        placeholder="Tell us about yourself"
                        value={bio}
                        onChange={(e) => setBio(e.target.value)}
                        disabled={isSaving}
                        rows={3}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <div className="relative">
                        <LinkIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                          id="website"
                          placeholder="https://yourwebsite.com"
                          value={websiteUrl}
                          onChange={(e) => setWebsiteUrl(e.target.value)}
                          disabled={isSaving}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="github">GitHub</Label>
                      <div className="relative">
                        <Github className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                          id="github"
                          placeholder="https://github.com/yourusername"
                          value={githubUrl}
                          onChange={(e) => setGithubUrl(e.target.value)}
                          disabled={isSaving}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="x">X (Twitter)</Label>
                      <div className="relative">
                        <svg viewBox="0 0 24 24" aria-hidden="true" className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 fill-current text-muted-foreground">
                          <g><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path></g>
                        </svg>
                        <Input
                          id="x"
                          placeholder="https://x.com/yourusername"
                          value={xUrl}
                          onChange={(e) => setXUrl(e.target.value)}
                          disabled={isSaving}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="youtube">YouTube</Label>
                      <div className="relative">
                        <svg viewBox="0 0 24 24" aria-hidden="true" className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 fill-current text-muted-foreground">
                          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                        </svg>
                        <Input
                          id="youtube"
                          placeholder="https://youtube.com/@yourchannel"
                          value={youtubeUrl}
                          onChange={(e) => setYoutubeUrl(e.target.value)}
                          disabled={isSaving}
                          className="pl-10"
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex flex-col items-start space-y-2">
                    {error && <p className="text-sm text-red-500">{error}</p>}
                    {success && <p className="text-sm text-green-500">{success}</p>}
                    <Button
                      type="submit"
                      className="bg-accent-green hover:bg-accent-green/90"
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Save Changes"
                      )}
                    </Button>
                  </CardFooter>
                </form>
              </Card>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="account">
          <Card>
            <CardHeader>
              <CardTitle>Account Settings</CardTitle>
              <CardDescription>
                Manage your account settings and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={profile?.email || ""}
                  disabled
                  className="bg-muted"
                />
                <p className="text-xs text-muted-foreground">
                  Your email address is managed through your authentication provider.
                </p>
              </div>
              
              {/* Add more account settings as needed */}
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="text-red-500 hover:bg-red-500/10 hover:text-red-500"
                onClick={async () => {
                  if (confirm("Are you sure you want to sign out?")) {
                    await supabase.auth.signOut();
                    router.push("/");
                  }
                }}
              >
                Sign Out
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}