"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Bell, Loader2, Check, Check<PERSON>he<PERSON>, Filter } from "lucide-react"
import Link from "next/link"
import { createBrowserClient } from "@supabase/ssr"
import type { Notification as NotificationType, PromptCard as PromptCardType } from "@/lib/types"
import { generatePromptUrl } from "@/lib/utils/url-helpers"
import { toast } from "sonner"
import { formatDistanceToNow } from "date-fns"

// Local interface for component use
interface Notification {
  id: string
  type: "like" | "comment" | "follow" | "mention" | "system" | "comment_prompt" | "reply_comment"
  message: string
  user?: {
    username: string
    id: string
    avatarUrl?: string
  }
  promptId?: string
  promptTitle?: string
  createdAt: string
  read: boolean
  // New fields from notification table for link generation
  prompt_short_id?: string
  prompt_category_slug?: string
  prompt_tool_slug?: string
  prompt_primary_tag_slug?: string
  prompt_title_slug?: string
  entity_title?: string
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState<"all" | "unread" | "read">("all")
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [totalCount, setTotalCount] = useState(0)
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  const ITEMS_PER_PAGE = 20

  // Load notifications on mount and when filter changes
  useEffect(() => {
    loadNotifications(true)
  }, [filter])

  // Load notifications from Supabase
  const loadNotifications = async (reset = false) => {
    setIsLoading(true)

    try {
      // Get current user
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        setNotifications([])
        setTotalCount(0)
        setIsLoading(false)
        return
      }

      const user = session.user

      const currentPage = reset ? 1 : page
      const offset = (currentPage - 1) * ITEMS_PER_PAGE

      // Build query based on filter
      let query = supabase
        .from("notifications")
        .select(`
          *,
          actor:actor_user_id (
            username,
            avatar_url
          )
        `, { count: 'exact' })
        .eq("recipient_user_id", user.id)
        .order("created_at", { ascending: false })
        .range(offset, offset + ITEMS_PER_PAGE - 1)

      // Apply filter
      if (filter === "unread") {
        query = query.eq("is_read", false)
      } else if (filter === "read") {
        query = query.eq("is_read", true)
      }

      const { data, error, count } = await query

      if (error) {
        console.error("Error fetching notifications:", error.message)
        toast.error("Failed to load notifications")
        setNotifications([])
        setTotalCount(0)
      } else {
        // Transform the data to match our Notification interface
        const transformedNotifications: Notification[] = data.map((n: any) => ({
          id: n.id,
          type: n.type as "like" | "comment" | "follow" | "mention" | "system" | "comment_prompt" | "reply_comment",
          message: getNotificationMessage(n.type),
          user: n.actor ? {
            username: n.actor.username,
            id: n.actor_user_id,
            avatarUrl: n.actor.avatar_url || "/placeholder-user.jpg",
          } : undefined,
          promptId: n.entity_type === "prompt" ? n.entity_id : undefined,
          promptTitle: n.entity_title || undefined,
          createdAt: n.created_at,
          read: n.is_read,
          // Add new fields for link generation
          prompt_short_id: n.prompt_short_id,
          prompt_category_slug: n.prompt_category_slug,
          prompt_tool_slug: n.prompt_tool_slug,
          prompt_primary_tag_slug: n.prompt_primary_tag_slug,
          prompt_title_slug: n.prompt_title_slug,
          entity_title: n.entity_title,
        }))

        if (reset) {
          setNotifications(transformedNotifications)
          setPage(1)
        } else {
          setNotifications(prev => [...prev, ...transformedNotifications])
        }
        
        setTotalCount(count || 0)
        setHasMore(transformedNotifications.length === ITEMS_PER_PAGE)
      }
    } catch (err) {
      console.error("Unexpected error loading notifications:", err)
      toast.error("An unexpected error occurred")
      setNotifications([])
      setTotalCount(0)
    } finally {
      setIsLoading(false)
    }
  }

  // Helper function to get notification message based on type
  const getNotificationMessage = (type: string): string => {
    switch (type) {
      case "like_prompt":
        return "liked your prompt"
      case "comment_prompt":
        return "commented on your prompt"
      case "reply_comment":
        return "replied to your comment"
      case "new_follower":
        return "started following you"
      case "mention":
        return "mentioned you in a comment"
      case "system":
        return "System notification"
      default:
        return "interacted with your content"
    }
  }

  // Helper function to generate notification link
  const generateNotificationLink = (notification: Notification): string => {
    // For prompt-related notifications, use generatePromptUrl
    if ((notification.type === 'comment_prompt' || notification.type === 'reply_comment') && notification.prompt_short_id) {
      try {
        // Construct minimal PromptCardType-like object from notification data
        const promptForUrl: Partial<PromptCardType> = {
          shortId: notification.prompt_short_id,
          title: notification.entity_title || 'Untitled Prompt',
          category: {
            slug: notification.prompt_category_slug || 'uncategorized',
            name: notification.prompt_category_slug || 'Uncategorized'
          },
          tool: notification.prompt_tool_slug ? {
            slug: notification.prompt_tool_slug,
            name: notification.prompt_tool_slug
          } : undefined,
          primary_tag_slug: notification.prompt_primary_tag_slug || 'misc'
        }
        
        return generatePromptUrl(promptForUrl as PromptCardType)
      } catch (error) {
        console.error(`Error generating prompt URL for notification ${notification.id}:`, error)
        return "#"
      }
    }
    
    // For user-related notifications
    if (notification.user) {
      return `/user/${notification.user.username}`
    }
    
    // Fallback for notifications missing prompt_short_id
    if (notification.type === 'comment_prompt' || notification.type === 'reply_comment') {
      console.warn(`Notification ID ${notification.id} of type ${notification.type} is missing prompt_short_id. Cannot generate link.`)
    }
    
    return "#"
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) return

      const user = session.user

      // Update all unread notifications to read in the database
      const { error } = await supabase
        .from("notifications")
        .update({ is_read: true })
        .eq("recipient_user_id", user.id)
        .eq("is_read", false)

      if (error) {
        console.error("Error marking all notifications as read:", error.message)
        toast.error("Failed to mark all as read")
        return
      }

      // Update local state
      setNotifications(notifications.map(n => ({ ...n, read: true })))
      toast.success("All notifications marked as read")
    } catch (err) {
      console.error("Unexpected error marking all as read:", err)
      toast.error("An unexpected error occurred")
    }
  }

  // Mark a single notification as read
  const markAsRead = async (id: string) => {
    try {
      // Update the notification in the database
      const { error } = await supabase
        .from("notifications")
        .update({ is_read: true })
        .eq("id", id)

      if (error) {
        console.error("Error marking notification as read:", error.message)
        return
      }

      // Update local state
      setNotifications(notifications.map(n => n.id === id ? { ...n, read: true } : n))
    } catch (err) {
      console.error("Unexpected error marking notification as read:", err)
    }
  }

  // Load more notifications
  const loadMore = () => {
    setPage(prev => prev + 1)
    loadNotifications(false)
  }

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return "just now"
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60)
      return `${minutes}m ago`
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600)
      return `${hours}h ago`
    } else {
      const days = Math.floor(diffInSeconds / 86400)
      return `${days}d ago`
    }
  }

  // Get filtered notifications for display
  const filteredNotifications = notifications.filter(notification => {
    if (filter === "unread") return !notification.read
    if (filter === "read") return notification.read
    return true
  })

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight text-foreground mb-2">
          Notifications
        </h1>
        <p className="text-muted-foreground">
          Stay updated with your latest interactions and activities
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              All Notifications
              {totalCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {totalCount}
                </Badge>
              )}
            </CardTitle>
            {unreadCount > 0 && (
              <Button variant="outline" size="sm" onClick={markAllAsRead}>
                <CheckCheck className="h-4 w-4 mr-2" />
                Mark all as read
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={filter} onValueChange={(value) => setFilter(value as "all" | "unread" | "read")} className="mb-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">
                All
                {totalCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {totalCount}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="unread">
                Unread
                {unreadCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {unreadCount}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="read">Read</TabsTrigger>
            </TabsList>
          </Tabs>

          {isLoading && notifications.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="mr-2 h-6 w-6 animate-spin" />
              <span className="text-muted-foreground">Loading notifications...</span>
            </div>
          ) : filteredNotifications.length > 0 ? (
            <div className="space-y-4">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`flex gap-4 p-4 rounded-lg border transition-colors ${
                    !notification.read 
                      ? "bg-accent-green/5 border-accent-green/20" 
                      : "bg-background border-border hover:bg-muted/50"
                  }`}
                >
                  {notification.user ? (
                    <Avatar className="h-10 w-10 flex-shrink-0">
                      <AvatarImage
                        src={notification.user.avatarUrl || "/placeholder-user.jpg"}
                        alt={notification.user.username}
                      />
                      <AvatarFallback>{notification.user.username.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                  ) : (
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-accent-green/10 flex-shrink-0">
                      <Bell className="h-5 w-5 text-accent-green" />
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm">
                          {notification.user && (
                            <Link 
                              href={`/user/${notification.user.username}`}
                              className="font-medium hover:underline"
                            >
                              {notification.user.username}
                            </Link>
                          )}{" "}
                          {notification.message}
                          {notification.promptTitle && (
                            <>
                              {" "}
                              <Link
                                href={generateNotificationLink(notification)}
                                className="font-medium hover:underline text-accent-green"
                                onClick={() => markAsRead(notification.id)}
                              >
                                "{notification.promptTitle}"
                              </Link>
                            </>
                          )}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-muted-foreground">
                            {formatRelativeTime(notification.createdAt)}
                          </span>
                          {!notification.read && (
                            <Badge variant="secondary" className="text-xs">
                              New
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 flex-shrink-0">
                        {!notification.read && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => markAsRead(notification.id)}
                            className="h-8 w-8 p-0"
                          >
                            <Check className="h-4 w-4" />
                            <span className="sr-only">Mark as read</span>
                          </Button>
                        )}
                        {generateNotificationLink(notification) !== "#" && (
                          <Button
                            variant="ghost"
                            size="sm"
                            asChild
                            onClick={() => markAsRead(notification.id)}
                          >
                            <Link href={generateNotificationLink(notification)}>
                              View
                            </Link>
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {hasMore && !isLoading && (
                <div className="flex justify-center pt-4">
                  <Button variant="outline" onClick={loadMore}>
                    Load more notifications
                  </Button>
                </div>
              )}

              {isLoading && notifications.length > 0 && (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading more...</span>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Bell className="h-12 w-12 text-muted-foreground/50 mb-4" />
              <h3 className="text-lg font-medium mb-2">
                {filter === "unread" ? "No unread notifications" : 
                 filter === "read" ? "No read notifications" : 
                 "No notifications yet"}
              </h3>
              <p className="text-muted-foreground max-w-md">
                {filter === "all" 
                  ? "When you receive notifications about comments, replies, and other interactions, they'll appear here."
                  : filter === "unread"
                  ? "All caught up! You have no unread notifications."
                  : "You haven't read any notifications yet."
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 