'use client' // Error components must be Client Components

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw, Search } from 'lucide-react'

export default function SearchError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Search error:', error)
  }, [error])

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-md mx-auto text-center space-y-6">
        <div className="flex justify-center">
          <AlertTriangle className="h-16 w-16 text-destructive" />
        </div>
        
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">Search Error</h1>
          <p className="text-muted-foreground">
            Something went wrong while searching. Please try again.
          </p>
        </div>

        <div className="space-y-3">
          <Button 
            onClick={reset}
            className="w-full"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try search again
          </Button>
          
          <Button 
            variant="outline" 
            onClick={() => window.location.href = '/search'}
            className="w-full"
          >
            <Search className="mr-2 h-4 w-4" />
            New search
          </Button>
        </div>
      </div>
    </div>
  )
} 