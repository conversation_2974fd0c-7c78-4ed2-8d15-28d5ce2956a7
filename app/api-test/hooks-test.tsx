"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card" // Added CardDescription
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge" // Added Badge
import { usePrompts } from "@/hooks/use-prompts"
import { useCategories } from "@/hooks/use-categories"
import { useTools } from "@/hooks/use-tools"
import { useTags } from "@/hooks/use-tags"
import { usePromptSearch } from "@/hooks/use-search" // Renamed hook
import type { PromptCard } from "@/lib/types" // Use PromptCard for search results

export default function HooksTestComponent() {
  return (
    <Card className="mt-8">
      <CardHeader>
        <CardTitle>Client-Side Hooks Test</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="prompts">
          <TabsList className="mb-4">
            <TabsTrigger value="prompts">Prompts Hook</TabsTrigger>
            <TabsTrigger value="categories">Categories Hook</TabsTrigger>
            <TabsTrigger value="tools">Tools Hook</TabsTrigger>
            <TabsTrigger value="tags">Tags Hook</TabsTrigger>
            <TabsTrigger value="search">Search Hook</TabsTrigger>
          </TabsList>

          <TabsContent value="prompts">
            <PromptsHookTest />
          </TabsContent>

          <TabsContent value="categories">
            <CategoriesHookTest />
          </TabsContent>

          <TabsContent value="tools">
            <ToolsHookTest />
          </TabsContent>

          <TabsContent value="tags">
            <TagsHookTest />
          </TabsContent>

          <TabsContent value="search">
            <SearchHookTest />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

function PromptsHookTest() {
  const [categoryId, setCategoryId] = useState<number | string | undefined>(undefined) // Updated type
  const [toolId, setToolId] = useState<number | string | undefined>(undefined) // Updated type
 
  const { prompts, isLoading, error, hasMore, loadMore } = usePrompts({
    categoryId: categoryId as number | undefined, // Cast to match hook parameter if needed, or update hook type
    toolId: toolId as number | undefined,         // Cast to match hook parameter if needed, or update hook type
    limit: 5,
  })

  const { categories } = useCategories()
  const { tools } = useTools()

  return (
    <div>
      <div className="mb-4">
        <h3 className="text-lg font-medium mb-2">Filter Options</h3>
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">Category</h4>
            <div className="flex flex-wrap gap-2 mb-4">
              <Button
                size="sm"
                variant={categoryId === undefined ? "default" : "outline"}
                onClick={() => setCategoryId(undefined)}
              >
                All
              </Button>
              {categories.map((category) => (
                <Button
                  key={category.id}
                  size="sm"
                  variant={categoryId === category.id ? "default" : "outline"}
                  onClick={() => setCategoryId(category.id)}
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Tool</h4>
            <div className="flex flex-wrap gap-2 mb-4">
              <Button
                size="sm"
                variant={toolId === undefined ? "default" : "outline"}
                onClick={() => setToolId(undefined)}
              >
                All
              </Button>
              {tools.map((tool) => (
                <Button
                  key={tool.id}
                  size="sm"
                  variant={toolId === tool.id ? "default" : "outline"}
                  onClick={() => setToolId(tool.id)}
                >
                  {tool.name}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {error && <div className="text-red-500 mb-4">Error: {error.message}</div>}

      <div className="space-y-4">
        {isLoading && prompts.length === 0 ? (
          <div>Loading prompts...</div>
        ) : prompts.length > 0 ? (
          <>
            {prompts.map((prompt) => (
              <div key={prompt.id} className="border p-4 rounded-md">
                <h3 className="font-semibold">{prompt.title}</h3>
                <div className="flex gap-2 mt-1">
                  {prompt.category?.name && ( // Added optional chaining
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">{prompt.category.name}</span>
                  )}
                  {prompt.tool?.name && ( // Added optional chaining
                    <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">{prompt.tool.name}</span>
                  )}
                </div>
                <p className="text-sm text-gray-500 mt-1">ID: {prompt.id}</p>
                <p className="mt-2 text-sm">{prompt.description}</p>
              </div>
            ))}

            {hasMore && (
              <Button onClick={loadMore} disabled={isLoading} className="w-full">
                {isLoading ? "Loading more..." : "Load More"}
              </Button>
            )}
          </>
        ) : (
          <div>No prompts found</div>
        )}
      </div>
    </div>
  )
}

function CategoriesHookTest() {
  const { categories, isLoading, error } = useCategories()

  return (
    <div>
      {error && <div className="text-red-500 mb-4">Error: {error.message}</div>}

      {isLoading ? (
        <div>Loading categories...</div>
      ) : categories.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {categories.map((category) => (
            <div key={category.id} className="border p-4 rounded-md">
              <h3 className="font-semibold">{category.name}</h3>
              <p className="text-sm text-gray-500">ID: {category.id}</p>
              <p className="text-sm text-gray-500">Prompts: {category.promptCount}</p>
              {category.description && <p className="mt-2 text-sm">{category.description}</p>}
            </div>
          ))}
        </div>
      ) : (
        <div>No categories found</div>
      )}
    </div>
  )
}

function ToolsHookTest() {
  const { tools, isLoading, error } = useTools()

  return (
    <div>
      {error && <div className="text-red-500 mb-4">Error: {error.message}</div>}

      {isLoading ? (
        <div>Loading tools...</div>
      ) : tools.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {tools.map((tool) => (
            <div key={tool.id} className="border p-4 rounded-md">
              <h3 className="font-semibold">{tool.name}</h3>
              <p className="text-sm text-gray-500">ID: {tool.id}</p>
              <p className="text-sm text-gray-500">Prompts: {tool.promptCount}</p>
              {tool.description && <p className="mt-2 text-sm">{tool.description}</p>}
            </div>
          ))}
        </div>
      ) : (
        <div>No tools found</div>
      )}
    </div>
  )
}

function TagsHookTest() {
  const { tags, isLoading, error } = useTags()

  return (
    <div>
      {error && <div className="text-red-500 mb-4">Error: {error.message}</div>}

      {isLoading ? (
        <div>Loading tags...</div>
      ) : tags.length > 0 ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          {tags.map((tag) => (
            <div key={tag.id} className="border p-4 rounded-md">
              <h3 className="font-semibold">{tag.name}</h3>
              <p className="text-sm text-gray-500">ID: {tag.id}</p>
              <p className="text-sm text-gray-500">Prompts: {tag.promptCount}</p>
            </div>
          ))}
        </div>
      ) : (
        <div>No tags found</div>
      )}
    </div>
  )
}

function SearchHookTest() {
  const [query, setQuery] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
 
  const { results, isLoading, error } = usePromptSearch(searchQuery, { // Removed hasMore, loadMore
    limit: 5,
  })
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setSearchQuery(query)
  }
 
  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle>usePromptSearch Hook Test</CardTitle>
        <CardDescription>Tests searching for prompts.</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSearch} className="flex gap-2 mb-4">
          <Input
            type="text"
            placeholder="Search prompts..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="flex-1"
          />
          <Button type="submit" disabled={isLoading}>Search</Button>
        </form>
 
        {isLoading && searchQuery ? (
          <div>Searching...</div>
        ) : searchQuery && results.length > 0 ? (
          <>
            {results.map((prompt: PromptCard) => ( // Ensure prompt is typed as PromptCard
              <div key={prompt.id} className="border p-4 rounded-md">
                <h3 className="font-semibold">{prompt.title}</h3>
                <div className="flex gap-2 mt-1">
                  {/* Add badges if available in PromptCard */}
                  {prompt.category?.name && <Badge variant="secondary">{prompt.category.name}</Badge>}
                  {prompt.tool?.name && <Badge variant="secondary">{prompt.tool.name}</Badge>}
                  {prompt.tags && prompt.tags.slice(0, 2).map(tag => <Badge key={tag.slug} variant="outline">{tag.name}</Badge>)}
                </div>
                <p className="mt-2 text-sm">{prompt.description}</p>
              </div>
            ))}
          </>
        ) : searchQuery ? (
          <div>No results found for "{searchQuery}"</div>
        ) : (
          <div>Enter a query to search for prompts.</div>
        )}
      </CardContent>
    </Card>
  )
}
