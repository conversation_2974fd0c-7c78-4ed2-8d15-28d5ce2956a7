import { unstable_noStore as noStore } from "next/cache"
import { getPrompts as getPromptCards, getCategories, getTools, getTags } from "@/lib/api-services" // Renamed getPrompts to getPromptCards
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import HooksTestComponent from "./hooks-test"
import { supabase } from "@/lib/api-client"

async function testDatabaseConnection() {
  noStore()
  try {
    // Test basic data fetching
    // Get a small sample for display
    const prompts = await getPromptCards({ limit: 5 }) // Use renamed function
 
    // Try a direct count query with no filters
    const { error: countError, count } = await supabase.from("prompts").select("*", { count: "exact", head: true })

    // Log for debugging
    console.log("Count query result:", { count, error: countError })

    // Alternative approach: get the count from the database schema
    // const { data: tableInfo, error: tableError } = await supabase.rpc("get_table_row_count", { table_name: "prompts" })

    // console.log("Table info:", { tableInfo, error: tableError })

    // Use the most reliable count we have
    const promptCount = count || 0

    const categories = await getCategories()
    const tools = await getTools()
    const tags = await getTags()

    return {
      success: true,
      data: {
        promptsCount: promptCount,
        categoriesCount: categories.length,
        toolsCount: tools.length,
        tagsCount: tags.length,
        // Include debug info
        countQueryResult: count,
        tableInfoResult: null, // tableInfo,
      },
    }
  } catch (error) {
    console.error("Database Connection Error:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    }
  }
}

export default async function Page() {
  const connectionTest = await testDatabaseConnection()

  return (
    <main className="flex flex-col p-4 max-w-6xl mx-auto">
      <div className="bg-white p-6 rounded-md shadow-md">
        <h1 className="text-2xl font-semibold mb-4">PromptHQ API Test</h1>
        <p className="text-gray-600">This page tests the connection to the Supabase database and our API services.</p>
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-md">
        <h2 className="text-xl font-semibold mb-2">Connection Status</h2>
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full mr-2 ${connectionTest.success ? "bg-green-500" : "bg-red-500"}`}></div>
          <span>{connectionTest.success ? "Connected to Database" : "Connection Error"}</span>
        </div>

        {connectionTest.success ? (
          <>
            <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white p-3 rounded shadow">
                <div className="text-sm text-gray-500">Prompts</div>
                <div className="text-2xl font-bold">{connectionTest.data?.promptsCount}</div> {/* Added optional chaining */}
              </div>
              <div className="bg-white p-3 rounded shadow">
                <div className="text-sm text-gray-500">Categories</div>
                <div className="text-2xl font-bold">{connectionTest.data?.categoriesCount}</div> {/* Added optional chaining */}
              </div>
              <div className="bg-white p-3 rounded shadow">
                <div className="text-sm text-gray-500">Tools</div>
                <div className="text-2xl font-bold">{connectionTest.data?.toolsCount}</div> {/* Added optional chaining */}
              </div>
              <div className="bg-white p-3 rounded shadow">
                <div className="text-sm text-gray-500">Tags</div>
                <div className="text-2xl font-bold">{connectionTest.data?.tagsCount}</div> {/* Added optional chaining */}
              </div>
            </div>
 
            {/* Debug info */}
            <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
              <details>
                <summary className="cursor-pointer font-medium">Debug Info</summary>
                <div className="mt-2">
                  <p>Count Query Result: {connectionTest.data?.countQueryResult}</p> {/* Added optional chaining */}
                  <p>Table Info Result: {connectionTest.data?.tableInfoResult}</p> {/* Added optional chaining */}
                </div>
              </details>
            </div>
          </>
        ) : (
          <div className="mt-2 p-3 bg-red-50 text-red-700 rounded">Error: {connectionTest.error}</div>
        )}
      </div>
 
      <div className="mt-8">
        <Tabs defaultValue="prompts">
          <TabsList className="mb-4">
            <TabsTrigger value="prompts">Prompts</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="tools">Tools</TabsTrigger>
            <TabsTrigger value="tags">Tags</TabsTrigger>
          </TabsList>
 
          <TabsContent value="prompts">
            <ServerDataTest type="prompts" />
          </TabsContent>
 
          <TabsContent value="categories">
            <ServerDataTest type="categories" />
          </TabsContent>
 
          <TabsContent value="tools">
            <ServerDataTest type="tools" />
          </TabsContent>
 
          <TabsContent value="tags">
            <ServerDataTest type="tags" />
          </TabsContent>
        </Tabs>
      </div>
 
      <HooksTestComponent />
    </main>
  )
}
 
async function ServerDataTest({ type }: { type: "prompts" | "categories" | "tools" | "tags" }) {
  noStore()
 
  let data: any[] = []
  let error = null
 
  try {
    switch (type) {
      case "prompts":
        data = await getPromptCards({ limit: 10 }) // Use renamed function
        break
      case "categories":
        data = await getCategories()
        break
      case "tools":
        data = await getTools()
        break
      case "tags":
        data = await getTags()
        break
    }
  } catch (err) {
    error = err instanceof Error ? err.message : "Unknown error"
  }
 
  return (
    <Card>
      <CardHeader>
        <CardTitle className="capitalize">{type} Data</CardTitle>
      </CardHeader>
      <CardContent>
        {error ? (
          <div className="text-red-500">Error: {error}</div>
        ) : data.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border p-2 text-left">ID</th>
                  <th className="border p-2 text-left">Name/Title</th>
                  {type === "prompts" && (
                    <>
                      <th className="border p-2 text-left">Category</th>
                      <th className="border p-2 text-left">Tool</th>
                    </>
                  )}
                </tr>
              </thead>
              <tbody>
                {data.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="border p-2">{item.id}</td>
                    <td className="border p-2">{item.title || item.name}</td>
                    {type === "prompts" && (
                      <>
                        <td className="border p-2">{item.category?.name || "N/A"}</td>
                        <td className="border p-2">{item.tool?.name || "N/A"}</td>
                      </>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-gray-500">No {type} found</div>
        )}
      </CardContent>
    </Card>
  )
}
