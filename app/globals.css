@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%; /* #020817 - slate-950 */
    --foreground: 210 40% 98%;    /* #f8fafc - slate-50 */
    --card: 222.2 84% 4.9%;        /* #020817 - slate-950 */
    --card-foreground: 210 40% 98%; /* #f8fafc - slate-50 */
    --popover: 222.2 84% 4.9%;    /* #020817 - slate-950 */
    --popover-foreground: 210 40% 98%; /* #f8fafc - slate-50 */
    --primary: 142.1 70.6% 45.3%; /* #4ADE80 - green-400 */
    --primary-foreground: 144.9 80.4% 10%; /* #14532d - green-900 */
    --secondary: 217.2 32.6% 17.5%; /* This seems to be a dark gray/blue, will keep for now */
    --secondary-foreground: 210 40% 98%; /* #f8fafc - slate-50 */
    --muted: 217.2 32.6% 17.5%; /* This seems to be a dark gray/blue, will keep for now */
    --muted-foreground: 215 20.2% 65.1%; /* #94a3b8 - slate-400 */
    --accent: 217.2 32.6% 17.5%; /* This seems to be a dark gray/blue, will keep for now */
    --accent-foreground: 210 40% 98%; /* #f8fafc - slate-50 */
    --destructive: 0 62.8% 30.6%; /* #7f1d1d - red-900 */
    --destructive-foreground: 210 40% 98%; /* #f8fafc - slate-50 */
    --border: 217.2 32.6% 17.5%; /* This seems to be a dark gray/blue, will keep for now */
    --input: 217.2 32.6% 17.5%; /* This seems to be a dark gray/blue, will keep for now */
    --ring: 142.4 71.8% 29.2%; /* This seems to be a green, will keep for now */

    --chart-1: 142.1 76.2% 46.3%; /* Green */
    --chart-2: 168 76.2% 46.3%; /* Teal */
    --chart-3: 190 76.2% 46.3%; /* Cyan */
    --chart-4: 212 76.2% 46.3%; /* Blue */
    --chart-5: 234 76.2% 46.3%; /* Indigo */
    --radius: 0.5rem; /* 8px - for cards, containers, dropdowns */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Variable highlighting for CodeMirror editor */
  .cm-variable-highlight {
    @apply bg-accent-green/20 text-accent-green px-1 py-0.5 rounded font-mono;
  }
}

@layer components {
  /* Custom background gradients for category thumbnails */
  .bg-gradient-code-generation {
    background-image: linear-gradient(to bottom right, #3b82f6, #1d4ed8); /* blue-500 to blue-700 */
  }
  .bg-gradient-creative-writing {
    background-image: linear-gradient(to bottom right, #22c55e, #15803d); /* green-500 to green-700 */
  }
  .bg-gradient-marketing {
    background-image: linear-gradient(to bottom right, #ec4899, #be185d); /* pink-500 to pink-700 */
  }
  .bg-gradient-image-generation {
    background-image: linear-gradient(to bottom right, #a855f7, #7e22ce); /* purple-500 to purple-700 */
  }
  .bg-gradient-data-analysis {
    background-image: linear-gradient(to bottom right, #14b8a6, #0f766e); /* teal-500 to teal-700 */
  }
  .bg-gradient-business {
    background-image: linear-gradient(to bottom right, #0ea5e9, #0369a1); /* sky-500 to sky-700 */
  }
  .bg-gradient-education {
    background-image: linear-gradient(to bottom right, #84cc16, #4d7c0f); /* lime-500 to lime-700 */
  }
  .bg-gradient-personal {
    background-image: linear-gradient(to bottom right, #8b5cf6, #6d28d9); /* violet-500 to violet-700 */
  }
  .bg-gradient-research {
    background-image: linear-gradient(to bottom right, #06b6d4, #0891b2); /* cyan-500 to cyan-700 */
  }
  .bg-gradient-social-media {
    background-image: linear-gradient(to bottom right, #f472b6, #db2777); /* pink-400 to pink-600 */
  }
  .bg-gradient-email {
    background-image: linear-gradient(to bottom right, #f97316, #c2410c); /* orange-500 to orange-700 */
  }
  .bg-gradient-seo {
    background-image: linear-gradient(to bottom right, #10b981, #047857); /* emerald-500 to emerald-700 */
  }
  .bg-gradient-copywriting {
    background-image: linear-gradient(to bottom right, #d946ef, #a21caf); /* fuchsia-500 to fuchsia-700 */
  }
  .bg-gradient-fiction {
    background-image: linear-gradient(to bottom right, #8b5cf6, #6d28d9); /* violet-500 to violet-700 */
  }
  .bg-gradient-non-fiction {
    background-image: linear-gradient(to bottom right, #6366f1, #4338ca); /* indigo-500 to indigo-700 */
  }
  .bg-gradient-audio {
    background-image: linear-gradient(to bottom right, #a855f7, #7e22ce); /* purple-500 to purple-700 */
  }
  .bg-gradient-video {
    background-image: linear-gradient(to bottom right, #ef4444, #b91c1c); /* red-500 to red-700 */
  }
   .bg-gradient-music {
    background-image: linear-gradient(to bottom right, #a855f7, #7e22ce); /* purple-500 to purple-700 */
  }
  .bg-gradient-other {
    background-image: linear-gradient(to bottom right, #64748b, #334155); /* slate-500 to slate-700 */
  }
}


/* Refined button styles */
.btn-accent-green {
  @apply bg-accent-green/90 text-black hover:bg-accent-green/100 transition-all duration-300 rounded-md;
  box-shadow: 0 0 3px rgba(74, 222, 128, 0.3);
}

.btn-accent-green:hover {
  box-shadow: 0 0 5px rgba(74, 222, 128, 0.5);
}

.btn-accent-blue {
  @apply bg-accent-blue/90 text-black hover:bg-accent-blue/100 transition-all duration-300 rounded-md;
  box-shadow: 0 0 3px rgba(56, 189, 248, 0.3);
}

.btn-accent-blue:hover {
  box-shadow: 0 0 5px rgba(56, 189, 248, 0.5);
}

.btn-accent-outline-green {
  @apply bg-transparent text-accent-green border border-accent-green hover:bg-accent-green/10 transition-all duration-300 rounded-md;
}

.btn-accent-outline-blue {
  @apply bg-transparent text-accent-blue border border-accent-blue hover:bg-accent-blue/10 transition-all duration-300 rounded-md;
}

.btn-accent-outline-pink {
  @apply bg-transparent text-accent-pink border border-accent-pink hover:bg-accent-pink/10 transition-all duration-300 rounded-md;
}

.text-accent-highlight {
  @apply text-accent-green;
}

main {
  max-width: 1200px;
  margin: 0 auto;
}
