"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import Link from "next/link"
import { Github, Mail } from "lucide-react"
import { createBrowserClient } from "@supabase/ssr"
import { useRouter } from "next/navigation"

export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    const form = e.currentTarget;
    const email = form.email.value;
    const password = form.password.value;

    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    setIsLoading(false);

    if (error) {
      console.error("Signin error:", error.message);
      setError(error.message);
      setIsLoading(false);
      return;
    } else {
      // Get the current user after sign-in
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        console.error("No user found after successful sign-in");
        router.push("/");
        return;
      }
      
      const user = session.user;
      
      // Fetch profile to check is_username_customized
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("is_username_customized")
        .eq("id", user.id)
        .single();

      if (profileError || !profileData) {
        console.error("Error fetching profile after signin:", profileError?.message);
        setError(profileError?.message || "Failed to load your profile");
        setIsLoading(false);
        return;
      } else if (profileData.is_username_customized === false) {
        // Redirect to set username page if not customized
        router.push("/auth/set-username");
      } else {
        // Redirect to homepage or dashboard if username is customized
        router.push("/"); // Or '/dashboard'
      }
    }
  };

  return (
    <div className="container mx-auto flex min-h-[calc(100vh-4rem)] flex-col items-center justify-center py-8">
      <Card className="mx-auto w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Sign in</CardTitle>
          <CardDescription>Enter your email and password to sign in to your account</CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4">
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="outline"
              disabled={isLoading}
              onClick={async () => {
                setIsLoading(true);
                const supabase = createBrowserClient(
                  process.env.NEXT_PUBLIC_SUPABASE_URL!,
                  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
                );
                const { error } = await supabase.auth.signInWithOAuth({
                  provider: 'github',
                  options: {
                    redirectTo: `${window.location.origin}/auth/callback`,
                  },
                });
                if (error) {
                  console.error("GitHub sign-in error:", error.message);
                  setError(`GitHub sign-in failed: ${error.message}`);
                  setIsLoading(false);
                }
              }}
            >
              <Github className="mr-2 h-4 w-4" />
              Github
            </Button>
            <Button
              variant="outline"
              disabled={isLoading}
              onClick={async () => {
                setIsLoading(true);
                const supabase = createBrowserClient(
                  process.env.NEXT_PUBLIC_SUPABASE_URL!,
                  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
                );
                const { error } = await supabase.auth.signInWithOAuth({
                  provider: 'google',
                  options: {
                    redirectTo: `${window.location.origin}/auth/callback`,
                  },
                });
                if (error) {
                  console.error("Google sign-in error:", error.message);
                  setError(`Google sign-in failed: ${error.message}`);
                  setIsLoading(false);
                }
              }}
            >
              <Mail className="mr-2 h-4 w-4" />
              Google
            </Button>
          </div>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">Or continue with</span>
            </div>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" required disabled={isLoading} />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input id="password" type="password" required disabled={isLoading} />
              </div>
              {error && <p className="text-sm text-red-500 mb-2">{error}</p>}
              <Button className="w-full bg-emerald-600 hover:bg-emerald-700" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign in"}
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col">
          <div className="text-sm text-muted-foreground">
            Don&apos;t have an account?{" "}
            <Link href="/sign-up" className="text-emerald-500 hover:text-emerald-600">
              Sign up
            </Link>
          </div>
          <Link href="/forgot-password" className="mt-2 text-sm text-emerald-500 hover:text-emerald-600">
            Forgot your password?
          </Link>
        </CardFooter>
      </Card>
    </div>
  )
}
