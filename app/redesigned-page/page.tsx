import { Suspense } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search } from "lucide-react"

// Import data fetching functions
import { getToolsWithPromptCount, getCategoriesWithPromptCount, getTools, getCategories } from "@/lib/api-services"

// Import Client Components from their new locations
import CategoriesSection from "@/components/home/<USER>"
import FeaturedPromptsSkeleton from "@/components/home/<USER>"
import FeaturedPrompts from "@/components/featured-prompts"
import PopularToolsSection from "@/components/home/<USER>" // Import the new component

// Import types from shared types file
import type { Category, Tool } from "@/lib/types"

// Helper function to safely parse category ID
function safeParseCategoryId(id: string | number): number {
  if (typeof id === 'number') {
    return id
  }
  
  const parsed = Number.parseInt(id, 10)
  if (Number.isNaN(parsed)) {
    console.warn(`Invalid category ID encountered: "${id}". Using fallback ID of 0.`)
    return 0 // Fallback to 0 for invalid IDs
  }
  
  return parsed
}

export default async function RedesignedHome() {
  // Fetch tools with prompt counts from the database
  let dbTools: Tool[] = []
  try {
    // Try to fetch tools with prompt counts
    dbTools = await getToolsWithPromptCount()
  } catch (error) {
    console.error("Error fetching tools with prompt counts:", error)
    try {
      // Fallback to regular tools fetch if the count fetch fails
      console.log("Falling back to regular tools fetch")
      dbTools = await getTools()
    } catch (fallbackError) {
      console.error("Error in fallback tools fetch:", fallbackError)
      // Will fall back to empty array if both fetches fail
    }
  }

  // Sort tools by prompt count (descending)
  const sortedTools = [...dbTools].sort((a, b) => (b.promptCount || 0) - (a.promptCount || 0))

  // Get top 6 tools for featured display
  const popularTools = sortedTools.slice(0, 6).map((tool) => ({
    id: tool.id,
    name: tool.name,
    slug: tool.slug,
    icon: tool.icon || "🔧", // Default icon if none is provided
    promptCount: tool.promptCount || 0,
  }))

  // Get remaining tools for additional display
  const additionalTools = sortedTools.slice(6).map((tool) => ({
    id: tool.id,
    name: tool.name,
    slug: tool.slug,
    icon: tool.icon || "🔧", // Default icon if none is provided
    promptCount: tool.promptCount || 0,
  }))

  // Fetch categories with prompt counts from the database
  let dbCategories: Category[] = []
  try {
    // Try to fetch categories with prompt counts
    dbCategories = await getCategoriesWithPromptCount()
  } catch (error) {
    console.error("Error fetching categories with prompt counts:", error)
    try {
      // Fallback to regular categories fetch if the count fetch fails
      console.log("Falling back to regular categories fetch")
      dbCategories = await getCategories()
    } catch (fallbackError) {
      console.error("Error in fallback categories fetch:", fallbackError)
      // Will fall back to empty array if both fetches fail
    }
  }

  // Sort categories by prompt count (descending)
  const sortedCategories = [...dbCategories].sort((a, b) => (b.promptCount || 0) - (a.promptCount || 0))

  // Get top 5 categories for featured display
  const featuredCategories = sortedCategories.slice(0, 5).map((category) => ({
    id: safeParseCategoryId(category.id),
    name: category.name,
    slug: category.slug,
    promptCount: category.promptCount || 0,
    imagePath: category.imagePath || `/images/categories/${category.slug}.png`,
  }))

  // Get remaining categories for additional display
  const additionalCategories = sortedCategories.slice(5).map((category) => ({
    id: safeParseCategoryId(category.id),
    name: category.name,
    slug: category.slug,
    promptCount: category.promptCount || 0,
    imagePath: category.imagePath || `/images/categories/${category.slug}.png`,
  }))

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <section className="mb-12 text-center">
        <h1 className="mb-4 text-3xl font-bold tracking-tight text-foreground md:text-4xl lg:text-5xl">
          Discover and Share <span className="text-accent-highlight">Awesome</span> Prompts
        </h1>
        <p className="mb-8 text-lg text-muted-foreground">
          Find prompts for any task, share your creations, and connect with a community of prompt engineers.
        </p>
        {/* Quick Search */}
        <div className="mx-auto max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search prompts, categories, tools..."
              className="w-full pl-10 pr-4 text-lg rounded-lg"
              style={{ borderRadius: "8px" }}
            />
          </div>
        </div>
        {/* Action Buttons */}
        <div className="mt-6 flex justify-center gap-4">
          <Button className="btn-accent-green rounded-md" style={{ borderRadius: "6px" }} asChild>
            <Link href="/prompt/submit">+ New Prompt</Link>
          </Button>
          <Button variant="outline" className="rounded-md" style={{ borderRadius: "6px" }} asChild>
            <Link href="/explore">Explore All Prompts</Link>
          </Button>
        </div>
      </section>

      {/* Getting Started Section (Placeholder) */}
      <section className="mb-12">
        <h2 className="mb-4 text-2xl font-bold">Getting Started</h2>
        <Card>
          <CardContent className="p-6">
            <p className="text-muted-foreground">
              This section will guide new users on how to find, use, and share prompts.
            </p>
            {/* Add steps, links, or a short video here */}
          </CardContent>
        </Card>
      </section>

      {/* Tabbed Content */}
      <Tabs defaultValue="prompts" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="prompts">Browse Prompts</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="tools">Tools</TabsTrigger>
        </TabsList>
        <TabsContent value="prompts">
          {/* Featured Prompts Section with Dropdown */}
          <Suspense fallback={<FeaturedPromptsSkeleton />}>
            <FeaturedPrompts />
          </Suspense>
        </TabsContent>
        <TabsContent value="categories">
          {/* Popular Categories Section with toggle */}
          <CategoriesSection featuredCategories={featuredCategories} additionalCategories={additionalCategories} />
        </TabsContent>
        <TabsContent value="tools">
          {/* Popular Tools Section (Client Component) */}
          <PopularToolsSection popularTools={popularTools} additionalTools={additionalTools} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
