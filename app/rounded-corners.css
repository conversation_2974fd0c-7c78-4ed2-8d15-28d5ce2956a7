/* Explicit border radius overrides */

/* Buttons */
.btn-accent-green,
.btn-accent-blue,
.btn-accent-outline-green,
.btn-accent-outline-blue,
.btn-accent-outline-pink {
  border-radius: 6px !important;
}

/* Search bar in navigation */
form.relative.w-full.rounded-lg input[type="search"] {
  border-radius: 8px !important;
}

/* Upvote/downvote buttons */
button[class*="flex-1"] {
  border-radius: 6px !important;
}

/* Remix prompt and Add to collection buttons */
button.w-full {
  border-radius: 6px !important;
}

/* Dropdowns for sorting */
[class*="DropdownMenuTrigger"],
button[class*="SelectTrigger"] {
  border-radius: 6px !important;
}

[class*="DropdownMenuContent"],
[class*="SelectContent"] {
  border-radius: 8px !important;
}

[class*="DropdownMenuItem"],
[class*="SelectItem"] {
  border-radius: 4px !important;
}

/* Comment sorting buttons */
button[class*="mostLiked"],
button[class*="newest"] {
  border-radius: 6px !important;
}
