"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { createBrowserClient } from "@supabase/ssr"
import { useToast } from "@/hooks/use-toast"

export default function SetUsernamePage() {
  const [username, setUsername] = useState("")
  const [currentUsername, setCurrentUsername] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { toast } = useToast()
  
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  // Fetch current user and their random username
  useEffect(() => {
    const fetchUserProfile = async () => {
      setIsLoading(true)
      
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        // Not logged in, redirect to sign in
        router.push("/sign-in")
        return
      }
      
      const user = session.user
      
      // Fetch the user's profile to get their current username
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("username, is_username_customized")
        .eq("id", user.id)
        .single()
      
      if (profileError) {
        console.error("Error fetching profile:", profileError.message)
        setError("Failed to load your profile. Please try again.")
        setIsLoading(false)
        return
      }
      
      if (profile.is_username_customized) {
        // Username already customized, redirect to homepage
        router.push("/")
        return
      }
      
      setCurrentUsername(profile.username)
      setUsername(profile.username) // Pre-fill with current random username
      setIsLoading(false)
    }
    
    fetchUserProfile()
  }, [supabase, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!username.trim() || username.trim().length < 3) {
      setError("Username must be at least 3 characters long")
      return
    }
    
    setIsSubmitting(true)
    setError(null)
    
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        setError("You must be logged in to set your username")
        setIsSubmitting(false)
        return
      }
      
      const user = session.user

      // Check if username is available (case-insensitive)
      const { data: existingUser, error: checkError } = await supabase
        .from("profiles")
        .select("id")
        .ilike("username", username.trim())
        .neq("id", user.id)
        .single()
      
      if (checkError && checkError.code !== "PGRST116") {
        console.error("Error checking username availability:", checkError.message)
        setError("Failed to check username availability. Please try again.")
        setIsSubmitting(false)
        return
      }
      
      if (existingUser) {
        setError("This username is already taken. Please choose another.")
        setIsSubmitting(false)
        return
      }
      
      // Update the user's profile with the new username
      const { error: updateError } = await supabase
        .from("profiles")
        .update({ 
          username: username.trim(),
          is_username_customized: true
        })
        .eq("id", user.id)
      
      if (updateError) {
        console.error("Error updating username:", updateError.message)
        setError("Failed to update username. Please try again.")
        setIsSubmitting(false)
        return
      }
      
      toast({
        title: "Username updated!",
        description: `Your username has been set to @${username.trim()}`,
      })
      
      // Redirect to homepage
      router.push("/")
      
    } catch (error) {
      console.error("Unexpected error:", error)
      setError("An unexpected error occurred. Please try again.")
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container mx-auto flex min-h-[calc(100vh-4rem)] flex-col items-center justify-center py-8">
      <Card className="mx-auto w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Choose your username</CardTitle>
          <CardDescription>
            This will be your public identity on PromptHQ. You can change it later in settings.
          </CardDescription>
        </CardHeader>
        
        {isLoading ? (
          <CardContent className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-accent-green" />
          </CardContent>
        ) : (
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  placeholder="Choose a username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  disabled={isSubmitting}
                  className="w-full"
                />
                {error && <p className="text-sm text-red-500">{error}</p>}
                <p className="text-xs text-muted-foreground">
                  Usernames can contain letters, numbers, underscores, and hyphens.
                </p>
              </div>
            </CardContent>
            
            <CardFooter>
              <Button 
                type="submit" 
                className="w-full bg-accent-green hover:bg-accent-green/90" 
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting username...
                  </>
                ) : (
                  "Continue"
                )}
              </Button>
            </CardFooter>
          </form>
        )}
      </Card>
    </div>
  )
}