import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");

  if (code) {
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );
    await supabase.auth.exchangeCodeForSession(code);

    // After successful authentication, check if the user has customized their username
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (session?.user) {
      const user = session.user;
      // Fetch the user's profile to check if username is customized
      const { data: profile, error } = await supabase
        .from("profiles")
        .select("is_username_customized")
        .eq("id", user.id)
        .single();

      if (error) {
        console.error("Error fetching profile in callback:", error.message);
        // Redirect to homepage if there's an error
        return NextResponse.redirect(new URL("/", requestUrl.origin));
      }

      // If username is not customized, redirect to set-username page
      if (profile && profile.is_username_customized === false) {
        return NextResponse.redirect(
          new URL("/auth/set-username", requestUrl.origin)
        );
      }
    }

    // Redirect to the homepage by default
    return NextResponse.redirect(new URL("/", requestUrl.origin));
  }

  // If no code is present, redirect to the sign-in page
  return NextResponse.redirect(new URL("/sign-in", requestUrl.origin));
}