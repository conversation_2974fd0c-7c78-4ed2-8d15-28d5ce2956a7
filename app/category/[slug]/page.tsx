import React from 'react'
import FilteredPromptsPageClient from '@/components/filtered-prompts-page-client'
import { getCategoryBySlug } from "@/lib/api-services" // Import the fetch function
import { notFound } from "next/navigation"; // Import notFound

interface CategoryPageProps {
  params: Promise<{
    slug: string
  }>
  // searchParams: { [key: string]: string | string[] | undefined };
}

// Add ISR revalidation - 5 minutes
export const revalidate = 300;

// Accept the full props object
export default async function CategoryPage(props: CategoryPageProps) {
  // Access slug from props.params
  const slug = (await props.params).slug;

  // Fetch category data on the server
  const category = await getCategoryBySlug(slug);

  // Handle case where the category is not found
  if (!category) {
    notFound();
  }

  // Render the client component, passing the fetched data
  return (
    <FilteredPromptsPageClient
      initialSlug={slug}
      entityType="category"
      initialEntityData={category} // Pass the fetched category data
      entityName="Category"
    />
  );
}
