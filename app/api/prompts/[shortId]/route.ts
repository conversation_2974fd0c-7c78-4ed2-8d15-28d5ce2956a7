// app/api/prompts/[shortId]/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getPromptByShortId } from 'lib/api-services'
import { addCacheHeaders } from 'lib/cache-headers'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shortId: string }> }
) {
  try {
    const { shortId } = await params
    const prompt = await getPromptByShortId(shortId)
    
    if (!prompt) {
      return new NextResponse('Prompt not found', { status: 404 })
    }

    const response = NextResponse.json(prompt)
    
    // Add cache headers - cache for 5 minutes since stats update periodically
    return addCacheHeaders(response, 'DYNAMIC')
  } catch (error) {
    console.error('Error fetching prompt:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}