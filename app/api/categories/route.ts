// app/api/categories/route.ts
import { NextResponse } from 'next/server'
import { getCategories } from 'lib/api-services'
import { addCacheHeaders } from 'lib/cache-headers'

export async function GET() {
  try {
    const categories = await getCategories()
    const response = NextResponse.json(categories)
    
    // Categories are static - cache for 1 hour
    return addCacheHeaders(response, 'STATIC')
  } catch (error) {
    console.error('Error fetching categories:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}