import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY! // Use service role key
)

export async function GET(request: NextRequest) {
  // Verify cron secret to prevent abuse
  const authHeader = request.headers.get('authorization')
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    return new Response('Unauthorized', { status: 401 })
  }

  try {
    // Call the refresh function (now updated to use non-concurrent refresh)
    const { error } = await supabase.rpc('refresh_prompt_caches')
    
    if (error) {
      console.error('Cache refresh error:', error)
      return new Response('Error refreshing cache', { status: 500 })
    }

    return new Response('Cache refreshed successfully', { status: 200 })
  } catch (error) {
    console.error('Cache refresh failed:', error)
    return new Response('Cache refresh failed', { status: 500 })
  }
}