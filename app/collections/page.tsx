"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import CollectionCard from "@/components/collection-card"
import CreateCollectionDialog from "@/components/create-collection-dialog"
import { Plus, FolderPlus, Loader2, BookmarkPlus, Info, HelpCircle, Sparkles, Users } from "lucide-react"
import { createBrowserClient } from "@supabase/ssr"
import type { Collection } from "@/lib/types"
import { useRouter } from "next/navigation"
import { toast } from "@/components/ui/use-toast"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export default function CollectionsPage() {
  const [activeTab, setActiveTab] = useState("my-collections")
  const [isLoading, setIsLoading] = useState(false)
  const [myCollections, setMyCollections] = useState<Collection[]>([])
  const [followedCollections, setFollowedCollections] = useState<Collection[]>([])
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const router = useRouter()

  // Format count helper function (same as in saved page)
  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1).replace(/\.0$/, "")}M`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
    } else {
      return count.toString()
    }
  }

  // Load collections on mount
  useEffect(() => {
    loadCollections()
  }, [])

  // Load collections with optimized queries
  const loadCollections = async () => {
    setIsLoading(true);
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    const { data: { session } } = await supabase.auth.getSession();
    const currentUserId = session?.user?.id;

    if (!currentUserId) {
      setIsLoading(false);
      setMyCollections([]);
      setFollowedCollections([]);
      return;
    }

    try {
      // Fetch My Collections
      const { data: myData, error: myError } = await supabase
        .from("collections")
        .select(`
          *,
          collection_prompts(count),
          user:user_id (username, id, avatar_url)
        `)
        .eq("user_id", currentUserId)
        .order("is_default", { ascending: false }) // Default collection first
        .order("default_type", { ascending: true }) // Sort by default_type ('my_prompts' first, 'saved_prompts' second)
        .order("created_at", { ascending: false }); // Then by date

      if (myError) {
        console.error("Error fetching my collections:", myError.message);
        setMyCollections([]);
      } else {
        // Use the transformCollection function from lib/transformers.ts to ensure consistency
        const transformedMyCollections = myData.map((c: any) => {
          const collectionPrompts = c.collection_prompts as unknown as [{count: number}] | [];
          // Create a collection object with the correct structure
          return {
            id: c.id,
            userId: c.user_id,
            name: c.name,
            description: c.description || '',
            icon: c.icon,
            isPublic: c.is_public,
            isDefault: c.is_default || false,
            defaultType: c.default_type,
            promptCount: collectionPrompts[0]?.count || 0,
            user: c.user,
            createdAt: c.created_at,
            updatedAt: c.updated_at
          };
        });
        setMyCollections(transformedMyCollections);
      }

      // Fetch Followed Collections with optimized query
      const { data: followedData, error: followedError } = await supabase
        .from("followed_collections")
        .select(`
          collection_id, 
          followed_at, 
          collection:collections(
            *,
            collection_prompts(count),
            user:user_id (username, id, avatar_url)
          )
        `)
        .eq("follower_id", currentUserId)
        .order("followed_at", { ascending: false });

      if (followedError) {
        // Handle table not found gracefully
        if (followedError.code === "42P01" || followedError.message?.includes("relation") || followedError.message?.includes("does not exist")) {
          console.warn("Followed collections feature not available yet");
          setFollowedCollections([]);
        } else {
          console.error("Error fetching followed collections:", followedError.message);
          setFollowedCollections([]);
        }
      } else {
        // Use the same transformation pattern for followed collections
        const transformedFollowedCollections = followedData
          .filter((item: any) => item.collection) // Filter out any null collections
          .map((item: any) => {
            const c = item.collection;
            const collectionPrompts = c.collection_prompts as unknown as [{count: number}] | [];
            return {
              id: c.id,
              userId: c.user_id,
              name: c.name,
              description: c.description || '',
              icon: c.icon,
              isPublic: c.is_public,
              isDefault: c.is_default || false,
              defaultType: c.default_type,
              promptCount: collectionPrompts[0]?.count || 0,
              user: c.user,
              createdAt: c.created_at,
              updatedAt: c.updated_at,
              followedAt: item.followed_at
            };
          });
        setFollowedCollections(transformedFollowedCollections);
      }
    } catch (error) {
      console.error("Error loading collections:", error);
      setMyCollections([]);
      setFollowedCollections([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle create collection
  const handleCreateCollection = async (name: string, description: string | null, imageFile: File | null, isPublic: boolean) => {
    try {
      setIsLoading(true);
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.user) {
        console.error("User not authenticated");
        setIsLoading(false); // Reset loading state
        return;
      }

      const user = session.user;

      // Handle image upload if provided
      let iconUrl = null;
      if (imageFile) {
        const fileName = `${user.id}/${Date.now()}-${imageFile.name.replace(/\s+/g, '_')}`;
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('collection-images')
          .upload(fileName, imageFile, { cacheControl: '3600', upsert: false });

        if (uploadError) {
          console.error("Error uploading image:", uploadError);
        } else if (uploadData) {
          iconUrl = supabase.storage.from('collection-images').getPublicUrl(fileName).data.publicUrl;
        }
      }

      // Create collection in Supabase
      const { data, error } = await supabase
        .from("collections")
        .insert({
          user_id: user.id,
          name,
          description,
          icon: iconUrl,
          is_public: isPublic,
        })
        .select();

      if (error) {
        console.error("Error creating collection:", error.message);
        setIsLoading(false); // Reset loading state
        return null;
      } else {
        console.log("Collection created:", data);

        if (data && data.length > 0) {
          // Add the new collection to the state
          const newCollection = {
            id: data[0].id,
            userId: user.id,
            name: data[0].name,
            description: data[0].description || '',
            icon: data[0].icon,
            isPublic: data[0].is_public,
            isDefault: data[0].is_default || false,
            defaultType: data[0].default_type,
            promptCount: 0,
            user: {
              username: user.user_metadata?.username || 'user',
              id: user.id,
              avatar_url: user.user_metadata?.avatar_url || null,
            },
            createdAt: data[0].created_at,
            updatedAt: data[0].updated_at,
          };

          setMyCollections([newCollection, ...myCollections]);
          setIsCreateDialogOpen(false);
          setIsLoading(false); // Reset loading state
          return data[0].id;
        }
        setIsLoading(false); // Reset loading state for edge case where data exists but is empty
      }
    } catch (error) {
      console.error("Error creating collection:", error);
      setIsLoading(false); // Reset loading state in case of error
      return null;
    }
  };
  
  // Handle edit collection
  const handleEditCollection = (collection: Collection) => {
    // This will be implemented in Phase 2
    console.log("Edit collection:", collection);
  };

  const handleDeleteCollection = async (collectionId: string) => {
    try {
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.user) {
        console.error('User not authenticated');
        return;
      }

      // Call the delete API
      const response = await fetch(`/api/collections/${collectionId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete collection');
      }

      // Remove the collection from the local state
      setMyCollections(prev => prev.filter(collection => collection.id !== collectionId));
      
      toast({
        title: "Collection deleted",
        description: "The collection has been successfully deleted.",
      });
    } catch (error) {
      console.error('Error deleting collection:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete collection. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <TooltipProvider>
      <div className="mx-auto px-4 py-8 max-w-[1400px] w-full" data-component-name="CollectionsPage">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">Collections</h1>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-5 w-5 text-muted-foreground hover:text-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="text-sm">
                  Organize your prompts into collections. Default collections (My Prompts, Saved Prompts) are automatically created,
                  or create custom collections for specific themes or projects.
                </p>
              </TooltipContent>
            </Tooltip>
          </div>

          <Button
            className="bg-accent-green hover:bg-accent-green/90"
            onClick={() => setIsCreateDialogOpen(true)}
          >
            <Plus className="mr-2 h-4 w-4" /> Create Collection
          </Button>
        </div>

        {/* Collection Types Info Section */}
        <Card className="mb-6 border-accent-green/20 bg-accent-green/5">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-accent-green mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                <h3 className="font-medium text-sm">Collection Types</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                  <div className="flex items-start gap-2">
                    <Sparkles className="h-4 w-4 text-accent-blue mt-0.5 flex-shrink-0" />
                    <div>
                      <span className="font-medium text-foreground">My Prompts:</span> Prompts you've created and submitted to the community
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <BookmarkPlus className="h-4 w-4 text-accent-green mt-0.5 flex-shrink-0" />
                    <div>
                      <span className="font-medium text-foreground">Saved Prompts:</span> Prompts you've bookmarked from other creators
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <FolderPlus className="h-4 w-4 text-accent-purple mt-0.5 flex-shrink-0" />
                    <div>
                      <span className="font-medium text-foreground">Custom Collections:</span> Organize prompts by theme, project, or purpose
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

      {/* Enhanced Tab Navigation with explore page styling */}
      <div className="border-b border-border/50">
        <div className="flex bg-muted/30 rounded-lg p-1 mb-4 max-w-2xl mx-auto">
          <Button
            variant="ghost"
            className={`flex-1 min-w-0 rounded-md py-2 sm:py-3 px-2 sm:px-4 font-medium transition-all duration-300 ease-in-out ${
              activeTab === "my-collections"
                ? "bg-primary text-primary-foreground shadow-md border border-primary/20 transform scale-[1.02]"
                : "text-muted-foreground hover:text-foreground hover:bg-background/80 hover:shadow-sm"
            }`}
            onClick={() => setActiveTab("my-collections")}
          >
            <span className="flex items-center justify-center gap-1 sm:gap-2 min-w-0">
              <span className={`transition-all duration-300 truncate text-xs sm:text-sm ${
                activeTab === "my-collections" ? "font-semibold" : "font-medium"
              }`}>
                My Collections
              </span>
              <Badge
                variant="secondary"
                className={`text-xs transition-all duration-300 flex-shrink-0 ${
                  activeTab === "my-collections"
                    ? "bg-primary-foreground/20 text-primary-foreground border border-primary-foreground/30"
                    : "bg-muted text-muted-foreground hover:bg-background/80"
                }`}
              >
                {formatCount(myCollections.length)}
              </Badge>
            </span>
          </Button>
          <Button
            variant="ghost"
            className={`flex-1 min-w-0 rounded-md py-2 sm:py-3 px-2 sm:px-4 font-medium transition-all duration-300 ease-in-out ${
              activeTab === "followed-collections"
                ? "bg-primary text-primary-foreground shadow-md border border-primary/20 transform scale-[1.02]"
                : "text-muted-foreground hover:text-foreground hover:bg-background/80 hover:shadow-sm"
            }`}
            onClick={() => setActiveTab("followed-collections")}
          >
            <span className="flex items-center justify-center gap-1 sm:gap-2 min-w-0">
              <span className={`transition-all duration-300 truncate text-xs sm:text-sm ${
                activeTab === "followed-collections" ? "font-semibold" : "font-medium"
              }`}>
                Followed Collections
              </span>
              <Badge
                variant="secondary"
                className={`text-xs transition-all duration-300 flex-shrink-0 ${
                  activeTab === "followed-collections"
                    ? "bg-primary-foreground/20 text-primary-foreground border border-primary-foreground/30"
                    : "bg-muted text-muted-foreground hover:bg-background/80"
                }`}
              >
                {formatCount(followedCollections.length)}
              </Badge>
            </span>
          </Button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === "my-collections" && (
        <div>
          {isLoading ? (
            <div className="flex h-40 items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-accent-green" />
            </div>
          ) : myCollections.length > 0 ? (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {/* Sort collections to ensure default collections are always pinned at the top */}
              {myCollections
                .sort((a, b) => {
                  // First sort by isDefault (true comes first)
                  if (a.isDefault && !b.isDefault) return -1;
                  if (!a.isDefault && b.isDefault) return 1;
                  
                  // Then sort default collections by defaultType ('my_prompts' first, 'saved_prompts' second)
                  if (a.isDefault && b.isDefault) {
                    if (a.defaultType === 'my_prompts' && b.defaultType === 'saved_prompts') return -1;
                    if (a.defaultType === 'saved_prompts' && b.defaultType === 'my_prompts') return 1;
                  }
                  
                  // Finally sort by creation date (newest first)
                  return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                })
                .map((collection) => (
                  <CollectionCard 
                    key={collection.id} 
                    collection={collection} 
                    isOwner={true}
                    onEdit={handleEditCollection}
                  />
                ))}
            </div>
          ) : (
            <Card className="border-dashed border-2 border-muted-foreground/20">
              <CardContent className="flex flex-col items-center justify-center py-16">
                <div className="relative mb-6">
                  <FolderPlus className="h-16 w-16 text-muted-foreground/50" />
                  <Sparkles className="absolute -top-2 -right-2 h-6 w-6 text-accent-green" />
                </div>
                <h3 className="mb-3 text-2xl font-semibold">Start Organizing Your Prompts</h3>
                <p className="mb-6 text-center text-muted-foreground max-w-md">
                  Collections help you organize prompts by theme, project, or purpose.
                  Your default collections (My Prompts, Saved Prompts) will appear here automatically when you start using PromptHQ.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 mb-6">
                  <Button
                    onClick={() => setIsCreateDialogOpen(true)}
                    className="bg-accent-green hover:bg-accent-green/90"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create Your First Collection
                  </Button>
                  <Button variant="outline" asChild>
                    <a href="/explore">
                      <Sparkles className="mr-2 h-4 w-4" />
                      Explore Prompts
                    </a>
                  </Button>
                </div>
                <div className="text-sm text-muted-foreground text-center space-y-1">
                  <p>💡 <strong>Tip:</strong> Save prompts from other creators to build your Saved Prompts collection</p>
                  <p>✨ <strong>Pro tip:</strong> Submit your own prompts to start building your My Prompts collection</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {activeTab === "followed-collections" && (
        <div>
          {isLoading ? (
            <div className="flex h-40 items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-accent-green" />
            </div>
          ) : followedCollections.length > 0 ? (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {followedCollections.map((collection) => (
                <CollectionCard 
                  key={collection.id} 
                  collection={collection} 
                  isOwner={false}
                />
              ))}
            </div>
          ) : (
            <Card className="border-dashed border-2 border-muted-foreground/20">
              <CardContent className="flex flex-col items-center justify-center py-16">
                <div className="relative mb-6">
                  <Users className="h-16 w-16 text-muted-foreground/50" />
                  <BookmarkPlus className="absolute -top-2 -right-2 h-6 w-6 text-accent-blue" />
                </div>
                <h3 className="mb-3 text-2xl font-semibold">Discover Amazing Collections</h3>
                <p className="mb-6 text-center text-muted-foreground max-w-md">
                  Follow collections from other creators to stay updated with their latest prompts.
                  Discover curated collections organized by theme, use case, or expertise.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 mb-6">
                  <Button variant="default" asChild className="bg-accent-blue hover:bg-accent-blue/90">
                    <a href="/explore">
                      <Users className="mr-2 h-4 w-4" />
                      Explore Collections
                    </a>
                  </Button>
                  <Button variant="outline" asChild>
                    <a href="/search?type=collections">
                      <BookmarkPlus className="mr-2 h-4 w-4" />
                      Search Collections
                    </a>
                  </Button>
                </div>
                <div className="text-sm text-muted-foreground text-center space-y-1">
                  <p>🔍 <strong>Tip:</strong> Use the search to find collections by topic or creator</p>
                  <p>👥 <strong>Community:</strong> Following collections helps you discover new prompt techniques</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Create Collection Dialog */}
      {isCreateDialogOpen && (
        <CreateCollectionDialog
          isOpen={isCreateDialogOpen}
          onClose={() => setIsCreateDialogOpen(false)}
          onCreate={handleCreateCollection}
        />
      )}
      </div>
    </TooltipProvider>
  )
}
