import { createServer<PERSON>lient } from "@supabase/ssr"
import { cookies } from "next/headers";
import { notFound, redirect } from "next/navigation";
import CollectionClientWrapper from "./collection-client-wrapper";
import type { Collection, PromptCard as PromptCardType } from "@/lib/types";

interface CollectionDetailsPageProps {
  params: Promise<{
    id: string; // Collection ID (UUID)
  }>;
}

// Fetch owned collection details
async function getOwnedCollectionDetails(supabase: any, collectionId: string, userId: string): Promise<(Collection & { prompts: PromptCardType[] }) | null> {
  // Fetch collection with ownership verification
  const { data: collectionData, error: collectionError } = await supabase
    .from("collections")
    .select(`*, user:user_id (id, username, avatar_url)`)
    .eq("id", collectionId)
    .eq("user_id", userId)
    .single();
    
  if (collectionError || !collectionData) {
    console.error("Error fetching collection:", collectionError?.message);
    return null;
  }
  
  // Fetch prompts in the collection
  const { data: promptsData, error: promptsError } = await supabase
    .from("collection_prompts")
    .select(`
      prompt_id,
      added_at,
      prompt:prompts (
        id, short_id, slug, title, description, image_url, 
        created_at, updated_at, is_public, view_count,
        category:categories (name, slug),
        tool:tools (name, slug),
        user:user_id (id, username, avatar_url),
        tags:prompt_tags (tag:tags (id, name, slug))
      )
    `)
    .eq("collection_id", collectionId)
    .order("added_at", { ascending: false });
    
  if (promptsError) {
    console.error("Error fetching prompts:", promptsError.message);
    return { ...collectionData, prompts: [] };
  }
  
  // Transform data into proper format
  const prompts = promptsData
    .filter((item: any) => item.prompt) // Filter out any null prompts
    .map((item: any) => {
      const prompt = item.prompt;
      return {
        id: prompt.id,
        shortId: prompt.short_id,
        slug: prompt.slug,
        title: prompt.title,
        description: prompt.description,
        imageUrl: prompt.image_url,
        category: prompt.category,
        tool: prompt.tool,
        user: prompt.user,
        tags: prompt.tags?.map((t: any) => t.tag) || [],
        primaryTagSlug: prompt.tags?.[0]?.tag?.slug || null,
        remixCount: 0, // Default value since remix_count column doesn't exist
        commentCount: 0, // Default value since comment_count column doesn't exist
        likeCount: 0, // Default value since like_count column doesn't exist
        viewCount: prompt.view_count || 0,
        isPublic: prompt.is_public,
        createdAt: prompt.created_at,
        updatedAt: prompt.updated_at,
      };
    });
  
  // Transform collection data to ensure consistent property naming
  const transformedCollection = {
    id: collectionData.id,
    userId: collectionData.user_id,
    name: collectionData.name,
    description: collectionData.description,
    icon: collectionData.icon,
    isPublic: Boolean(collectionData.is_public),
    isDefault: Boolean(collectionData.is_default),
    defaultType: collectionData.default_type,
    createdAt: collectionData.created_at,
    updatedAt: collectionData.updated_at,
    user: collectionData.user,
    prompts: prompts
  };
  
  return transformedCollection;
}


export default async function CollectionDetailsPage({ params }: CollectionDetailsPageProps) {
  // In Next.js App Router, params is already available synchronously, but we need to ensure
  // all async operations are properly awaited
  const { id: collectionId } = await params;
  
  // Use cookies directly without awaiting - cookies() is not a Promise in Next.js
  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookieStore.set(name, value, options)
          })
        },
      },
    }
  );

  // Get current user for ownership check
  const { data: { session } } = await supabase.auth.getSession();
  const currentUser = session?.user || null;

  if (!currentUser) {
    // Redirect to login if not authenticated
    redirect("/sign-in?redirect=/collections/" + collectionId);
  }

  const collection = await getOwnedCollectionDetails(supabase, collectionId, currentUser.id);

  if (!collection) {
    // Not found or not owned by the current user
    notFound();
  }

  return <CollectionClientWrapper initialCollection={collection} currentUserId={currentUser.id} />;
}