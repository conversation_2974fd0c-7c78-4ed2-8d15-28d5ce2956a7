"use client";

import dynamic from "next/dynamic";
import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import type { Collection, PromptCard as PromptCardType } from "@/lib/types";

// Dynamically import the client component with SSR disabled
const CollectionDetailClient = dynamic(() => import("./collection-detail-client"), {
  ssr: false,
  loading: () => (
    <div className="flex h-[70vh] w-full items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
    </div>
  ),
});

interface CollectionClientWrapperProps {
  initialCollection: Collection & { prompts: PromptCardType[] };
  currentUserId: string;
}

export default function CollectionClientWrapper({
  initialCollection,
  currentUserId,
}: CollectionClientWrapperProps) {
  // This wrapper ensures the client component is only rendered in the browser
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return (
      <div className="flex h-[70vh] w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  return (
    <CollectionDetailClient
      initialCollection={initialCollection}
      currentUserId={currentUserId}
    />
  );
}
