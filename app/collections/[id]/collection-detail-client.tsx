"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash, PlusCircle, Lock, Globe, ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import { toast } from "@/components/ui/use-toast";
import {
  createBrowserClient
} from "@supabase/ssr";
import type { SupabaseClient } from "@supabase/supabase-js";
import type { Collection, PromptCard as PromptCardType } from "@/lib/types";
import Image from "next/image";

// Dynamically import components that might cause SSR issues
const PromptCard = dynamic(() => import("@/components/prompt-card"), { ssr: false });
const EditCollectionDialog = dynamic(() => import("@/components/edit-collection-dialog"), { ssr: false });
const AddPromptsToCollectionDialog = dynamic(() => import("@/components/add-prompts-to-collection-dialog"), { ssr: false });

interface CollectionDetailClientProps {
  initialCollection: Collection & { prompts: PromptCardType[] };
  currentUserId: string;
}

export default function CollectionDetailClient({
  initialCollection,
  currentUserId,
}: CollectionDetailClientProps) {
  const [collection, setCollection] = useState(initialCollection);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddPromptsDialogOpen, setIsAddPromptsDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null);
  const router = useRouter();
  
  // Initialize Supabase client on the client-side only
  useEffect(() => {
    // This code will only run in the browser, not during SSR
    setSupabase(createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    ));
  }, []);

  // Handle update collection
  const handleUpdateCollection = async (
    collectionId: string,
    collectionData: {
      name?: string;
      description?: string | null;
      icon?: string | null;
      imageFile?: File | null;
      removeCurrentImage?: boolean;
      isPublic?: boolean;
    }
  ) => {
    console.log('[CollectionDetailClient] handleUpdateCollection called with:', {
      collectionId,
      collectionData,
      isPublicDefined: collectionData.isPublic !== undefined,
      isPublicValue: collectionData.isPublic
    });
    
    // Only proceed if supabase client is initialized
    if (!supabase) {
      console.error('[CollectionDetailClient] Supabase client not initialized');
      toast({
        title: "Error updating collection",
        description: "Could not connect to the database. Please try again.",
        variant: "destructive",
      });
      return;
    }
    let iconUrl = collectionData.icon;
    
    try {
      // Handle image uploads and updates if present
      if (collectionData.imageFile || collectionData.removeCurrentImage) {
        console.log('[CollectionDetailClient] Detected image update');
        
        // Handle image upload if there's a new image file
        if (collectionData.imageFile) {
          console.log('[CollectionDetailClient] Processing image file upload');
          const file = collectionData.imageFile;
          
          // Create a unique filename
          const timestamp = Date.now();
          const randomId = Math.random().toString(36).substring(2, 10);
          const safeFileName = file.name.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_.-]/g, '');
          const fileName = `${currentUserId}/${timestamp}-${randomId}-${safeFileName}`;
          
          // Upload the file
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('collection-images')
            .upload(fileName, file, { 
              cacheControl: '3600', 
              upsert: true,
              contentType: file.type
            });

          if (uploadError) {
            console.error('[CollectionDetailClient] Storage upload error:', uploadError);
            throw uploadError;
          }
          
          // Get the public URL
          const publicUrlResult = supabase.storage.from('collection-images').getPublicUrl(fileName);
          iconUrl = publicUrlResult.data.publicUrl;
        } else if (collectionData.removeCurrentImage) {
          console.log('[CollectionDetailClient] Removing current image');
          iconUrl = null;
        }
      }
      
      // Prepare RPC parameters - order matters! Match the SQL function parameter order
      const rpcParams: any = {
        p_user_id: currentUserId,
        p_collection_id: collectionId
      };
      
      // Add optional parameters in the correct order
      if (collectionData.name !== undefined) rpcParams.p_name = collectionData.name;
      if (collectionData.description !== undefined) rpcParams.p_description = collectionData.description;
      if (collectionData.imageFile || collectionData.removeCurrentImage) {
        rpcParams.p_icon_url = iconUrl;
      }
      if (collectionData.isPublic !== undefined) {
        rpcParams.p_is_public = collectionData.isPublic;
        console.log('[CollectionDetailClient] Setting p_is_public to:', collectionData.isPublic);
      } else {
        console.log('[CollectionDetailClient] isPublic is undefined, not setting p_is_public');
      }

      // Log detailed information about the RPC call parameters
      console.log('[CollectionDetailClient] Calling update_user_collection RPC with params:', JSON.stringify(rpcParams, null, 2));
      console.log('[CollectionDetailClient] Collection ID type:', typeof collectionId, 'Value:', collectionId);
      console.log('[CollectionDetailClient] User ID type:', typeof currentUserId, 'Value:', currentUserId);
      
      if (collectionData.name !== undefined) {
        console.log('[CollectionDetailClient] Name update - Type:', typeof collectionData.name, 'Value:', collectionData.name);
      }
      
      // Call the RPC function
      const { data, error } = await supabase
        .rpc("update_user_collection", rpcParams);

      // Log detailed response information
      console.log('[CollectionDetailClient] RPC response data:', data ? JSON.stringify(data, null, 2) : 'null');
      console.log('[CollectionDetailClient] RPC response error:', error ? {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        fullError: JSON.stringify(error)
      } : 'null');
      
      if (error) {
        console.error('[CollectionDetailClient] RPC error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        toast({
          title: "Error updating collection",
          description: error.message || "An unexpected error occurred while updating the collection.",
          variant: "destructive",
        });
        return;
      }
      
      // Process successful response
      if (data && Array.isArray(data) && data.length > 0) {
        const updatedCollection = data[0];
        console.log('[CollectionDetailClient] Successfully updated collection via RPC:', updatedCollection);
        
        // Update local state with the returned data
        setCollection({
          ...collection,
          name: updatedCollection.name,
          description: updatedCollection.description,
          icon: updatedCollection.icon,
          isPublic: Boolean(updatedCollection.is_public)
        });
        
        toast({
          title: "Collection updated",
          description: "Your collection has been updated successfully.",
        });
      } else {
        console.log('[CollectionDetailClient] No data returned from RPC, but no error either');
        
        // If we have updated data locally, update the collection with that
        if (
          collectionData.name ||
          collectionData.description !== undefined ||
          collectionData.icon !== undefined ||
          collectionData.isPublic !== undefined
        ) {
          console.log('[CollectionDetailClient] Updating collection with local data:', collectionData);
          setCollection(prevCollection => ({
            ...prevCollection,
            ...(collectionData.name && { name: collectionData.name }),
            ...(collectionData.description !== undefined && { description: collectionData.description }),
            ...(iconUrl !== undefined && { icon: iconUrl }),
            ...(collectionData.isPublic !== undefined && { isPublic: collectionData.isPublic })
          }));
        }
        
        // Show success message
        toast({
          title: "Collection updated",
          description: "Your collection has been updated successfully.",
        });
      }
    } catch (error) {
      console.error("Error updating collection:", error);
      toast({
        title: "Error updating collection",
        description: "An unexpected error occurred.",
        variant: "destructive",
      });
    }
  };

  // Handle delete collection
  const handleDeleteCollection = async () => {
    if (collection.isDefault) {
      toast({
        title: "Cannot delete default collection",
        description: "The default collection cannot be deleted.",
        variant: "destructive",
      });
      return;
    }

    if (!confirm("Are you sure you want to delete this collection? This action cannot be undone.")) {
      return;
    }

    setIsDeleting(true);

    // Only proceed if supabase client is initialized
    if (!supabase) {
      toast({
        title: "Error deleting collection",
        description: "Could not connect to the database. Please try again.",
        variant: "destructive",
      });
      return;
    }

    try {
      // First delete all prompt associations (this doesn't delete the prompts themselves)
      const { error: associationsError } = await supabase
        .from("collection_prompts")
        .delete()
        .eq("collection_id", collection.id);

      if (associationsError) {
        throw associationsError;
      }

      // Then delete the collection
      const { error: collectionError } = await supabase
        .from("collections")
        .delete()
        .eq("id", collection.id)
        .eq("user_id", currentUserId);

      if (collectionError) {
        throw collectionError;
      }

      toast({
        title: "Collection deleted",
        description: "Your collection has been deleted successfully.",
      });

      // Navigate back to collections page
      router.push("/collections");
    } catch (error: any) {
      console.error("Error deleting collection:", error);
      toast({
        title: "Error deleting collection",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle add prompts to collection
  const handleAddPrompts = async (collectionId: string, promptIds: string[]) => {
    if (!promptIds.length) return;
    
    // Only proceed if supabase client is initialized
    if (!supabase) {
      console.error('[CollectionDetailClient] Supabase client not initialized');
      toast({
        title: "Error adding prompts",
        description: "Could not connect to the database. Please try again.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Prepare the data for insertion
      const promptsToAdd = promptIds.map((promptId) => ({
        collection_id: collectionId,
        prompt_id: promptId,
        added_at: new Date().toISOString(),
      }));

      // Insert the new associations
      const { error } = await supabase.from("collection_prompts").insert(promptsToAdd);

      if (error) {
        throw error;
      }

      // Fetch the newly added prompts to update the UI
      const { data: newPrompts, error: fetchError } = await supabase
        .from("prompts")
        .select(`
          id, short_id, slug, title, description, image_url, 
          created_at, updated_at, is_public, view_count,
          category:categories (name, slug),
          tool:tools (name, slug),
          user:user_id (id, username, avatar_url),
          tags:prompt_tags (tag:tags (id, name, slug)),
          upvotes, comment_count, remix_count
        `)
        .in("id", promptIds);

      if (fetchError) {
        throw fetchError;
      }

      // Transform the new prompts from snake_case to camelCase to match existing prompts format
      const transformed = newPrompts.map((p: any) => ({
        id: p.id,
        shortId: p.short_id,
        slug: p.slug,
        title: p.title,
        description: p.description,
        imageUrl: p.image_url,
        category: p.category,
        tool: p.tool,
        user: p.user,
        tags: p.tags?.map((t: any) => t.tag) ?? [],
        primaryTagSlug: p.tags?.[0]?.tag?.slug ?? null,
        remixCount: p.remix_count ?? 0,
        commentCount: p.comment_count ?? 0,
        likeCount: p.upvotes ?? 0,
        viewCount: p.view_count ?? 0,
        isPublic: Boolean(p.is_public),
        createdAt: p.created_at,
        updatedAt: p.updated_at,
      }));
      
      // Update the local state with the normalized prompts
      setCollection({ ...collection, prompts: [...collection.prompts, ...transformed] });

      toast({
        title: "Prompts added",
        description: `${promptIds.length} prompts added to collection.`,
      });
    } catch (error: any) {
      console.error("Error adding prompts to collection:", error);
      toast({
        title: "Error adding prompts",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    }
  };

  // Handle remove prompt from collection
  const handleRemovePrompt = async (promptId: string) => {
    // Only proceed if supabase client is initialized
    if (!supabase) {
      toast({
        title: "Error removing prompt",
        description: "Could not connect to the database. Please try again.",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from("collection_prompts")
        .delete()
        .eq("collection_id", collection.id)
        .eq("prompt_id", promptId);

      if (error) {
        throw error;
      }

      // Update local state by removing the prompt
      setCollection({
        ...collection,
        prompts: collection.prompts.filter((prompt) => prompt.id !== promptId),
      });

      toast({
        title: "Prompt removed",
        description: "The prompt has been removed from this collection.",
      });
    } catch (error: any) {
      console.error("Error removing prompt from collection:", error);
      toast({
        title: "Error removing prompt",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back button */}
      <div className="mb-6">
        <Link href="/collections">
          <Button variant="ghost" size="sm" className="pl-0 text-muted-foreground">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Collections
          </Button>
        </Link>
      </div>

      {/* Header */}
      <div className="mb-8 rounded-xl border bg-card p-6 shadow-sm">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">{collection.name}</h1>
              {collection.isDefault && (
                <Badge className="bg-accent-blue text-white">Default</Badge>
              )}
              <Badge
                variant="outline"
                className={`flex items-center gap-1 ${
                  collection.isPublic
                    ? "bg-green-500/20 text-green-600"
                    : "bg-gray-500/20 text-gray-600"
                }`}
              >
                {collection.isPublic ? (
                  <Globe className="h-3 w-3" />
                ) : (
                  <Lock className="h-3 w-3" />
                )}{" "}
                {collection.isPublic ? "Public" : "Private"}
              </Badge>
            </div>
            {collection.description && (
              <p className="mt-2 text-muted-foreground">{collection.description}</p>
            )}
            <p className="mt-2 text-sm text-muted-foreground">
              {collection.prompts.length} prompts
            </p>
          </div>

          <div className="flex flex-col gap-4 items-end">
            {collection.icon && (
              <div className="rounded-lg overflow-hidden border shadow-sm w-32 h-32 relative">
                <Image 
                  src={collection.icon} 
                  alt={`${collection.name} thumbnail`} 
                  fill
                  className="object-cover"
                  sizes="128px"
                  quality={85}
                />
              </div>
            )}
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(true)}
              disabled={collection.isDefault}
            >
              <Edit className="mr-2 h-4 w-4" /> Edit Collection
            </Button>
          </div>
        </div>
      </div>

      {/* Prompts List */}
      <h2 className="mb-4 text-xl font-bold">Prompts in Collection</h2>
      {collection.prompts.length > 0 ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {collection.prompts.map((prompt) => (
            <PromptCard
              key={prompt.id}
              prompt={prompt}
              maxTags={1}
              isOwner={prompt.user.id === currentUserId}
              onUnsave={() => handleRemovePrompt(prompt.id)}
            />
          ))}
        </div>
      ) : (
        <Card className="rounded-lg py-12">
          <CardContent className="text-center text-muted-foreground">
            <p className="text-lg">This collection is empty.</p>
          </CardContent>
        </Card>
      )}

      {/* Edit Collection Dialog */}
      {isEditDialogOpen && (
        <EditCollectionDialog
          isOpen={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
          collection={{
            ...collection,
            isPublic: Boolean(collection.isPublic)
          }}
          onUpdate={handleUpdateCollection}
          onDelete={handleDeleteCollection}
        />
      )}

      {/* Add Prompts Dialog */}
      {isAddPromptsDialogOpen && (
        <AddPromptsToCollectionDialog
          isOpen={isAddPromptsDialogOpen}
          onClose={() => setIsAddPromptsDialogOpen(false)}
          collectionId={collection.id}
          onAddPrompts={handleAddPrompts}
        />
      )}
    </div>
  );
}
