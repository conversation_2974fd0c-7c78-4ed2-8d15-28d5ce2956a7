import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function NotFound() {
  return (
    <div className="container mx-auto flex min-h-[60vh] flex-col items-center justify-center px-4 py-8 text-center">
      <h1 className="mb-4 text-4xl font-bold">Tag Not Found</h1>
      <p className="mb-8 text-xl text-muted-foreground">
        The tag you're looking for doesn't exist or has been removed.
      </p>
      <div className="flex gap-4">
        <Button asChild className="bg-accent-green hover:bg-accent-green/90">
          <Link href="/explore">Explore Tags</Link>
        </Button>
        <Button asChild variant="outline">
          <Link href="/">Return Home</Link>
        </Button>
      </div>
    </div>
  )
}
