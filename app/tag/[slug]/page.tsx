import React from 'react'
import FilteredPromptsPageClient from '@/components/filtered-prompts-page-client'
import { getTagBySlug } from "@/lib/api-services" // Import the fetch function
import { notFound } from "next/navigation"; // Import notFound

interface TagPageProps {
  params: Promise<{
    slug: string
  }>
  // You can also add searchParams here if needed later
  // searchParams: { [key: string]: string | string[] | undefined };
}

// Accept the full props object instead of destructuring params directly
export default async function TagPage(props: TagPageProps) {
  // Access slug from props.params
  const slug = (await props.params).slug;

  // Fetch tag data on the server
  const tag = await getTagBySlug(slug);

  // Handle case where the tag is not found
  if (!tag) {
    notFound();
  }

  // Render the client component, passing the fetched data
  return (
    <FilteredPromptsPageClient
      initialSlug={slug}
      entityType="tag"
      initialEntityData={tag} // Pass the fetched tag data
      entityName="Tag"
    />
  );
}
