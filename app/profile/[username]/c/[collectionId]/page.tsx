import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";
import PublicCollectionDetailClient from "./public-collection-detail-client";
import type { Collection, PromptCard as PromptCardType } from "@/lib/types";

interface PublicCollectionDetailsPageProps {
  params: Promise<{
    username: string;
    collectionId: string; // Collection ID (UUID)
  }>;
}

// Fetch public collection details
async function getPublicCollectionDetails(supabase: any, username: string, collectionId: string): Promise<(Collection & { prompts: PromptCardType[] }) | null> {
  // First fetch the user by username
  const { data: userData, error: userError } = await supabase
    .from("profiles")
    .select("id, username")
    .eq("username", username)
    .single();
  
  if (userError || !userData) {
    console.error("Error fetching user:", userError?.message);
    return null;
  }
  
  // Then fetch the collection, ensuring it's public and belongs to the user
  const { data: collectionData, error: collectionError } = await supabase
    .from("collections")
    .select(`*, user:user_id (id, username, avatar_url)`)
    .eq("id", collectionId)
    .eq("user_id", userData.id)
    .eq("is_public", true)
    .single();
    
  if (collectionError || !collectionData) {
    console.error("Error fetching collection:", collectionError?.message);
    return null;
  }
  
  // Fetch PUBLIC prompts in the collection
  const { data: promptsData, error: promptsError } = await supabase
    .from("collection_prompts")
    .select(`
      prompt_id,
      added_at,
      prompt:prompts!inner (
        id, short_id, slug, title, description, image_url, 
        created_at, updated_at, is_public, view_count,
        category:category_id!inner (name, slug),
        tool:tool_id (name, slug),
        user:user_id!inner (id, username, avatar_url),
        tags:prompt_tags (tag:tags (id, name, slug))
      )
    `)
    .eq("collection_id", collectionId)
    .eq("prompt.is_public", true)
    .order("added_at", { ascending: false });
    
  if (promptsError) {
    console.error("Error fetching prompts:", promptsError.message);
    // Still transform collection data properly even if prompts fail
    const transformedCollection: Collection = {
      id: collectionData.id,
      userId: collectionData.user_id,
      name: collectionData.name,
      description: collectionData.description,
      icon: collectionData.icon,
      isPublic: collectionData.is_public,
      isDefault: collectionData.is_default,
      defaultType: collectionData.default_type,
      createdAt: collectionData.created_at,
      updatedAt: collectionData.updated_at,
      promptCount: collectionData.prompt_count,
      viewCount: collectionData.view_count,
      user: collectionData.user ? {
        username: collectionData.user.username,
        avatar_url: collectionData.user.avatar_url
      } : undefined,
    };
    return { ...transformedCollection, prompts: [] };
  }
  
  // Get prompt IDs for fetching statistics
  const promptIds = promptsData
    .filter((item: any) => {
      // Only include public prompts with required data
      return item.prompt && 
             item.prompt.is_public && 
             item.prompt.category && 
             item.prompt.user &&
             item.prompt.title;
    })
    .map((item: any) => item.prompt.id);

  // Fetch statistics for all prompts in one query
  let statisticsMap: Record<string, any> = {};
  if (promptIds.length > 0) {
    const { data: statisticsData, error: statisticsError } = await supabase
      .from("prompt_statistics")
      .select("id, rating, upvotes, downvotes, comment_count, remix_count")
      .in("id", promptIds);
    
    if (statisticsError) {
      console.error("Error fetching prompt statistics:", statisticsError.message);
    } else {
      // Create a map for quick lookup
      statisticsMap = (statisticsData || []).reduce((acc: any, stat: any) => {
        acc[stat.id] = stat;
        return acc;
      }, {});
    }
  }

  // Filter to only include public prompts and transform data
  const prompts = promptsData
    .filter((item: any) => {
      // Only include public prompts with required data
      return item.prompt && 
             item.prompt.is_public && 
             item.prompt.category && 
             item.prompt.user &&
             item.prompt.title;
    })
    .map((item: any) => {
      const prompt = item.prompt;
      const stats = statisticsMap[prompt.id] || {};
      
      return {
        id: prompt.id,
        shortId: prompt.short_id,
        slug: prompt.slug,
        title: prompt.title || 'Untitled Prompt',
        description: prompt.description || '',
        imageUrl: prompt.image_url,
        category: {
          name: prompt.category?.name || 'Uncategorized',
          slug: prompt.category?.slug || 'uncategorized'
        },
        tool: prompt.tool ? {
          name: prompt.tool.name,
          slug: prompt.tool.slug
        } : undefined,
        user: {
          id: prompt.user.id,
          username: prompt.user.username || 'unknown',
          avatarUrl: prompt.user.avatar_url
        },
        tags: prompt.tags?.map((t: any) => t.tag).filter(Boolean) || [],
        primary_tag_slug: prompt.tags?.[0]?.tag?.slug || null,
        remixCount: stats.remix_count || 0,
        commentCount: stats.comment_count || 0,
        viewCount: prompt.view_count || 0,
        likeCount: stats.upvotes || 0,
        isPublic: prompt.is_public,
        createdAt: prompt.created_at,
        updatedAt: prompt.updated_at,
      };
    });
  
  // Transform collection data to match Collection interface
  const transformedCollection: Collection = {
    id: collectionData.id,
    userId: collectionData.user_id,
    name: collectionData.name,
    description: collectionData.description,
    icon: collectionData.icon,
    isPublic: collectionData.is_public,
    isDefault: collectionData.is_default,
    defaultType: collectionData.default_type,
    createdAt: collectionData.created_at,
    updatedAt: collectionData.updated_at,
    promptCount: collectionData.prompt_count,
    viewCount: collectionData.view_count,
    user: collectionData.user ? {
      username: collectionData.user.username,
      avatar_url: collectionData.user.avatar_url
    } : undefined,
  };

  return { ...transformedCollection, prompts };
}


export default async function PublicCollectionDetailsPage({ params }: PublicCollectionDetailsPageProps) {
  const { username, collectionId } = await params;
  
  // Get cookies store properly
  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookieStore.set(name, value, options)
          })
        },
      },
    }
  );
  
  // Get current user for follow status and ownership checks
  const { data: { session } } = await supabase.auth.getSession();
  const currentUser = session?.user || null;
  
  // Fetch collection with verification
  const collection = await getPublicCollectionDetails(supabase, username, collectionId);
  
  if (!collection) {
    notFound();
  }
  
  return <PublicCollectionDetailClient initialCollection={collection} currentUserId={currentUser?.id || null} />;
}