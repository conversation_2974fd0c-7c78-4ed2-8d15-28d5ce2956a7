"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, Globe, ArrowLeft, BookmarkPlus, Loader2 } from "lucide-react";
import Link from "next/link";
import PromptCard from "@/components/prompt-card";
import { useRouter } from "next/navigation";
import { createBrowserClient } from "@supabase/ssr";
import { toast } from "@/components/ui/use-toast";
import type { Collection, PromptCard as PromptCardType } from "@/lib/types";

interface PublicCollectionDetailClientProps {
  initialCollection: Collection & { prompts: PromptCardType[] };
  currentUserId: string | null;
}

export default function PublicCollectionDetailClient({
  initialCollection,
  currentUserId,
}: PublicCollectionDetailClientProps) {
  const [collection, setCollection] = useState(initialCollection);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isFollowLoading, setIsFollowLoading] = useState(false);
  const router = useRouter();

  // Check if user is following this collection
  const checkFollowStatus = async () => {
    if (!currentUserId) return;
    
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    
    try {
      // Use EXISTS query for better performance
      const { data, error } = await supabase
        .from("followed_collections")
        .select("id")
        .eq("collection_id", collection.id)
        .eq("follower_id", currentUserId)
        .limit(1);
        
      if (error) {
        // Handle table not found or no rows found gracefully
        if (error.code === "PGRST116" || error.code === "42P01" || error.code === "PGRST301" || 
            error.message?.includes("relation") || error.message?.includes("does not exist")) {
          setIsFollowing(false);
          return;
        }
        console.warn("Error checking follow status:", error);
        setIsFollowing(false);
        return;
      }
      
      setIsFollowing(data && data.length > 0);
    } catch (error) {
      console.warn("Error checking follow status:", error);
      setIsFollowing(false);
    }
  };

  // Handle follow/unfollow collection with optimistic updates
  const handleFollowCollection = async () => {
    if (!currentUserId) {
      router.push(`/sign-in?redirect=/profile/${collection.user?.username || 'user'}/c/${collection.id}`);
      return;
    }
    
    // Optimistic update
    const previousFollowState = isFollowing;
    setIsFollowing(!isFollowing);
    setIsFollowLoading(true);
    
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    
    try {
      if (previousFollowState) {
        // Unfollow
        const { error } = await supabase
          .from("followed_collections")
          .delete()
          .eq("collection_id", collection.id)
          .eq("follower_id", currentUserId);
          
        if (error) {
          // Revert optimistic update
          setIsFollowing(previousFollowState);
          
          if (error.code === "42P01" || error.message?.includes("relation") || error.message?.includes("does not exist")) {
            toast({
              title: "Feature not available",
              description: "Collection following is not yet available.",
              variant: "destructive",
            });
          } else {
            throw error;
          }
          return;
        }
        
        toast({
          title: "Collection unfollowed",
          description: "This collection has been removed from your followed collections.",
        });
      } else {
        // Follow
        const { error } = await supabase
          .from("followed_collections")
          .insert({
            collection_id: collection.id,
            follower_id: currentUserId,
            followed_at: new Date().toISOString(),
          });
          
        if (error) {
          // Revert optimistic update
          setIsFollowing(previousFollowState);
          
          if (error.code === "42P01" || error.message?.includes("relation") || error.message?.includes("does not exist")) {
            toast({
              title: "Feature not available",
              description: "Collection following is not yet available.",
              variant: "destructive",
            });
          } else {
            throw error;
          }
          return;
        }
        
        toast({
          title: "Collection followed",
          description: "This collection has been added to your followed collections.",
        });
      }
    } catch (error: any) {
      // Revert optimistic update
      setIsFollowing(previousFollowState);
      console.error("Error following/unfollowing collection:", error);
      toast({
        title: "Error",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsFollowLoading(false);
    }
  };

  // Check follow status on component mount
  useEffect(() => {
    checkFollowStatus();
  }, [currentUserId, collection.id]);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back button */}
      <div className="mb-6">
        <Link href="/collections">
          <Button variant="ghost" size="sm" className="pl-0 text-muted-foreground">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Collections
          </Button>
        </Link>
      </div>

      {/* Header */}
      <div className="mb-8 rounded-xl border bg-card p-6 shadow-sm">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">{collection.name}</h1>
              <Badge variant="outline" className="bg-green-500/20 text-green-600 flex items-center gap-1">
                <Globe className="h-3 w-3" /> Public
              </Badge>
            </div>
            {collection.description && (
              <p className="mt-2 text-muted-foreground">{collection.description}</p>
            )}
            <div className="mt-2 flex items-center gap-2">
              <p className="text-sm text-muted-foreground">
                {collection.prompts.length} prompts
              </p>
              <span className="text-muted-foreground">•</span>
              <p className="text-sm text-muted-foreground">
                Curated by{" "}
                <Link href={`/profile/${collection.user?.username || 'user'}`} className="font-medium hover:underline">
                  @{collection.user?.username || 'user'}
                </Link>
              </p>
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              onClick={handleFollowCollection}
              disabled={isFollowLoading}
            >
              {isFollowLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <BookmarkPlus className="mr-2 h-4 w-4" />
              )}
              {isFollowing ? "Following" : "Follow Collection"}
            </Button>
          </div>
        </div>
      </div>

      {/* Prompts List */}
      <h2 className="mb-4 text-xl font-bold">Prompts in Collection</h2>
      {collection.prompts.length > 0 ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {collection.prompts.map((prompt) => (
            <PromptCard
              key={prompt.id}
              prompt={prompt}
              maxTags={1}
              isOwner={prompt.user.id === currentUserId}
            />
          ))}
        </div>
      ) : (
        <Card className="rounded-lg py-12">
          <CardContent className="text-center text-muted-foreground">
            <p className="text-lg">This collection is empty.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
