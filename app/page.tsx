// Add ISR revalidation - 5 minutes
export const revalidate = 300;

import { Suspense } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search } from "lucide-react"

// Import data fetching functions
import { getToolsWithPromptCount, getCategoriesWithPromptCount, getTools, getCategories } from "@/lib/api-services"
import { getPromptsServerAction } from "@/lib/server-actions"

// Import Client Components from their new locations
import CategoriesSection from "@/components/home/<USER>"
import FeaturedPromptsSkeleton from "@/components/home/<USER>"
import FeaturedPromptsOptimized from "@/components/featured-prompts-optimized"
import PopularToolsSectionClient from "@/components/home/<USER>";
import GettingStartedSection from "@/components/home/<USER>"

// Import types from shared types file
import type { Category, Tool, PromptCard } from "@/lib/types"

// Helper function to safely parse category ID (important if ID can be string or number)
function safeParseCategoryId(id: string | number): number {
  if (typeof id === 'number') {
    return id
  }
  
  const parsed = Number.parseInt(id, 10)
  if (Number.isNaN(parsed)) {
    console.warn(`Invalid category ID encountered: "${id}". Using fallback ID of 0.`)
    return 0 // Fallback to 0 for invalid IDs, or handle error as appropriate
  }
  
  return parsed
}

export default async function Home() {
  // Fetch tools with prompt counts from the database
  let dbTools: Tool[] = []
  try {
    dbTools = await getToolsWithPromptCount()
  } catch (error) {
    console.error("Error fetching tools with prompt counts:", error)
    try {
      dbTools = await getTools() // Fallback
    } catch (fallbackError) {
      console.error("Error in fallback tools fetch:", fallbackError)
    }
  }

  const sortedTools = [...dbTools].sort((a, b) => (b.promptCount || 0) - (a.promptCount || 0))
  const popularTools = sortedTools.slice(0, 6).map((tool) => ({
    id: tool.id, // Assuming tool.id is already correctly typed
    name: tool.name,
    slug: tool.slug,
    icon: tool.icon || "🔧",
    promptCount: tool.promptCount || 0,
  }))
  const additionalTools = sortedTools.slice(6).map((tool) => ({
    id: tool.id,
    name: tool.name,
    slug: tool.slug,
    icon: tool.icon || "🔧",
    promptCount: tool.promptCount || 0,
  }))

  // Fetch categories with prompt counts
  let dbCategories: Category[] = []
  try {
    dbCategories = await getCategoriesWithPromptCount()
  } catch (error) {
    console.error("Error fetching categories with prompt counts:", error)
    try {
      dbCategories = await getCategories() // Fallback
    } catch (fallbackError) {
      console.error("Error in fallback categories fetch:", fallbackError)
    }
  }

  const sortedCategories = [...dbCategories].sort((a, b) => (b.promptCount || 0) - (a.promptCount || 0))
  const featuredCategories = sortedCategories.slice(0, 5).map((category) => ({
    id: safeParseCategoryId(category.id), // Use safeParseCategoryId
    name: category.name,
    slug: category.slug,
    promptCount: category.promptCount || 0,
    imagePath: category.imagePath || `/images/categories/${category.slug}.png`,
  }))
  const additionalCategories = sortedCategories.slice(5).map((category) => ({
    id: safeParseCategoryId(category.id), // Use safeParseCategoryId
    name: category.name,
    slug: category.slug,
    promptCount: category.promptCount || 0,
    imagePath: category.imagePath || `/images/categories/${category.slug}.png`,
  }))

  // Pre-fetch featured prompts data for instant tab switching
  let featuredPromptsData: {
    popular: PromptCard[];
    newest: PromptCard[];
    trending: PromptCard[];
  } = {
    popular: [],
    newest: [],
    trending: []
  };

  try {
    // Fetch all three types of featured prompts in parallel
    const [popularPrompts, newestPrompts, trendingPrompts] = await Promise.all([
      getPromptsServerAction({
        limit: 6,
        offset: 0,
        sortBy: "rating",
        sortOrder: "desc",
      }),
      getPromptsServerAction({
        limit: 6,
        offset: 0,
        sortBy: "created_at",
        sortOrder: "desc",
      }),
      getPromptsServerAction({
        limit: 6,
        offset: 0,
        sortBy: "trending_score",
        sortOrder: "desc",
      }),
    ]);

    featuredPromptsData = {
      popular: popularPrompts,
      newest: newestPrompts,
      trending: trendingPrompts,
    };
  } catch (error) {
    console.error("Error pre-fetching featured prompts:", error);
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section - Redesigned for better impact and immediate search */}
      <section className="mb-12 text-center">
        <h1 className="mb-4 text-3xl font-bold tracking-tight text-foreground md:text-4xl lg:text-5xl">
          Discover and Share <span className="text-accent-green">Awesome</span> Prompts
        </h1>
        <p className="mb-8 text-lg text-muted-foreground md:text-xl">
          Find prompts for any task, share your creations, and connect with a community of prompt engineers.
        </p>
        {/* Quick Search - Placed prominently in the hero */}
        <form action="/search" method="GET" className="mx-auto max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
            <Input
              type="search"
              name="q" // Ensure the name attribute is 'q' for the search page
              placeholder="Search prompts, categories, tools..."
              className="w-full rounded-lg pl-10 pr-4 text-base md:text-lg" // Increased font size
              style={{ borderRadius: "8px" }} // Consistent rounded corners
              aria-label="Search Prompts"
            />
          </div>
          {/* Submit button is implicit on Enter, or can be added if desired */}
        </form>
        {/* Action Buttons */}
        <div className="mt-6 flex flex-col items-center gap-3 sm:flex-row sm:justify-center">
          <Button 
            className="w-full sm:w-auto bg-accent-green text-black hover:bg-accent-green/90 transition-all duration-300" 
            style={{ borderRadius: "6px" }} // Explicit rounded corners
            asChild
          >
            <Link href="/prompt/submit">+ New Prompt</Link>
          </Button>
          <Button 
            variant="outline" 
            className="w-full sm:w-auto" 
            style={{ borderRadius: "6px" }} // Explicit rounded corners
            asChild
          >
            <Link href="/explore">Explore All Prompts</Link>
          </Button>
        </div>
      </section>

      {/* Getting Started Section - Only shown to new visitors */}
      <GettingStartedSection />
      
      {/* Tabbed Content - For more organised browsing */}
      <Tabs defaultValue="prompts" className="w-full">
        <TabsList className="grid w-full grid-cols-3 md:max-w-md mx-auto">
          <TabsTrigger value="prompts">Browse Prompts</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="tools">Tools</TabsTrigger>
        </TabsList>

        {/* Prompts Tab - Contains Featured Prompts with pre-fetched data */}
        <TabsContent value="prompts" className="mt-6">
          <FeaturedPromptsOptimized initialData={featuredPromptsData} />
        </TabsContent>

        {/* Categories Tab - Contains CategoriesSection */}
        <TabsContent value="categories" className="mt-6">
          <CategoriesSection 
            featuredCategories={featuredCategories} 
            additionalCategories={additionalCategories} 
          />
        </TabsContent>

        {/* Tools Tab - Contains PopularToolsSectionClient */}
        <TabsContent value="tools" className="mt-6">
          <PopularToolsSectionClient 
            popularTools={popularTools} 
            additionalTools={additionalTools} 
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
