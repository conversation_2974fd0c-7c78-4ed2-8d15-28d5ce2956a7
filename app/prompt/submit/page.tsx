"use client"

import type React from "react"
import { useState, useEffect, useMemo, useC<PERSON>back, useRef } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { X, Upload, Loader2, CheckCircle, PlusCircle, Lock, Globe, Brain, ArrowLeft } from "lucide-react"
import { allTools } from "@/lib/data/tools"
import { allTags } from "@/lib/data/tags"
import { allCategories } from "@/lib/data/categories"
import { 
  getAIModelsForTool, 
  createPrompt, 
  getUserCollections, 
  addPromptToCollection, 
  createCollection 
} from "@/lib/api-services"
import type { AIModel as BaseAIModel, Collection, CreatePromptData } from "@/lib/types"
import { createTitleSlug } from "@/lib/utils/url-helpers"
import { createBrowserClient } from "@supabase/ssr"
import { toast } from "sonner"
import { Combobox, type ComboboxOption } from "@/components/ui/combobox"
import { TagInput, type TagOption } from "@/components/ui/tag-input"
import CreateCollectionDialog from "@/components/create-collection-dialog"
import { Switch } from "@/components/ui/switch"
import { DragDropImageUpload } from "@/components/ui/drag-drop-image-upload"
import MarkdownPromptEditor from "@/components/ui/markdown-prompt-editor"

// Extended AIModel interface with displayName property
interface AIModel extends BaseAIModel {
  displayName?: string;
}

export default function SubmitPromptPage() {
  const router = useRouter()
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
  
  // Form state
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)
  const [submissionProgress, setSubmissionProgress] = useState("")
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [promptText, setPromptText] = useState("")
  const [instructions, setInstructions] = useState("")
  const [exampleInput, setExampleInput] = useState("")
  const [exampleOutput, setExampleOutput] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedTool, setSelectedTool] = useState<string>("") 
  const [selectedAIModel, setSelectedAIModel] = useState<string>("") 
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isPublic, setIsPublic] = useState(true)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false) // Default to false for cleaner initial UI
  const [userEnteredAiModel, setUserEnteredAiModel] = useState<string>("")
  
  // Tags state
  const [selectedTags, setSelectedTags] = useState<string[]>([])
    
  // Collections state
  const [collections, setCollections] = useState<Collection[]>([])
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null)
  const [isLoadingCollections, setIsLoadingCollections] = useState(false)
  const [isCreateCollectionDialogOpen, setIsCreateCollectionDialogOpen] = useState(false)
  const [myPromptsCollectionId, setMyPromptsCollectionId] = useState<string | null>(null)
  
  // Models state
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]) 
  const [isLoadingModels, setIsLoadingModels] = useState(false)
    
  // User state
  const [user, setUser] = useState<any>(null)
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  
  // NLP Analysis state - enabled by default, no toggle UI
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [nlpSuggestions, setNlpSuggestions] = useState<{
    category?: string;
    tool?: string;
    tags?: string[];
    confidence?: {
      category?: 'high' | 'medium' | 'low';
      tool?: 'high' | 'medium' | 'low';
      tags?: 'high' | 'medium' | 'low';
    };
  }>({})
  const [hasAcceptedSuggestions, setHasAcceptedSuggestions] = useState({
    category: false,
    tool: false,
    tags: false
  })
  const [lastAnalyzedContent, setLastAnalyzedContent] = useState("")

  // Error state for form validation
  const [errors, setErrors] = useState({
    title: false,
    promptText: false,
    category: false,
    tool: false,
    tags: false
  })

  // Check authentication and redirect if not logged in
  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true)
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session?.user) {
          router.push('/sign-in?redirect=/prompt/submit')
          return
        }
        setUser(session.user)
      } catch (error) {
        console.error('Error checking auth:', error)
        router.push('/sign-in?redirect=/prompt/submit')
      } finally {
        setIsCheckingAuth(false)
      }
    }
    
    checkAuth()
  }, [router, supabase])
  
  // Effect to fetch AI models when tool changes
  useEffect(() => {
    const fetchModels = async () => {
      if (!selectedTool) {
        setAvailableModels([])
        return
      }
      
      setIsLoadingModels(true)
      try {
        const selectedToolObj = allTools.find(tool => String(tool.id) === selectedTool)
        if (!selectedToolObj) {
          setAvailableModels([])
          return
        }
        const models = await getAIModelsForTool(selectedToolObj.slug)
        const formattedModels = models.map(model => ({
          ...model,
          displayName: model.provider ? `${model.name} (${model.provider})` : model.name
        })).sort((a, b) => a.name.localeCompare(b.name))
        setAvailableModels(formattedModels)
      } catch (error) {
        console.error('Error fetching AI models:', error)
        setAvailableModels([])
      } finally {
        setIsLoadingModels(false)
      }
    }
    
    fetchModels()
  }, [selectedTool])

  // Effect to fetch user collections
  useEffect(() => {
    const fetchCollections = async () => {
      if (!user) return
      
      setIsLoadingCollections(true)
      try {
        const userCollections = await getUserCollections(user.id, { includePrivate: true })
        setCollections(userCollections)
        
        // Find "My Prompts" collection
        const myPromptsCollection = userCollections.find(
          collection => collection.name === "My Prompts" && collection.isDefault
        )
        if (myPromptsCollection) {
          setMyPromptsCollectionId(myPromptsCollection.id)
        }
      } catch (error) {
        console.error('Error fetching collections:', error)
      } finally {
        setIsLoadingCollections(false)
      }
    }
    
    fetchCollections()
  }, [user])

  // Debounced NLP analysis function with lazy loading - now runs by default
  const analyzePromptContent = useCallback(
    async (title: string, description: string, promptText: string) => {
      // Don't analyze if any suggestions have already been accepted
      if (hasAcceptedSuggestions.category || hasAcceptedSuggestions.tool || hasAcceptedSuggestions.tags) {
        return;
      }

      // Need at least some content to analyze
      const combinedLength = title.length + description.length + promptText.length;
      if (combinedLength < 50) {
        return;
      }

      // Don't re-analyze the same content
      const currentContent = `${title}|${description}|${promptText}`;
      if (currentContent === lastAnalyzedContent) {
        return;
      }

      setLastAnalyzedContent(currentContent);
      setIsAnalyzing(true);

      try {
        // Import the NLP analysis function dynamically for better performance
        const { analysePrompt } = await import('@/lib/nlp/index');
        
        // Prepare prompt details for analysis
        const promptDetails = {
          title: title.trim(),
          description: description.trim(),
          promptText: promptText.trim(),
        };

        // Run the analysis
        const result = await analysePrompt(promptDetails, {
          includeDebug: false,
          enableMLFallback: true,
          maxTags: 5
        });

        // Convert slugs to IDs for the form
        const suggestions: typeof nlpSuggestions = {};

        if (result.category) {
          const categoryObj = allCategories.find(cat => cat.slug === result.category);
          if (categoryObj) {
            suggestions.category = categoryObj.id.toString();
          }
        }

        if (result.tool) {
          const toolObj = allTools.find(tool => tool.slug === result.tool);
          if (toolObj) {
            suggestions.tool = toolObj.id.toString();
          }
        }

        if (result.tags && result.tags.length > 0) {
          const tagIds: string[] = [];
          result.tags.forEach(tagSlug => {
            const tagObj = allTags.find(tag => tag.slug === tagSlug);
            if (tagObj) {
              tagIds.push(tagObj.id.toString());
            }
          });
          if (tagIds.length > 0) {
            suggestions.tags = tagIds;
          }
        }

        // Set confidence levels
        suggestions.confidence = result.confidence;

        setNlpSuggestions(suggestions);
        
        console.log('NLP analysis completed:', {
          input: { title, description, promptText: promptText.substring(0, 50) + '...' },
          result: suggestions,
          confidence: result.confidence
        });

      } catch (error) {
        console.error('Error analyzing prompt content:', error);
        // Fail silently for NLP analysis
        setNlpSuggestions({});
      } finally {
        setIsAnalyzing(false);
      }
    },
    [hasAcceptedSuggestions, lastAnalyzedContent]
  );

  // Debounced effect for NLP analysis - always enabled
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      analyzePromptContent(title, description, promptText);
    }, 1000); // 1 second debounce

    return () => clearTimeout(timeoutId);
  }, [title, description, promptText, analyzePromptContent]);

  const acceptSuggestion = (type: 'category' | 'tool' | 'tags') => {
    if (type === 'category' && nlpSuggestions.category) {
      setSelectedCategory(nlpSuggestions.category);
      setHasAcceptedSuggestions(prev => ({ ...prev, category: true }));
    } else if (type === 'tool' && nlpSuggestions.tool) {
      setSelectedTool(nlpSuggestions.tool);
      setHasAcceptedSuggestions(prev => ({ ...prev, tool: true }));
    } else if (type === 'tags' && nlpSuggestions.tags) {
      setSelectedTags(prev => [...new Set([...prev, ...nlpSuggestions.tags!])]);
      setHasAcceptedSuggestions(prev => ({ ...prev, tags: true }));
    }
  };

  const acceptIndividualTag = (tagId: string) => {
    setSelectedTags(prev => [...new Set([...prev, tagId])]);
  };

  const handleCreateCollection = async (name: string, description: string | null, imageFile: File | null, isPublic: boolean) => {
    try {
      const newCollection = await createCollection(user.id, {
        name,
        description,
        imageFile,
        is_public: isPublic
      })
      
      // Add to collections list
      setCollections(prev => [newCollection, ...prev])
      
      // Select the new collection
      setSelectedCollection(newCollection.id)
      
      toast.success("Collection created!", {
        description: `"${name}" has been created and selected.`,
      })
      
      setIsCreateCollectionDialogOpen(false)
    } catch (error) {
      console.error('Error creating collection:', error)
      toast.error("Error creating collection", {
        description: error instanceof Error ? error.message : "An unexpected error occurred.",
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    if (!user) {
      toast.error("Authentication required", {
        description: "Please sign in to submit a prompt.",
      })
      return
    }

    // Reset errors
    setErrors({
      title: false,
      promptText: false,
      category: false,
      tool: false,
      tags: false
    })

    // Basic validation - description is now optional
    const validationErrors = {
      title: !title.trim(),
      promptText: !promptText.trim(),
      category: !selectedCategory,
      tool: !selectedTool,
      tags: selectedTags.length === 0
    }

    const hasErrors = Object.values(validationErrors).some(error => error)

    if (hasErrors) {
      setErrors(validationErrors)
      
      // Create error message based on missing fields
      const missingFields = []
      if (validationErrors.title) missingFields.push("title")
      if (validationErrors.promptText) missingFields.push("prompt text")
      if (validationErrors.category) missingFields.push("category")
      if (validationErrors.tool) missingFields.push("AI tool")
      if (validationErrors.tags) missingFields.push("at least one tag")

      toast.error("Missing required fields", {
        description: `Please fill in: ${missingFields.join(", ")}.`,
      })
      return
    }

    setIsSubmitting(true)
    try {
      // Handle image upload if provided
      let imageUrl: string | undefined = undefined
      if (imageFile) {
        setSubmissionProgress("Uploading image...")
        try {
          const fileName = `${user.id}/${Date.now()}-${imageFile.name}`
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('prompt-images')
            .upload(fileName, imageFile, {
              cacheControl: '3600',
              upsert: false
            })
          
          if (uploadError) {
            console.error('Error uploading image:', uploadError)
            toast.error("Image upload failed", {
              description: "Failed to upload image. Continuing without image.",
            })
          } else {
            const publicUrlResult = supabase.storage
              .from('prompt-images')
              .getPublicUrl(fileName)
            imageUrl = publicUrlResult.data.publicUrl
          }
        } catch (error) {
          console.error('Error in image upload process:', error)
          toast.error("Image upload failed", {
            description: "Failed to upload image. Continuing without image.",
          })
        }
      }

      setSubmissionProgress("Creating your prompt...")
      const promptData: CreatePromptData = {
        userId: user.id,
        title: title.trim(),
        description: description.trim() || undefined,
        promptText: promptText.trim(),
        instructions: instructions.trim() || undefined,
        exampleInput: exampleInput.trim() || undefined,
        exampleOutputText: exampleOutput.trim() || undefined,
        categoryId: parseInt(selectedCategory),
        toolId: parseInt(selectedTool),
        tagIds: selectedTags.map(id => parseInt(id)),
        isPublic,
        aiModelId: selectedAIModel ? parseInt(selectedAIModel) : undefined,
        userEnteredAiModel: userEnteredAiModel.trim() || undefined,
        imageUrl: imageUrl,
      }

      const { promptId, shortId, error } = await createPrompt(promptData)
      
      if (error || !promptId || !shortId) {
        throw new Error(error?.message || "Failed to create prompt")
      }

      console.log('Prompt created successfully:', { promptId, shortId })

      // Add to collection if one is selected, otherwise defaults to "My Prompts"
      const collectionId = selectedCollection || myPromptsCollectionId;
      if (collectionId && promptId) {
        setSubmissionProgress("Adding to collection...")
        try {
          await addPromptToCollection(user.id, promptId, collectionId)
          console.log('Prompt added to collection successfully')
        } catch (collectionError) {
          console.error('Error adding prompt to collection:', collectionError)
          // Don't fail the whole submission for this
        }
      }

      setSubmissionProgress("Finalizing...")
      // Get the slugs for URL construction
      const selectedCategoryObj = allCategories.find(cat => String(cat.id) === selectedCategory)
      const selectedToolObj = allTools.find(tool => String(tool.id) === selectedTool)
      const selectedTagObj = allTags.find(tag => String(tag.id) === selectedTags[0]) // Use first tag
      
      const categorySlug = selectedCategoryObj?.slug || "uncategorized"
      const toolSlug = selectedToolObj?.slug || "no-tool"
      const tagSlug = selectedTagObj?.slug || "no-tag"
      const titleSlug = createTitleSlug(title)

      // Construct the correct URL format: /prompt/[categorySlug]/[toolSlug]/[tagSlug]/[titleSlug]/[shortId]
      const promptUrl = `/prompt/${categorySlug}/${toolSlug}/${tagSlug}/${titleSlug}/${shortId}`
      
      console.log('Constructed prompt URL:', promptUrl)

      // Scroll to top to ensure success message is visible
      window.scrollTo({ top: 0, behavior: 'smooth' })

      // Show positive reinforcement success message (Reflective Design)
      toast.success("🎉 Prompt published successfully!", {
        description: `"${title}" is now live and helping the community master AI. You're making a difference!`,
        duration: 5000, // Show longer for positive reinforcement
      })

      // Trigger success animation (Visceral Design)
      setShowSuccessAnimation(true)
      setIsRedirecting(true)
      setIsSubmitting(false)
      setSubmissionProgress("")

      // Add a small delay for the success message to be seen (Visceral Design)
      setTimeout(() => {
        router.push(promptUrl)
      }, 1500) // Slightly longer to appreciate the success state
      
    } catch (error) {
      console.error('Error submitting prompt:', error)
      setSubmissionProgress("")
      toast.error("Error submitting prompt", {
        description: error instanceof Error ? error.message : "An unexpected error occurred. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
      setSubmissionProgress("")
    }
  }

  // Prepare options for comboboxes
  const categoryOptions: ComboboxOption[] = useMemo(() => 
    allCategories.map(category => ({
      value: String(category.id),
      label: category.name
    })), []
  )

  const toolOptions: ComboboxOption[] = useMemo(() => 
    allTools.map(tool => ({
      value: String(tool.id),
      label: tool.name
    })), []
  )

  const modelOptions: ComboboxOption[] = useMemo(() => 
    availableModels.map(model => ({
      value: String(model.id),
      label: model.displayName || model.name
    })), [availableModels]
  )

  const tagOptions: TagOption[] = useMemo(() => 
    allTags.map(tag => ({
      value: String(tag.id),
      label: tag.name
    })), []
  )

  const collectionOptions: ComboboxOption[] = useMemo(() => 
    collections.map(collection => ({
      value: collection.id,
      label: collection.name,
      // Don't include icon for now to avoid TypeScript issues - will be handled in the combobox component separately
    })), [collections]
  )

  // Custom render function for collection options with icons
  const renderCollectionOption = (option: ComboboxOption) => {
    const collection = collections.find(c => c.id === option.value);
    return (
      <div className="flex items-center">
        {collection?.isPublic ? (
          <Globe className="h-3 w-3 mr-2 text-muted-foreground" />
        ) : (
          <Lock className="h-3 w-3 mr-2 text-muted-foreground" />
        )}
        {option.label}
      </div>
    );
  };

  // Custom render function for selected collection value with icon
  const renderCollectionValue = (option: ComboboxOption) => {
    const collection = collections.find(c => c.id === option.value);
    return (
      <div className="flex items-center">
        {collection?.isPublic ? (
          <Globe className="h-3 w-3 mr-2 text-muted-foreground" />
        ) : (
          <Lock className="h-3 w-3 mr-2 text-muted-foreground" />
        )}
        {option.label}
      </div>
    );
  };

  // Show loading state while checking authentication
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  // Don't render the form if user is not authenticated (will redirect)
  if (!user) {
    return null
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className={`mx-auto max-w-3xl transition-all duration-500 ${showSuccessAnimation ? 'ring-2 ring-accent-green/50 shadow-lg shadow-accent-green/20' : ''}`}>
        {showSuccessAnimation && (
          <div className="fixed inset-0 bg-background/95 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="text-center space-y-6 p-8 bg-card border rounded-lg shadow-2xl max-w-md mx-4">
              <div className="w-20 h-20 mx-auto bg-accent-green/20 rounded-full flex items-center justify-center">
                <CheckCircle className="w-10 h-10 text-accent-green animate-pulse" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-foreground">🎉 Prompt Published!</h3>
                <p className="text-muted-foreground mt-3 text-lg">
                  Your prompt is now live and helping the community master AI. You're making a difference!
                </p>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Taking you to your prompt...</span>
              </div>
            </div>
          </div>
        )}
        <CardHeader>
          <Button variant="ghost" size="sm" onClick={() => router.back()} className="mb-2 self-start pl-0 text-muted-foreground" disabled={isSubmitting || isRedirecting}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
          <CardTitle className="text-2xl">Submit Your Prompt</CardTitle>
          <CardDescription>
            Share your AI prompt with the community and help others discover new possibilities
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {/* Title Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Title <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => {
                    setTitle(e.target.value)
                    if (errors.title && e.target.value.trim()) {
                      setErrors(prev => ({ ...prev, title: false }))
                    }
                  }}
                  placeholder="Give your prompt a clear, descriptive title..."
                  required
                  disabled={isSubmitting || isRedirecting}
                  className={errors.title ? "border-red-500 focus-visible:ring-red-500" : ""}
                />
                {errors.title && (
                  <p className="text-sm text-red-500 mt-1">Title is required</p>
                )}
              </div>
            </div>

            {/* Description Field - Now Optional */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <div className="col-span-3">
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe what your prompt does and how it helps users..."
                  className="min-h-[100px]"
                  disabled={isSubmitting || isRedirecting}
                />
                <p className="text-xs text-muted-foreground mt-1">Optional - helps others understand your prompt better</p>
              </div>
            </div>

            {/* Prompt Text Field */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="prompt-text" className="text-right">
                Prompt Text <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-2">
                <div className={errors.promptText ? "border border-red-500 rounded-md" : ""}>
                  <MarkdownPromptEditor
                    value={promptText}
                    onChange={(value) => {
                      setPromptText(value)
                      if (errors.promptText && value.trim()) {
                        setErrors(prev => ({ ...prev, promptText: false }))
                      }
                    }}
                    disabled={isSubmitting || isRedirecting}
                  />
                </div>
                {errors.promptText && (
                  <p className="text-sm text-red-500">Prompt text is required</p>
                )}
                <p className="text-xs text-muted-foreground">
                  Use markdown formatting for better readability. Placeholders should be indicated with <span className="bg-accent-green/20 text-accent-green px-1 py-0.5 rounded font-mono text-xs">[[replace this]]</span> format.
                </p>
              </div>
            </div>

            {/* Image Upload Field - Moved higher */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">Preview Image</Label>
              <div className="col-span-3">
                <DragDropImageUpload
                  imageFile={imageFile}
                  imagePreview={imagePreview}
                  onImageChange={(file, preview) => {
                    setImageFile(file)
                    setImagePreview(preview)
                  }}
                  disabled={isSubmitting || isRedirecting}
                />
                <p className="text-sm text-muted-foreground mt-1">Add an image to make your prompt more appealing.</p>
              </div>
            </div>

            {/* Category Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Category <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className={errors.category ? "border border-red-500 rounded-md" : ""}>
                  <Combobox
                    options={categoryOptions}
                    value={selectedCategory}
                    onChange={(value) => {
                      setSelectedCategory(value)
                      if (errors.category && value) {
                        setErrors(prev => ({ ...prev, category: false }))
                      }
                    }}
                    placeholder="Select a category..."
                    emptyText="No categories found."
                    disabled={isSubmitting || isRedirecting}
                  />
                </div>
                {errors.category && (
                  <p className="text-sm text-red-500 mt-1">Category is required</p>
                )}
                {nlpSuggestions.category && !hasAcceptedSuggestions.category && (
                  <div className="space-y-1 mt-2">
                    <p className="text-xs text-accent-green">💡 AI suggests:</p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => acceptSuggestion('category')}
                      className="h-6 px-2 text-xs bg-accent-green/10 border-accent-green/20 text-accent-green hover:bg-accent-green/20"
                    >
                      <PlusCircle className="h-3 w-3 mr-1" />
                      {allCategories.find(cat => cat.id.toString() === nlpSuggestions.category)?.name}
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* AI Tool Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                AI Tool <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className={errors.tool ? "border border-red-500 rounded-md" : ""}>
                  <Combobox
                    options={toolOptions}
                    value={selectedTool}
                    onChange={(value) => {
                      setSelectedTool(value)
                      if (errors.tool && value) {
                        setErrors(prev => ({ ...prev, tool: false }))
                      }
                    }}
                    placeholder="Select an AI tool..."
                    emptyText="No AI tools found."
                    disabled={isSubmitting || isRedirecting}
                  />
                </div>
                {errors.tool && (
                  <p className="text-sm text-red-500 mt-1">AI tool is required</p>
                )}
                {nlpSuggestions.tool && !hasAcceptedSuggestions.tool && (
                  <div className="space-y-1 mt-2">
                    <p className="text-xs text-accent-green">💡 AI suggests:</p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => acceptSuggestion('tool')}
                      className="h-6 px-2 text-xs bg-accent-green/10 border-accent-green/20 text-accent-green hover:bg-accent-green/20"
                    >
                      <PlusCircle className="h-3 w-3 mr-1" />
                      {allTools.find(tool => tool.id.toString() === nlpSuggestions.tool)?.name}
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* AI Model Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">AI Model</Label>
              <div className="col-span-3">
                {selectedTool ? (
                  <div className="space-y-2">
                    <Combobox
                      options={modelOptions}
                      value={selectedAIModel}
                      onChange={setSelectedAIModel}
                      placeholder={isLoadingModels ? "Loading models..." : "Select an AI model..."}
                      emptyText="No AI models found."
                      disabled={isLoadingModels || isSubmitting || isRedirecting}
                    />
                    {isLoadingModels && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading available models...
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">Or enter manually:</span>
                      <Input
                        placeholder="e.g., GPT-4, Claude-3, Custom Model..."
                        value={userEnteredAiModel}
                        onChange={(e) => setUserEnteredAiModel(e.target.value)}
                        disabled={isSubmitting || isRedirecting}
                        className="flex-1"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground italic">
                    Select an AI tool first to see available models
                  </div>
                )}
              </div>
            </div>

            {/* Tags Selection */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right">
                Tags <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className={errors.tags ? "border border-red-500 rounded-md" : ""}>
                  <TagInput
                    options={tagOptions}
                    selectedTags={selectedTags}
                    onTagsChange={(tags) => {
                      setSelectedTags(tags)
                      if (errors.tags && tags.length > 0) {
                        setErrors(prev => ({ ...prev, tags: false }))
                      }
                    }}
                    placeholder="Search and select tags (minimum 1 required)..."
                    maxTags={5}
                    className={isSubmitting || isRedirecting ? 'opacity-50 pointer-events-none' : ''}
                  />
                </div>
                {errors.tags && (
                  <p className="text-sm text-red-500 mt-1">At least one tag is required</p>
                )}
                {nlpSuggestions.tags && nlpSuggestions.tags.length > 0 && !hasAcceptedSuggestions.tags && (
                  <div className="space-y-1 mt-2">
                    <p className="text-xs text-accent-green">💡 AI suggests these tags:</p>
                    <div className="flex flex-wrap gap-1">
                      {nlpSuggestions.tags.map(tagId => {
                        const tag = allTags.find(t => t.id.toString() === tagId);
                        if (!tag) return null;
                        return (
                          <Button
                            key={tagId}
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => acceptIndividualTag(tagId)}
                            className="h-6 px-2 text-xs bg-accent-green/10 border-accent-green/20 text-accent-green hover:bg-accent-green/20"
                            disabled={selectedTags.includes(tagId)}
                          >
                            {selectedTags.includes(tagId) ? (
                              <CheckCircle className="h-3 w-3 mr-1" />
                            ) : (
                              <PlusCircle className="h-3 w-3 mr-1" />
                            )}
                            {tag.name}
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Visibility Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Visibility</Label>
              <div className="col-span-3 flex items-center space-x-3">
                <Switch
                  id="isPublic"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                  disabled={isSubmitting || isRedirecting}
                />
                <div className="flex items-center space-x-2">
                  {isPublic ? (
                    <Globe className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Lock className="h-4 w-4 text-muted-foreground" />
                  )}
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">{isPublic ? 'Public' : 'Private'}</span>
                    <p className="text-sm text-muted-foreground">
                      {isPublic ? 'Visible to everyone' : 'Only visible to you'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Collection Field - Updated with icons and optional indication */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right flex items-center justify-between">
                Collection
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsCreateCollectionDialogOpen(true)}
                  className="h-7 px-2 text-xs ml-2"
                  disabled={isSubmitting || isRedirecting}
                >
                  <PlusCircle className="h-3 w-3 mr-1" />
                  New
                </Button>
              </Label>
              <div className="col-span-3">
                <Combobox
                  options={collectionOptions}
                  value={selectedCollection || ""}
                  onChange={(value) => setSelectedCollection(value || null)}
                  placeholder={isLoadingCollections ? "Loading collections..." : "Select a collection (optional)..."}
                  emptyText="No collections found."
                  disabled={isLoadingCollections || isSubmitting || isRedirecting}
                  renderOption={renderCollectionOption}
                  renderValue={renderCollectionValue}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Optional - prompts are saved to "My Prompts" collection by default.
                </p>
                {isLoadingCollections && (
                  <div className="flex items-center text-sm text-muted-foreground mt-1">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading your collections...
                  </div>
                )}
              </div>
            </div>

            {/* Advanced Options Toggle */}
            <div className="grid grid-cols-4 items-center gap-4 border-t pt-6">
              <div></div>
              <div className="col-span-3">
                <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border">
                  <div className="flex flex-col">
                    <h3 className="text-sm font-medium">Advanced Options</h3>
                    <p className="text-xs text-muted-foreground">Add instructions and examples</p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                    disabled={isSubmitting || isRedirecting}
                    className="ml-4"
                  >
                    {showAdvancedOptions ? 'Hide' : 'Show'} Advanced
                  </Button>
                </div>
              </div>
            </div>

            {/* Advanced Options */}
            {showAdvancedOptions && (
              <>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="instructions" className="text-right pt-2">Instructions</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="instructions"
                      value={instructions}
                      onChange={(e) => setInstructions(e.target.value)}
                      placeholder="Provide step-by-step instructions on how to use this prompt effectively..."
                      className="min-h-[80px]"
                      disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Guidance for using the prompt.</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="example-input" className="text-right pt-2">Example Input</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="example-input"
                      value={exampleInput}
                      onChange={(e) => setExampleInput(e.target.value)}
                      placeholder="Show an example of what input users should provide..."
                      className="min-h-[80px]"
                      disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Show what input works well.</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="example-output" className="text-right pt-2">Example Output</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="example-output"
                      value={exampleOutput}
                      onChange={(e) => setExampleOutput(e.target.value)}
                      placeholder="Show what kind of output users can expect..."
                      className="min-h-[80px]"
                      disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Show typical output.</p>
                  </div>
                </div>
              </>
            )}
          </CardContent>
          <CardFooter className="flex justify-between items-center pt-6">
            <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting || isRedirecting}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || isRedirecting}
              className={`transition-all duration-300 ${
                showSuccessAnimation 
                  ? 'bg-accent-green hover:bg-accent-green text-white shadow-lg shadow-accent-green/30' 
                  : 'bg-accent-green hover:bg-accent-green/90'
              }`}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {submissionProgress || "Creating Your Prompt..."}
                </>
              ) : isRedirecting ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2 animate-pulse" />
                  Success! Redirecting...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Submit Prompt
                </>
              )}
            </Button>
          </CardFooter>
        </form>

        {/* Create Collection Dialog */}
        <CreateCollectionDialog
          isOpen={isCreateCollectionDialogOpen}
          onClose={() => setIsCreateCollectionDialogOpen(false)}
          onCreate={handleCreateCollection}
        />
      </Card>
    </div>
  )
}
