import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertCircle, Home, PlusCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export default function SubmitPromptNotFound() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <Card className="rounded-xl border shadow-sm">
            <CardContent className="p-8">
              <div className="mb-6">
                <AlertCircle className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h1 className="text-2xl font-bold text-foreground mb-2">Page Not Found</h1>
                <p className="text-muted-foreground">
                  The submit prompt page you're looking for doesn't exist or may have been moved.
                </p>
              </div>
              
              <div className="space-y-3">
                <Button asChild className="w-full">
                  <Link href="/">
                    <Home className="mr-2 h-4 w-4" />
                    Back to Home
                  </Link>
                </Button>
                
                <Button variant="outline" asChild className="w-full">
                  <Link href="/prompt/submit">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Submit a Prompt
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 