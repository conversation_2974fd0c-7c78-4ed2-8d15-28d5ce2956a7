import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON>eader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function SubmitPromptLoading() {
  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mx-auto max-w-3xl">
        <CardHeader>
          <Skeleton className="h-6 w-16 mb-2" /> {/* Back Button */}
          <Skeleton className="h-8 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent className="space-y-6">
          {/* NLP Analysis Toggle */}
          <div className="grid grid-cols-4 items-center gap-4 border-b pb-6">
            <div></div>
            <div className="col-span-3">
              <Skeleton className="h-16 w-full" />
            </div>
          </div>

          {/* Title */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>

          {/* Prompt Text */}
          <div className="grid grid-cols-4 items-start gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-48 w-full" /></div>
          </div>

          {/* Image Upload */}
          <div className="grid grid-cols-4 items-start gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-24 w-full" /></div>
          </div>
          
          {/* Category */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>

          {/* AI Tool */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>

          {/* AI Model */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>

          {/* Tags */}
          <div className="grid grid-cols-4 items-start gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>

          {/* Collection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>

          {/* Visibility */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-6 w-1/2" /></div>
          </div>

          {/* Advanced Options Toggle */}
          <div className="grid grid-cols-4 items-center gap-4 border-t pt-6">
            <div></div>
            <div className="col-span-3"><Skeleton className="h-16 w-full" /></div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Skeleton className="h-4 w-24" /> {/* Required fields text */}
          <div className="flex gap-2">
            <Skeleton className="h-10 w-20" /> {/* Cancel Button */}
            <Skeleton className="h-10 w-32" /> {/* Submit Button */}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
} 