"use client"

import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle2, ArrowRight, Sparkles, Users, User, Bookmark } from "lucide-react" // Added Bookmark icon
import Link from "next/link"
import AddToCollectionPopover from "@/components/add-to-collection-popover" // Import the popover

export default function RemixSuccessPage() {
  const searchParams = useSearchParams()
  const isPublic = searchParams.get("public") === "true"
  const newPromptId = searchParams.get("id"); // Get the new prompt ID from query params

  // Placeholder functions for collection actions
  const handleAddToCollection = async (promptId: string, collectionId: string) => {
      console.log(`Adding prompt ${promptId} to collection ${collectionId}`);
      // TODO: Implement API call
  }

  const handleCreateAndAddToCollection = async (promptId: string, collectionName: string) => {
      console.log(`Creating collection "${collectionName}" and adding prompt ${promptId}`);
      // TODO: Implement API call
  }


  return (
    <div className="container mx-auto px-4 py-16">
      <Card className="mx-auto max-w-md text-center">
        <CardHeader>
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <CheckCircle2 className="h-10 w-10 text-green-600" />
          </div>
          <CardTitle className="text-2xl">Remix {isPublic ? "Published" : "Saved"}!</CardTitle>
          <CardDescription>
            {isPublic
              ? "Your remixed prompt has been published and is now available to the community."
              : "Your remixed prompt has been saved to your private collection."}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-muted p-4">
            <div className="flex items-center justify-center gap-2 text-sm font-medium">
              <Sparkles className="h-4 w-4 text-purple-500" />
              <span>Remix created successfully</span>
            </div>
            <p className="mt-2 text-sm text-muted-foreground">
              {isPublic
                ? "Thank you for contributing to the community! Your remix will help others discover new ways to use AI prompts."
                : "You can find your remixed prompt in your personal collection. Feel free to edit it further or publish it later."}
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col gap-3">
          {/* Add to Collection Button/Popover */}
          {newPromptId && ( // Only show if we have a prompt ID
              <AddToCollectionPopover
                  promptId={newPromptId}
                  isOwnPrompt={true} // Since this is a newly created remix by the current user
                  onAddToCollection={handleAddToCollection}
                  onCreateAndAddToCollection={handleCreateAndAddToCollection}
                  trigger={
                      <Button variant="outline" className="w-full">
                          <Bookmark className="mr-2 h-4 w-4" /> Add to Collection
                      </Button>
                  }
                  // isSavedInCollections={...} // Pass actual data here if needed
              />
          )}

          <Button asChild className="w-full bg-purple-600 hover:bg-purple-700">
            <Link href={isPublic ? "/explore" : "/saved"}>
              {isPublic ? <Users className="mr-2 h-4 w-4" /> : <User className="mr-2 h-4 w-4" />}
              {isPublic ? "Explore More Prompts" : "View My Saved Prompts"}
            </Link>
          </Button>
          <Button asChild variant="outline" className="w-full">
            <Link href="/">
              Go to Home
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
