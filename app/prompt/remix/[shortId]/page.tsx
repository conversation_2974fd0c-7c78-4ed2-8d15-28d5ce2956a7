"use client"
import { useState, useEffect, useMemo, use } from "react"
// props should already contain the plain string; unwrap before
// const { shortId } = await params  // ← if this must stay async, move it to a Server Component
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { 
  X, Upload, Loader2, CheckCircle, PlusCircle, Lock, Globe, ArrowLeft, Brain 
} from "lucide-react"
import { allTools } from "@/lib/data/tools"
import { allTags } from "@/lib/data/tags"
import { allCategories } from "@/lib/data/categories"
import { 
  getAIModelsForTool, 
  createPrompt, 
  getUserCollections, 
  addPromptToCollection, 
  getPromptByShortId, 
  createCollection 
} from "@/lib/api-services"
import type { AIModel as BaseAIModel, Collection, CreatePromptData, Prompt } from "@/lib/types"
import { createTitleSlug } from "@/lib/utils/url-helpers"
import { createBrowserClient } from "@supabase/ssr"
import { toast } from "sonner"
import { Combobox, type ComboboxOption } from "@/components/ui/combobox"
import { TagInput, type TagOption } from "@/components/ui/tag-input"
import CreateCollectionDialog from "@/components/create-collection-dialog"
import { Switch } from "@/components/ui/switch"
import { DragDropImageUpload } from "@/components/ui/drag-drop-image-upload"
import MarkdownPromptEditor from "@/components/ui/markdown-prompt-editor"

// Extended AIModel interface with displayName property
interface AIModel extends BaseAIModel {
  displayName?: string;
}

interface PageProps {
  params: Promise<{
    shortId: string
  }>
}

export default function RemixPromptPage({ params }: PageProps) {
  // Unwrap the params Promise using React.use()
  const { shortId } = use(params)
  
  const router = useRouter()
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
  
  // Original prompt data
  const [originalPrompt, setOriginalPrompt] = useState<Prompt | null>(null)
  const [isLoadingOriginal, setIsLoadingOriginal] = useState(true)
  
  // Form state
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [promptText, setPromptText] = useState("")
  const [instructions, setInstructions] = useState("")
  const [exampleInput, setExampleInput] = useState("")
  const [exampleOutput, setExampleOutput] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedTool, setSelectedTool] = useState<string>("") 
  const [selectedAIModel, setSelectedAIModel] = useState<string>("") 
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isPublic, setIsPublic] = useState(true)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false) // Default to false for cleaner initial UI
  const [userEnteredAiModel, setUserEnteredAiModel] = useState<string>("")
  
  // Tags state
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  
  // Collections state
  const [collections, setCollections] = useState<Collection[]>([])
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null)
  const [isLoadingCollections, setIsLoadingCollections] = useState(false)
  const [isCreateCollectionDialogOpen, setIsCreateCollectionDialogOpen] = useState(false)
  const [myPromptsCollectionId, setMyPromptsCollectionId] = useState<string | null>(null)
  
  // Models state
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]) 
  const [isLoadingModels, setIsLoadingModels] = useState(false)
  
  // User state
  const [user, setUser] = useState<any>(null)
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)

  // Error state for form validation
  const [errors, setErrors] = useState({
    title: false,
    promptText: false,
    category: false,
    tool: false,
    tags: false
  })

  // Memoized options for better performance
  const categoryOptions = useMemo(() => 
    allCategories
      .filter(cat => cat.id != null)
      .map(cat => ({ value: String(cat.id), label: cat.name })), 
    []
  )
  
  const toolOptions = useMemo(() => 
    allTools
      .filter(tool => tool.id != null)
      .map(tool => ({ value: String(tool.id), label: tool.name })), 
    []
  )
  
  const tagOptions = useMemo(() => 
    allTags
      .filter(tag => tag.id != null)
      .map(tag => ({ value: String(tag.id), label: tag.name })), 
    []
  )
  
  const modelOptions = useMemo(() => 
    availableModels
      .filter(model => model.id != null)
      .map(model => ({ 
        value: String(model.id), 
        label: model.displayName || model.name 
      })), 
    [availableModels]
  )

  const collectionOptions = useMemo(() => 
    collections
      .filter(collection => collection.defaultType !== 'saved_prompts')
      .map((collection) => ({
        value: collection.id,
        label: collection.name
      })), 
    [collections]
  )

  // Handler for creating new collection
  const handleCreateCollection = async (name: string, description: string | null, imageFile: File | null, isPublic: boolean) => {
    try {
      const newCollection = await createCollection(user.id, {
        name,
        description,
        imageFile,
        is_public: isPublic
      })
      
      setCollections(prev => [...prev, newCollection])
      setSelectedCollection(newCollection.id)
      
      toast.success("Collection created successfully!", {
        description: `"${name}" has been added to your collections.`,
      })
      
      return newCollection
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error creating collection:', error)
      }
      toast.error("Failed to create collection", {
        description: "Please try again later.",
      })
      throw error
    }
  }

  // Fetch original prompt and check authentication
  useEffect(() => {
    const checkAuthAndFetchPrompt = async () => {
      setIsCheckingAuth(true)
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session?.user) {
          router.push(`/sign-in?redirect=/prompt/remix/${shortId}`)
          return
        }
        setUser(session.user)
        
        // Fetch the original prompt
        setIsLoadingOriginal(true)
        try {
          const prompt = await getPromptByShortId(shortId)
          if (!prompt) {
            toast.error("Prompt not found", {
              description: "The prompt you're trying to remix doesn't exist.",
            })
            router.push('/')
            return
          }
          
          setOriginalPrompt(prompt)
          
          // Pre-fill form with original prompt data but modify title
          setTitle(`${prompt.title} (Remix)`)
          setDescription(prompt.description || "")
          setPromptText(prompt.text || "")
          setInstructions(prompt.instructions || "")
          setExampleInput(prompt.exampleInput || "")
          setExampleOutput(prompt.exampleOutput || "")
          
          // Set category
          if (typeof prompt.category === 'object' && prompt.category?.id) {
            setSelectedCategory(prompt.category.id.toString())
          }
          
          // Set tool
          if (typeof prompt.tool === 'object' && prompt.tool?.id) {
            setSelectedTool(prompt.tool.id.toString())
          }
          
          // Set AI model
          if (prompt.ai_model && typeof prompt.ai_model === 'object' && prompt.ai_model.id) {
            setSelectedAIModel(prompt.ai_model.id.toString())
          } else if (prompt.user_entered_ai_model) {
            setUserEnteredAiModel(prompt.user_entered_ai_model)
          }
          
          // Set tags
          if (prompt.tags && Array.isArray(prompt.tags)) {
            const tagIds = prompt.tags
              .filter(tag => tag && typeof tag === 'object' && tag.id != null)
              .map(tag => String(tag.id))
              .filter(id => id !== 'null' && id !== 'undefined')
            setSelectedTags(tagIds)
          }
          
          // Set image preview if available
          if (prompt.imageUrl) {
            setImagePreview(prompt.imageUrl)
          }
          
          // Show advanced options if original has them
          if (prompt.instructions || prompt.exampleInput || prompt.exampleOutput) {
            setShowAdvancedOptions(true)
          }
          
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Error fetching original prompt:', error)
          }
          toast.error("Failed to load original prompt", {
            description: "Please try again later.",
          })
          router.push('/')
        } finally {
          setIsLoadingOriginal(false)
        }
        
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error checking auth:', error)
        }
        router.push(`/sign-in?redirect=/prompt/remix/${shortId}`)
      } finally {
        setIsCheckingAuth(false)
      }
    }
    
    checkAuthAndFetchPrompt()
  }, [router, supabase, shortId])

  // Effect to fetch AI models when tool changes
  useEffect(() => {
    const fetchModels = async () => {
      if (!selectedTool) {
        setAvailableModels([])
        return
      }
      
      setIsLoadingModels(true)
      try {
        const selectedToolObj = allTools.find(tool => String(tool.id) === selectedTool)
        if (!selectedToolObj) {
          setAvailableModels([])
          return
        }
        
        const models = await getAIModelsForTool(selectedToolObj.slug)
        const formattedModels = models.map(model => ({
          ...model,
          displayName: model.provider ? `${model.name} (${model.provider})` : model.name
        })).sort((a, b) => a.name.localeCompare(b.name))
        setAvailableModels(formattedModels)
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching AI models:', error)
        }
        setAvailableModels([])
      } finally {
        setIsLoadingModels(false)
      }
    }
    
    fetchModels()
  }, [selectedTool])

  // Effect to fetch user collections
  useEffect(() => {
    const fetchCollections = async () => {
      if (!user) return
      
      setIsLoadingCollections(true)
      try {
        const userCollections = await getUserCollections(user.id, { includePrivate: true })
        setCollections(userCollections)
        
        // Find "My Prompts" collection
        const myPromptsCollection = userCollections.find(
          collection => collection.name === "My Prompts" && collection.isDefault
        )
        if (myPromptsCollection) {
          setMyPromptsCollectionId(myPromptsCollection.id)
          setSelectedCollection(myPromptsCollection.id) // Default to My Prompts
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching collections:', error)
        }
      } finally {
        setIsLoadingCollections(false)
      }
    }
    
    fetchCollections()
  }, [user])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    if (!user) {
      toast.error("Please sign in to submit a prompt")
      return
    }

    // Client-side validation
    const newErrors = {
      title: !title.trim(),
      promptText: !promptText.trim(),
      category: !selectedCategory,
      tool: !selectedTool,
      tags: selectedTags.length === 0
    }

    setErrors(newErrors)

    if (Object.values(newErrors).some(Boolean)) {
      toast.error("Please fill in all required fields")
      return
    }

    setIsSubmitting(true)
    setShowSuccessAnimation(false)
    
    try {
      const promptData: CreatePromptData = {
        title: title.trim(),
        description: description?.trim() || undefined,
        promptText: promptText.trim(),
        instructions: instructions?.trim() || undefined,
        exampleInput: exampleInput?.trim() || undefined,
        exampleOutputText: exampleOutput?.trim() || undefined,
        categoryId: Number(selectedCategory),
        toolId: Number(selectedTool),
        tagIds: selectedTags.map(Number),
        isPublic,
        aiModelId: selectedAIModel ? Number(selectedAIModel) : null,
        userEnteredAiModel: userEnteredAiModel.trim() || undefined,
        imageFile,
        // Add original prompt reference for remix tracking
        originalPromptId: originalPrompt?.id,
      }
      
      const result = await createPrompt(user.id, promptData)
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create prompt')
      }
      
      // Add to collection if selected
      if (selectedCollection && result.promptId) {
        try {
          await addPromptToCollection(result.promptId, selectedCollection, user.id)
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Failed to add to collection, but prompt was created:', error)
          }
        }
      }
      
      setShowSuccessAnimation(true)
      
      toast.success("Prompt remixed successfully!", {
        description: "Your remixed prompt is now live and helping the community.",
      })
      
      setIsRedirecting(true)
      setIsSubmitting(false)
      
      // Redirect to the new prompt's page
      const redirectUrl = result.slug ? `/${result.slug}` : '/saved'
      
      setTimeout(() => {
        router.push(redirectUrl)
      }, 1500)

    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error creating remixed prompt:', error)
      }
      toast.error("Failed to remix prompt", {
        description: error instanceof Error ? error.message : "An error occurred.",
      })
      setIsSubmitting(false)
      setShowSuccessAnimation(false)
    }
  }

  const renderCollectionOption = (option: ComboboxOption) => {
    const collection = collections.find(c => c.id === option.value)
    return (
      <div className="flex items-center gap-2">
        {collection?.isPublic ? (
          <Globe className="w-4 h-4 text-muted-foreground" />
        ) : (
          <Lock className="w-4 h-4 text-muted-foreground" />
        )}
        <span>{option.label}</span>
      </div>
    )
  }

  const renderCollectionValue = (option: ComboboxOption) => {
    const collection = collections.find(c => c.id === option.value)
    return (
      <div className="flex items-center gap-2">
        {collection?.isPublic ? (
          <Globe className="w-4 h-4 text-muted-foreground" />
        ) : (
          <Lock className="w-4 h-4 text-muted-foreground" />
        )}
        <span>{option.label}</span>
      </div>
    )
  }

  if (isCheckingAuth || isLoadingOriginal) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="mx-auto max-w-3xl">
          <CardHeader>
            <div className="h-6 w-16 mb-2 bg-muted animate-pulse rounded" />
            <div className="h-8 w-3/4 mb-2 bg-muted animate-pulse rounded" />
            <div className="h-4 w-1/2 bg-muted animate-pulse rounded" />
          </CardHeader>
          <CardContent className="space-y-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="grid grid-cols-4 items-center gap-4">
                <div className="h-4 w-full bg-muted animate-pulse rounded" />
                <div className="col-span-3 h-10 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="h-10 w-20 bg-muted animate-pulse rounded" />
            <div className="h-10 w-32 bg-muted animate-pulse rounded" />
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mx-auto max-w-3xl relative">
        {/* Success Animation Overlay */}
        {showSuccessAnimation && (
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <CheckCircle className="w-16 h-16 text-accent-green animate-pulse" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-foreground">🎭 Prompt Remixed!</h3>
                <p className="text-muted-foreground mt-3 text-lg">
                  Your remixed prompt is now live and helping the community master AI.
                </p>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Taking you to your new prompt...</span>
              </div>
            </div>
          </div>
        )}
        
        <CardHeader>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => router.back()} 
            className="mb-2 self-start pl-0 text-muted-foreground"
            disabled={isSubmitting || isRedirecting}
          >
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
          <CardTitle className="text-2xl">Remix This Prompt</CardTitle>
          <CardDescription>
            Create your own version based on "{originalPrompt?.title}". Make it your own!
          </CardDescription>
        </CardHeader>
        
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {/* Original Prompt Reference */}
            {originalPrompt && (
              <div className="p-4 bg-muted/30 rounded-lg border">
                <div className="flex items-center gap-2 mb-2">
                  <Brain className="h-4 w-4 text-accent-green" />
                  <span className="text-sm font-medium text-accent-green">Original Prompt</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Remixing: <span className="font-medium text-foreground">"{originalPrompt.title}"</span>
                  {originalPrompt.user?.username && (
                    <span> by {originalPrompt.user.username}</span>
                  )}
                </p>
              </div>
            )}

            {/* Title Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Title <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => {
                    setTitle(e.target.value)
                    if (errors.title && e.target.value.trim()) {
                      setErrors(prev => ({ ...prev, title: false }))
                    }
                  }}
                  placeholder="Give your remixed prompt a unique title..."
                  required
                  disabled={isSubmitting || isRedirecting}
                  className={errors.title ? "border-red-500 focus-visible:ring-red-500" : ""}
                />
                {errors.title && (
                  <p className="text-sm text-red-500 mt-1">Title is required</p>
                )}
              </div>
            </div>

            {/* Description Field - Now Optional */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <div className="col-span-3">
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe what makes your remix unique..."
                  className="min-h-[100px]"
                  disabled={isSubmitting || isRedirecting}
                />
                <p className="text-xs text-muted-foreground mt-1">Optional - helps others understand your improvements</p>
              </div>
            </div>

            {/* Prompt Text Field */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="prompt-text" className="text-right">
                Prompt Text <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-2">
                <div className={errors.promptText ? "border border-red-500 rounded-md" : ""}>
                  <MarkdownPromptEditor
                    value={promptText}
                    onChange={(value) => {
                      setPromptText(value)
                      if (errors.promptText && value.trim()) {
                        setErrors(prev => ({ ...prev, promptText: false }))
                      }
                    }}
                    disabled={isSubmitting || isRedirecting}
                  />
                </div>
                {errors.promptText && (
                  <p className="text-sm text-red-500">Prompt text is required</p>
                )}
                <p className="text-xs text-muted-foreground">
                  Modify the original prompt to improve it or add your own twist. Use <span className="bg-accent-green/20 text-accent-green px-1 py-0.5 rounded font-mono text-xs">[[replace this]]</span> for placeholders.
                </p>
              </div>
            </div>

            {/* Image Upload Field */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">Preview Image</Label>
              <div className="col-span-3">
                <DragDropImageUpload
                  imageFile={imageFile}
                  imagePreview={imagePreview}
                  onImageChange={(file, preview) => {
                    setImageFile(file)
                    setImagePreview(preview)
                  }}
                  disabled={isSubmitting || isRedirecting}
                />
                <p className="text-sm text-muted-foreground mt-1">Add a new image or keep the original.</p>
              </div>
            </div>

            {/* Category Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Category <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className={errors.category ? "border border-red-500 rounded-md" : ""}>
                  <Combobox
                    options={categoryOptions}
                    value={selectedCategory}
                    onChange={(value) => {
                      setSelectedCategory(value)
                      if (errors.category && value) {
                        setErrors(prev => ({ ...prev, category: false }))
                      }
                    }}
                    placeholder="Select a category..."
                    emptyText="No categories found."
                    disabled={isSubmitting || isRedirecting}
                  />
                </div>
                {errors.category && (
                  <p className="text-sm text-red-500 mt-1">Category is required</p>
                )}
              </div>
            </div>

            {/* AI Tool Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                AI Tool <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className={errors.tool ? "border border-red-500 rounded-md" : ""}>
                  <Combobox
                    options={toolOptions}
                    value={selectedTool}
                    onChange={(value) => {
                      setSelectedTool(value)
                      setSelectedAIModel("") // Reset AI model when tool changes
                      if (errors.tool && value) {
                        setErrors(prev => ({ ...prev, tool: false }))
                      }
                    }}
                    placeholder="Select an AI tool..."
                    emptyText="No AI tools found."
                    disabled={isSubmitting || isRedirecting}
                  />
                </div>
                {errors.tool && (
                  <p className="text-sm text-red-500 mt-1">AI tool is required</p>
                )}
              </div>
            </div>

            {/* AI Model Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">AI Model</Label>
              <div className="col-span-3">
                {selectedTool ? (
                  <div className="space-y-2">
                    <Combobox
                      options={modelOptions}
                      value={selectedAIModel}
                      onChange={setSelectedAIModel}
                      placeholder={isLoadingModels ? "Loading models..." : "Select an AI model..."}
                      emptyText="No AI models found."
                      disabled={isLoadingModels || isSubmitting || isRedirecting}
                    />
                    {isLoadingModels && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading available models...
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">Or enter manually:</span>
                      <Input
                        placeholder="e.g., GPT-4, Claude-3, Custom Model..."
                        value={userEnteredAiModel}
                        onChange={(e) => setUserEnteredAiModel(e.target.value)}
                        disabled={isSubmitting || isRedirecting}
                        className="flex-1"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground italic">
                    Select an AI tool first to see available models
                  </div>
                )}
              </div>
            </div>

            {/* Tags Selection */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right">
                Tags <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className={errors.tags ? "border border-red-500 rounded-md" : ""}>
                  <TagInput
                    options={tagOptions}
                    selectedTags={selectedTags}
                    onTagsChange={(tags) => {
                      setSelectedTags(tags)
                      if (errors.tags && tags.length > 0) {
                        setErrors(prev => ({ ...prev, tags: false }))
                      }
                    }}
                    placeholder="Search and select tags (minimum 1 required)..."
                    maxTags={5}
                    className={isSubmitting || isRedirecting ? 'opacity-50 pointer-events-none' : ''}
                  />
                </div>
                {errors.tags && (
                  <p className="text-sm text-red-500 mt-1">At least one tag is required</p>
                )}
              </div>
            </div>

            {/* Visibility Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Visibility</Label>
              <div className="col-span-3 flex items-center space-x-3">
                <Switch
                  id="isPublic"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                  disabled={isSubmitting || isRedirecting}
                />
                <div className="flex items-center space-x-2">
                  {isPublic ? (
                    <Globe className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Lock className="h-4 w-4 text-muted-foreground" />
                  )}
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">{isPublic ? 'Public' : 'Private'}</span>
                    <p className="text-sm text-muted-foreground">
                      {isPublic ? 'Visible to everyone' : 'Only visible to you'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Collection Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right flex items-center justify-between">
                Collection
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsCreateCollectionDialogOpen(true)}
                  className="h-7 px-2 text-xs ml-2"
                  disabled={isSubmitting || isRedirecting}
                >
                  <PlusCircle className="h-3 w-3 mr-1" />
                  New
                </Button>
              </Label>
              <div className="col-span-3">
                <Combobox
                  options={collectionOptions}
                  value={selectedCollection || ""}
                  onChange={(value) => setSelectedCollection(value || null)}
                  placeholder={isLoadingCollections ? "Loading collections..." : "Select a collection (optional)..."}
                  emptyText="No collections found."
                  disabled={isLoadingCollections || isSubmitting || isRedirecting}
                  renderOption={renderCollectionOption}
                  renderValue={renderCollectionValue}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Optional - prompts are saved to "My Prompts" collection by default.
                </p>
                {isLoadingCollections && (
                  <div className="flex items-center text-sm text-muted-foreground mt-1">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading your collections...
                  </div>
                )}
              </div>
            </div>

            {/* Advanced Options Toggle */}
            <div className="grid grid-cols-4 items-center gap-4 border-t pt-6">
              <div></div>
              <div className="col-span-3">
                <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border">
                  <div className="flex flex-col">
                    <h3 className="text-sm font-medium">Advanced Options</h3>
                    <p className="text-xs text-muted-foreground">Add instructions and examples</p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                    disabled={isSubmitting || isRedirecting}
                    className="ml-4"
                  >
                    {showAdvancedOptions ? 'Hide' : 'Show'} Advanced
                  </Button>
                </div>
              </div>
            </div>

            {/* Advanced Options */}
            {showAdvancedOptions && (
              <>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="instructions" className="text-right pt-2">Instructions</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="instructions"
                      value={instructions}
                      onChange={(e) => setInstructions(e.target.value)}
                      placeholder="Provide step-by-step instructions on how to use this prompt effectively..."
                      className="min-h-[80px]"
                      disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Guidance for using the prompt.</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="example-input" className="text-right pt-2">Example Input</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="example-input"
                      value={exampleInput}
                      onChange={(e) => setExampleInput(e.target.value)}
                      placeholder="Show an example of what input users should provide..."
                      className="min-h-[80px]"
                      disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Show what input works well.</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="example-output" className="text-right pt-2">Example Output</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="example-output"
                      value={exampleOutput}
                      onChange={(e) => setExampleOutput(e.target.value)}
                      placeholder="Show what kind of output users can expect..."
                      className="min-h-[80px]"
                      disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Show typical output.</p>
                  </div>
                </div>
              </>
            )}
          </CardContent>
          
          <CardFooter className="flex justify-between items-center pt-6">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => router.back()} 
              disabled={isSubmitting || isRedirecting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || isRedirecting}
              className={`transition-all duration-300 ${
                showSuccessAnimation 
                  ? 'bg-accent-green hover:bg-accent-green text-white shadow-lg shadow-accent-green/30' 
                  : 'bg-accent-green hover:bg-accent-green/90'
              }`}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating Your Remix...
                </>
              ) : isRedirecting ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2 animate-pulse" />
                  Success! Redirecting...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Remix Prompt
                </>
              )}
            </Button>
          </CardFooter>
        </form>

        {/* Create Collection Dialog */}
        <CreateCollectionDialog
          isOpen={isCreateCollectionDialogOpen}
          onClose={() => setIsCreateCollectionDialogOpen(false)}
          onCreate={handleCreateCollection}
        />
      </Card>
    </div>
  )
}
