"use client"

import type React from "react"

import { useState, useEffect, useMemo } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { X, Upload, Loader2, CheckCircle, AlertCircle, Trash2 } from "lucide-react"
import { allTools } from "@/lib/data/tools"
import { allTags } from "@/lib/data/tags"
import { allCategories } from "@/lib/data/categories"
import { getPromptByShortId, updatePrompt, deletePrompt, getAIModelsForTool, getUser<PERSON>ollections, addPromptToCollection, getCategories, getTools, getTags } from "@/lib/api-services"
import type { AIModel as BaseAIModel, Collection, CreatePromptData } from "@/lib/types"

// Extended AIModel interface with displayName property
interface AIModel extends BaseAIModel {
  displayName?: string;
}
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { toast } from "@/components/ui/use-toast"
import { Combobox, type ComboboxOption } from "@/components/ui/combobox"
import { TagInput, type TagOption } from "@/components/ui/tag-input"
import { CreateCollectionModal } from "@/components/create-collection-modal"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"

export default function EditPromptPage() {
  const router = useRouter()
  const params = useParams()
  const shortId = params?.shortId as string
  const supabase = createClientComponentClient()
  
  // Form state
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [prompt, setPrompt] = useState<any>(null)
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [promptText, setPromptText] = useState("")
  const [instructions, setInstructions] = useState("")
  const [exampleInput, setExampleInput] = useState("")
  const [exampleOutput, setExampleOutput] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedTool, setSelectedTool] = useState<string>("") 
  const [selectedAIModel, setSelectedAIModel] = useState<string>("") 
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isPublic, setIsPublic] = useState(true)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(true) // Default to true for edit page
  const [userEnteredAiModel, setUserEnteredAiModel] = useState<string>("")
  
  // Tags state
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [tagInput, setTagInput] = useState("")
  
  // Collections state
  const [collections, setCollections] = useState<Collection[]>([])
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null)
  const [isLoadingCollections, setIsLoadingCollections] = useState(false)
  const [isCreateCollectionModalOpen, setIsCreateCollectionModalOpen] = useState(false)
  
  // Models state
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]) 
  const [isLoadingModels, setIsLoadingModels] = useState(false)
  
  // State for categories, tools, and tags for redirect URL construction
  const [categoriesData, setCategoriesData] = useState<Array<{id: number, slug: string}>>([])
  const [toolsData, setToolsData] = useState<Array<{id: number, slug: string}>>([])
  const [tagsData, setTagsData] = useState<Array<{id: number, slug: string}>>([])
  const [isLoadingData, setIsLoadingData] = useState(false)
  
  // User state
  const [user, setUser] = useState<any>(null)
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  
  // Delete dialog state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  
  // Error state
  const [error, setError] = useState<string | null>(null)

  // Check authentication and redirect if not logged in
  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true)
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          router.push('/login')
          return
        }
        setUser(user)
      } catch (error) {
        console.error('Authentication error:', error)
        router.push('/login')
      } finally {
        setIsCheckingAuth(false)
      }
    }
    
    checkAuth()
  }, [router, supabase.auth])

  // Fetch collections when user is authenticated
  useEffect(() => {
    const fetchCollections = async () => {
      if (!user) return
      
      setIsLoadingCollections(true)
      try {
        const collections = await getUserCollections(user.id, { includePrivate: true })
        setCollections(collections)
      } catch (error) {
        console.error('Error fetching collections:', error)
        toast({
          title: "Failed to load collections",
          description: "Your collections couldn't be loaded. You can still edit your prompt.",
          variant: "destructive",
        })
      } finally {
        setIsLoadingCollections(false)
      }
    }
    
    fetchCollections()
  }, [user])

  // Effect to fetch categories, tools, and tags data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoadingData(true)
      try {
        // Fetch categories, tools, and tags data
        const [categoriesResult, toolsResult, tagsResult] = await Promise.all([
          getCategories(),
          getTools(),
          getTags()
        ])
        
        setCategoriesData(categoriesResult.map(cat => ({ id: Number(cat.id), slug: cat.slug })))
        setToolsData(toolsResult.map(tool => ({ id: Number(tool.id), slug: tool.slug })))
        setTagsData(tagsResult.map(tag => ({ id: Number(tag.id), slug: tag.slug })))
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setIsLoadingData(false)
      }
    }
    
    fetchData()
  }, [])

  // Fetch prompt data
  useEffect(() => {
    const fetchPrompt = async () => {
      if (!shortId || isCheckingAuth) return
      
      setIsLoading(true)
      try {
        const promptData = await getPromptByShortId(shortId)
        
        if (!promptData) {
          setError("Prompt not found")
          return
        }
        
        // Check if the current user is the owner of the prompt
        if (user && promptData.user?.id !== user.id) {
          setError("You don't have permission to edit this prompt")
          return
        }
        
        setPrompt(promptData)
        
        // Populate form fields
        setTitle(promptData.title || "")
        setDescription(promptData.description || "")
        setPromptText(promptData.text || "")
        setInstructions(promptData.instructions || "")
        setExampleInput(promptData.exampleInput || "")
        setExampleOutput(promptData.exampleOutput || "")
        setIsPublic(promptData.isPublic !== false) // Default to true if not specified
        
        // Set category, tool, and AI model
        if (promptData.category && typeof promptData.category === 'object' && promptData.category.id) {
          setSelectedCategory(String(promptData.category.id))
        }
        
        if (promptData.tool && typeof promptData.tool === 'object' && promptData.tool.id) {
          setSelectedTool(String(promptData.tool.id))
          // Fetch AI models for this tool
          fetchModels(promptData.tool.slug)
        }
        
        if (promptData.ai_model?.id) {
          setSelectedAIModel(String(promptData.ai_model.id))
          setUserEnteredAiModel(`${promptData.ai_model.provider} - ${promptData.ai_model.name}`)
        }
        
        // Set tags
        if (promptData.tags && Array.isArray(promptData.tags)) {
          setSelectedTags(promptData.tags.map((tag: any) => String(tag.id)))
        }
        
        // Set image preview if available
        if (promptData.imageUrl) {
          setImagePreview(promptData.imageUrl)
        }
        
      } catch (error) {
        console.error('Error fetching prompt:', error)
        setError("Failed to load prompt data")
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchPrompt()
  }, [shortId, user, isCheckingAuth])
  
  // Fetch AI models when tool changes
  const fetchModels = async (toolSlug: string) => {
    if (!toolSlug) return
    
    setIsLoadingModels(true)
    try {
      const models = await getAIModelsForTool(toolSlug)
      // Add displayName property to each model
      const modelsWithDisplayName = models.map(model => ({
        ...model,
        displayName: `${model.provider} - ${model.name}`
      }))
      setAvailableModels(modelsWithDisplayName)
    } catch (error) {
      console.error('Error fetching AI models:', error)
    } finally {
      setIsLoadingModels(false)
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    console.log('=== SUBMIT START ===');
    console.log('User:', user ? 'Exists' : 'Missing');
    console.log('Prompt:', prompt ? 'Exists' : 'Missing');
    
    if (!user || !prompt) {
      console.error('Missing user or prompt data');
      return;
    }
    
    // Validate required fields
    console.log('Validating fields:', {
      title: !!title,
      description: !!description,
      promptText: !!promptText,
      selectedCategory: selectedCategory,
      selectedTool: selectedTool,
      selectedTagsCount: selectedTags.length
    });
    
    if (!title || !description || !promptText || !selectedCategory || !selectedTool || selectedTags.length === 0) {
      toast({
        title: "Missing required fields",
        description: "Please fill in all required fields marked with an asterisk (*).",
        variant: "destructive",
      })
      console.error('Validation failed: Missing required fields');
      return
    }
    
    setIsSubmitting(true)
    try {
      // Prepare data for image upload if needed
      let imageUrl = prompt.imageUrl
      console.log('Original imageUrl:', imageUrl);
      console.log('ImageFile exists:', !!imageFile);
      console.log('ImagePreview exists:', !!imagePreview);
      
      // If there's a new image file, upload it first
      if (imageFile) {
        console.log('Processing image upload...');
        console.log('Image file details:', {
          name: imageFile.name,
          size: imageFile.size,
          type: imageFile.type
        });
        
        const fileExt = imageFile.name.split('.').pop()
        const filePath = `prompt-images/${user.id}/${Date.now()}.${fileExt}`
        console.log('Generated file path:', filePath);
        
        // Upload the image to storage
        console.log('Starting Supabase upload...');
        const uploadResult = await supabase.storage
          .from('prompt-images')
          .upload(filePath, imageFile)
        
        console.log('Upload result:', JSON.stringify(uploadResult, null, 2));
        
        if (uploadResult.error) {
          console.error('Upload error:', JSON.stringify(uploadResult.error, null, 2));
          throw uploadResult.error;
        }
        
        // Get the public URL
        console.log('Getting public URL...');
        const publicUrlResult = supabase.storage.from('prompt-images').getPublicUrl(filePath);
        console.log('Public URL result:', JSON.stringify(publicUrlResult, null, 2));
        
        imageUrl = publicUrlResult.data.publicUrl;
        console.log('New image URL:', imageUrl);
      } else if (imagePreview === null) {
        // If image preview is null but was previously set, it means user removed the image
        console.log('Image removed by user, setting imageUrl to null');
        imageUrl = null;
      }
      
      // Parse tag IDs to numbers
      const tagIds = selectedTags.map(tagId => Number(tagId))
      
      // Update prompt using the API service
      const { success, error } = await updatePrompt(prompt.id, {
        title,
        description,
        promptText,
        instructions,
        exampleInput,
        exampleOutputText: exampleOutput,
        categoryId: Number(selectedCategory),
        toolId: Number(selectedTool),
        aiModelId: selectedAIModel ? Number(selectedAIModel) : null,
        userEnteredAiModel: userEnteredAiModel,
        isPublic,
        userId: user.id,
        tagIds,
        imageUrl
      })
      
      if (!success) throw error
      
      // If a collection is selected, add the prompt to it
      if (selectedCollection && selectedCollection !== 'create-new') {
        await addPromptToCollection(user.id, prompt.id, selectedCollection)
      }
      
      toast({
        title: "Prompt updated",
        description: "Your prompt has been successfully updated.",
      })
      
      // Construct the full URL for the prompt detail page
      // Get the category and tool slugs from the selected options
      const selectedCategoryObj = allCategories.find(cat => String(cat.id) === selectedCategory)
      const selectedToolObj = allTools.find(tool => String(tool.id) === selectedTool)
      
      // Create a slug from the title
      const titleSlug = title.toLowerCase()
        .replace(/[^\\w\\s-]/g, '') // Remove special characters
        .replace(/\\s+/g, '-')     // Replace spaces with hyphens
        .replace(/-+/g, '-')      // Replace multiple hyphens with a single one
      
      if (selectedCategoryObj && selectedToolObj) {
        // Use the full URL format
        router.push(`/prompt/${selectedCategoryObj.slug}/${selectedToolObj.slug}/${titleSlug}/${shortId}`)
      } else {
        // Fallback to the simple format if category or tool is not found
        router.push(`/prompt/${shortId}`)
      }
      
    } catch (error: any) {
      console.error('Error updating prompt:', error)
      console.error('Error details:', JSON.stringify(error, null, 2))
      console.error('Error name:', error?.name)
      console.error('Error message:', error?.message)
      console.error('Error stack:', error?.stack)
      
      // Check if it's a Supabase storage error
      if (error?.statusCode) {
        console.error('Supabase status code:', error.statusCode)
        console.error('Supabase error message:', error.error?.message)
      }
      
      toast({
        title: "Failed to update prompt",
        description: error.message || "An error occurred while updating your prompt.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }
