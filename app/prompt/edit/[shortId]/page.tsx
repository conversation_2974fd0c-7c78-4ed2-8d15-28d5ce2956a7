"use client"

import type React from "react"

import { useState, useEffect, useMemo } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Upload, Loader2, CheckCircle, Lock, Globe, AlertCircle, Trash2, ArrowLeft } from "lucide-react"
import { allTools } from "@/lib/data/tools"
import { allTags } from "@/lib/data/tags"
import { allCategories } from "@/lib/data/categories"
import { getPromptByShortId, updatePrompt, deletePrompt, getAIModelsForTool, createCollection } from "@/lib/api-services"
import type { AIModel as BaseAIModel, <PERSON>reate<PERSON>rom<PERSON>D<PERSON>, Prompt, Collection } from "@/lib/types"
import type { User } from "@supabase/supabase-js"
import { createBrowserClient } from "@supabase/ssr"
import { toast } from "sonner"
import { Combobox } from "@/components/ui/combobox"
import { TagInput } from "@/components/ui/tag-input"
import { Switch } from "@/components/ui/switch"
import { DragDropImageUpload } from "@/components/ui/drag-drop-image-upload"
import MarkdownPromptEditor from "@/components/ui/markdown-prompt-editor"
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog"

// Extended AIModel interface with displayName property
interface AIModel extends BaseAIModel {
  displayName?: string;
}

// Extended interface for update data that includes additional properties
interface UpdatePromptData extends Partial<CreatePromptData> {
  imageFile?: File;
  removeCurrentImage?: boolean;
}

export default function EditPromptPage() {
  const router = useRouter()
  const params = useParams()
  const shortId = params?.shortId as string
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
  
  // Form state
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)
  const [prompt, setPrompt] = useState<Prompt | null>(null)
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [promptText, setPromptText] = useState("")
  const [instructions, setInstructions] = useState("")
  const [exampleInput, setExampleInput] = useState("")
  const [exampleOutput, setExampleOutput] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedTool, setSelectedTool] = useState<string>("") 
  const [selectedAIModel, setSelectedAIModel] = useState<string>("") 
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageRemoved, setImageRemoved] = useState(false)
  const [isPublic, setIsPublic] = useState(true)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)
  const [userEnteredAiModel, setUserEnteredAiModel] = useState<string>("")
  
  // Tags state
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  
  // Collections state (needed for handleCreateCollection)
  const [collections, setCollections] = useState<Collection[]>([])
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null)
  
  // Models state
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]) 
  const [isLoadingModels, setIsLoadingModels] = useState(false)
  
  // User state
  const [user, setUser] = useState<User | null>(null)
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  
  // Error state
  const [error, setError] = useState<string | null>(null)
  const [errors, setErrors] = useState({
    title: false,
    promptText: false,
    category: false,
    tool: false,
    tags: false
  })

  // Delete dialog state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Memoized options for better performance
  const categoryOptions = useMemo(() => 
    allCategories.map(cat => ({ value: String(cat.id), label: cat.name })), 
    []
  )
  
  const toolOptions = useMemo(() => 
    allTools.map(tool => ({ value: String(tool.id), label: tool.name })), 
    []
  )
  
  const tagOptions = useMemo(() => 
    allTags.map(tag => ({ value: String(tag.id), label: tag.name })), 
    []
  )
  
  const modelOptions = useMemo(() => 
    availableModels.map(model => ({ 
      value: String(model.id), 
      label: model.displayName || model.name 
    })), 
    [availableModels]
  )

  // Handler for creating new collection
  const handleCreateCollection = async (name: string, description: string | null, imageFile: File | null, isPublic: boolean) => {
    try {
      const newCollection = await createCollection(user!.id, {
        name,
        description,
        imageFile,
        is_public: isPublic
      })
      
      setCollections(prev => [...prev, newCollection])
      setSelectedCollection(newCollection.id)
      
      toast.success("Collection created successfully!", {
        description: `"${name}" has been added to your collections.`,
      })
      
      return newCollection
    } catch (error) {
      console.error('Error creating collection:', error)
      toast.error("Failed to create collection", {
        description: "Please try again later.",
      })
      throw error
    }
  }

  // Check authentication and redirect if not logged in
  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true)
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session?.user) {
          router.push(`/sign-in?redirect=/prompt/edit/${shortId}`)
          return
        }
        setUser(session.user)
      } catch (error) {
        console.error('Error checking auth:', error)
        router.push(`/sign-in?redirect=/prompt/edit/${shortId}`)
      } finally {
        setIsCheckingAuth(false)
      }
    }
    
    checkAuth()
  }, [router, supabase, shortId])
  
  // Effect to fetch AI models when tool changes
  useEffect(() => {
    const fetchModels = async () => {
      if (!selectedTool) {
        setAvailableModels([])
        return
      }
      
      setIsLoadingModels(true)
      try {
        const selectedToolObj = allTools.find(tool => String(tool.id) === selectedTool)
        if (!selectedToolObj) {
          setAvailableModels([])
          return
        }
        
        const models = await getAIModelsForTool(selectedToolObj.slug)
        const formattedModels = models.map(model => ({
          ...model,
          displayName: model.provider ? `${model.name} (${model.provider})` : model.name
        })).sort((a, b) => a.name.localeCompare(b.name))
        setAvailableModels(formattedModels)
      } catch (error) {
        console.error('Error fetching AI models:', error)
        setAvailableModels([])
      } finally {
        setIsLoadingModels(false)
      }
    }
    
    fetchModels()
  }, [selectedTool])

  // Fetch prompt data
  useEffect(() => {
    const fetchPrompt = async () => {
      if (!shortId || isCheckingAuth || !user) return
      
      setIsLoading(true)
      try {
        const promptData = await getPromptByShortId(shortId)
        
        if (!promptData) {
          setError("Prompt not found.")
          return
        }
        
        if (promptData.user?.id !== user.id) {
          setError("You don't have permission to edit this prompt.")
          return
        }
        
        setPrompt(promptData)
        setTitle(promptData.title || "")
        setDescription(promptData.description || "")
        setPromptText(promptData.text || "")
        setInstructions(promptData.instructions || "")
        setExampleInput(promptData.exampleInput || "")
        setExampleOutput(promptData.exampleOutput || "")
        setIsPublic(promptData.isPublic !== false)
        
        if (promptData.category && typeof promptData.category === 'object' && promptData.category.id) {
          setSelectedCategory(String(promptData.category.id))
        }
        
        if (promptData.tool && typeof promptData.tool === 'object' && promptData.tool.id) {
          setSelectedTool(String(promptData.tool.id))
        }
        
        if (promptData.ai_model?.id) {
          setSelectedAIModel(String(promptData.ai_model.id))
        }
        setUserEnteredAiModel(promptData.user_entered_ai_model || "")
        
        if (promptData.tags && Array.isArray(promptData.tags)) {
          const tagIds = promptData.tags.map((tag: { id?: number | string } | string) => 
            typeof tag === 'object' && tag.id ? String(tag.id) : String(tag)
          ).filter(Boolean)
          setSelectedTags(tagIds)
        }
        
        if (promptData.imageUrl) {
          setImagePreview(promptData.imageUrl)
          setImageRemoved(false)
        }
        
        // Show advanced options if original has them
        if (promptData.instructions || promptData.exampleInput || promptData.exampleOutput) {
          setShowAdvancedOptions(true)
        }
        
        setError(null)
      } catch (error) {
        console.error('Error fetching prompt for editing:', error)
        setError("Failed to load prompt data. Please try again.")
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchPrompt()
  }, [shortId, user, isCheckingAuth])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    if (!user || !prompt) {
      toast.error("User or prompt data is missing. Please refresh.")
      return
    }

    // Client-side validation
    const newErrors = {
      title: !title.trim(),
      promptText: !promptText.trim(),
      category: !selectedCategory,
      tool: !selectedTool,
      tags: selectedTags.length === 0
    }

    setErrors(newErrors)

    if (Object.values(newErrors).some(Boolean)) {
      toast.error("Please fill in all required fields")
      return
    }

    setIsSubmitting(true)
    setShowSuccessAnimation(false)
    
    try {
      const updateData: UpdatePromptData = {
        title: title.trim(),
        description: description?.trim() || undefined,
        promptText: promptText.trim(),
        instructions: instructions?.trim() || undefined,
        exampleInput: exampleInput?.trim() || undefined,
        exampleOutputText: exampleOutput?.trim() || undefined,
        categoryId: Number(selectedCategory),
        toolId: Number(selectedTool),
        tagIds: selectedTags.map(Number),
        isPublic,
        aiModelId: selectedAIModel ? Number(selectedAIModel) : null,
        userEnteredAiModel: userEnteredAiModel.trim() || undefined,
        imageUrl: imageRemoved ? null : (imageFile ? undefined : imagePreview)
      };

      if (imageFile) {
        updateData.imageFile = imageFile
      } else if (imageRemoved) {
        updateData.removeCurrentImage = true
      }
      
      const result = await updatePrompt(prompt.id, user.id, updateData)
      
      if (!result.success) {
        throw new Error(result.error || 'Update failed')
      }
      
      setShowSuccessAnimation(true)
      
      toast.success("Prompt updated successfully!", {
        description: `"${title}" has been updated and is now live.`,
      })
      
      setIsRedirecting(true)
      setIsSubmitting(false)

      // Use the updatedSlug from the result for redirection
      const redirectUrl = result.updatedSlug 
        ? `/${result.updatedSlug}`
        : `/prompt/${shortId}`

      setTimeout(() => {
        router.push(redirectUrl)
      }, 1500)

    } catch (error) {
      console.error('Error updating prompt:', error)
      toast.error("Failed to update prompt", {
        description: error instanceof Error ? error.message : "An error occurred.",
      })
      setIsSubmitting(false)
      setShowSuccessAnimation(false)
    }
  }

  // Handle prompt deletion
  const handleDelete = async () => {
    if (!user || !prompt) return
    
    setIsDeleting(true)
    try {
      const { success, error } = await deletePrompt(prompt.id, user.id)
      
      if (!success) {
        throw new Error(error?.message || "Failed to delete prompt")
      }

      toast.success("Prompt deleted successfully", {
        description: `"${prompt.title}" has been removed.`,
      })
      
      setIsDeleteDialogOpen(false)
      router.push('/saved')
      
    } catch (error) {
      console.error('Error deleting prompt:', error)
      toast.error("Failed to delete prompt", {
        description: error instanceof Error ? error.message : "An error occurred.",
      })
      setIsDeleting(false)
    }
  }

  if (isCheckingAuth || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="mx-auto max-w-3xl">
          <CardHeader>
            <div className="h-6 w-16 mb-2 bg-muted animate-pulse rounded" />
            <div className="h-8 w-3/4 mb-2 bg-muted animate-pulse rounded" />
            <div className="h-4 w-1/2 bg-muted animate-pulse rounded" />
          </CardHeader>
          <CardContent className="space-y-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="grid grid-cols-4 items-center gap-4">
                <div className="h-4 w-full bg-muted animate-pulse rounded" />
                <div className="col-span-3 h-10 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="h-10 w-24 bg-muted animate-pulse rounded" />
            <div className="h-10 w-32 bg-muted animate-pulse rounded" />
          </CardFooter>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="mx-auto max-w-3xl">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center gap-4">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-6 w-6" />
                <h2 className="text-xl font-semibold">{error}</h2>
              </div>
              <Button onClick={() => router.push('/')}>Go Home</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mx-auto max-w-3xl relative">
        {/* Success Animation Overlay */}
        {showSuccessAnimation && (
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <CheckCircle className="w-16 h-16 text-accent-green animate-pulse" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-foreground">✨ Prompt Updated!</h3>
                <p className="text-muted-foreground mt-3 text-lg">
                  Your prompt has been successfully updated and is now live.
                </p>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Taking you to your updated prompt...</span>
              </div>
            </div>
          </div>
        )}
        
        <CardHeader>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => router.back()} 
            className="mb-2 self-start pl-0 text-muted-foreground"
            disabled={isSubmitting || isRedirecting}
          >
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
          <CardTitle className="text-2xl">Edit Your Prompt</CardTitle>
          <CardDescription>
            Refine your prompt&apos;s details. Your contributions help the community!
          </CardDescription>
        </CardHeader>
        
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {/* Title Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Title <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => {
                    setTitle(e.target.value)
                    if (errors.title && e.target.value.trim()) {
                      setErrors(prev => ({ ...prev, title: false }))
                    }
                  }}
                  placeholder="Give your prompt a clear, descriptive title..."
                  required
                  disabled={isSubmitting || isRedirecting}
                  className={errors.title ? "border-red-500 focus-visible:ring-red-500" : ""}
                />
                {errors.title && (
                  <p className="text-sm text-red-500 mt-1">Title is required</p>
                )}
              </div>
            </div>

            {/* Description Field - Now Optional */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <div className="col-span-3">
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe what your prompt does and how it helps users..."
                  className="min-h-[100px]"
                  disabled={isSubmitting || isRedirecting}
                />
                <p className="text-xs text-muted-foreground mt-1">Optional - helps others understand your prompt better</p>
              </div>
            </div>

            {/* Prompt Text Field */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="prompt-text" className="text-right">
                Prompt Text <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-2">
                <div className={errors.promptText ? "border border-red-500 rounded-md" : ""}>
                  <MarkdownPromptEditor
                    value={promptText}
                    onChange={(value) => {
                      setPromptText(value)
                      if (errors.promptText && value.trim()) {
                        setErrors(prev => ({ ...prev, promptText: false }))
                      }
                    }}
                    disabled={isSubmitting || isRedirecting}
                  />
                </div>
                {errors.promptText && (
                  <p className="text-sm text-red-500">Prompt text is required</p>
                )}
                <p className="text-xs text-muted-foreground">
                  Use markdown formatting for better readability. Placeholders should be indicated with <span className="bg-accent-green/20 text-accent-green px-1 py-0.5 rounded font-mono text-xs">[[replace this]]</span> format.
                </p>
              </div>
            </div>
            
            {/* Image Upload Field */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">Preview Image</Label>
              <div className="col-span-3">
                <DragDropImageUpload
                  imageFile={imageFile}
                  imagePreview={imagePreview}
                  onImageChange={(file, preview) => {
                    setImageFile(file)
                    setImagePreview(preview)
                    // Derive imageRemoved from the new values instead of old state
                    const wasImageRemoved = !file && !preview && !!imagePreview
                    setImageRemoved(wasImageRemoved)
                  }}
                  disabled={isSubmitting || isRedirecting}
                />
                <p className="text-sm text-muted-foreground mt-1">Update or remove the image for your prompt.</p>
              </div>
            </div>

            {/* Category Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Category <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className={errors.category ? "border border-red-500 rounded-md" : ""}>
                  <Combobox
                    options={categoryOptions}
                    value={selectedCategory}
                    onChange={(value) => {
                      setSelectedCategory(value)
                      if (errors.category && value) {
                        setErrors(prev => ({ ...prev, category: false }))
                      }
                    }}
                    placeholder="Select a category..."
                    emptyText="No categories found."
                    disabled={isSubmitting || isRedirecting}
                  />
                </div>
                {errors.category && (
                  <p className="text-sm text-red-500 mt-1">Category is required</p>
                )}
              </div>
            </div>

            {/* AI Tool Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                AI Tool <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className={errors.tool ? "border border-red-500 rounded-md" : ""}>
                  <Combobox
                    options={toolOptions}
                    value={selectedTool}
                    onChange={(value) => {
                      setSelectedTool(value)
                      setSelectedAIModel("") // Reset AI model when tool changes
                      if (errors.tool && value) {
                        setErrors(prev => ({ ...prev, tool: false }))
                      }
                    }}
                    placeholder="Select an AI tool..."
                    emptyText="No AI tools found."
                    disabled={isSubmitting || isRedirecting}
                  />
                </div>
                {errors.tool && (
                  <p className="text-sm text-red-500 mt-1">AI tool is required</p>
                )}
              </div>
            </div>

            {/* AI Model Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">AI Model</Label>
              <div className="col-span-3">
                {selectedTool ? (
                  <div className="space-y-2">
                    <Combobox
                      options={modelOptions}
                      value={selectedAIModel}
                      onChange={setSelectedAIModel}
                      placeholder={isLoadingModels ? "Loading models..." : "Select an AI model..."}
                      emptyText="No AI models found."
                      disabled={isLoadingModels || isSubmitting || isRedirecting}
                    />
                    {isLoadingModels && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading available models...
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">Or enter manually:</span>
                      <Input
                        placeholder="e.g., GPT-4, Claude-3, Custom Model..."
                        value={userEnteredAiModel}
                        onChange={(e) => setUserEnteredAiModel(e.target.value)}
                        disabled={isSubmitting || isRedirecting}
                        className="flex-1"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground italic">
                    Select an AI tool first to see available models
                  </div>
                )}
              </div>
            </div>

            {/* Tags Selection */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right">
                Tags <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className={errors.tags ? "border border-red-500 rounded-md" : ""}>
                  <TagInput
                    options={tagOptions}
                    selectedTags={selectedTags}
                    onTagsChange={(tags) => {
                      setSelectedTags(tags)
                      if (errors.tags && tags.length > 0) {
                        setErrors(prev => ({ ...prev, tags: false }))
                      }
                    }}
                    placeholder="Search and select tags (minimum 1 required)..."
                    maxTags={5}
                    className={isSubmitting || isRedirecting ? 'opacity-50 pointer-events-none' : ''}
                  />
                </div>
                {errors.tags && (
                  <p className="text-sm text-red-500 mt-1">At least one tag is required</p>
                )}
              </div>
            </div>

            {/* Visibility Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Visibility</Label>
              <div className="col-span-3 flex items-center space-x-3">
                <Switch
                  id="isPublic"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                  disabled={isSubmitting || isRedirecting}
                />
                <div className="flex items-center space-x-2">
                  {isPublic ? (
                    <Globe className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Lock className="h-4 w-4 text-muted-foreground" />
                  )}
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">{isPublic ? 'Public' : 'Private'}</span>
                    <p className="text-sm text-muted-foreground">
                      {isPublic ? 'Visible to everyone' : 'Only visible to you'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Advanced Options Toggle */}
            <div className="grid grid-cols-4 items-center gap-4 border-t pt-6">
              <div></div>
              <div className="col-span-3">
                <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border">
                  <div className="flex flex-col">
                    <h3 className="text-sm font-medium">Advanced Options</h3>
                    <p className="text-xs text-muted-foreground">Add instructions and examples</p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                    disabled={isSubmitting || isRedirecting}
                    className="ml-4"
                  >
                    {showAdvancedOptions ? 'Hide' : 'Show'} Advanced
                  </Button>
                </div>
              </div>
            </div>

            {/* Advanced Options */}
            {showAdvancedOptions && (
              <>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="instructions" className="text-right pt-2">Instructions</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="instructions"
                      value={instructions}
                      onChange={(e) => setInstructions(e.target.value)}
                      placeholder="Provide step-by-step instructions on how to use this prompt effectively..."
                      className="min-h-[80px]"
                      disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Guidance for using the prompt.</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="example-input" className="text-right pt-2">Example Input</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="example-input"
                      value={exampleInput}
                      onChange={(e) => setExampleInput(e.target.value)}
                      placeholder="Show an example of what input users should provide..."
                      className="min-h-[80px]"
                      disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Show what input works well.</p>
                  </div>
                </div>

                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="example-output" className="text-right pt-2">Example Output</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="example-output"
                      value={exampleOutput}
                      onChange={(e) => setExampleOutput(e.target.value)}
                      placeholder="Show what kind of output users can expect..."
                      className="min-h-[80px]"
                      disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Show typical output.</p>
                  </div>
                </div>
              </>
            )}
          </CardContent>
          
          <CardFooter className="flex justify-between items-center pt-6">
            <Button 
              type="button" 
              variant="destructive" 
              onClick={() => setIsDeleteDialogOpen(true)} 
              disabled={isSubmitting || isRedirecting || isDeleting}
              className="flex items-center"
            >
              <Trash2 className="mr-2 h-4 w-4" /> 
              Delete Prompt
            </Button>
            
            <div className="flex gap-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => router.back()} 
                disabled={isSubmitting || isRedirecting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || isRedirecting}
                className={`transition-all duration-300 ${
                  showSuccessAnimation 
                    ? 'bg-accent-green hover:bg-accent-green text-white shadow-lg shadow-accent-green/30' 
                    : 'bg-accent-green hover:bg-accent-green/90'
                }`}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Updating Your Prompt...
                  </>
                ) : isRedirecting ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2 animate-pulse" />
                    Success! Redirecting...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Update Prompt
                  </>
                )}
              </Button>
            </div>
          </CardFooter>
        </form>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete this prompt?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete your prompt
                and remove it from our servers. Are you sure you want to continue?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleDelete} 
                disabled={isDeleting}
                className="bg-destructive hover:bg-destructive/90"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> 
                    Deleting...
                  </>
                ) : (
                  "Yes, delete prompt"
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </Card>
    </div>
  )
}
