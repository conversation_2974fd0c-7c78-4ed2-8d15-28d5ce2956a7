"use client"

import type React from "react"

import { useState, useEffect, useMemo } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { X, Upload, Loader2, CheckCircle, AlertCircle, Trash2 } from "lucide-react"
import { allTools } from "@/lib/data/tools"
import { allTags } from "@/lib/data/tags"
import { allCategories } from "@/lib/data/categories"
import { getPromptByShortId, updatePrompt, deletePrompt, getAIModelsForTool, getUser<PERSON>ollections, addPromptToCollection, getCategories, getTools, getTags } from "@/lib/api-services"
import type { AIModel as BaseAIModel, Collection, CreatePromptData } from "@/lib/types"

// Extended AIModel interface with displayName property
interface AIModel extends BaseAIModel {
  displayName?: string;
}
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { toast } from "@/components/ui/use-toast"
import { Combobox, type ComboboxOption } from "@/components/ui/combobox"
import { TagInput, type TagOption } from "@/components/ui/tag-input"
import { CreateCollectionModal } from "@/components/create-collection-modal"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"

export default function EditPromptPage() {
  const router = useRouter()
  const params = useParams()
  const shortId = params?.shortId as string
  const supabase = createClientComponentClient()
  
  // Form state
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [prompt, setPrompt] = useState<any>(null)
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [promptText, setPromptText] = useState("")
  const [instructions, setInstructions] = useState("")
  const [exampleInput, setExampleInput] = useState("")
  const [exampleOutput, setExampleOutput] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedTool, setSelectedTool] = useState<string>("") 
  const [selectedAIModel, setSelectedAIModel] = useState<string>("") 
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isPublic, setIsPublic] = useState(true)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(true) // Default to true for edit page
  const [userEnteredAiModel, setUserEnteredAiModel] = useState<string>("")
  
  // Tags state
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [tagInput, setTagInput] = useState("")
  
  // Collections state
  const [collections, setCollections] = useState<Collection[]>([])
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null)
  const [isLoadingCollections, setIsLoadingCollections] = useState(false)
  const [isCreateCollectionModalOpen, setIsCreateCollectionModalOpen] = useState(false)
  
  // Models state
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]) 
  const [isLoadingModels, setIsLoadingModels] = useState(false)
  
  // State for categories, tools, and tags for redirect URL construction
  const [categoriesData, setCategoriesData] = useState<Array<{id: number, slug: string}>>([])
  const [toolsData, setToolsData] = useState<Array<{id: number, slug: string}>>([])
  const [tagsData, setTagsData] = useState<Array<{id: number, slug: string}>>([])
  const [isLoadingData, setIsLoadingData] = useState(false)
  
  // User state
  const [user, setUser] = useState<any>(null)
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  
  // Delete dialog state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  
  // Error state
  const [error, setError] = useState<string | null>(null)

  // Check authentication and redirect if not logged in
  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true)
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          router.push('/login')
          return
        }
        setUser(user)
      } catch (error) {
        console.error('Authentication error:', error)
        router.push('/login')
      } finally {
        setIsCheckingAuth(false)
      }
    }
    
    checkAuth()
  }, [router, supabase.auth])

  // Fetch collections when user is authenticated
  useEffect(() => {
    const fetchCollections = async () => {
      if (!user) return
      
      setIsLoadingCollections(true)
      try {
        const collections = await getUserCollections(user.id, { includePrivate: true })
        setCollections(collections)
      } catch (error) {
        console.error('Error fetching collections:', error)
        toast({
          title: "Failed to load collections",
          description: "Your collections couldn't be loaded. You can still edit your prompt.",
          variant: "destructive",
        })
      } finally {
        setIsLoadingCollections(false)
      }
    }
    
    fetchCollections()
  }, [user])

  // Effect to fetch categories, tools, and tags data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoadingData(true)
      try {
        // Fetch categories, tools, and tags data
        const [categoriesResult, toolsResult, tagsResult] = await Promise.all([
          getCategories(),
          getTools(),
          getTags()
        ])
        
        setCategoriesData(categoriesResult.map(cat => ({ id: Number(cat.id), slug: cat.slug })))
        setToolsData(toolsResult.map(tool => ({ id: Number(tool.id), slug: tool.slug })))
        setTagsData(tagsResult.map(tag => ({ id: Number(tag.id), slug: tag.slug })))
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setIsLoadingData(false)
      }
    }
    
    fetchData()
  }, [])

  // Fetch prompt data
  useEffect(() => {
    const fetchPrompt = async () => {
      if (!shortId || isCheckingAuth) return
      
      setIsLoading(true)
      try {
        const promptData = await getPromptByShortId(shortId)
        
        if (!promptData) {
          setError("Prompt not found")
          return
        }
        
        // Check if the current user is the owner of the prompt
        if (user && promptData.user?.id !== user.id) {
          setError("You don't have permission to edit this prompt")
          return
        }
        
        setPrompt(promptData)
        
        // Populate form fields
        setTitle(promptData.title || "")
        setDescription(promptData.description || "")
        setPromptText(promptData.text || "")
        setInstructions(promptData.instructions || "")
        setExampleInput(promptData.exampleInput || "")
        setExampleOutput(promptData.exampleOutput || "")
        setIsPublic(promptData.isPublic !== false) // Default to true if not specified
        
        // Set category, tool, and AI model
        if (promptData.category && typeof promptData.category === 'object' && promptData.category.id) {
          setSelectedCategory(String(promptData.category.id))
        }
        
        if (promptData.tool && typeof promptData.tool === 'object' && promptData.tool.id) {
          setSelectedTool(String(promptData.tool.id))
          // Fetch AI models for this tool
          fetchModels(promptData.tool.slug)
        }
        
        if (promptData.ai_model?.id) {
          setSelectedAIModel(String(promptData.ai_model.id))
          setUserEnteredAiModel(`${promptData.ai_model.provider} - ${promptData.ai_model.name}`)
        }
        
        // Set tags
        if (promptData.tags && Array.isArray(promptData.tags)) {
          setSelectedTags(promptData.tags.map((tag: any) => String(tag.id)))
        }
        
        // Set image preview if available
        if (promptData.imageUrl) {
          setImagePreview(promptData.imageUrl)
        }
        
      } catch (error) {
        console.error('Error fetching prompt:', error)
        setError("Failed to load prompt data")
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchPrompt()
  }, [shortId, user, isCheckingAuth])
  
  // Fetch AI models when tool changes
  const fetchModels = async (toolSlug: string) => {
    if (!toolSlug) return
    
    setIsLoadingModels(true)
    try {
      const models = await getAIModelsForTool(toolSlug)
      // Add displayName property to each model
      const modelsWithDisplayName = models.map(model => ({
        ...model,
        displayName: `${model.provider} - ${model.name}`
      }))
      setAvailableModels(modelsWithDisplayName)
    } catch (error) {
      console.error('Error fetching AI models:', error)
    } finally {
      setIsCheckingAuth(false)
    }
  }

  // This useEffect already exists above, removing duplicate

// Effect to fetch categories, tools, and tags data
useEffect(() => {
  const fetchData = async () => {
    setIsLoadingData(true)
    try {
      // Fetch categories, tools, and tags data
      const [categoriesResult, toolsResult, tagsResult] = await Promise.all([
        getCategories(),
        getTools(),
        getTags()
      ])
      
      setCategoriesData(categoriesResult.map(cat => ({ id: Number(cat.id), slug: cat.slug })))
      setToolsData(toolsResult.map(tool => ({ id: Number(tool.id), slug: tool.slug })))
      setTagsData(tagsResult.map(tag => ({ id: Number(tag.id), slug: tag.slug })))
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setIsLoadingData(false)
    }
  }
  
  fetchData()
}, [])

// Fetch prompt data
useEffect(() => {
  const fetchPrompt = async () => {
    if (!shortId || isCheckingAuth) return
    
    setIsLoading(true)
    try {
      const promptData = await getPromptByShortId(shortId)
      
      if (!promptData) {
        setError("Prompt not found")
        return
      }
      
      // Check if the current user is the owner of the prompt
      if (user && promptData.user?.id !== user.id) {
        setError("You don't have permission to edit this prompt")
        return
      }
      
      setPrompt(promptData)
      
      // Populate form fields
      setTitle(promptData.title || "")
      setDescription(promptData.description || "")
      setPromptText(promptData.text || "")
      setInstructions(promptData.instructions || "")
      setExampleInput(promptData.exampleInput || "")
      setExampleOutput(promptData.exampleOutput || "")
      setIsPublic(promptData.isPublic !== false) // Default to true if not specified
      
      // Set category, tool, and AI model
      if (promptData.category && typeof promptData.category === 'object' && promptData.category.id) {
        setSelectedCategory(String(promptData.category.id))
      }
      
      if (promptData.tool && typeof promptData.tool === 'object' && promptData.tool.id) {
        setSelectedTool(String(promptData.tool.id))
        // Fetch AI models for this tool
        fetchModels(promptData.tool.slug)
      }
      
      if (promptData.ai_model?.id) {
        setSelectedAIModel(String(promptData.ai_model.id))
        setUserEnteredAiModel(`${promptData.ai_model.provider} - ${promptData.ai_model.name}`)
      }
      
      // Set tags
      if (promptData.tags && Array.isArray(promptData.tags)) {
        setSelectedTags(promptData.tags.map((tag: any) => String(tag.id)))
      }
      
      // Set image preview if available
      if (promptData.imageUrl) {
        setImagePreview(promptData.imageUrl)
      }
      
    } catch (error) {
      console.error('Error fetching prompt:', error)
      setError("Failed to load prompt data")
    } finally {
      setIsLoading(false)
    }
  }
  
  fetchPrompt()
}, [shortId, user, isCheckingAuth])

// Handle form submission
const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
  e.preventDefault()
  
  console.log('=== SUBMIT START ===');
  console.log('User:', user ? 'Exists' : 'Missing');
  console.log('Prompt:', prompt ? 'Exists' : 'Missing');
  
  if (!user || !prompt) {
    console.error('Missing user or prompt data');
    return;
  }
  
  // Validate required fields
  console.log('Validating fields:', {
    title: !!title,
    description: !!description,
    promptText: !!promptText,
    selectedCategory: selectedCategory,
    selectedTool: selectedTool,
    selectedTagsCount: selectedTags.length
  });
  
  if (!title || !description || !promptText || !selectedCategory || !selectedTool || selectedTags.length === 0) {
    toast({
      title: "Missing required fields",
      description: "Please fill in all required fields marked with an asterisk (*).",
      variant: "destructive",
    })
    console.error('Validation failed: Missing required fields');
    return
  }
  
  setIsSubmitting(true)
  try {
    // Prepare data for image upload if needed
    let imageUrl = prompt.imageUrl
    console.log('Original imageUrl:', imageUrl);
    console.log('ImageFile exists:', !!imageFile);
    console.log('ImagePreview exists:', !!imagePreview);
    
    // If there's a new image file, upload it first
    if (imageFile) {
      console.log('Processing image upload...');
      console.log('Image file details:', {
        name: imageFile.name,
        size: imageFile.size,
        type: imageFile.type
      });
      
      const fileExt = imageFile.name.split('.').pop()
      const filePath = `prompt-images/${user.id}/${Date.now()}.${fileExt}`
      console.log('Generated file path:', filePath);
      
      // Upload the image to storage
      console.log('Starting Supabase upload...');
      const uploadResult = await supabase.storage
        .from('prompt-images')
        .upload(filePath, imageFile)
      
      console.log('Upload result:', JSON.stringify(uploadResult, null, 2));
      
      if (uploadResult.error) {
        console.error('Upload error:', JSON.stringify(uploadResult.error, null, 2));
        throw uploadResult.error;
      }
      
      // Get the public URL
      console.log('Getting public URL...');
      const publicUrlResult = supabase.storage.from('prompt-images').getPublicUrl(filePath);
      console.log('Public URL result:', JSON.stringify(publicUrlResult, null, 2));
      
      imageUrl = publicUrlResult.data.publicUrl;
      console.log('New image URL:', imageUrl);
    } else if (imagePreview === null) {
      // If image preview is null but was previously set, it means user removed the image
      console.log('Image removed by user, setting imageUrl to null');
      imageUrl = null;
    }
    
    // Parse tag IDs to numbers
    const tagIds = selectedTags.map(tagId => Number(tagId))
    
    // Update prompt using the API service
    const { success, error } = await updatePrompt(prompt.id, {
      title,
      description,
      promptText,
      instructions,
      exampleInput,
      exampleOutputText: exampleOutput,
      categoryId: Number(selectedCategory),
      toolId: Number(selectedTool),
      aiModelId: selectedAIModel ? Number(selectedAIModel) : null,
      userEnteredAiModel: userEnteredAiModel,
      isPublic,
      userId: user.id,
      tagIds,
      imageUrl
    })
    
    if (!success) throw error
    
    // If a collection is selected, add the prompt to it
    if (selectedCollection && selectedCollection !== 'create-new') {
      await addPromptToCollection(user.id, prompt.id, selectedCollection)
    }
    
    toast({
      title: "Prompt updated",
      description: "Your prompt has been successfully updated.",
    })
    
    // Construct the full URL for the prompt detail page
    // Get the category and tool slugs from the selected options
    const selectedCategoryObj = allCategories.find(cat => String(cat.id) === selectedCategory)
    const selectedToolObj = allTools.find(tool => String(tool.id) === selectedTool)
    
    // Create a slug from the title
    const titleSlug = title.toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-')     // Replace spaces with hyphens
      .replace(/-+/g, '-')      // Replace multiple hyphens with a single one
    
    if (selectedCategoryObj && selectedToolObj) {
      // Use the full URL format
      router.push(`/prompt/${selectedCategoryObj.slug}/${selectedToolObj.slug}/${titleSlug}/${shortId}`)
    } else {
      // Fallback to the simple format if category or tool is not found
      router.push(`/prompt/${shortId}`)
    }
    
  } catch (error: any) {
    console.error('Error updating prompt:', error)
    console.error('Error details:', JSON.stringify(error, null, 2))
    console.error('Error name:', error?.name)
    console.error('Error message:', error?.message)
    console.error('Error stack:', error?.stack)
    
    // Check if it's a Supabase storage error
    if (error?.statusCode) {
      console.error('Supabase status code:', error.statusCode)
      console.error('Supabase error message:', error.error?.message)
    }
    
    toast({
      title: "Failed to update prompt",
      description: error.message || "An error occurred while updating your prompt.",
      variant: "destructive",
    })
  } finally {
    setIsSubmitting(false)
  }
}

// Handle prompt deletion
const handleDelete = async () => {
  if (!user || !prompt) return
  
  setIsDeleting(true)
  try {
    // Delete prompt using the API service
    const { success, error } = await deletePrompt(prompt.id)
    
    if (!success) throw error
    
    toast({
      title: "Prompt deleted",
      description: "Your prompt has been successfully deleted.",
    })
    
    // Close dialog and redirect to home page
    setIsDeleteDialogOpen(false)
    router.push('/')
    
  } catch (error: any) {
    console.error('Error deleting prompt:', error)
    toast({
      title: "Failed to delete prompt",
      description: error.message || "An error occurred while deleting your prompt.",
      variant: "destructive",
    })
  } finally {
    setIsDeleting(false)
  }
}

// Convert allTags to the format expected by TagInput
const tagOptions = useMemo(() => 
  allTags.map(tag => ({ value: String(tag.id), label: tag.name })), 
  []
)

  } catch (error: any) {
    console.error('Error updating prompt:', error)
    toast({
      title: "Failed to update prompt",
      description: error.message || "An error occurred while updating your prompt.",
      variant: "destructive",
    })
  } finally {
    setIsSubmitting(false)
  }
}

  // Handle prompt deletion
  const handleDelete = async () => {
    if (!user || !prompt) return
    
    setIsDeleting(true)
    try {
      // Delete prompt using the API service
      const { success, error } = await deletePrompt(prompt.id)
      
      if (!success) throw error
      
      toast({
        title: "Prompt deleted",
        description: "Your prompt has been successfully deleted.",
      })
      
      // Close dialog and redirect to home page
      setIsDeleteDialogOpen(false)
      router.push('/')
      
    } catch (error: any) {
      console.error('Error deleting prompt:', error)
      toast({
        title: "Failed to delete prompt",
        description: error.message || "An error occurred while deleting your prompt.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  // Convert allTags to the format expected by TagInput
  const tagOptions = useMemo(() => 
    allTags.map(tag => ({ value: String(tag.id), label: tag.name })), 
    []
  )

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Check file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please select an image under 2MB in size.",
          variant: "destructive",
        })
        return
      }
      
      // Store the file for upload
      setImageFile(file)
      
      // Create preview
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  if (isCheckingAuth || isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex h-screen flex-col items-center justify-center gap-4">
        <div className="flex items-center gap-2 text-destructive">
          <AlertCircle className="h-6 w-6" />
          <h2 className="text-xl font-semibold">{error}</h2>
        </div>
        <Button onClick={() => router.push('/')}>Go Home</Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mx-auto max-w-3xl">
        <CardHeader>
          <CardTitle className="text-2xl">Edit Prompt</CardTitle>
          <CardDescription>
            Update your prompt details. Make changes and click Update to save them.
          </CardDescription>
        </CardHeader>
        
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="title">Title <span className="text-red-500">*</span></Label>
              <Input 
                id="title" 
                placeholder="Enter a descriptive title" 
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
                disabled={isSubmitting}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description <span className="text-red-500">*</span></Label>
              <Textarea 
                id="description" 
                placeholder="Describe what your prompt does and how to use it"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                required
                disabled={isSubmitting}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="prompt">Prompt Text <span className="text-red-500">*</span></Label>
              <Textarea 
                id="prompt" 
                placeholder="Enter your prompt text here"
                value={promptText}
                onChange={(e) => setPromptText(e.target.value)}
                required
                disabled={isSubmitting}
                className="min-h-[150px]"
              />
            </div>
            
            {/* Image Upload - Optional */}
            <div className="space-y-2">
              <Label htmlFor="image">Image (Optional)</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="image"
                  type="file"
                  accept="image/jpeg,image/png,image/webp"
                  className="hidden"
                  onChange={handleImageChange}
                  disabled={isSubmitting}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById("image")?.click()}
                  disabled={isSubmitting}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Image
                </Button>
                {imagePreview && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setImagePreview(null)
                      setImageFile(null)
                    }}
                    disabled={isSubmitting}
                  >
                    Remove
                  </Button>
                )}
              </div>
              <p className="text-xs text-muted-foreground">JPG, PNG or WebP, max 2MB</p>
              {imagePreview && (
                <div className="mt-2">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="max-h-32 rounded-md object-contain"
                  />
                </div>
              )}
            </div>
            
            {/* Advanced Options Toggle */}
            <div className="flex items-center space-x-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                disabled={isSubmitting}
              >
                {showAdvancedOptions ? "Hide Advanced Options" : "Show Advanced Options"}
              </Button>
              <span className="text-xs text-muted-foreground">
                Optional fields for more detailed prompts
              </span>
            </div>
            
            {/* Advanced Options */}
            {showAdvancedOptions && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="instructions">Instructions</Label>
                  <Textarea 
                    id="instructions" 
                    placeholder="Additional instructions for using the prompt"
                    value={instructions}
                    onChange={(e) => setInstructions(e.target.value)}
                    disabled={isSubmitting}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="example-input">Example Input</Label>
                  <Textarea 
                    id="example-input" 
                    placeholder="Example input for your prompt"
                    value={exampleInput}
                    onChange={(e) => setExampleInput(e.target.value)}
                    disabled={isSubmitting}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="example-output">Example Output</Label>
                  <Textarea 
                    id="example-output" 
                    placeholder="Example output from your prompt"
                    value={exampleOutput}
                    onChange={(e) => setExampleOutput(e.target.value)}
                    disabled={isSubmitting}
                  />
                </div>
              </>
            )}
            
            {/* Category and Tool in one row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Category */}
              <div className="space-y-2">
                <Label htmlFor="category">Category <span className="text-red-500">*</span></Label>
                <Combobox 
                  options={allCategories.map(category => ({ value: String(category.id), label: category.name }))}
                  value={selectedCategory}
                  onChange={setSelectedCategory}
                  placeholder="Select a category"
                  emptyText="No categories found"
                />
              </div>
              
              {/* Tool */}
              <div className="space-y-2">
                <Label htmlFor="tool">Tool <span className="text-red-500">*</span></Label>
                <Combobox 
                  options={allTools.map(tool => ({ value: String(tool.id), label: tool.name }))}
                  value={selectedTool}
                  onChange={(value) => {
                    setSelectedTool(value)
                    setSelectedAIModel("") // Reset AI model when tool changes
                    if (value) {
                      const selectedToolObj = allTools.find(t => String(t.id) === value)
                      if (selectedToolObj) {
                        fetchModels(selectedToolObj.slug)
                      }
                    }
                  }}
                  placeholder="Select a tool"
                  emptyText="No tools found"
                />
              </div>
            </div>
            
            {/* AI Model */}
            <div className="space-y-2">
              <Label htmlFor="ai-model-input">AI Model (Optional)</Label>
              <Input 
                id="ai-model-input" 
                placeholder="Enter AI model name (e.g., GPT-4o, Claude 3)" 
                value={userEnteredAiModel}
                onChange={(e) => setUserEnteredAiModel(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                Enter the AI model you used for this prompt
              </p>
            </div>
            
            {/* Tags */}
            <div className="space-y-2">
              <Label htmlFor="tags">Tags (up to 5) <span className="text-red-500">*</span></Label>
              <TagInput 
                options={tagOptions}
                selectedTags={selectedTags}
                onTagsChange={setSelectedTags}
                placeholder="Search for tags..."
                maxTags={5}
                showSelectedInField={true}
              />
            </div>
            
            {/* Save to Collection */}
            <div className="space-y-2">
              <Label htmlFor="collection">Save to Collection</Label>
              <Select 
                value={selectedCollection || ''} 
                onValueChange={(value) => {
                  if (value === 'create-new') {
                    setIsCreateCollectionModalOpen(true)
                    return
                  }
                  setSelectedCollection(value)
                }}
                disabled={isSubmitting || isLoadingCollections}
              >
                <SelectTrigger id="collection">
                  <SelectValue placeholder="My Prompts" />
                </SelectTrigger>
                <SelectContent>
                  {collections.map((collection) => (
                    <SelectItem 
                      key={collection.id} 
                      value={collection.id}
                    >
                      {collection.name} {collection.is_default && "(Default)"}
                    </SelectItem>
                  ))}
                  <SelectItem value="create-new" className="text-primary font-medium">
                    + Create New Collection
                  </SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Your prompt will be saved to "My Prompts" by default.
              </p>
            </div>
            
            {/* Visibility */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="is-public">Visibility</Label>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="is-public"
                    checked={isPublic}
                    onChange={(e) => setIsPublic(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <Label htmlFor="is-public" className="text-sm font-normal">
                    Make this prompt public
                  </Label>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                Public prompts are visible to everyone. Private prompts are only visible to you.
              </p>
            </div>
          </CardContent>
          
          <CardFooter className="flex justify-between">
            <div className="flex gap-2">
              <Button 
                type="button" 
                variant="destructive" 
                onClick={() => setIsDeleteDialogOpen(true)}
                disabled={isSubmitting}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Prompt
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => router.push(`/prompt/${shortId}`)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
            <Button 
              type="submit" 
              disabled={isSubmitting} 
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Prompt"
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Prompt</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this prompt? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Create Collection Modal */}
      {user && (
        <CreateCollectionModal
          userId={user.id}
          isOpen={isCreateCollectionModalOpen}
          onClose={() => setIsCreateCollectionModalOpen(false)}
          onSuccess={(collectionId) => {
            // Fetch the updated collections list
            const fetchCollections = async () => {
              try {
                const userCollections = await getUserCollections(user.id)
                setCollections(userCollections)
                // Select the newly created collection
                setSelectedCollection(collectionId)
              } catch (error) {
                console.error('Error fetching collections:', error)
              }
            }
            fetchCollections()
            setIsCreateCollectionModalOpen(false)
          }}
        />
      )}
    </div>
  )
}
