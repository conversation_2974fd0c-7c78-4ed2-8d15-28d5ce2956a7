import { notFound } from "next/navigation"
import { getPromptByShortId } from "@/lib/api-services"
import PromptDetailView from "@/components/prompt-detail-view"

interface PageProps {
  params: Promise<{
    shortId: string
    categorySlug: string
    toolSlug: string
    tagSlug: string
    titleSlug: string
  }>
}

export default async function PromptDetailPage({ params }: PageProps) {
  try {
    // Await params before accessing its properties
    const { shortId } = await params;
    
    // ADD THIS LOG:
    console.log(`[PromptDetailPage] Received params.shortId from URL: "${shortId}"`);
    console.log(`Fetching prompt with shortId: ${shortId}`)

    // Fetch the prompt data
    const prompt = await getPromptByShortId(shortId)

    if (!prompt) {
      console.error(`Prompt with shortId ${shortId} not found`)
      return notFound()
    }

    console.log(`Prompt fetched successfully:`, {
      id: prompt.id,
      title: prompt.title,
      text: prompt.text?.substring(0, 50) + "...",
      rating: prompt.likeCount, // Using likeCount instead of rating
      commentCount: prompt.commentCount,
      user: prompt.user,
    })

    // The PromptDetailView component fetches its own comments.

    return <PromptDetailView prompt={prompt} />
  } catch (error) {
    console.error("Error fetching prompt:", error)
    return notFound()
  }
}
