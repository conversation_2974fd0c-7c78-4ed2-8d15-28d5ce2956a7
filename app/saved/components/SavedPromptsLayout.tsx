"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@supabase/ssr"
import { getCategoriesWithPromptCount, getToolsWithPromptCount, getTagsWithPromptCount } from "@/lib/api-services"
import TabNavigation from "./TabNavigation"
import SearchAndFilters from "./SearchAndFilters"
import AllPromptsTab from "./AllPromptsTab"
import SavedPromptsTab from "./SavedPromptsTab"
import MyPromptsTab from "./MyPromptsTab"
import type { Category, Tool, Tag } from "@/lib/types"
import type { SortOption } from "./SearchAndFilters"
import { useRouter } from "next/navigation"

interface SavedPageState {
  activeTab: "all" | "saved" | "my"
  searchQuery: string
  selectedCategories: string[]
  selectedTools: string[]
  selectedTags: string[]
  sortBy: SortOption
  viewMode: "grid" | "list"
}

export default function SavedPromptsLayout() {
  const router = useRouter()
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const [isLoadingUser, setIsLoadingUser] = useState(true)
  
  // State for the interface
  const [state, setState] = useState<SavedPageState>({
    activeTab: "all",
    searchQuery: "",
    selectedCategories: [],
    selectedTools: [],
    selectedTags: [],
    sortBy: "newest",
    viewMode: "grid",
  })

  // Available filter options
  const [availableCategories, setAvailableCategories] = useState<Category[]>([])
  const [availableTools, setAvailableTools] = useState<Tool[]>([])
  const [availableTags, setAvailableTags] = useState<Tag[]>([])

  // Tab counts (placeholder values for now)
  const [tabCounts, setTabCounts] = useState({
    all: 0,
    saved: 0,
    my: 0,
  })

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  // Handle initial hash and hash changes
  useEffect(() => {
    const getTabFromHash = () => {
      if (typeof window === 'undefined') return "all"
      
      const hash = window.location.hash.replace('#', '')
      switch (hash) {
        case 'saved-prompts':
          return "saved"
        case 'my-prompts':
          return "my"
        case 'all-prompts':
          return "all"
        default:
          return "all"
      }
    }

    // Set initial tab from hash
    const initialTab = getTabFromHash()
    setState(prev => ({ 
      ...prev, 
      activeTab: initialTab,
      sortBy: initialTab === "saved" ? "recently_saved" : "newest"
    }))

    // Listen for hash changes
    const handleHashChange = () => {
      const newTab = getTabFromHash()
      setState(prev => ({ 
        ...prev, 
        activeTab: newTab,
        sortBy: newTab === "saved" ? "recently_saved" : "newest"
      }))
    }

    window.addEventListener('hashchange', handleHashChange)
    return () => window.removeEventListener('hashchange', handleHashChange)
  }, [])

  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setCurrentUserId(session?.user?.id || null)
      } catch (error) {
        console.error('Error loading user:', error)
        setCurrentUserId(null)
      } finally {
        setIsLoadingUser(false)
      }
    }

    loadUser()
  }, [supabase])

  // Load filter options
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const [categoriesData, toolsData, tagsData] = await Promise.all([
          getCategoriesWithPromptCount(),
          getToolsWithPromptCount(),
          getTagsWithPromptCount(),
        ])

        setAvailableCategories(categoriesData || [])
        setAvailableTools(toolsData || [])
        setAvailableTags(tagsData || [])
      } catch (error) {
        console.error("Error loading filter options:", error)
      }
    }

    loadFilterOptions()
  }, [])

  // Load tab counts (simplified for Phase 1)
  useEffect(() => {
    const loadCounts = async () => {
      if (!currentUserId) {
        setTabCounts({
          all: 0,
          saved: 0,
          my: 0,
        })
        return
      }

      try {
        // Get user's own prompts count
        const { count: myPromptsCount, error: myPromptsError } = await supabase
          .from("prompts")
          .select("*", { count: "exact", head: true })
          .eq("user_id", currentUserId)
          .eq("is_public", true)

        if (myPromptsError) {
          console.error("Error counting my prompts:", myPromptsError)
        }

        // Get saved prompts count from collections
        // UPDATED: Count from ALL collections, not just default saved_prompts
        
        // First, get all collection IDs owned by the user
        const { data: userCollections, error: collectionsError } = await supabase
          .from("collections")
          .select("id")
          .eq("user_id", currentUserId)

        let savedPromptsCount = 0
        if (!collectionsError && userCollections && userCollections.length > 0) {
          const collectionIds = userCollections.map(c => c.id)
          
          // Count unique prompts across all user's collections
          const { count, error: savedCountError } = await supabase
            .from("collection_prompts")
            .select("prompt_id", { count: "exact", head: true })
            .in("collection_id", collectionIds)

          if (!savedCountError) {
            savedPromptsCount = count || 0
          } else {
            console.error("Error counting saved prompts:", savedCountError)
          }
        } else if (collectionsError) {
          console.error("Error fetching user collections:", collectionsError)
        }

        // Calculate total: user's prompts + saved prompts
        const totalCount = (myPromptsCount || 0) + savedPromptsCount

        setTabCounts({
          all: totalCount,
          saved: savedPromptsCount,
          my: myPromptsCount || 0,
        })
      } catch (error) {
        console.error("Error loading counts:", error)
        // Fallback to 0 counts if there's an error
        setTabCounts({
          all: 0,
          saved: 0,
          my: 0,
        })
      }
    }

    loadCounts()
  }, [currentUserId])

  // State update helpers
  const updateState = (updates: Partial<SavedPageState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }

  const handleTabChange = (tab: "all" | "saved" | "my") => {
    const hashMap = {
      "all": "all-prompts",
      "saved": "saved-prompts", 
      "my": "my-prompts"
    }
    
    // Update URL hash
    window.history.replaceState(null, '', `#${hashMap[tab]}`)
    
    updateState({ 
      activeTab: tab,
      // Reset sort when switching to saved tab to show "recently saved" option
      sortBy: tab === "saved" ? "recently_saved" : "newest"
    })
  }

  const handleSearchChange = (searchQuery: string) => {
    updateState({ searchQuery })
  }

  const handleCategoriesChange = (selectedCategories: string[]) => {
    updateState({ selectedCategories })
  }

  const handleToolsChange = (selectedTools: string[]) => {
    updateState({ selectedTools })
  }

  const handleTagsChange = (selectedTags: string[]) => {
    updateState({ selectedTags })
  }

  const handleSortChange = (sortBy: SortOption) => {
    updateState({ sortBy })
  }

  const handleViewModeChange = (viewMode: "grid" | "list") => {
    updateState({ viewMode })
  }

  const handleClearFilters = () => {
    updateState({
      searchQuery: "",
      selectedCategories: [],
      selectedTools: [],
      selectedTags: [],
    })
  }

  // Get the appropriate tab content
  const renderTabContent = () => {
    const commonProps = {
      searchQuery: state.searchQuery,
      selectedCategories: state.selectedCategories,
      selectedTools: state.selectedTools,
      selectedTags: state.selectedTags,
      sortBy: state.sortBy,
      viewMode: state.viewMode,
      currentUserId: currentUserId || undefined,
    }

    switch (state.activeTab) {
      case "all":
        return <AllPromptsTab {...commonProps} />
      case "saved":
        return <SavedPromptsTab {...commonProps} />
      case "my":
        return <MyPromptsTab {...commonProps} />
      default:
        return <AllPromptsTab {...commonProps} />
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">My Prompts Hub</h1>
        <p className="text-muted-foreground">
          Discover, save, and manage prompts all in one place
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="mb-6">
        <TabNavigation
          activeTab={state.activeTab}
          onTabChange={handleTabChange}
          counts={tabCounts}
        />
      </div>

      {/* Search and Filters */}
      <div className="mb-6">
        <SearchAndFilters
          searchQuery={state.searchQuery}
          onSearchChange={handleSearchChange}
          selectedCategories={state.selectedCategories}
          onCategoriesChange={handleCategoriesChange}
          selectedTools={state.selectedTools}
          onToolsChange={handleToolsChange}
          selectedTags={state.selectedTags}
          onTagsChange={handleTagsChange}
          sortBy={state.sortBy}
          onSortChange={handleSortChange}
          viewMode={state.viewMode}
          onViewModeChange={handleViewModeChange}
          activeTab={state.activeTab}
          onClearFilters={handleClearFilters}
          availableCategories={availableCategories}
          availableTools={availableTools}
          availableTags={availableTags}
          isLoading={isLoadingUser}
        />
      </div>

      {/* Tab Content */}
      <div>
        {renderTabContent()}
      </div>
    </div>
  )
} 