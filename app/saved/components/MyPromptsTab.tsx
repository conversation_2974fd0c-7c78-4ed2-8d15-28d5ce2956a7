"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, Plus, Edit, Trash2, Globe, Lock } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import PromptCard from "@/components/prompt-card"
import PromptListItem from "@/components/prompt-list-item"
import Link from "next/link"
import { getPrompts, deletePrompt } from "@/lib/api-services"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import type { PromptCard as PromptCardType } from "@/lib/types"
import type { SortOption } from "./SearchAndFilters"

interface MyPromptsTabProps {
  searchQuery: string
  selectedCategories: string[]
  selectedTools: string[]
  selectedTags: string[]
  sortBy: SortOption
  viewMode: "grid" | "list"
  currentUserId?: string
}

export default function MyPromptsTab({
  searchQuery,
  selectedCategories,
  selectedTools,
  selectedTags,
  sortBy,
  viewMode,
  currentUserId,
}: MyPromptsTabProps) {
  const [prompts, setPrompts] = useState<PromptCardType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [promptToDelete, setPromptToDelete] = useState<{ id: string; title: string } | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  
  const { toast } = useToast()
  const router = useRouter()
  const limit = 20

  // Convert our SortOption to API-compatible values
  const getApiSortBy = (sortOption: SortOption): string => {
    switch (sortOption) {
      case "newest":
        return "created_at"
      case "oldest":
        return "created_at"
      case "most_popular":
        return "like_count"
      case "most_viewed":
        return "view_count"
      case "alphabetical":
        return "title"
      case "trending":
        return "trending_score"
      default:
        return "created_at"
    }
  }

  const getApiSortOrder = (sortOption: SortOption): "asc" | "desc" => {
    return sortOption === "oldest" || sortOption === "alphabetical" ? "asc" : "desc"
  }

  // Load user's prompts
  const loadMyPrompts = async (reset: boolean = false) => {
    if (!currentUserId) {
      setPrompts([])
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      const currentOffset = reset ? 0 : offset

      const fetchedPrompts = await getPrompts({
        categorySlugs: selectedCategories.length > 0 ? selectedCategories : undefined,
        toolSlugs: selectedTools.length > 0 ? selectedTools : undefined,
        tagSlugs: selectedTags.length > 0 ? selectedTags : undefined,
        searchQuery: searchQuery || undefined,
        userId: currentUserId, // Filter by current user
        currentUserId: currentUserId, // NEW: Pass current user ID for saved status
        limit,
        offset: currentOffset,
        sortBy: getApiSortBy(sortBy),
        sortOrder: getApiSortOrder(sortBy),
      })

      if (reset) {
        setPrompts(fetchedPrompts)
        setOffset(limit)
      } else {
        setPrompts(prev => [...prev, ...fetchedPrompts])
        setOffset(prev => prev + limit)
      }

      setHasMore(fetchedPrompts.length === limit)
    } catch (error) {
      console.error("Error loading my prompts:", error)
      toast({
        title: "Error",
        description: "Failed to load your prompts. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Reset and reload when filters change
  useEffect(() => {
    setOffset(0)
    loadMyPrompts(true)
  }, [searchQuery, selectedCategories, selectedTools, selectedTags, sortBy, currentUserId])

  // Load more prompts
  const loadMore = () => {
    if (!isLoading && hasMore) {
      loadMyPrompts(false)
    }
  }

  // Handle editing a prompt
  const handleEditPrompt = (promptShortId: string) => {
    router.push(`/prompt/edit/${promptShortId}`)
  }

  // Handle deleting a prompt
  const handleDeletePrompt = (promptId: string, promptTitle: string) => {
    setPromptToDelete({ id: promptId, title: promptTitle })
    setDeleteDialogOpen(true)
  }

  // Confirm deletion
  const confirmDelete = async () => {
    if (!promptToDelete || !currentUserId) return

    setIsDeleting(true)
    try {
      const { success, error } = await deletePrompt(promptToDelete.id, currentUserId)
      
      if (!success) {
        throw new Error(error?.message || "Failed to delete prompt")
      }

      // Remove from local state
      setPrompts(prev => prev.filter(p => p.id !== promptToDelete.id))
      
      // Show success toast
      toast({
        title: "Success",
        description: `"${promptToDelete.title}" deleted`,
      })

      // Close dialog
      setDeleteDialogOpen(false)
      setPromptToDelete(null)
    } catch (error: any) {
      console.error("Error deleting prompt:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to delete prompt. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  // Handle toggle save (not applicable for owned prompts)
  const handleToggleSave = async (promptId: string, currentSaveStatus: boolean) => {
    // Users can't save their own prompts
    return
  }

  // Custom PromptCard component with additional actions for owned prompts
  const MyPromptCard = ({ prompt }: { prompt: PromptCardType }) => (
    <div className="relative">
      <PromptCard 
        prompt={prompt} 
        onToggleSave={handleToggleSave}
        isSaved={false}
        isOwner={true}
      />
      
      {/* Action buttons overlay */}
      <div className="absolute top-2 right-2 z-20 flex gap-1">
        {/* Privacy indicator - styled like collections */}
        {prompt.isPublic ? (
          <Badge className="border-none px-2 py-1 font-medium text-xs text-white bg-accent-green rounded-md flex items-center gap-1">
            <Globe className="h-3 w-3" /> Public
          </Badge>
        ) : (
          <Badge className="border-none px-2 py-1 font-medium text-xs text-white bg-gray-600 rounded-md flex items-center gap-1">
            <Lock className="h-3 w-3" /> Private
          </Badge>
        )}

        {/* Edit button */}
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 bg-black/40 backdrop-blur-sm hover:bg-black/60 text-white hover:text-blue-400"
          onClick={() => handleEditPrompt(prompt.shortId || prompt.id)}
        >
          <Edit className="h-4 w-4" />
        </Button>

        {/* Delete button */}
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 bg-black/40 backdrop-blur-sm hover:bg-black/60 text-white hover:text-red-400"
          onClick={() => handleDeletePrompt(prompt.id, prompt.title)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )

  // Custom PromptListItem component with additional actions
  const MyPromptListItem = ({ prompt }: { prompt: PromptCardType }) => (
    <div className="relative">
      <PromptListItem 
        prompt={prompt}
        onToggleSave={handleToggleSave}
        isSaved={false}
      />
      
      {/* Action buttons overlay */}
      <div className="absolute top-4 right-16 z-10 flex gap-2">
        {/* Privacy indicator - styled like collections */}
        {prompt.isPublic ? (
          <Badge className="border-none px-2 py-1 font-medium text-xs text-white bg-accent-green rounded-md flex items-center gap-1">
            <Globe className="h-3 w-3" /> Public
          </Badge>
        ) : (
          <Badge className="border-none px-2 py-1 font-medium text-xs text-white bg-gray-600 rounded-md flex items-center gap-1">
            <Lock className="h-3 w-3" /> Private
          </Badge>
        )}

        {/* Edit button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleEditPrompt(prompt.shortId || prompt.id)}
        >
          <Edit className="mr-1 h-4 w-4" />
          Edit
        </Button>

        {/* Delete button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleDeletePrompt(prompt.id, prompt.title)}
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          <Trash2 className="mr-1 h-4 w-4" />
          Delete
        </Button>
      </div>
    </div>
  )

  if (!currentUserId) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Plus className="mb-4 h-12 w-12 text-muted-foreground" />
          <h3 className="mb-2 text-xl font-semibold">Sign In Required</h3>
          <p className="mb-6 text-center text-muted-foreground">
            Please sign in to view and manage your prompts.
          </p>
          <Button asChild>
            <Link href="/sign-in">Sign In</Link>
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (isLoading && prompts.length === 0) {
    return (
      <div className="flex h-40 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (prompts.length === 0 && !isLoading) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Plus className="mb-4 h-12 w-12 text-muted-foreground" />
          <h3 className="mb-2 text-xl font-semibold">No Prompts Found</h3>
          <p className="mb-6 text-center text-muted-foreground">
            {searchQuery || selectedCategories.length || selectedTools.length || selectedTags.length
              ? "No prompts match your current search and filters."
              : "You haven't created any prompts yet. Create your first prompt to get started!"}
          </p>
          <div className="flex gap-3">
            <Button asChild className="bg-primary hover:bg-primary/90">
              <Link href="/prompt/create">
                <Plus className="mr-2 h-4 w-4" />
                Create Prompt
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/explore">Explore Examples</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Create New Prompt Button */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">Your Prompts ({prompts.length})</h2>
          <p className="text-sm text-muted-foreground">
            Manage and organize your created prompts
          </p>
        </div>
        <Button asChild>
          <Link href="/prompt/create">
            <Plus className="mr-2 h-4 w-4" />
            Create New Prompt
          </Link>
        </Button>
      </div>

      {/* Prompts Grid/List */}
      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {prompts.map((prompt) => (
            <MyPromptCard key={prompt.id} prompt={prompt} />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {prompts.map((prompt) => (
            <MyPromptListItem key={prompt.id} prompt={prompt} />
          ))}
        </div>
      )}

      {/* Load More Button */}
      {hasMore && (
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={loadMore}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More"
            )}
          </Button>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Prompt</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{promptToDelete?.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 