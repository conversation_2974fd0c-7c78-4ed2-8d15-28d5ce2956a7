"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { 
  Search, 
  X, 
  ChevronDown, 
  ChevronUp, 
  Filter,
  LayoutGrid, 
  List 
} from "lucide-react"
import type { Category, Tool, Tag } from "@/lib/types"

export type SortOption = 
  | "newest" 
  | "oldest"
  | "most_popular"
  | "most_viewed"
  | "alphabetical"
  | "recently_saved"
  | "trending"

interface SearchAndFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  selectedCategories: string[]
  onCategoriesChange: (categories: string[]) => void
  selectedTools: string[]
  onToolsChange: (tools: string[]) => void
  selectedTags: string[]
  onTagsChange: (tags: string[]) => void
  sortBy: SortOption
  onSortChange: (sort: SortOption) => void
  viewMode: "grid" | "list"
  onViewModeChange: (mode: "grid" | "list") => void
  activeTab: "all" | "saved" | "my"
  onClearFilters: () => void
  availableCategories: Category[]
  availableTools: Tool[]
  availableTags: Tag[]
  isLoading?: boolean
}

export default function SearchAndFilters({
  searchQuery,
  onSearchChange,
  selectedCategories,
  onCategoriesChange,
  selectedTools,
  onToolsChange,
  selectedTags,
  onTagsChange,
  sortBy,
  onSortChange,
  viewMode,
  onViewModeChange,
  activeTab,
  onClearFilters,
  availableCategories = [],
  availableTools = [],
  availableTags = [],
  isLoading = false,
}: SearchAndFiltersProps) {
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)

  const getSortOptions = (): { value: SortOption; label: string }[] => {
    const baseOptions = [
      { value: "newest" as const, label: "Newest" },
      { value: "oldest" as const, label: "Oldest" },
      { value: "most_popular" as const, label: "Most Popular" },
      { value: "most_viewed" as const, label: "Most Viewed" },
      { value: "alphabetical" as const, label: "A-Z" },
      { value: "trending" as const, label: "Trending" },
    ]

    if (activeTab === "saved") {
      return [
        { value: "recently_saved" as const, label: "Recently Saved" },
        ...baseOptions,
      ]
    }

    return baseOptions
  }

  const hasActiveFilters = 
    selectedCategories.length > 0 || 
    selectedTools.length > 0 || 
    selectedTags.length > 0

  const toggleCategory = (categorySlug: string) => {
    if (selectedCategories.includes(categorySlug)) {
      onCategoriesChange(selectedCategories.filter(c => c !== categorySlug))
    } else {
      onCategoriesChange([...selectedCategories, categorySlug])
    }
  }

  const toggleTool = (toolSlug: string) => {
    if (selectedTools.includes(toolSlug)) {
      onToolsChange(selectedTools.filter(t => t !== toolSlug))
    } else {
      onToolsChange([...selectedTools, toolSlug])
    }
  }

  const toggleTag = (tagSlug: string) => {
    if (selectedTags.includes(tagSlug)) {
      onTagsChange(selectedTags.filter(t => t !== tagSlug))
    } else {
      onTagsChange([...selectedTags, tagSlug])
    }
  }

  const topCategories = availableCategories.slice(0, 6)
  const topTools = availableTools.slice(0, 4)
  const topTags = availableTags.slice(0, 3)

  return (
    <div className="space-y-4">
      {/* Main Search and Controls Row */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        {/* Search Input */}
        <div className="relative w-full md:w-80">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder={`Search ${
              activeTab === "all" ? "all prompts" : 
              activeTab === "saved" ? "saved prompts" : 
              "your prompts"
            }...`}
            className="pl-10 pr-4"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            disabled={isLoading}
          />
        </div>

        {/* Sort and View Controls */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Sort by:</span>
          <Select 
            value={sortBy} 
            onValueChange={(value) => onSortChange(value as SortOption)}
            disabled={isLoading}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {getSortOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* View Mode Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewModeChange(viewMode === "grid" ? "list" : "grid")}
            disabled={isLoading}
          >
            {viewMode === "grid" ? <List className="h-4 w-4" /> : <LayoutGrid className="h-4 w-4" />}
          </Button>

          {/* Advanced Filters Toggle */}
          <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
            <CollapsibleTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={hasActiveFilters ? "border-primary text-primary" : ""}
              >
                <Filter className="mr-2 h-4 w-4" />
                Filters
                {hasActiveFilters && (
                  <Badge variant="secondary" className="ml-2 h-5 text-xs">
                    {selectedCategories.length + selectedTools.length + selectedTags.length}
                  </Badge>
                )}
                {isAdvancedOpen ? (
                  <ChevronUp className="ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="ml-2 h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
          </Collapsible>
        </div>
      </div>

      {/* Quick Filter Pills */}
      {(topCategories.length > 0 || topTools.length > 0 || topTags.length > 0) && (
        <div className="flex flex-wrap gap-2">
          {/* Category Pills */}
          {topCategories.map((category) => (
            <Button
              key={category.slug}
              variant={selectedCategories.includes(category.slug) ? "default" : "outline"}
              size="sm"
              onClick={() => toggleCategory(category.slug)}
              disabled={isLoading}
              className="h-8 text-xs"
            >
              {category.name}
              {selectedCategories.includes(category.slug) && (
                <X className="ml-1 h-3 w-3" />
              )}
            </Button>
          ))}

          {/* Tool Pills */}
          {topTools.map((tool) => (
            <Button
              key={tool.slug}
              variant={selectedTools.includes(tool.slug) ? "default" : "outline"}
              size="sm"
              onClick={() => toggleTool(tool.slug)}
              disabled={isLoading}
              className="h-8 text-xs"
            >
              {tool.name}
              {selectedTools.includes(tool.slug) && (
                <X className="ml-1 h-3 w-3" />
              )}
            </Button>
          ))}

          {/* Tag Pills */}
          {topTags.map((tag) => (
            <Button
              key={tag.slug}
              variant={selectedTags.includes(tag.slug) ? "default" : "outline"}
              size="sm"
              onClick={() => toggleTag(tag.slug)}
              disabled={isLoading}
              className="h-8 text-xs"
            >
              #{tag.name}
              {selectedTags.includes(tag.slug) && (
                <X className="ml-1 h-3 w-3" />
              )}
            </Button>
          ))}

          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              disabled={isLoading}
              className="h-8 text-xs text-muted-foreground"
            >
              Clear all
            </Button>
          )}
        </div>
      )}

      {/* Advanced Filters */}
      <Collapsible open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
        <CollapsibleContent className="space-y-4 rounded-md border p-4">
          <div className="grid gap-4 md:grid-cols-3">
            {/* All Categories */}
            <div>
              <h4 className="mb-2 text-sm font-medium">Categories</h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {availableCategories.map((category) => (
                  <div key={category.slug} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`category-${category.slug}`}
                      checked={selectedCategories.includes(category.slug)}
                      onChange={() => toggleCategory(category.slug)}
                      disabled={isLoading}
                      className="rounded border-border"
                    />
                    <label 
                      htmlFor={`category-${category.slug}`}
                      className="text-sm cursor-pointer"
                    >
                      {category.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* All Tools */}
            <div>
              <h4 className="mb-2 text-sm font-medium">Tools</h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {availableTools.map((tool) => (
                  <div key={tool.slug} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`tool-${tool.slug}`}
                      checked={selectedTools.includes(tool.slug)}
                      onChange={() => toggleTool(tool.slug)}
                      disabled={isLoading}
                      className="rounded border-border"
                    />
                    <label 
                      htmlFor={`tool-${tool.slug}`}
                      className="text-sm cursor-pointer"
                    >
                      {tool.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* All Tags */}
            <div>
              <h4 className="mb-2 text-sm font-medium">Tags</h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {availableTags.map((tag) => (
                  <div key={tag.slug} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`tag-${tag.slug}`}
                      checked={selectedTags.includes(tag.slug)}
                      onChange={() => toggleTag(tag.slug)}
                      disabled={isLoading}
                      className="rounded border-border"
                    />
                    <label 
                      htmlFor={`tag-${tag.slug}`}
                      className="text-sm cursor-pointer"
                    >
                      #{tag.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
} 