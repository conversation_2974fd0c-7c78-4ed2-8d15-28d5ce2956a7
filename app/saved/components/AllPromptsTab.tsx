"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2, Bookmark, Heart } from "lucide-react"
import PromptCard from "@/components/prompt-card"
import PromptListItem from "@/components/prompt-list-item"
import AddToCollectionDialog from "@/components/add-to-collection-dialog"
import Link from "next/link"
import { createBrowserClient } from "@supabase/ssr"
import { getPrompts } from "@/lib/api-services"
import { useToast } from "@/hooks/use-toast"
import type { PromptCard as PromptCardType } from "@/lib/types"
import type { SortOption } from "./SearchAndFilters"
import type { PostgrestError } from '@supabase/supabase-js'

interface AllPromptsTabProps {
  searchQuery: string
  selectedCategories: string[]
  selectedTools: string[]
  selectedTags: string[]
  sortBy: SortOption
  viewMode: "grid" | "list"
  currentUserId?: string
}

export default function AllPromptsTab({
  searchQuery,
  selectedCategories,
  selectedTools,
  selectedTags,
  sortBy,
  viewMode,
  currentUserId,
}: AllPromptsTabProps) {
  const [prompts, setPrompts] = useState<PromptCardType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [saveDialogOpen, setSaveDialogOpen] = useState(false)
  const [selectedPromptId, setSelectedPromptId] = useState<string | null>(null)
  const [selectedPromptTitle, setSelectedPromptTitle] = useState<string>("")
  
  const { toast } = useToast()
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  // Convert our SortOption to API-compatible values for user's own prompts
  const getApiSortBy = (sortOption: SortOption): string => {
    switch (sortOption) {
      case "recently_saved":
      case "newest":
        return "created_at"
      case "oldest":
        return "created_at"
      case "most_popular":
        return "like_count"
      case "most_viewed":
        return "view_count"
      case "alphabetical":
        return "title"
      case "trending":
        return "trending_score"
      default:
        return "created_at"
    }
  }

  const getApiSortOrder = (sortOption: SortOption): "asc" | "desc" => {
    return sortOption === "oldest" || sortOption === "alphabetical" ? "asc" : "desc"
  }

  // Load all user's prompts (saved + owned)
  const loadAllPrompts = async () => {
    if (!currentUserId) {
      setPrompts([])
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      
      console.log('Loading prompts for user:', currentUserId)
      
      // First, get user's own prompts
      let userPrompts: PromptCardType[] = []
      try {
        userPrompts = await getPrompts({
          categorySlugs: selectedCategories.length > 0 ? selectedCategories : undefined,
          toolSlugs: selectedTools.length > 0 ? selectedTools : undefined,
          tagSlugs: selectedTags.length > 0 ? selectedTags : undefined,
          searchQuery: searchQuery || undefined,
          userId: currentUserId,
          currentUserId: currentUserId, // NEW: Pass current user ID for saved status
          limit: 1000, // Get all user's prompts
          offset: 0,
          sortBy: getApiSortBy(sortBy),
          sortOrder: getApiSortOrder(sortBy),
        })
        console.log('Fetched user prompts:', userPrompts.length)
      } catch (userPromptsError) {
        console.error('Error fetching user prompts:', userPromptsError)
        userPrompts = [] // Continue with empty array if this fails
      }

      // Second, get saved prompt IDs and timestamps from collections
      // UPDATED: Get from ALL collections, not just default saved_prompts
      const { data: collectionPrompts, error: collectionPromptsError } = await supabase
        .from("collection_prompts")
        .select(`
          prompt_id, 
          added_at,
          collections!inner(
            user_id
          )
        `)
        .eq("collections.user_id", currentUserId)
        .order("added_at", { ascending: false })

      let savedPromptIds: string[] = []
      let savedAtMap = new Map<string, string>()

      if (collectionPromptsError) {
        console.error("Error fetching collection prompts:", {
          message: collectionPromptsError.message,
          details: collectionPromptsError.details,
          hint: collectionPromptsError.hint,
          code: collectionPromptsError.code,
          originalError: collectionPromptsError
        })
        // Continue with empty arrays instead of throwing
        savedPromptIds = []
        savedAtMap = new Map()
      } else {
        // Get unique prompt IDs (in case same prompt is in multiple collections)
        savedPromptIds = [...new Set((collectionPrompts || []).map(item => item.prompt_id))]
        savedAtMap = new Map((collectionPrompts || []).map(item => [item.prompt_id, item.added_at]))
      }

      // If there are saved prompts, we'll get them using a simpler approach
      let savedPrompts: PromptCardType[] = []
      if (savedPromptIds.length > 0) {
        try {
          // Get all prompts and filter for the saved ones
          // This is less efficient but more reliable than complex joins
          const allPrompts = await getPrompts({
            limit: 1000, // Get a large number to include all possible saved prompts
            offset: 0,
            currentUserId: currentUserId, // NEW: Pass current user ID for saved status
          })
          
          savedPrompts = allPrompts
            .filter(prompt => savedPromptIds.includes(prompt.id))
            .map(prompt => ({
              ...prompt,
              savedAt: savedAtMap.get(prompt.id) || new Date().toISOString()
            }))
        } catch (error) {
          console.warn("Error fetching saved prompt details:", error)
          // Continue with empty saved prompts if this fails
          savedPrompts = []
        }
      }

      // Filter saved prompts to exclude user's own prompts (to avoid duplicates)
      const filteredSavedPrompts = savedPrompts.filter(prompt => prompt.user.id !== currentUserId)

      // Combine user's prompts and saved prompts
      let allPrompts = [...userPrompts, ...filteredSavedPrompts]

      // Remove duplicates by ID (in case there are any)
      const uniquePrompts = allPrompts.reduce((acc, current) => {
        const existing = acc.find(item => item.id === current.id)
        if (!existing) {
          acc.push(current)
        }
        return acc
      }, [] as PromptCardType[])

      // Apply client-side filters
      let filteredPrompts = uniquePrompts

      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        filteredPrompts = filteredPrompts.filter(prompt =>
          prompt.title.toLowerCase().includes(query) ||
          prompt.description.toLowerCase().includes(query)
        )
      }

      if (selectedCategories.length > 0) {
        filteredPrompts = filteredPrompts.filter(prompt =>
          prompt.category && selectedCategories.includes(prompt.category.slug)
        )
      }

      if (selectedTools.length > 0) {
        filteredPrompts = filteredPrompts.filter(prompt =>
          prompt.tool && selectedTools.includes(prompt.tool.slug)
        )
      }

      if (selectedTags.length > 0) {
        filteredPrompts = filteredPrompts.filter(prompt =>
          prompt.tags && prompt.tags.some(tag => 
            tag.slug && selectedTags.includes(tag.slug)
          )
        )
      }

             // Apply sorting
       filteredPrompts.sort((a, b) => {
         const safeParseDate = (dateStr: string | undefined): number => {
           if (!dateStr) return 0
           try {
             return new Date(dateStr).getTime()
           } catch {
             return 0
           }
         }

         switch (sortBy) {
           case "recently_saved":
             // Sort by savedAt for saved prompts, createdAt for owned prompts
             const aDate = (a as any).savedAt || a.createdAt
             const bDate = (b as any).savedAt || b.createdAt
             return safeParseDate(bDate) - safeParseDate(aDate)
           case "newest":
             return safeParseDate(b.createdAt) - safeParseDate(a.createdAt)
           case "oldest":
             return safeParseDate(a.createdAt) - safeParseDate(b.createdAt)
           case "most_popular":
             return (b.likeCount || 0) - (a.likeCount || 0)
           case "most_viewed":
             return (b.viewCount || 0) - (a.viewCount || 0)
           case "alphabetical":
             return a.title.localeCompare(b.title)
           case "trending":
             return (b.trendingScore || 0) - (a.trendingScore || 0)
           default:
             return safeParseDate(b.createdAt) - safeParseDate(a.createdAt)
         }
       })

      setPrompts(filteredPrompts)
    } catch (error: unknown) {
      let errorMessage = "Failed to load your prompts. Please try again."
      let errorDetails: any = {}
      
      if (error && typeof error === 'object') {
        if ('message' in error) {
          errorMessage = (error as Error).message
          errorDetails.message = (error as Error).message
        }
        if ('details' in error) {
          errorDetails.details = (error as any).details
        }
        if ('hint' in error) {
          errorDetails.hint = (error as any).hint
        }
        if ('code' in error) {
          errorDetails.code = (error as any).code
        }
      }
      
      console.error("Error loading all prompts:", errorDetails)
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Reset and reload when filters change
  useEffect(() => {
    loadAllPrompts()
  }, [searchQuery, selectedCategories, selectedTools, selectedTags, sortBy, currentUserId])

  // Handle saving a prompt (only for saved prompts that aren't owned by user)
  const handleToggleSave = async (promptId: string, currentSaveStatus: boolean) => {
    if (!currentUserId) {
      toast({
        title: "Sign in required",
        description: "Please sign in to save prompts.",
        variant: "destructive",
      })
      return
    }

    // Check if this is user's own prompt
    const prompt = prompts.find(p => p.id === promptId)
    if (prompt?.user.id === currentUserId) {
      toast({
        title: "Can't save own prompt",
        description: "You can't save your own prompts.",
        variant: "default",
      })
      return
    }

    const promptTitle = prompt?.title || "Prompt"

    if (!currentSaveStatus) {
      // User wants to save the prompt
      setSelectedPromptId(promptId)
      setSelectedPromptTitle(promptTitle)
      setSaveDialogOpen(true)
    } else {
      // User wants to unsave the prompt
      try {
        // UPDATED: Remove from ALL collections owned by the user, not just default saved_prompts
        
        // First, get all collection IDs owned by the user
        const { data: userCollections, error: collectionsError } = await supabase
          .from("collections")
          .select("id")
          .eq("user_id", currentUserId)

        if (collectionsError) throw collectionsError

        const collectionIds = (userCollections || []).map(c => c.id)

        if (collectionIds.length === 0) {
          throw new Error("No collections found for user")
        }

        // Remove from all user's collections
        const { error } = await supabase
          .from("collection_prompts")
          .delete()
          .eq("prompt_id", promptId)
          .in("collection_id", collectionIds)

        if (error) throw error

        // Remove from local state
        setPrompts(prev => prev.filter(p => p.id !== promptId))
        
        toast({
          title: "Prompt unsaved",
          description: "The prompt has been removed from all your collections.",
        })
      } catch (error) {
        console.error("Error unsaving prompt:", error)
        toast({
          title: "Error",
          description: "Failed to unsave prompt. Please try again.",
          variant: "destructive",
        })
      }
    }
  }

  // Handle successful save
  const handleSaveSuccess = () => {
    toast({
      title: "Success",
      description: `"${selectedPromptTitle}" has been saved to your collection.`,
    })
    setSaveDialogOpen(false)
    setSelectedPromptId(null)
    setSelectedPromptTitle("")
    // Reload to show the newly saved prompt
    loadAllPrompts()
  }

  // Check if a prompt is saved by the user
  const isPromptSaved = (promptId: string): boolean => {
    const prompt = prompts.find(p => p.id === promptId)
    // If it has savedAt, it's a saved prompt
    return !!(prompt && (prompt as any).savedAt)
  }

  // Check if a prompt is owned by the user
  const isPromptOwned = (promptId: string): boolean => {
    const prompt = prompts.find(p => p.id === promptId)
    return prompt?.user.id === currentUserId
  }

  if (!currentUserId) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Bookmark className="mb-4 h-12 w-12 text-muted-foreground" />
          <h3 className="mb-2 text-xl font-semibold">Sign In Required</h3>
          <p className="mb-6 text-center text-muted-foreground">
            Please sign in to view your saved and created prompts.
          </p>
          <Button asChild>
            <Link href="/sign-in">Sign In</Link>
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (isLoading) {
    return (
      <div className="flex h-40 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (prompts.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Heart className="mb-4 h-12 w-12 text-muted-foreground" />
          <h3 className="mb-2 text-xl font-semibold">No Prompts Found</h3>
          <p className="mb-6 text-center text-muted-foreground">
            {searchQuery || selectedCategories.length || selectedTools.length || selectedTags.length
              ? "No saved or created prompts match your current search and filters."
              : "You haven't saved any prompts or created any prompts yet. Start exploring!"}
          </p>
          <div className="flex gap-2">
            <Button asChild className="bg-primary hover:bg-primary/90">
              <Link href="/explore">Explore Prompts</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/create">Create Prompt</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Summary */}
      <div className="text-sm text-muted-foreground">
        Showing {prompts.length} prompts (saved and created by you)
      </div>

      {/* Prompts Grid/List */}
      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {prompts.map((prompt) => (
            <PromptCard 
              key={prompt.id} 
              prompt={prompt} 
              onToggleSave={() => handleToggleSave(prompt.id, isPromptSaved(prompt.id))}
              isSaved={isPromptSaved(prompt.id)}
              isOwner={isPromptOwned(prompt.id)}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {prompts.map((prompt) => (
            <PromptListItem 
              key={prompt.id} 
              prompt={prompt}
              onToggleSave={() => handleToggleSave(prompt.id, isPromptSaved(prompt.id))}
              isSaved={isPromptSaved(prompt.id)}
            />
          ))}
        </div>
      )}

      {/* Save to Collection Dialog */}
      {selectedPromptId && (
        <AddToCollectionDialog
          isOpen={saveDialogOpen}
          onClose={() => setSaveDialogOpen(false)}
          promptId={selectedPromptId}
          promptTitle={selectedPromptTitle}
          onSuccess={handleSaveSuccess}
        />
      )}
    </div>
  )
} 