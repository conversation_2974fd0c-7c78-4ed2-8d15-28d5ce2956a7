"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2, Bookmark } from "lucide-react"
import PromptCard from "@/components/prompt-card"
import PromptListItem from "@/components/prompt-list-item"
import Link from "next/link"
import { createBrowserClient } from "@supabase/ssr"
import { useToast } from "@/hooks/use-toast"
import { transformPromptCard } from "@/lib/transformers"
import type { PromptCard as PromptCardType } from "@/lib/types"
import type { SortOption } from "./SearchAndFilters"
import type { PostgrestError } from '@supabase/supabase-js'

interface SavedPromptsTabProps {
  searchQuery: string
  selectedCategories: string[]
  selectedTools: string[]
  selectedTags: string[]
  sortBy: SortOption
  viewMode: "grid" | "list"
  currentUserId?: string
}

export default function SavedPromptsTab({
  searchQuery,
  selectedCategories,
  selectedTools,
  selectedTags,
  sortBy,
  viewMode,
  currentUserId,
}: SavedPromptsTabProps) {
  const [prompts, setPrompts] = useState<PromptCardType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)
  
  const { toast } = useToast()
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
  const limit = 20

  // Load saved prompts
  const loadSavedPrompts = async (reset: boolean = false) => {
    if (!currentUserId) {
      console.log('No currentUserId provided, skipping saved prompts load')
      setPrompts([])
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      const currentOffset = reset ? 0 : offset

      // UPDATED: Get ALL saved prompts from ANY collection, not just default saved_prompts
      const { data: collectionPrompts, error: collectionPromptsError } = await supabase
        .from("collection_prompts")
        .select(`
          prompt_id, 
          added_at,
          collections!inner(
            user_id
          )
        `)
        .eq("collections.user_id", currentUserId)
        .order("added_at", { ascending: false })
        .range(currentOffset, currentOffset + limit - 1)

      if (collectionPromptsError) {
        console.error("Error fetching collection prompts:", {
          message: collectionPromptsError.message,
          details: collectionPromptsError.details,
          hint: collectionPromptsError.hint,
          code: collectionPromptsError.code,
          originalError: collectionPromptsError
        })
        throw collectionPromptsError
      }

      // Get unique prompt IDs (in case same prompt is in multiple collections)
      const uniquePromptIds = [...new Set((collectionPrompts || []).map(item => item.prompt_id))]
      const savedAtMap = new Map((collectionPrompts || []).map(item => [item.prompt_id, item.added_at]))
      
      console.log('Collection prompts found:', collectionPrompts?.length || 0)
      console.log('Unique saved prompt IDs:', uniquePromptIds)

      // If there are saved prompts, fetch their full details using the optimized view
      let transformedPrompts: PromptCardType[] = []
      
      if (uniquePromptIds.length > 0) {
        try {
          // Use the prompt_card_details view which has all the optimized joins
          console.log('Fetching prompt details for IDs:', uniquePromptIds)
          const { data: promptDetails, error: promptDetailsError } = await supabase
            .from('prompt_card_details')
            .select('*')
            .in('id', uniquePromptIds)
            .eq('is_public', true)

          if (promptDetailsError) {
            console.error('Error fetching prompt details:', {
              message: promptDetailsError.message,
              details: promptDetailsError.details,
              hint: promptDetailsError.hint,
              code: promptDetailsError.code,
              savedPromptIds: uniquePromptIds,
              query: 'prompt_card_details with IDs'
            })
            throw promptDetailsError
          }

          // Transform the data using the standard transformer and add savedAt
          transformedPrompts = (promptDetails || [])
            .map((promptData: any) => {
              try {
                if (!promptData) return null

                // Use the standard transformer
                const prompt = transformPromptCard(promptData)
                
                // Add the savedAt timestamp and mark as saved
                return {
                  ...prompt,
                  isSaved: true, // All prompts in this tab are saved
                  savedAt: savedAtMap.get(promptData.id) || new Date().toISOString(),
                }
              } catch (itemError) {
                console.error(`Error transforming prompt ${promptData?.id}:`, itemError)
                return null
              }
            })
            .filter(Boolean) as PromptCardType[]

        } catch (detailsError) {
          console.error('Error fetching saved prompt details:', detailsError)
          // Continue with empty array if this fails
          transformedPrompts = []
        }
      }

      console.log('Transformed prompts:', transformedPrompts.length)

      // Apply client-side sorting
      transformedPrompts.sort((a, b) => {
        const safeParseDate = (dateStr: string | undefined): number => {
          if (!dateStr) return 0
          try {
            return new Date(dateStr).getTime()
          } catch {
            return 0
          }
        }

        switch (sortBy) {
          case "recently_saved":
            const aDate = (a as any).savedAt || a.createdAt
            const bDate = (b as any).savedAt || b.createdAt
            return safeParseDate(bDate) - safeParseDate(aDate)
          case "newest":
            return safeParseDate(b.createdAt) - safeParseDate(a.createdAt)
          case "oldest":
            return safeParseDate(a.createdAt) - safeParseDate(b.createdAt)
          case "most_popular":
            return (b.likeCount || 0) - (a.likeCount || 0)
          case "most_viewed":
            return (b.viewCount || 0) - (a.viewCount || 0)
          case "alphabetical":
            return a.title.localeCompare(b.title)
          case "trending":
            return (b.trendingScore || 0) - (a.trendingScore || 0)
          default:
            // Default to recently saved
            const aDateDefault = (a as any).savedAt || a.createdAt
            const bDateDefault = (b as any).savedAt || b.createdAt
            return safeParseDate(bDateDefault) - safeParseDate(aDateDefault)
        }
      })

      // Apply client-side filters
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        transformedPrompts = transformedPrompts.filter(prompt =>
          prompt.title.toLowerCase().includes(query) ||
          prompt.description.toLowerCase().includes(query)
        )
      }

      if (selectedCategories.length > 0) {
        transformedPrompts = transformedPrompts.filter(prompt =>
          prompt.category && selectedCategories.includes(prompt.category.slug)
        )
      }

      if (selectedTools.length > 0) {
        transformedPrompts = transformedPrompts.filter(prompt =>
          prompt.tool && selectedTools.includes(prompt.tool.slug)
        )
      }

      if (selectedTags.length > 0) {
        transformedPrompts = transformedPrompts.filter(prompt =>
          prompt.tags && prompt.tags.some(tag => 
            tag.slug && selectedTags.includes(tag.slug)
          )
        )
      }

      if (reset) {
        setPrompts(transformedPrompts)
        setOffset(limit)
      } else {
        setPrompts(prev => [...prev, ...transformedPrompts])
        setOffset(prev => prev + limit)
      }

      setHasMore(transformedPrompts.length === limit)
    } catch (error: unknown) {
      const pgError = error as PostgrestError | Error | unknown
      
      let errorMessage = "Failed to load saved prompts. Please try again."
      let errorDetails: any = {}
      
      if (error && typeof error === 'object') {
        if ('message' in error) {
          errorMessage = (error as Error).message
          errorDetails.message = (error as Error).message
        }
        if ('details' in error) {
          errorDetails.details = (error as any).details
        }
        if ('hint' in error) {
          errorDetails.hint = (error as any).hint
        }
        if ('code' in error) {
          errorDetails.code = (error as any).code
        }
      }
      
      console.error("Error loading saved prompts:", errorDetails)
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Reset and reload when filters change
  useEffect(() => {
    setOffset(0)
    loadSavedPrompts(true)
  }, [searchQuery, selectedCategories, selectedTools, selectedTags, sortBy, currentUserId])

  // Load more prompts
  const loadMore = () => {
    if (!isLoading && hasMore) {
      loadSavedPrompts(false)
    }
  }

  // Handle unsaving a prompt
  const handleUnsave = async (promptId: string) => {
    if (!currentUserId) return

    try {
      // UPDATED: Remove from ALL collections owned by the user, not just default saved_prompts
      
      // First, get all collection IDs owned by the user
      const { data: userCollections, error: collectionsError } = await supabase
        .from("collections")
        .select("id")
        .eq("user_id", currentUserId)

      if (collectionsError) throw collectionsError

      const collectionIds = (userCollections || []).map(c => c.id)

      if (collectionIds.length === 0) {
        throw new Error("No collections found for user")
      }

      // Remove from all user's collections
      const { error } = await supabase
        .from("collection_prompts")
        .delete()
        .eq("prompt_id", promptId)
        .in("collection_id", collectionIds)

      if (error) throw error

      // Remove from local state
      setPrompts(prev => prev.filter(prompt => prompt.id !== promptId))
      
      toast({
        title: "Prompt unsaved",
        description: "The prompt has been removed from all your collections.",
      })
    } catch (error: unknown) {
      let errorMessage = "Failed to unsave prompt. Please try again."
      let errorDetails: any = {}
      
      if (error && typeof error === 'object') {
        if ('message' in error) {
          errorMessage = (error as Error).message
          errorDetails.message = (error as Error).message
        }
        if ('details' in error) {
          errorDetails.details = (error as any).details
        }
        if ('hint' in error) {
          errorDetails.hint = (error as any).hint
        }
        if ('code' in error) {
          errorDetails.code = (error as any).code
        }
      }
      
      console.error("Error unsaving prompt:", errorDetails)
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  // Handle toggle save (for consistency with other components)
  const handleToggleSave = async (promptId: string, currentSaveStatus: boolean) => {
    if (currentSaveStatus) {
      // If currently saved, unsave it
      await handleUnsave(promptId)
    }
    // If not saved, this shouldn't happen in the saved tab, but we'll ignore it
  }

  if (!currentUserId) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Bookmark className="mb-4 h-12 w-12 text-muted-foreground" />
          <h3 className="mb-2 text-xl font-semibold">Sign In Required</h3>
          <p className="mb-6 text-center text-muted-foreground">
            Please sign in to view your saved prompts.
          </p>
          <Button asChild>
            <Link href="/sign-in">Sign In</Link>
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (isLoading && prompts.length === 0) {
    return (
      <div className="flex h-40 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (prompts.length === 0 && !isLoading) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Bookmark className="mb-4 h-12 w-12 text-muted-foreground" />
          <h3 className="mb-2 text-xl font-semibold">No Saved Prompts Found</h3>
          <p className="mb-6 text-center text-muted-foreground">
            {searchQuery || selectedCategories.length || selectedTools.length || selectedTags.length
              ? "No saved prompts match your current search and filters."
              : "You haven't saved any prompts yet. Start exploring and save prompts you like!"}
          </p>
          <Button asChild className="bg-primary hover:bg-primary/90">
            <Link href="/explore">Explore Prompts</Link>
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Prompts Grid/List */}
      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {prompts.map((prompt) => (
            <PromptCard 
              key={prompt.id} 
              prompt={prompt} 
              onToggleSave={handleToggleSave}
              onUnsave={handleUnsave}
              isSaved={true} // All prompts in this tab are saved
            />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {prompts.map((prompt) => (
            <PromptListItem 
              key={prompt.id} 
              prompt={prompt}
              onToggleSave={handleToggleSave}
              isSaved={true}
            />
          ))}
        </div>
      )}

      {/* Load More Button */}
      {hasMore && (
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={loadMore}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More"
            )}
          </Button>
        </div>
      )}
    </div>
  )
} 