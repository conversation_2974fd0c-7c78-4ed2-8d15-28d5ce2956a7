"use client"

import { useState, useEffect } from "react"
import <PERSON>b<PERSON><PERSON><PERSON> from "./TabNavigation"
import SearchAndFilters from "./SearchAndFilters"
import AllPromptsTab from "./AllPromptsTab"
import SavedPromptsTab from "./SavedPromptsTab"
import MyPromptsTab from "./MyPromptsTab"
import type { Category, Tool, Tag, PromptCard } from "@/lib/types"
import type { SortOption } from "./SearchAndFilters"
import { useRouter } from "next/navigation"

interface SavedPageState {
  activeTab: "all" | "saved" | "my"
  searchQuery: string
  selectedCategories: string[]
  selectedTools: string[]
  selectedTags: string[]
  sortBy: SortOption
  viewMode: "grid" | "list"
}

interface SavedPromptsLayoutOptimizedProps {
  currentUserId: string
  initialData: {
    allPrompts: PromptCard[]
    savedPrompts: PromptCard[]
    myPrompts: PromptCard[]
    counts: {
      all: number
      saved: number
      my: number
    }
  }
  availableCategories: Category[]
  availableTools: Tool[]
  availableTags: Tag[]
}

export default function SavedPromptsLayoutOptimized({
  currentUserId,
  initialData,
  availableCategories,
  availableTools,
  availableTags,
}: SavedPromptsLayoutOptimizedProps) {
  const router = useRouter()
  
  // State for the interface
  const [state, setState] = useState<SavedPageState>({
    activeTab: "all",
    searchQuery: "",
    selectedCategories: [],
    selectedTools: [],
    selectedTags: [],
    sortBy: "newest",
    viewMode: "grid",
  })

  // Handle initial hash and hash changes
  useEffect(() => {
    const getTabFromHash = () => {
      if (typeof window === 'undefined') return "all"
      
      const hash = window.location.hash.replace('#', '')
      switch (hash) {
        case 'saved-prompts':
          return "saved"
        case 'my-prompts':
          return "my"
        case 'all-prompts':
          return "all"
        default:
          return "all"
      }
    }

    // Set initial tab from hash
    const initialTab = getTabFromHash()
    setState(prev => ({ 
      ...prev, 
      activeTab: initialTab,
      sortBy: initialTab === "saved" ? "recently_saved" : "newest"
    }))

    // Listen for hash changes
    const handleHashChange = () => {
      const newTab = getTabFromHash()
      setState(prev => ({ 
        ...prev, 
        activeTab: newTab,
        sortBy: newTab === "saved" ? "recently_saved" : "newest"
      }))
    }

    window.addEventListener('hashchange', handleHashChange)
    return () => window.removeEventListener('hashchange', handleHashChange)
  }, [])

  const updateState = (updates: Partial<SavedPageState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }

  const handleTabChange = (tab: "all" | "saved" | "my") => {
    updateState({ 
      activeTab: tab,
      sortBy: tab === "saved" ? "recently_saved" : "newest"
    })
    
    // Update URL hash
    const hashMap = {
      all: "all-prompts",
      saved: "saved-prompts", 
      my: "my-prompts"
    }
    window.location.hash = hashMap[tab]
  }

  const handleSearchChange = (searchQuery: string) => {
    updateState({ searchQuery })
  }

  const handleCategoriesChange = (selectedCategories: string[]) => {
    updateState({ selectedCategories })
  }

  const handleToolsChange = (selectedTools: string[]) => {
    updateState({ selectedTools })
  }

  const handleTagsChange = (selectedTags: string[]) => {
    updateState({ selectedTags })
  }

  const handleSortChange = (sortBy: SortOption) => {
    updateState({ sortBy })
  }

  const handleViewModeChange = (viewMode: "grid" | "list") => {
    updateState({ viewMode })
  }

  const handleClearFilters = () => {
    updateState({
      searchQuery: "",
      selectedCategories: [],
      selectedTools: [],
      selectedTags: [],
    })
  }

  const renderTabContent = () => {
    const commonProps = {
      searchQuery: state.searchQuery,
      selectedCategories: state.selectedCategories,
      selectedTools: state.selectedTools,
      selectedTags: state.selectedTags,
      sortBy: state.sortBy,
      viewMode: state.viewMode,
      currentUserId,
    }

    switch (state.activeTab) {
      case "all":
        return (
          <AllPromptsTab
            {...commonProps}
          />
        )
      case "saved":
        return (
          <SavedPromptsTab
            {...commonProps}
          />
        )
      case "my":
        return (
          <MyPromptsTab
            {...commonProps}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <TabNavigation
        activeTab={state.activeTab}
        onTabChange={handleTabChange}
        counts={initialData.counts}
      />

      {/* Search and Filters */}
      <SearchAndFilters
        searchQuery={state.searchQuery}
        onSearchChange={handleSearchChange}
        selectedCategories={state.selectedCategories}
        onCategoriesChange={handleCategoriesChange}
        selectedTools={state.selectedTools}
        onToolsChange={handleToolsChange}
        selectedTags={state.selectedTags}
        onTagsChange={handleTagsChange}
        sortBy={state.sortBy}
        onSortChange={handleSortChange}
        viewMode={state.viewMode}
        onViewModeChange={handleViewModeChange}
        activeTab={state.activeTab}
        onClearFilters={handleClearFilters}
        availableCategories={availableCategories}
        availableTools={availableTools}
        availableTags={availableTags}
      />

      {/* Tab Content */}
      {renderTabContent()}
    </div>
  )
} 