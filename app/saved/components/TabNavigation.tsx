"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface TabNavigationProps {
  activeTab: "all" | "saved" | "my"
  onTabChange: (tab: "all" | "saved" | "my") => void
  counts: {
    all: number
    saved: number
    my: number
  }
}

export default function TabNavigation({ activeTab, onTabChange, counts }: TabNavigationProps) {
  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1).replace(/\.0$/, "")}M`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1).replace(/\.0$/, "")}K`
    } else {
      return count.toString()
    }
  }

  const tabs = [
    { id: "all" as const, label: "All Prompts", count: counts.all },
    { id: "saved" as const, label: "Saved Prompts", count: counts.saved },
    { id: "my" as const, label: "My Prompts", count: counts.my },
  ]

  return (
    <div className="border-b border-border">
      <div className="flex">
        {tabs.map((tab) => (
          <Button
            key={tab.id}
            variant="ghost"
            className={`flex-1 rounded-none border-b-2 py-4 font-medium transition-colors ${
              activeTab === tab.id
                ? "border-primary text-primary bg-primary/5"
                : "border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/50"
            }`}
            onClick={() => onTabChange(tab.id)}
          >
            <span className="flex items-center gap-2">
              {tab.label}
              <Badge 
                variant="secondary" 
                className={`text-xs ${
                  activeTab === tab.id 
                    ? "bg-primary/20 text-primary" 
                    : "bg-muted text-muted-foreground"
                }`}
              >
                {formatCount(tab.count)}
              </Badge>
            </span>
          </Button>
        ))}
      </div>
    </div>
  )
} 