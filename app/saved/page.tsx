// Add ISR revalidation - 5 minutes for user-specific data
export const revalidate = 300;

import { redirect } from "next/navigation"
import { createReadOnlySupabaseClient } from "@/lib/supabase/server"
import { getSavedPromptsDataServerAction, getCategoriesServerAction, getToolsServerAction, getTagsServerAction } from "@/lib/server-actions"
import SavedPromptsLayoutOptimized from "./components/SavedPromptsLayoutOptimized"

export default async function SavedPage() {
  const supabase = await createReadOnlySupabaseClient()
  
  // Get the current user
  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error || !session?.user) {
    redirect("/auth/signin?redirect=/saved")
  }

  const currentUserId = session.user.id

  // Pre-fetch all data for instant tab switching
  const [savedPromptsData, categories, tools, tags] = await Promise.all([
    getSavedPromptsDataServerAction(currentUserId),
    getCategoriesServerAction(),
    getToolsServerAction(), 
    getTagsServerAction(),
  ])

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">My Prompts</h1>
        <p className="text-muted-foreground">
          Manage your saved prompts and your own creations
        </p>
      </div>

      <SavedPromptsLayoutOptimized
        currentUserId={currentUserId}
        initialData={savedPromptsData}
        availableCategories={categories}
        availableTools={tools}
        availableTags={tags}
      />
    </div>
  )
}
