// test-nlp-optimizations.js
// Simple Node.js test for NLP optimizations

const { analysePrompt } = require('./lib/nlp/index.ts');

async function testOptimizations() {
  console.log('🧠 Testing NLP Optimizations...\n');

  const testCases = [
    {
      name: 'CAT-IG-001: Midjourney Syntax',
      details: {
        title: 'Epic Fantasy Art',
        promptText: '/imagine prompt: A majestic dragon soaring over ancient castles, cinematic lighting, photorealistic --ar 16:9 --v 6'
      },
      expected: { category: 'image-generation', tool: 'midjourney' }
    },
    {
      name: 'PERF-VS-001: Marketing Slogan',
      details: {
        title: 'Brand Slogan',
        promptText: 'Create a catchy slogan for our new eco-friendly water bottle brand'
      },
      expected: { category: 'marketing', tool: 'chatgpt' }
    },
    {
      name: 'USP-GEN-001: Vague Image Request',
      details: {
        title: 'Cool Robot',
        promptText: 'make picture cool robot'
      },
      expected: { category: 'image-generation', tool: 'dall-e' }
    },
    {
      name: 'CAT-CW-002: Creative Writing (Tool Inference)',
      details: {
        title: 'Poem Creation',
        promptText: 'Write a beautiful poem about the changing seasons and the passage of time'
      },
      expected: { category: 'creative-writing', tool: 'chatgpt' }
    },
    {
      name: 'CAT-CG-001: Code Generation (Tool Inference)',
      details: {
        title: 'Python FastAPI',
        promptText: 'Create a Python FastAPI application with user authentication and database integration'
      },
      expected: { category: 'code-generation', tool: 'chatgpt' }
    },
    {
      name: 'CSC-008: Short Prompt Performance',
      details: {
        title: 'List',
        promptText: 'Grocery List'
      },
      expected: { category: 'personal', tool: 'chatgpt' }
    }
  ];

  let passCount = 0;
  let totalCount = testCases.length;

  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);
    console.log(`Input: "${testCase.details.promptText}"`);
    
    const startTime = Date.now();
    
    try {
      const result = await analysePrompt(testCase.details, { 
        includeDebug: true, 
        enableMLFallback: false 
      });
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      console.log(`\n✅ Results (${executionTime}ms):`);
      console.log(`  Category: ${result.category || 'None'} (expected: ${testCase.expected.category})`);
      console.log(`  Tool: ${result.tool || 'None'} (expected: ${testCase.expected.tool})`);
      console.log(`  Tags: ${result.tags?.join(', ') || 'None'}`);
      console.log(`  Confidence: Category=${result.confidence.category}, Tool=${result.confidence.tool}`);
      
      if (result.debug) {
        console.log(`  Debug: ${result.debug.processedTokensCount} tokens processed`);
      }

      // Check if prediction matches expectations
      const categoryMatch = result.category === testCase.expected.category;
      const toolMatch = result.tool === testCase.expected.tool;
      
      if (categoryMatch && toolMatch) {
        console.log(`\n✅ PASS: Both category and tool match expectations`);
        passCount++;
      } else {
        console.log(`\n⚠️  PARTIAL: ${categoryMatch ? 'Category✓' : 'Category✗'} ${toolMatch ? 'Tool✓' : 'Tool✗'}`);
        if (categoryMatch || toolMatch) {
          passCount += 0.5; // Partial credit
        }
      }

      // Performance check for short prompts
      if (testCase.name.includes('Short Prompt Performance') && executionTime > 100) {
        console.log(`⚠️  Performance Warning: ${executionTime}ms (expected <100ms for short prompts)`);
      }

    } catch (error) {
      console.log(`\n❌ ERROR: ${error.message}`);
    }

    console.log('\n' + '─'.repeat(80));
  }

  console.log(`\n🎉 Test Results: ${passCount}/${totalCount} passed (${Math.round(passCount/totalCount*100)}%)`);
  
  if (passCount/totalCount >= 0.6) {
    console.log('✅ Target of 60%+ pass rate achieved!');
  } else {
    console.log('❌ Target of 60%+ pass rate not yet achieved');
  }
}

// Run tests
testOptimizations().catch(console.error); 