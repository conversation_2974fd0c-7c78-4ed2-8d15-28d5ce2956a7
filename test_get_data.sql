-- Test getting actual data from the function with a real user ID
-- First, let's get a real user ID from the database
SELECT 'Testing with real user ID:' as test_info;

-- Get a user ID that has collections with actual saved prompts
SELECT id as user_id, username
FROM profiles
WHERE id IN (
    SELECT DISTINCT c.user_id
    FROM collections c
    JOIN collection_prompts cp ON c.id = cp.collection_id
)
LIMIT 1;

-- Check what collections this user has
SELECT 'User collections:' as info;
SELECT c.id, c.name, c.default_type, COUNT(cp.prompt_id) as prompt_count
FROM collections c
LEFT JOIN collection_prompts cp ON c.id = cp.collection_id
WHERE c.user_id = (SELECT id FROM profiles WHERE id IN (SELECT DISTINCT c.user_id FROM collections c JOIN collection_prompts cp ON c.id = cp.collection_id) LIMIT 1)
GROUP BY c.id, c.name, c.default_type;

-- Check what prompts are in this user's collections
SELECT 'Prompts in user collections:' as info;
SELECT cp.prompt_id, p.title, c.name as collection_name
FROM collection_prompts cp
JOIN collections c ON cp.collection_id = c.id
JOIN prompts p ON cp.prompt_id = p.id
WHERE c.user_id = (SELECT id FROM profiles WHERE id IN (SELECT DISTINCT c.user_id FROM collections c JOIN collection_prompts cp ON c.id = cp.collection_id) LIMIT 1)
LIMIT 5;

-- Debug: Check collection_prompts table directly
SELECT 'Direct collection_prompts check:' as info;
SELECT COUNT(*) as total_collection_prompts FROM collection_prompts;

-- Check if there are any collection_prompts for this user
SELECT 'Collection prompts for this user:' as info;
SELECT COUNT(*) as user_collection_prompts
FROM collection_prompts cp
JOIN collections c ON cp.collection_id = c.id
WHERE c.user_id = (SELECT id FROM profiles WHERE id IN (SELECT DISTINCT c.user_id FROM collections c JOIN collection_prompts cp ON c.id = cp.collection_id) LIMIT 1);

-- Test with that user ID (replace with actual ID from above)
SELECT 'Testing get_prompts_unified function:' as info;
SELECT
    id,
    title,
    is_saved_by_user,
    category_name,
    author_username
FROM get_prompts_unified(
    (SELECT id FROM profiles WHERE id IN (SELECT DISTINCT c.user_id FROM collections c JOIN collection_prompts cp ON c.id = cp.collection_id) LIMIT 1),
    5,
    0
)
LIMIT 5;

-- Test specifically with one of the saved prompt IDs
SELECT 'Testing with specific saved prompt:' as info;
SELECT
    id,
    title,
    is_saved_by_user,
    category_name,
    author_username
FROM get_prompts_unified(
    (SELECT id FROM profiles WHERE id IN (SELECT DISTINCT c.user_id FROM collections c JOIN collection_prompts cp ON c.id = cp.collection_id) LIMIT 1),
    100,
    0
)
WHERE id = '00ce80db-a4d3-4150-a32f-5ce9636c5f06';
