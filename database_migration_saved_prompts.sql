-- Migration: Add saved prompts status to prompt fetching
-- This file contains the database changes needed for the filled bookmark feature

-- Step 1: Create function to get prompts with saved status
CREATE OR REPLACE FUNCTION get_prompts_with_saved_status(
  p_user_id uuid DEFAULT NULL,
  p_limit integer DEFAULT 20,
  p_offset integer DEFAULT 0,
  p_category_slugs text[] DEFAULT NULL,
  p_tool_slugs text[] DEFAULT NULL,
  p_tag_slugs text[] DEFAULT NULL,
  p_ai_model_slugs text[] DEFAULT NULL,
  p_search_query text DEFAULT NULL,
  p_author_id uuid DEFAULT NULL,
  p_sort_by text DEFAULT 'created_at',
  p_sort_order text DEFAULT 'desc'
)
RETURNS TABLE (
  id uuid,
  short_id text,
  title text,
  description text,
  image_url text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  is_public boolean,
  view_count integer,
  primary_tag_id integer,
  category_id integer,
  tool_id integer,
  author_id uuid,
  search_vector tsvector,
  tag_slugs_array text[],
  category_name text,
  category_slug text,
  tool_name text,
  tool_slug text,
  author_username text,
  author_avatar_url text,
  primary_tag_slug text,
  tags jsonb,
  rating bigint,
  comment_count bigint,
  trending_score double precision,
  ai_model_id integer,
  ai_model_provider text,
  ai_model_name text,
  ai_model_slug text,
  ai_model_deprecated boolean,
  is_saved_by_user boolean
) AS $$
BEGIN
  RETURN QUERY
  WITH user_saved_prompts AS (
    SELECT DISTINCT cp.prompt_id
    FROM collection_prompts cp
    JOIN collections c ON cp.collection_id = c.id
    WHERE c.user_id = p_user_id 
      AND p_user_id IS NOT NULL
  )
  SELECT 
    pcd.id,
    pcd.short_id,
    pcd.title,
    pcd.description,
    pcd.image_url,
    pcd.created_at,
    pcd.updated_at,
    pcd.is_public,
    pcd.view_count,
    pcd.primary_tag_id,
    pcd.category_id,
    pcd.tool_id,
    pcd.author_id,
    pcd.search_vector,
    pcd.tag_slugs_array,
    pcd.category_name,
    pcd.category_slug,
    pcd.tool_name,
    pcd.tool_slug,
    pcd.author_username,
    pcd.author_avatar_url,
    pcd.primary_tag_slug,
    pcd.tags,
    pcd.rating,
    pcd.comment_count,
    pcd.trending_score,
    pcd.ai_model_id,
    pcd.ai_model_provider,
    pcd.ai_model_name,
    pcd.ai_model_slug,
    pcd.ai_model_deprecated,
    CASE WHEN usp.prompt_id IS NOT NULL THEN true ELSE false END AS is_saved_by_user
  FROM prompt_card_details pcd
  LEFT JOIN user_saved_prompts usp ON pcd.id = usp.prompt_id
  WHERE pcd.is_public = true
    AND (p_category_slugs IS NULL OR pcd.category_slug = ANY(p_category_slugs))
    AND (p_tool_slugs IS NULL OR pcd.tool_slug = ANY(p_tool_slugs))
    AND (p_tag_slugs IS NULL OR pcd.tag_slugs_array && p_tag_slugs)
    AND (p_ai_model_slugs IS NULL OR pcd.ai_model_slug = ANY(p_ai_model_slugs))
    AND (p_author_id IS NULL OR pcd.author_id = p_author_id)
    AND (p_search_query IS NULL OR pcd.search_vector @@ plainto_tsquery(p_search_query))
  ORDER BY 
    CASE 
      WHEN p_sort_by = 'created_at' AND p_sort_order = 'desc' THEN pcd.created_at
    END DESC,
    CASE 
      WHEN p_sort_by = 'created_at' AND p_sort_order = 'asc' THEN pcd.created_at
    END ASC,
    CASE 
      WHEN p_sort_by = 'rating' AND p_sort_order = 'desc' THEN pcd.rating
    END DESC,
    CASE 
      WHEN p_sort_by = 'rating' AND p_sort_order = 'asc' THEN pcd.rating
    END ASC,
    CASE 
      WHEN p_sort_by = 'trending_score' AND p_sort_order = 'desc' THEN pcd.trending_score
    END DESC,
    CASE 
      WHEN p_sort_by = 'trending_score' AND p_sort_order = 'asc' THEN pcd.trending_score
    END ASC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Step 2: Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_collection_prompts_collection_prompt 
ON collection_prompts (collection_id, prompt_id);

CREATE INDEX IF NOT EXISTS idx_collections_user_default_type 
ON collections (user_id, default_type);

CREATE INDEX IF NOT EXISTS idx_collection_prompts_prompt_id 
ON collection_prompts (prompt_id);

-- Step 3: Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_prompts_with_saved_status TO authenticated;
GRANT EXECUTE ON FUNCTION get_prompts_with_saved_status TO anon; 