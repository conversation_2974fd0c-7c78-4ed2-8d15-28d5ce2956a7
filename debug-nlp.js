// Debug Script for NLP Issues
// Run this in browser console to debug why suggestions aren't appearing

(function() {
  'use strict';
  
  console.log('🔍 NLP Debug Script Started...');
  
  // Check NLP state and functionality
  function debugNLPState() {
    console.log('\n🧠 NLP State Debugging:');
    
    // Check if React state is accessible
    const titleInput = document.getElementById('title');
    if (titleInput && titleInput._owner) {
      console.log('  • React component detected');
    } else {
      console.log('  • React state not directly accessible');
    }
    
    // Check for analyzing state indicators
    const brainIcons = document.querySelectorAll('svg');
    const analyzingText = Array.from(document.querySelectorAll('*')).find(el => 
      el.textContent && el.textContent.includes('Analyzing')
    );
    
    console.log(`  • Brain icons found: ${brainIcons.length}`);
    console.log(`  • "Analyzing" text: ${analyzingText ? '✅ Found' : '❌ Missing'}`);
  }
  
  // Check suggestion box selectors
  function debugSuggestionBoxes() {
    console.log('\n📋 Suggestion Box Debugging:');
    
    // Check various possible selectors
    const selectors = [
      '[class*="bg-blue-50"]',      // Category (blue)
      '[class*="bg-green-50"]',     // Tool (green)  
      '[class*="bg-purple-50"]',    // Tags (purple)
      '[class*="blue-"]',
      '[class*="green-"]', 
      '[class*="purple-"]',
      '[class*="bg-"][class*="-50"]',
      '[class*="suggestion"]',
      '[class*="nlp"]',
      '[class*="brain"]'
    ];
    
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      console.log(`  • ${selector}: ${elements.length} elements`);
      if (elements.length > 0) {
        elements.forEach((el, i) => {
          console.log(`    [${i}] Classes: ${el.className}`);
          console.log(`    [${i}] Text: ${el.textContent.substring(0, 100)}...`);
        });
      }
    });
  }
  
  // Check console logs for NLP activity
  function debugConsoleActivity() {
    console.log('\n📝 Console Activity Monitoring:');
    
    const originalLog = console.log;
    const originalError = console.error;
    let nlpLogs = [];
    
    // Intercept console logs
    console.log = function(...args) {
      if (args[0] && typeof args[0] === 'string') {
        if (args[0].includes('[NLP]') || args[0].includes('[PromptSubmission]')) {
          nlpLogs.push({
            type: 'log',
            message: args[0],
            time: Date.now()
          });
        }
      }
      originalLog.apply(console, args);
    };
    
    console.error = function(...args) {
      if (args[0] && typeof args[0] === 'string') {
        if (args[0].includes('[NLP]') || args[0].includes('nlp') || args[0].includes('analysis')) {
          nlpLogs.push({
            type: 'error', 
            message: args[0],
            time: Date.now()
          });
        }
      }
      originalError.apply(console, args);
    };
    
    return {
      getLogs: () => nlpLogs,
      restore: () => {
        console.log = originalLog;
        console.error = originalError;
      }
    };
  }
  
  // Test NLP manually with detailed logging
  async function testNLPWithDebug() {
    console.log('\n🧪 Manual NLP Test with Debug:');
    
    const titleInput = document.getElementById('title');
    const promptTextArea = document.querySelector('[data-color-mode="dark"]');
    
    if (!titleInput || !promptTextArea) {
      console.log('❌ Form elements not found');
      return;
    }
    
    // Monitor console activity
    const monitor = debugConsoleActivity();
    
    // Set test content
    const testTitle = 'Debug Test: Creative Writing';
    const testPrompt = 'Write a compelling fantasy story with dragons and magic and epic adventures spanning multiple kingdoms';
    
    console.log(`Setting content: "${testTitle}" + "${testPrompt}"`);
    
    titleInput.value = testTitle;
    promptTextArea.value = testPrompt;
    
    // Trigger events
    ['input', 'change', 'keyup', 'keydown'].forEach(eventType => {
      titleInput.dispatchEvent(new Event(eventType, { bubbles: true }));
      promptTextArea.dispatchEvent(new Event(eventType, { bubbles: true }));
    });
    
    console.log('⏳ Waiting 5 seconds for analysis...');
    
    // Wait and check periodically
    for (let i = 0; i < 5; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const logs = monitor.getLogs();
      const recentLogs = logs.filter(log => Date.now() - log.time < 2000);
      
      if (recentLogs.length > 0) {
        console.log(`📝 Recent logs (${i+1}s):`, recentLogs);
      }
      
      // Check for suggestion boxes
      const suggestions = document.querySelectorAll('[class*="bg-"][class*="-50"]');
      if (suggestions.length > 0) {
        console.log(`📋 Found ${suggestions.length} potential suggestion boxes at ${i+1}s`);
        break;
      }
    }
    
    // Final report
    const allLogs = monitor.getLogs();
    console.log('\n📊 Final Analysis Results:');
    console.log(`  • Total NLP logs captured: ${allLogs.length}`);
    console.log(`  • Analysis triggered: ${allLogs.some(log => log.message.includes('Starting analysis'))}`);
    console.log(`  • Analysis completed: ${allLogs.some(log => log.message.includes('Analysis complete'))}`);
    console.log(`  • Errors detected: ${allLogs.filter(log => log.type === 'error').length}`);
    
    // Show all captured logs
    if (allLogs.length > 0) {
      console.log('\n📝 Captured NLP Logs:');
      allLogs.forEach((log, i) => {
        console.log(`  [${i}] ${log.type.toUpperCase()}: ${log.message}`);
      });
    }
    
    monitor.restore();
  }
  
  // Check if NLP analysis is enabled
  function checkNLPEnabled() {
    console.log('\n⚙️ NLP Enablement Check:');
    
    const titleInput = document.getElementById('title');
    const promptTextArea = document.querySelector('[data-color-mode="dark"]');
    
    if (titleInput && promptTextArea) {
      const totalLength = titleInput.value.length + promptTextArea.value.length;
      console.log(`  • Current content length: ${totalLength} chars`);
      console.log(`  • Enable threshold: 50 chars`);
      console.log(`  • Analysis threshold: 30 chars`);
      console.log(`  • Should enable NLP: ${totalLength >= 50 ? '✅ Yes' : '❌ No'}`);
      console.log(`  • Should analyze: ${totalLength >= 30 ? '✅ Yes' : '❌ No'}`);
    }
  }
  
  // Main debug function
  async function runDebugSuite() {
    console.log('🔧 NLP Debug Suite');
    console.log('==================\n');
    
    debugNLPState();
    debugSuggestionBoxes();
    checkNLPEnabled();
    
    console.log('\n🧪 Running manual NLP test...');
    await testNLPWithDebug();
    
    console.log('\n✅ Debug suite complete!');
  }
  
  // Export debug functions
  window.nlpDebug = {
    runDebugSuite,
    debugNLPState,
    debugSuggestionBoxes,
    checkNLPEnabled,
    testNLPWithDebug
  };
  
  console.log('🔧 NLP Debug functions loaded!');
  console.log('🎯 Run: window.nlpDebug.runDebugSuite()');
  
  return window.nlpDebug;
  
})(); 