// Test script to debug prompt fetching issues
// Run with: node scripts/test-prompt-fetching.js

const { createClient } = require('@supabase/supabase-js');

// You'll need to set these environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  console.log('Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testPromptFetching() {
  console.log('🔍 Testing prompt fetching...\n');

  // Test 1: Check if prompt_card_details view exists and has data
  console.log('1. Testing prompt_card_details view...');
  try {
    const { data, error } = await supabase
      .from('prompt_card_details')
      .select('id, title, category_name, tool_name')
      .eq('is_public', true)
      .limit(5);

    if (error) {
      console.error('❌ Error querying prompt_card_details:', error);
    } else {
      console.log('✅ prompt_card_details query successful');
      console.log(`   Found ${data?.length || 0} prompts`);
      if (data && data.length > 0) {
        console.log('   Sample prompt:', data[0]);
      }
    }
  } catch (err) {
    console.error('❌ Exception querying prompt_card_details:', err);
  }

  console.log('\n2. Testing get_prompts_with_saved_status RPC function...');
  try {
    const { data, error } = await supabase.rpc('get_prompts_with_saved_status', {
      p_user_id: null,
      p_limit: 5,
      p_offset: 0
    });

    if (error) {
      console.error('❌ Error calling get_prompts_with_saved_status:', error);
    } else {
      console.log('✅ get_prompts_with_saved_status RPC successful');
      console.log(`   Found ${data?.length || 0} prompts`);
      if (data && data.length > 0) {
        console.log('   Sample prompt:', data[0]);
      }
    }
  } catch (err) {
    console.error('❌ Exception calling get_prompts_with_saved_status:', err);
  }

  console.log('\n3. Testing basic prompts table...');
  try {
    const { data, error } = await supabase
      .from('prompts')
      .select('id, title, is_public')
      .eq('is_public', true)
      .limit(5);

    if (error) {
      console.error('❌ Error querying prompts table:', error);
    } else {
      console.log('✅ prompts table query successful');
      console.log(`   Found ${data?.length || 0} prompts`);
    }
  } catch (err) {
    console.error('❌ Exception querying prompts table:', err);
  }

  console.log('\n4. Testing categories table...');
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('id, name, slug')
      .limit(5);

    if (error) {
      console.error('❌ Error querying categories table:', error);
    } else {
      console.log('✅ categories table query successful');
      console.log(`   Found ${data?.length || 0} categories`);
    }
  } catch (err) {
    console.error('❌ Exception querying categories table:', err);
  }

  console.log('\n5. Testing tools table...');
  try {
    const { data, error } = await supabase
      .from('tools')
      .select('id, name, slug')
      .limit(5);

    if (error) {
      console.error('❌ Error querying tools table:', error);
    } else {
      console.log('✅ tools table query successful');
      console.log(`   Found ${data?.length || 0} tools`);
    }
  } catch (err) {
    console.error('❌ Exception querying tools table:', err);
  }

  console.log('\n🏁 Test complete!');
}

testPromptFetching().catch(console.error); 