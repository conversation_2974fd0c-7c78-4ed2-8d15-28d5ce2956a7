// Test script to verify frontend API calls
// Run with: node scripts/test-frontend-api.js

import { createClient } from '@supabase/supabase-js';

// You'll need to set these environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  console.log('Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Simulate the getPrompts function from api-services.ts
async function testGetPrompts(params) {
  console.log('🔍 Testing getPrompts with params:', params);
  
  try {
    // Test the RPC function first
    if (params.currentUserId) {
      console.log('Testing RPC function...');
      const { data, error } = await supabase.rpc("get_prompts_with_saved_status", {
        p_user_id: params.currentUserId,
        p_limit: params.limit || 20,
        p_offset: params.offset || 0,
        p_category_slugs: params.categorySlugs || null,
        p_tool_slugs: params.toolSlugs || null,
        p_tag_slugs: params.tagSlugs || null,
        p_ai_model_slugs: params.aiModelSlugs || null,
        p_search_query: params.searchQuery || null,
        p_author_id: params.userId || null,
        p_sort_by: params.sortBy || "created_at",
        p_sort_order: params.sortOrder || "desc",
      });

      if (error) {
        console.error('❌ RPC function error:', error);
        throw error;
      }
      
      console.log('✅ RPC function success, data length:', Array.isArray(data) ? data.length : 0);
      if (data && data.length > 0) {
        console.log('Sample prompt:', data[0]);
      }
      return data;
    }
    
    // Test the standard query
    console.log('Testing standard query...');
    let query = supabase
      .from("prompt_card_details")
      .select("*")
      .eq("is_public", true)
      .order(params.sortBy || "created_at", { ascending: (params.sortOrder || "desc") === "asc" })
      .range(params.offset || 0, (params.offset || 0) + (params.limit || 20) - 1);

    if (params.categorySlugs && params.categorySlugs.length > 0) {
      query = query.in("category_slug", params.categorySlugs);
    }
    if (params.toolSlugs && params.toolSlugs.length > 0) {
      query = query.in("tool_slug", params.toolSlugs);
    }
    if (params.tagSlugs && params.tagSlugs.length > 0) {
      query = query.overlaps("tag_slugs_array", params.tagSlugs);
    }
    if (params.aiModelSlugs && params.aiModelSlugs.length > 0) {
      query = query.in("ai_model_slug", params.aiModelSlugs);
    }
    if (params.searchQuery) {
      query = query.textSearch("search_vector", params.searchQuery, { type: 'plain' });
    }
    if (params.userId) {
      query = query.eq("author_id", params.userId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('❌ Standard query error:', error);
      throw error;
    }
    
    console.log('✅ Standard query success, data length:', data?.length || 0);
    if (data && data.length > 0) {
      console.log('Sample prompt:', data[0]);
    }
    return data;
    
  } catch (error) {
    console.error('❌ Error in testGetPrompts:', error);
    return [];
  }
}

async function runTests() {
  console.log('🚀 Starting frontend API tests...\n');

  // Test 1: Basic category filter
  console.log('1. Testing category filter (writing)...');
  await testGetPrompts({
    categorySlugs: ['writing'],
    limit: 5,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  console.log('\n2. Testing tool filter (chatgpt)...');
  await testGetPrompts({
    toolSlugs: ['chatgpt'],
    limit: 5,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  console.log('\n3. Testing tag filter (creative)...');
  await testGetPrompts({
    tagSlugs: ['creative'],
    limit: 5,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  console.log('\n4. Testing trending sort...');
  await testGetPrompts({
    limit: 5,
    sortBy: 'trending_score',
    sortOrder: 'desc'
  });

  console.log('\n5. Testing with user ID (simulated)...');
  await testGetPrompts({
    currentUserId: '00000000-0000-0000-0000-000000000000', // Fake UUID
    limit: 5,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  console.log('\n🏁 Tests complete!');
}

runTests().catch(console.error); 