// Test script to verify specific category fetching
// Run with: node scripts/test-specific-category.js

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testSpecificCategory() {
  console.log('🔍 Testing creative-writing category...\n');

  try {
    // Test the standard query for creative-writing category
    let query = supabase
      .from("prompt_card_details")
      .select("*")
      .eq("is_public", true)
      .eq("category_slug", "creative-writing")
      .order("created_at", { ascending: false })
      .range(0, 19);

    const { data, error } = await query;

    if (error) {
      console.error('❌ Standard query error:', error);
      return;
    }
    
    console.log('✅ Standard query success, data length:', data?.length || 0);
    if (data && data.length > 0) {
      console.log('Sample prompts:');
      data.slice(0, 3).forEach((prompt, index) => {
        console.log(`${index + 1}. ${prompt.title} (${prompt.category_slug})`);
      });
    } else {
      console.log('❌ No prompts found for creative-writing category');
    }

    // Also test the RPC function
    console.log('\n🔍 Testing RPC function for creative-writing...');
    const { data: rpcData, error: rpcError } = await supabase.rpc("get_prompts_with_saved_status", {
      p_user_id: '00000000-0000-0000-0000-000000000000', // Fake UUID
      p_limit: 20,
      p_offset: 0,
      p_category_slugs: ['creative-writing'],
      p_tool_slugs: null,
      p_tag_slugs: null,
      p_ai_model_slugs: null,
      p_search_query: null,
      p_author_id: null,
      p_sort_by: "created_at",
      p_sort_order: "desc",
    });

    if (rpcError) {
      console.error('❌ RPC function error:', rpcError);
      return;
    }
    
    console.log('✅ RPC function success, data length:', Array.isArray(rpcData) ? rpcData.length : 0);
    if (rpcData && rpcData.length > 0) {
      console.log('Sample prompts from RPC:');
      rpcData.slice(0, 3).forEach((prompt, index) => {
        console.log(`${index + 1}. ${prompt.title} (${prompt.category_slug})`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testSpecificCategory(); 