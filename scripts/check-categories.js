// <PERSON>ript to check what categories exist in the database
// Run with: node scripts/check-categories.js

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkCategories() {
  console.log('🔍 Checking categories in database...\n');

  try {
    const { data: categories, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) {
      console.error('❌ Error fetching categories:', error);
      return;
    }

    console.log('✅ Categories found:', categories.length);
    categories.forEach(cat => {
      console.log(`- ${cat.name} (slug: ${cat.slug})`);
    });

    console.log('\n🔍 Checking prompts by category...\n');
    
    for (const category of categories.slice(0, 5)) { // Check first 5 categories
      const { data: prompts, error: promptError } = await supabase
        .from('prompt_card_details')
        .select('id, title, category_slug')
        .eq('category_slug', category.slug)
        .eq('is_public', true)
        .limit(3);

      if (promptError) {
        console.error(`❌ Error fetching prompts for ${category.slug}:`, promptError);
        continue;
      }

      console.log(`📝 ${category.name} (${category.slug}): ${prompts.length} prompts`);
      if (prompts.length > 0) {
        prompts.forEach(p => console.log(`  - ${p.title}`));
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkCategories(); 