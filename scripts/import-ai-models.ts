import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse/sync';
import { supabase } from '../lib/supabase/client';

// Helper functions
function generateSlug(provider: string, modelName: string): string {
  return `${provider.toLowerCase()}-${modelName.toLowerCase()}`
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-');
}

function determineModelType(modelName: string): string {
  if (/image|dall-e|stable diffusion|midjourney|firefly/i.test(modelName)) {
    return 'image';
  } else if (/video|sora|gen-3|lyria/i.test(modelName)) {
    return 'video';
  } else if (/3d|avatar|rodin/i.test(modelName)) {
    return '3d';
  } else if (/audio|music|tts|speech|eleven/i.test(modelName)) {
    return 'audio';
  } else {
    return 'text';
  }
}

// Map provider to the appropriate tool_id in the tools table
// This is a simplified mapping - you may need to adjust based on your actual tools
const providerToolMap: Record<string, number> = {
  'OpenAI': 1,
  'Anthropic': 2,
  'Google': 3,
  'Meta AI': 4,
  'Mistral AI': 5,
  'Stability AI': 6,
  'Midjourney': 7,
  'Adobe': 8,
  'RunwayML': 9,
  'xAI': 10,
};

async function importAIModels() {
  try {
    // Read CSV file
    const csvPath = path.join(process.cwd(), 'docs', 'AI Providers and Models.csv');
    const csvData = fs.readFileSync(csvPath, 'utf8');
    const records = parse(csvData, { columns: true });
    
    console.log(`Found ${records.length} AI models in CSV file`);
    
    // Prepare models for insertion
    const models = records.map((record: any) => {
      const provider = record.Provider;
      const modelName = record['Model Name'];
      
      return {
        provider: provider,
        tool_name: modelName,
        slug: generateSlug(provider, modelName),
        type: determineModelType(modelName),
        deprecated: false, // Assume all models in the list are current
        tool_id: providerToolMap[provider] || 1, // Default to 1 if unknown
      };
    });
    
    // Batch insert models (in chunks to avoid hitting limits)
    const chunkSize = 50;
    for (let i = 0; i < models.length; i += chunkSize) {
      const chunk = models.slice(i, i + chunkSize);
      
      const { data, error } = await supabase
        .from('ai_models')
        .upsert(chunk, { onConflict: 'provider,tool_name' });
      
      if (error) {
        console.error(`Error importing AI models (chunk ${i/chunkSize + 1}):`, error);
      } else {
        console.log(`Successfully imported chunk ${i/chunkSize + 1} (${chunk.length} models)`);
      }
    }
    
    console.log('AI model import completed');
  } catch (error) {
    console.error('Error in importAIModels:', error);
  }
}

// Run the import function
importAIModels();
