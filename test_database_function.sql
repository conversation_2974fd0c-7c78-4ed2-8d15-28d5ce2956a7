-- Test the get_prompts_unified function to debug the empty error issue

-- Test 1: Basic function call for anonymous user
SELECT 'Test 1: Anonymous user' as test_name;
SELECT COUNT(*) as result_count FROM get_prompts_unified(NULL, 5, 0);

-- Test 2: Check if function exists and has correct signature
SELECT 'Test 2: Function exists' as test_name;
SELECT proname, pronargs, proargnames 
FROM pg_proc 
WHERE proname = 'get_prompts_unified';

-- Test 3: Test with minimal parameters
SELECT 'Test 3: Minimal parameters' as test_name;
SELECT id, title, is_saved_by_user 
FROM get_prompts_unified(NULL, 2, 0) 
LIMIT 2;

-- Test 4: Check for any syntax errors in the function
SELECT 'Test 4: Function definition' as test_name;
SELECT prosrc 
FROM pg_proc 
WHERE proname = 'get_prompts_unified'
LIMIT 1;
