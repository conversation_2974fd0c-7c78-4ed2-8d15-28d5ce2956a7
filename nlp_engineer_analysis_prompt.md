# AI Prompt: NLP System Analysis & Optimization

## Role
You are a **Senior NLP Engineer** with 10+ years of experience in text classification systems, rule-based engines, and performance optimization. Your expertise includes:

- Building and optimizing NLP classification systems for production environments
- Designing rule-based engines with keyword matching and pattern recognition
- Performance tuning for real-time text analysis (sub-100ms latency requirements)
- Confidence scoring algorithms and threshold optimization
- Hybrid ML/rule-based approaches for text classification
- Client-side NLP optimization and bundle size management

## Task
I need you to analyze and optimize our NLP prompt classification system that categorizes user prompts into categories (creative-writing, code-generation, marketing, etc.), predicts appropriate AI tools (ChatGPT, Claude, Midjourney, etc.), and suggests relevant tags.

**Current Performance Issues:**
- Only 35.44% test pass rate (28/79 tests passing)
- Average latency of 104ms (target: <80ms)
- Many predictions defaulting to "other" instead of specific categories/tools
- Confidence scores often below expected thresholds

## System Architecture

Our system uses a **hybrid rule-based + ML fallback approach** with these components:

### Core Files Structure:
```
lib/nlp/
├── index.ts              # Main analysis service and orchestration
├── ruleEngine.ts         # Scoring algorithms and confidence calculation  
├── keywordData.ts        # Static keyword mappings and regex patterns
├── tokenise.ts           # Text preprocessing with compromise.js
├── keywordExtractor.ts   # Keyphrase extraction with retext-keywords
└── mlClassifier.ts       # ML fallback using FastText

app/prompt/submit/page.tsx # Frontend integration (lines 78-388)
```

### Current Workflow:
1. **Text Preprocessing**: Tokenization and keyphrase extraction
2. **Pattern Detection**: Regex-based tool identification  
3. **Keyword Scoring**: Category scoring based on keyword matches
4. **Tag Prediction**: With category-based boosting
5. **ML Fallback**: For low-confidence cases
6. **Confidence Calculation**: Final thresholding and suggestions

### Performance Baseline:
- **Overall Pass Rate**: 35.44% (28/79 tests)
- **Category Accuracy**: 92.41% 
- **Tool Detection**: 84.81%
- **Tag Accuracy**: 93% (Jaccard Index)
- **Average Latency**: 104.35ms
- **Confidence Scores**: Category 0.80, Tool 0.75, Tags 0.78

## Key Failure Patterns to Analyze

Based on our test results, these are the main issues:

### 1. Confidence Score Issues:
- CAT-CW-001: Category confidence 0.70 (expected 0.80-1.00)
- CSC-006: Tool confidence 0.70 (expected 0.80-1.00)
- CSC-007: Category confidence 0.85 (expected 0.95-1.00)

### 2. Tool Detection Failures:
- CAT-CW-002: Expected ChatGPT, got "other"
- CAT-CG-001: Expected ChatGPT, got "other" 
- CAT-MKT-001: Expected ChatGPT, got "other"

### 3. Category Misclassifications:
- CAT-IG-001: Expected image-generation, got "other"
- PERF-VS-001: Expected marketing, got "other"
- USP-GEN-001: Expected image-generation, got "other"

### 4. Performance Issues:
- CSC-008: 144.18ms (expected ≤100ms)
- Multiple tests exceeding latency thresholds

## Your Analysis Tasks

### 1. Root Cause Analysis
Examine the rule engine logic and identify why we're getting so many "other" predictions:

**Rule Engine (`ruleEngine.ts`):**
- Analyze `calculateKeywordScores()`, `calculatePatternScores()`, `calculatePhraseScores()`
- Review threshold logic in `getTopSuggestion()` and `getTopTagSuggestions()`
- Evaluate the "safe gap multiplier" (currently 1.5)
- Assess score combination and boosting strategies

**Keyword Data (`keywordData.ts`):**
- Review keyword coverage for each category/tool
- Analyze regex pattern effectiveness for tool detection
- Identify missing keywords causing misclassifications
- Check for conflicting or overlapping keywords

### 2. Performance Bottleneck Analysis
Identify what's causing the 104ms average latency:
- Text preprocessing pipeline efficiency
- Keyphrase extraction overhead
- Scoring algorithm complexity
- ML fallback trigger frequency

### 3. Confidence Scoring Issues
Analyze why confidence scores are below expected thresholds:
- Are thresholds too conservative?
- Is the confidence calculation algorithm flawed?
- Are we penalizing correct predictions?

## Required Deliverables

### 1. **Immediate Fixes** (Target: 60%+ pass rate)
Provide specific, actionable recommendations:

**Threshold Adjustments:**
- Exact threshold values to test for categories, tools, tags
- Safe gap multiplier optimization
- Minimum score threshold adjustments

**Keyword Enhancements:**
- Specific keywords to add for failing test cases
- Regex pattern improvements for tool detection
- Keyword weight adjustments

**Algorithm Improvements:**
- Code changes for scoring algorithms
- Better score combination strategies
- Enhanced confidence calculation methods

### 2. **Performance Optimization** (Target: <80ms)
**Specific optimizations:**
- Bottleneck identification and solutions
- Code-level optimizations for heavy operations
- Caching strategies
- Bundle size reduction techniques

### 3. **Implementation Plan**
- Priority-ranked list of changes (high/medium/low impact)
- Step-by-step implementation sequence
- Risk assessment for each change
- A/B testing recommendations

## Success Criteria

**Primary Goals:**
- Increase pass rate from 35.44% to 70%+
- Reduce latency from 104ms to <80ms
- Reduce "other" category predictions by 50%+
- Improve confidence score reliability

**Constraints:**
- Client-side execution only (browser environment)
- Bundle size limitations
- Real-time performance requirements
- No server-side ML inference available

## Expected Output Format

Please structure your response as:

1. **Executive Summary** (2-3 sentences on main issues)
2. **Root Cause Analysis** (specific problems identified)
3. **Immediate Action Items** (ranked by impact/effort)
4. **Code Changes** (specific modifications with examples)
5. **Performance Optimizations** (concrete improvements)
6. **Implementation Roadmap** (step-by-step plan)

Focus on actionable, specific recommendations with clear rationale. Provide code examples where appropriate and prioritize changes by impact vs. effort ratio. 