# Automated NLP Test Suite for Submit Page

## Overview

This comprehensive test suite contains **25 diverse test cases** designed to evaluate the NLP suggestion system across all categories, tools, and various levels of specificity and ambiguity. It automatically tests category detection, tool recommendation, and tag generation while providing detailed analytics.

## Test Categories Covered

### 🎨 Creative Writing (3 tests)
- **CW-001**: Fantasy Story - Highly Specific (dragons, magic systems, world-building)
- **CW-002**: Creative Writing - Ambiguous (minimal description, tests fallback)
- **CW-003**: Fiction Writing - Medium Specificity (character development, Victorian setting)

### 💻 Code Generation (3 tests)  
- **CG-001**: Python Algorithm - Very Specific (complex sorting algorithms, performance analysis)
- **CG-002**: JavaScript - Ambiguous (debugging request without details)
- **CG-003**: React Component - Medium Specificity (dashboard with hooks and TypeScript)

### 🖼️ Image Generation (3 tests)
- **IG-001**: Midjourney - Explicit Pattern (cyberpunk portrait with Midjourney syntax)
- **IG-002**: DALL-E Style - Artistic Description (surreal landscape with photorealistic style)
- **IG-003**: Stable Diffusion - Technical Prompt (professional headshot with technical parameters)

### 📈 Marketing (2 tests)
- **MK-001**: Marketing Campaign - Comprehensive (sustainable fashion, multi-channel strategy)
- **MK-002**: Simple Ad Copy - Basic (fitness app promotion)

### 🏢 Business (1 test)
- **BZ-001**: Business Strategy - Detailed (SaaS market analysis, go-to-market strategy)

### 🔍 Research (2 tests)
- **RS-001**: Academic Research - Complex (literature review with citations)
- **RS-002**: Simple Research Query - Basic (renewable energy trends)

### 🎵 Audio (2 tests)
- **AU-001**: Music Generation - Suno Style (orchestral soundtrack with genre tags)
- **AU-002**: Voice Synthesis - ElevenLabs (professional narration voice)

### 🎬 Video (1 test)
- **VD-001**: Runway Video Generation (futuristic city with motion graphics)

### 📧 Email (1 test)
- **EM-001**: Email Campaign - Professional (onboarding sequence with CTAs)

### 🔧 Edge Cases (5 tests)
- **EC-001**: Very Short Prompt (minimal content: "Help me.")
- **EC-002**: Mixed Categories (combines code, creative writing, and marketing)
- **EC-003**: Claude-Specific Pattern (step-by-step analysis request)
- **EC-004**: Technical Documentation (API documentation without examples)
- **EC-005**: Long Complex Prompt (enterprise architecture with 500+ words)

## Test Variations

### Specificity Levels
- **High Specificity**: Detailed prompts with specific requirements, examples, and technical details
- **Medium Specificity**: Clear purpose with moderate detail and context
- **Low Specificity/Ambiguous**: Minimal details, vague requests, tests fallback behavior

### Content Variations
- **No Description**: Tests with `description: null` to test title/prompt-only analysis
- **With Instructions**: Additional guidance field testing
- **With Examples**: Input/output examples to test contextual understanding
- **Length Variations**: From 8 characters ("Help me.") to 500+ word enterprise prompts

### Expected Outcomes
Each test defines expected results for:
- **Category**: Primary category classification
- **Tool**: Recommended AI tool
- **Tags**: Array of relevant tags
- **Confidence**: Expected confidence levels (monitored but not strictly validated)

## How to Run the Tests

### Quick Start
1. Navigate to `/prompt/submit` page
2. Open browser console (F12)
3. Copy and paste the entire `automated-nlp-test-suite.js` script
4. Run: `window.nlpTestSuite.runAllTests()`

### Individual Test
```javascript
// Run a specific test by ID
const result = await window.nlpTestSuite.runSingleTest(
  window.nlpTestSuite.testCases.find(t => t.id === 'CW-001')
);
```

### Selective Testing
```javascript
// Run only creative writing tests
const creativeTests = window.nlpTestSuite.testCases.filter(t => t.id.startsWith('CW'));
for (const test of creativeTests) {
  await window.nlpTestSuite.runSingleTest(test);
}
```

## Understanding Results

### Real-Time Output
Each test provides immediate feedback:
```
🧪 Running Test CW-001: Fantasy Story - Highly Specific
📝 Title: "Epic Dragon Quest Generator"
📄 Description: Generate compelling fantasy adventures...
💬 Prompt Length: 234 chars
⏳ Waiting for NLP analysis...
📊 Results:
  Category: creative-writing ✅ (expected: creative-writing)
  Tool: chatgpt ✅ (expected: chatgpt)  
  Tags: [fantasy, creative, story, character] ✅ (expected: [fantasy, creative, story, character])
  Confidence: Category=high, Tool=medium, Tags=high
```

### Summary Report
After all tests complete, you get a comprehensive report:

```
🎯 TEST SUITE SUMMARY REPORT
================================
📊 Total Tests: 25
⏱️  Duration: 127s

📈 Success Rates:
  Category: 22/25 (88%)
  Tool: 19/25 (76%)
  Tags: 23/25 (92%)

🏷️  Category Performance:
  creative-writing: 3/3 (100%)
  code-generation: 2/3 (67%)
  image-generation: 3/3 (100%)
  marketing: 2/2 (100%)
  business: 1/1 (100%)
  research: 2/2 (100%)
  audio: 2/2 (100%)
  video: 1/1 (100%)
  email: 1/1 (100%)
  other: 0/1 (0%)

🔧 Tool Performance:
  chatgpt: 15/18 (83%)
  claude: 2/3 (67%)
  midjourney: 1/1 (100%)
  dall-e: 1/1 (100%)
  stable-diffusion: 1/1 (100%)
  jasper-ai: 0/1 (0%)
  perplexity-ai: 2/2 (100%)
  suno: 1/1 (100%)
  elevenlabs: 1/1 (100%)
  runway: 1/1 (100%)
```

### Failed Tests Analysis
The report details any failures:
```
❌ Failed Tests (3):
  CG-002: JavaScript - Ambiguous Request
    Category: got "other" expected "code-generation"
    Tool: got "chatgpt" expected "chatgpt" ✅
  
  EC-001: Very Short Prompt  
    Category: got "other" expected "other" ✅
    Tags: got [] expected [help]
```

### Confidence Levels
Tracks confidence distribution:
```
🎯 Average Confidence:
  category: High=18, Medium=5, Low=2
  tool: High=12, Medium=8, Low=5  
  tags: High=20, Medium=4, Low=1
```

## Data Export and Analysis

### Export Results
```javascript
// Copy results to clipboard for external analysis
copy(JSON.stringify(window.nlpTestResults, null, 2));
```

### Results Structure
Each test result contains:
```javascript
{
  testId: "CW-001",
  testName: "Fantasy Story - Highly Specific",
  input: {
    title: "Epic Dragon Quest Generator",
    description: "Generate compelling fantasy adventures...",
    promptLength: 234,
    hasInstructions: true,
    hasExamples: true
  },
  expected: {
    category: "creative-writing",
    tool: "chatgpt", 
    tags: ["fantasy", "creative", "story", "character"]
  },
  actual: {
    category: "creative-writing",
    tool: "chatgpt",
    tags: ["fantasy", "creative", "story", "character"],
    confidence: { category: "high", tool: "medium", tags: "high" }
  },
  success: {
    category: true,
    tool: true, 
    tags: true
  },
  debug: { /* detailed analysis info */ }
}
```

## Interpreting Results for Algorithm Refinement

### High Success Areas
- **Image Generation**: 100% success indicates strong pattern recognition for visual prompts
- **Audio Generation**: Suno and ElevenLabs patterns work well
- **Single-Purpose Categories**: Research, video, email show good classification

### Areas for Improvement
- **Ambiguous Prompts**: Low-specificity tests may need better fallback logic
- **Mixed-Category Prompts**: Complex requests combining multiple domains
- **Tool Specificity**: Some tools may need stronger distinguishing patterns

### Confidence Analysis
- **High Confidence + Success**: Algorithm is performing well
- **High Confidence + Failure**: Algorithm is confident but wrong (needs pattern adjustment)
- **Low Confidence + Success**: Algorithm is correct but uncertain (needs stronger signals)
- **Low Confidence + Failure**: Algorithm is confused (needs better training data)

## Customizing Tests

### Adding New Tests
```javascript
window.nlpTestSuite.testCases.push({
  id: 'CUSTOM-001',
  name: 'Your Custom Test',
  title: 'Test Title',
  description: 'Test description',
  promptText: 'Your test prompt content...',
  expectedCategory: 'your-category',
  expectedTool: 'your-tool',
  expectedTags: ['tag1', 'tag2']
});
```

### Modifying Expectations
Adjust expected results based on algorithm improvements:
```javascript
// Update expectations for a specific test
const test = window.nlpTestSuite.testCases.find(t => t.id === 'CW-001');
test.expectedTags = ['fantasy', 'adventure', 'creative', 'story'];
```

## Performance Monitoring

### Timing Analysis
- **Per Test**: ~5 seconds (3s debounce + 2s analysis)
- **Full Suite**: ~2-3 minutes for 25 tests
- **Memory Usage**: Monitor browser console for performance warnings

### Optimization Suggestions
- Run tests during off-peak development times
- Consider splitting into smaller test batches for rapid iteration
- Use selective testing for specific algorithm improvements

## Integration with Development Workflow

### Before Algorithm Changes
1. Run full test suite to establish baseline
2. Export results for comparison

### After Algorithm Changes  
1. Run full test suite again
2. Compare success rates and confidence levels
3. Focus on regression analysis (tests that got worse)

### Continuous Monitoring
- Weekly runs to catch gradual degradation
- A/B testing with different algorithm versions
- User feedback correlation with test results

This comprehensive test suite provides the foundation for data-driven NLP algorithm refinement and ensures consistent performance across diverse use cases. 