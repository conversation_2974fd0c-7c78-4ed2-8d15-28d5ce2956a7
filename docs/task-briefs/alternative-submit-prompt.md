## UX Rationale for Edit Prompt Page

The redesign of the Edit Prompt page aims to enhance the user experience by:

*   **Visceral Level**: Creating a visually clean, organised, and less daunting interface. The use of familiar UI patterns and clear visual hierarchy for form fields and actions will make the page feel professional and trustworthy.
*   **Behavioural Level**: Streamlining the editing process through progressive disclosure for advanced options. Essential fields are readily accessible, while less frequently modified details are neatly tucked away but easily reachable. Clear feedback on save/delete actions and intuitive image management improve usability.
*   **Reflective Level**: Empowering users by making the editing process straightforward, reinforcing their role as contributors. A clear "Delete" option with confirmation respects user control, and successful updates provide positive reinforcement, encouraging further contributions.

## Implementation: Enhanced Edit Prompt Page

I will create a new route structure for the redesigned edit page at `app/prompt/edit-v2/[shortId]/`.

1.  **New file: `/Users/<USER>/Documents/Code/PromptHQ/app/prompt/edit-v2/[shortId]/page.tsx`**
    This will be the main page component for the redesigned edit prompt form. It will fetch the existing prompt data, allow modifications, and handle updates or deletion.

2.  **New file: `/Users/<USER>/Documents/Code/PromptHQ/app/prompt/edit-v2/[shortId]/loading.tsx`**
    This will be the loading skeleton for the redesigned edit page.

3.  **New file: `/Users/<USER>/Documents/Code/PromptHQ/app/prompt/edit-v2/[shortId]/not-found.tsx`**
    This will be the "not found" page specific to the redesigned edit route.

```typescript
// Create new file: /Users/<USER>/Documents/Code/PromptHQ/app/prompt/edit-v2/[shortId]/page.tsx
// Content will be based on the existing edit page and incorporate UX improvements.
// For brevity, I will provide the improved page.tsx here.
// The original /Users/<USER>/Documents/Code/PromptHQ/app/prompt/edit/[shortId]/page.tsx will remain untouched.
```

```typescript
// Create new file: /Users/<USER>/Documents/Code/PromptHQ/app/prompt/edit-v2/[shortId]/loading.tsx
// Content will be similar to the existing loading.tsx for the edit page.
// Example:
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button"; // Added
import { Loader2 } from "lucide-react"; // Added

export default function EditPromptLoadingV2() {
  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mx-auto max-w-3xl">
        <CardHeader>
          <Skeleton className="h-8 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Title */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>
          {/* Description */}
          <div className="grid grid-cols-4 items-start gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-20 w-full" /></div>
          </div>
          {/* Prompt Text */}
          <div className="grid grid-cols-4 items-start gap-4">
             <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-32 w-full" /></div>
          </div>
          {/* Image Upload Placeholder */}
          <div className="grid grid-cols-4 items-start gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-24 w-full" /></div>
          </div>
          
          {/* Category & Tool */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>
          {/* AI Model */}
           <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>
          {/* Tags */}
          <div className="grid grid-cols-4 items-start gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-10 w-full" /></div>
          </div>
          {/* Visibility */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Skeleton className="h-4 w-full col-span-1" />
            <div className="col-span-3"><Skeleton className="h-6 w-1/2" /></div>
          </div>
           {/* Advanced Options Toggle Placeholder */}
          <div className="grid grid-cols-4 items-center gap-4 border-t pt-6">
             <div></div>
             <div className="col-span-3"><Skeleton className="h-12 w-full" /></div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Skeleton className="h-10 w-24" /> {/* Delete Button */}
          <Skeleton className="h-10 w-32" /> {/* Save Changes Button */}
        </CardFooter>
      </Card>
    </div>
  );
}
```

```typescript
// Create new file: /Users/<USER>/Documents/Code/PromptHQ/app/prompt/edit-v2/[shortId]/not-found.tsx
// Content will be similar to the existing not-found.tsx for the original edit page.
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { AlertCircle, Home, Search } from "lucide-react"; // Added icons
import { Card, CardContent } from "@/components/ui/card"; // Added Card

export default function EditPromptNotFoundV2() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <Card className="rounded-xl border shadow-sm">
            <CardContent className="p-8">
              <div className="mb-6">
                <AlertCircle className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h1 className="text-2xl font-bold text-foreground mb-2">Prompt Not Found</h1>
                <p className="text-muted-foreground">
                  The prompt you are trying to edit doesn't exist or may have been removed.
                </p>
              </div>
              
              <div className="space-y-3">
                <Button asChild className="w-full">
                  <Link href="/">
                    <Home className="mr-2 h-4 w-4" />
                    Back to Home
                  </Link>
                </Button>
                
                <Button variant="outline" asChild className="w-full">
                  <Link href="/search">
                    <Search className="mr-2 h-4 w-4" />
                    Search Prompts
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
```

```typescript
// Create new file: /Users/<USER>/Documents/Code/PromptHQ/app/prompt/edit-v2/[shortId]/page.tsx
"use client"

import type React from "react" // Added type import for React
import { useState, useEffect, useMemo } from "react"
import { useRouter, useParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge" // Added Badge
import { X, Upload, Loader2, CheckCircle, PlusCircle, Lock, Globe, AlertCircle, Trash2, ArrowLeft } from "lucide-react" // Added icons
import { allTools } from "@/lib/data/tools"
import { allTags } from "@/lib/data/tags"
import { allCategories } from "@/lib/data/categories"
import { 
  getPromptByShortId, 
  updatePrompt, 
  deletePrompt, // Added deletePrompt
  getAIModelsForTool, 
  getUserCollections, 
  addPromptToCollection, 
  createCollection // Added createCollection
} from "@/lib/api-services"
import type { AIModel as BaseAIModel, Collection, CreatePromptData, Prompt } from "@/lib/types" // Added Prompt type
import dynamic from "next/dynamic"
import { createTitleSlug } from "@/lib/utils/url-helpers"
import { createBrowserClient } from "@supabase/ssr"
import { toast } from "@/components/ui/use-toast" // Using shadcn/ui toast
import { Combobox, type ComboboxOption } from "@/components/ui/combobox"
import { TagInput, type TagOption } from "@/components/ui/tag-input"
import CreateCollectionDialog from "@/components/create-collection-dialog" // Changed from modal to dialog
import { Switch } from "@/components/ui/switch"
import { DragDropImageUpload } from "@/components/ui/drag-drop-image-upload"
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from "@/components/ui/alert-dialog" // For delete confirmation

// Import the code editor with dynamic import to avoid SSR issues
const CodeEditor = dynamic(
  () => import("@uiw/react-textarea-code-editor").then((mod) => mod.default),
  { ssr: false }
)

// Extended AIModel interface with displayName property
interface AIModel extends BaseAIModel {
  displayName?: string;
}

export default function EditPromptPageV2() {
  const router = useRouter()
  const params = useParams()
  const shortId = params?.shortId as string
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
  
  // Form state
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [prompt, setPrompt] = useState<Prompt | null>(null) // Changed type to Prompt
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [promptText, setPromptText] = useState("")
  const [instructions, setInstructions] = useState("")
  const [exampleInput, setExampleInput] = useState("")
  const [exampleOutput, setExampleOutput] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedTool, setSelectedTool] = useState<string>("") 
  const [selectedAIModel, setSelectedAIModel] = useState<string>("") 
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageRemoved, setImageRemoved] = useState(false) 
  const [isPublic, setIsPublic] = useState(true)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false) // Default to false for cleaner initial UI
  const [userEnteredAiModel, setUserEnteredAiModel] = useState<string>("")
  
  // Tags state
  const [selectedTags, setSelectedTags] = useState<string[]>([])
    
  // Collections state - No collection selection in edit form for now to simplify, could be added back
  // const [collections, setCollections] = useState<Collection[]>([])
  // const [selectedCollection, setSelectedCollection] = useState<string | null>(null)
  // const [isLoadingCollections, setIsLoadingCollections] = useState(false)
  // const [isCreateCollectionDialogOpen, setIsCreateCollectionDialogOpen] = useState(false)
  // const [myPromptsCollectionId, setMyPromptsCollectionId] = useState<string | null>(null)
  
  // Models state
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]) 
  const [isLoadingModels, setIsLoadingModels] = useState(false)
    
  // User state
  const [user, setUser] = useState<any>(null) // Consider using Supabase User type
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  
  // Error state
  const [error, setError] = useState<string | null>(null)

  // Delete dialog state
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Check authentication and redirect if not logged in
  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true)
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session?.user) {
          router.push(`/sign-in?redirect=/prompt/edit-v2/${shortId}`) // Redirect to new edit page
          return
        }
        setUser(session.user)
      } catch (error) {
        console.error('Error checking auth:', error)
        router.push(`/sign-in?redirect=/prompt/edit-v2/${shortId}`)
      } finally {
        setIsCheckingAuth(false)
      }
    }
    
    checkAuth()
  }, [router, supabase, shortId])
  
  // Effect to fetch AI models when tool changes
  useEffect(() => {
    const fetchModels = async () => {
      if (!selectedTool) {
        setAvailableModels([])
        return
      }
      
      setIsLoadingModels(true)
      try {
        const selectedToolObj = allTools.find(tool => String(tool.id) === selectedTool)
        if (!selectedToolObj) {
          setAvailableModels([])
          return
        }
        const models = await getAIModelsForTool(selectedToolObj.slug)
        const formattedModels = models.map(model => ({
          ...model,
          displayName: model.provider ? `${model.name} (${model.provider})` : model.name
        })).sort((a, b) => a.name.localeCompare(b.name))
        setAvailableModels(formattedModels)
      } catch (error) {
        console.error('Error fetching AI models:', error)
        setAvailableModels([])
      } finally {
        setIsLoadingModels(false)
      }
    }
    
    fetchModels()
  }, [selectedTool])

  // Fetch prompt data
  useEffect(() => {
    const fetchPrompt = async () => {
      if (!shortId || isCheckingAuth || !user) return
      
      setIsLoading(true)
      try {
        const promptData = await getPromptByShortId(shortId)
        
        if (!promptData) {
          setError("Prompt not found.")
          return
        }
        
        if (promptData.user?.id !== user.id) {
          setError("You don't have permission to edit this prompt.")
          return
        }
        
        setPrompt(promptData)
        setTitle(promptData.title || "")
        setDescription(promptData.description || "")
        setPromptText(promptData.text || "") // Ensure prompt.text is used
        setInstructions(promptData.instructions || "")
        setExampleInput(promptData.exampleInput || "")
        setExampleOutput(promptData.exampleOutput || "")
        setIsPublic(promptData.isPublic !== false)
        
        if (promptData.category && typeof promptData.category === 'object' && promptData.category.id) {
          setSelectedCategory(String(promptData.category.id))
        }
        
        if (promptData.tool && typeof promptData.tool === 'object' && promptData.tool.id) {
          setSelectedTool(String(promptData.tool.id))
        }
        
        if (promptData.ai_model?.id) {
          setSelectedAIModel(String(promptData.ai_model.id))
        }
        setUserEnteredAiModel(promptData.user_entered_ai_model || ""); // Set user entered model
        
        if (promptData.tags && Array.isArray(promptData.tags)) {
          const tagIds = promptData.tags.map((tag: any) => 
            typeof tag === 'object' && tag.id ? String(tag.id) : String(tag)
          ).filter(Boolean)
          setSelectedTags(tagIds)
        }
        
        if (promptData.imageUrl) {
          setImagePreview(promptData.imageUrl)
          setImageRemoved(false)
        }
        
        if (promptData.instructions || promptData.exampleInput || promptData.exampleOutput) {
          setShowAdvancedOptions(true)
        }
        
        setError(null)
      } catch (error) {
        console.error('Error fetching prompt for editing:', error)
        setError("Failed to load prompt data. Please try again.")
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchPrompt()
  }, [shortId, user, isCheckingAuth])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    if (!user || !prompt) {
      toast({ title: "Error", description: "User or prompt data is missing. Please refresh.", variant: "destructive" })
      return
    }

    // Validations
    if (!title.trim() || title.length < 5 || title.length > 100) {
      toast({ title: "Invalid title", description: "Title must be between 5 and 100 characters.", variant: "destructive"})
      return
    }
    if (description && (description.length < 20 || description.length > 500)) {
      toast({ title: "Invalid description", description: "Description must be between 20 and 500 characters if provided.", variant: "destructive"})
      return
    }
    if (!promptText.trim() || promptText.length < 30 || promptText.length > 10000) {
      toast({ title: "Invalid prompt text", description: "Prompt text must be between 30 and 10,000 characters.", variant: "destructive"})
      return
    }
    if (!selectedCategory || !selectedTool || selectedTags.length === 0 || selectedTags.length > 5) {
      toast({ title: "Missing required fields", description: "Category, AI Tool, and 1-5 Tags are required.", variant: "destructive"})
      return
    }

    setIsSubmitting(true)
    try {
      const updateData: Partial<CreatePromptData> = { // Use CreatePromptData for consistency
        title: title.trim(),
        description: description?.trim() || undefined,
        promptText: promptText.trim(),
        instructions: instructions?.trim() || undefined,
        exampleInput: exampleInput?.trim() || undefined,
        exampleOutputText: exampleOutput?.trim() || undefined,
        categoryId: Number(selectedCategory),
        toolId: Number(selectedTool),
        tagIds: selectedTags.map(Number),
        isPublic,
        aiModelId: selectedAIModel ? Number(selectedAIModel) : null,
        userEnteredAiModel: userEnteredAiModel.trim() || undefined,
        // Image handling: imageUrl will be managed by updatePrompt if imageFile or imageRemoved is passed
        imageUrl: imageRemoved ? null : (imageFile ? undefined : imagePreview) // Pass null if removed, undefined if new file (handled by updatePrompt), else existing
      };

      if (imageFile) {
        (updateData as any).imageFile = imageFile; // Pass file object if new image selected
      } else if (imageRemoved) {
        (updateData as any).removeCurrentImage = true; // Signal to remove existing image
      }
      
      const result = await updatePrompt(prompt.id, user.id, updateData)
      
      if (!result.success) {
        throw new Error(result.error || 'Update failed')
      }
      
      toast({ title: "Prompt updated!", description: `"${title}" has been successfully updated.`, variant: "default" })
      setIsRedirecting(true)
      setIsSubmitting(false)

      // Use the updatedSlug from the result for redirection
      const redirectUrl = result.updatedSlug 
        ? `/${result.updatedSlug}` // Assuming updatedSlug is the full path like `prompt/cat/tool/tag/title/shortId`
        : `/prompt/${shortId}`; // Fallback to old shortId if slug isn't returned (should not happen ideally)

      setTimeout(() => {
        router.push(redirectUrl)
      }, 1500)

    } catch (error) {
      console.error('Error updating prompt:', error)
      toast({ title: "Failed to update prompt", description: error instanceof Error ? error.message : "An error occurred.", variant: "destructive" })
      setIsSubmitting(false)
    }
  }

  // Handle prompt deletion
  const handleDelete = async () => {
    if (!user || !prompt) return
    
    setIsDeleting(true)
    try {
      const { success, error } = await deletePrompt(prompt.id, user.id) // Pass userId for authorization
      
      if (!success) {
        throw new Error(error?.message || "Failed to delete prompt")
      }

      toast({ title: "Prompt deleted", description: `"${promptToDelete?.title}" has been successfully deleted.`})
      setIsDeleteDialogOpen(false)
      setPromptToDelete(null)
      router.push('/') // Redirect to homepage after deletion
      
    } catch (error: any) {
      console.error('Error deleting prompt:', error)
      toast({ title: "Failed to delete prompt", description: error.message || "An error occurred.", variant: "destructive" })
      setIsDeleting(false)
    }
  }

  if (isCheckingAuth || isLoading) {
    return <EditPromptLoadingV2 />; // Use the new loading component
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="mx-auto max-w-3xl">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center gap-4">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-6 w-6" />
                <h2 className="text-xl font-semibold">{error}</h2>
              </div>
              <Button onClick={() => router.push('/')}>Go Home</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mx-auto max-w-3xl">
        <CardHeader>
          <Button variant="ghost" size="sm" onClick={() => router.back()} className="mb-2 self-start pl-0 text-muted-foreground">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
          <CardTitle className="text-2xl">Edit Your Prompt</CardTitle>
          <CardDescription>
            Refine your prompt's details. Your contributions help the community!
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {/* Title Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Title <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <Input id="title" value={title} onChange={(e) => setTitle(e.target.value)} placeholder="e.g., Epic Fantasy Story Idea Generator" required disabled={isSubmitting || isRedirecting} />
                {title && title.length < 5 && (<p className="mt-1 text-sm text-red-500">Title must be at least 5 characters</p>)}
                {title && title.length > 100 && (<p className="mt-1 text-sm text-red-500">Title must be at most 100 characters</p>)}
              </div>
            </div>

            {/* Prompt Text Field */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="prompt-text" className="text-right">
                Prompt Text <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-2">
                <div style={{ resize: 'vertical', overflow: 'auto', minHeight: '200px', border: '1px solid hsl(var(--border))', borderRadius: 'var(--radius)' }}>
                  <CodeEditor id="prompt-text" value={promptText} language="markdown" placeholder="Enter the full text of your AI prompt here..." onChange={(e) => setPromptText(e.target.value)} padding={15} data-color-mode="dark" style={{ fontSize: '14px', backgroundColor: 'hsl(var(--background))', color: 'hsl(var(--foreground))', fontFamily: 'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace', height: '100%', border: 'none' }} disabled={isSubmitting || isRedirecting} />
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <div>{promptText.length < 30 && (<p className="text-red-500">Minimum 30 characters</p>)}{promptText.length > 10000 && (<p className="text-red-500">Maximum 10,000 characters</p>)}</div>
                  <div>{promptText.length}/10,000</div>
                </div>
              </div>
            </div>
            
            {/* Image Upload Field */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="image" className="text-right">Image</Label>
              <div className="col-span-3 space-y-2">
                <DragDropImageUpload imageFile={imageFile} imagePreview={imagePreview || prompt?.imageUrl} onImageChange={(file, preview) => { setImageFile(file); setImagePreview(preview); if(!file) setImageRemoved(true); else setImageRemoved(false); }} disabled={isSubmitting || isRedirecting} maxSizeMB={1.5} placeholder={{ title: "Click to upload or drag and drop", subtitle: "PNG, JPG, GIF up to 1.5MB" }}/>
                <p className="text-sm text-muted-foreground">Update or remove the image for your prompt.</p>
              </div>
            </div>

            {/* Category Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="category" className="text-right">Category <span className="text-red-500">*</span></Label>
              <div className="col-span-3"><Combobox options={allCategories.map(cat => ({ value: String(cat.id), label: cat.name }))} value={selectedCategory} onChange={setSelectedCategory} placeholder="Select a category" emptyText="No categories found" disabled={isSubmitting || isRedirecting} /></div>
            </div>

            {/* AI Tool Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tool" className="text-right">AI Tool <span className="text-red-500">*</span></Label>
              <div className="col-span-3"><Combobox options={allTools.map(tool => ({ value: String(tool.id), label: tool.name }))} value={selectedTool} onChange={val => { setSelectedTool(val); setSelectedAIModel(""); }} placeholder="Select an AI tool" emptyText="No AI tools found" disabled={isSubmitting || isRedirecting} /></div>
            </div>

            {/* AI Model Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="model" className="text-right">AI Model</Label>
              <div className="col-span-3">
                {selectedTool ? (<div className="space-y-2"><Combobox options={availableModels.map(model => ({ value: String(model.id), label: model.displayName || model.name }))} value={selectedAIModel} onChange={setSelectedAIModel} placeholder="Select model (optional)" emptyText="No models found" disabled={isSubmitting || isLoadingModels || isRedirecting} />{isLoadingModels && (<div className="flex items-center text-sm text-muted-foreground"><Loader2 className="mr-2 h-3 w-3 animate-spin" />Loading models...</div>)}<div className="flex items-center space-x-2"><span className="text-sm text-muted-foreground">Or enter manually:</span><Input placeholder="e.g., GPT-4o" value={userEnteredAiModel} onChange={e => setUserEnteredAiModel(e.target.value)} disabled={isSubmitting || isRedirecting} className="flex-1" /></div></div>) : (<div className="text-sm text-muted-foreground">Select an AI Tool first</div>)}
              </div>
            </div>

            {/* Tags Selection */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="tags" className="text-right">Tags <span className="text-red-500">*</span></Label>
              <div className="col-span-3"><TagInput placeholder="Add up to 5 tags" selectedTags={selectedTags} onTagsChange={setSelectedTags} options={allTags.map(tag => ({ value: String(tag.id), label: tag.name }))} maxTags={5} className={isSubmitting || isRedirecting ? 'opacity-50 pointer-events-none' : ''} />{selectedTags.length === 0 && (<p className="mt-1 text-sm text-red-500">Add at least one tag</p>)}</div>
            </div>

            {/* Visibility Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="visibility" className="text-right">Visibility</Label>
              <div className="col-span-3 flex items-center space-x-3"><Switch id="isPublic" checked={isPublic} onCheckedChange={setIsPublic} disabled={isSubmitting || isRedirecting} /><div className="flex flex-col"><span className="text-sm font-medium">{isPublic ? 'Public' : 'Private'}</span><p className="text-sm text-muted-foreground">{isPublic ? 'Visible to everyone' : 'Only visible to you'}</p></div></div>
            </div>

            {/* Advanced Options Toggle */}
            <div className="grid grid-cols-4 items-center gap-4 border-t pt-6">
              <div></div>
              <div className="col-span-3"><div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border"><div className="flex flex-col"><h3 className="text-sm font-medium">Advanced Options</h3><p className="text-xs text-muted-foreground">Add instructions and examples</p></div><Button type="button" variant="outline" onClick={() => setShowAdvancedOptions(!showAdvancedOptions)} disabled={isSubmitting || isRedirecting} className="ml-4">{showAdvancedOptions ? 'Hide' : 'Show'} Advanced</Button></div></div>
            </div>

            {/* Advanced Options */}
            {showAdvancedOptions && (
              <>
                <div className="grid grid-cols-4 items-start gap-4"><Label htmlFor="description-adv" className="text-right pt-2">Description</Label><div className="col-span-3"><Textarea id="description-adv" value={description} onChange={e => setDescription(e.target.value)} placeholder="A brief, compelling description..." rows={3} disabled={isSubmitting || isRedirecting} /><p className="text-sm text-muted-foreground mt-1">Benefits, use cases, etc. (min 20, max 500 chars)</p>{description && description.length < 20 && (<p className="mt-1 text-sm text-red-500">Min 20 characters</p>)}{description && description.length > 500 && (<p className="mt-1 text-sm text-red-500">Max 500 characters</p>)}</div></div>
                <div className="grid grid-cols-4 items-start gap-4"><Label htmlFor="instructions" className="text-right pt-2">Instructions</Label><div className="col-span-3"><Textarea id="instructions" value={instructions} onChange={e => setInstructions(e.target.value)} placeholder="How to use this prompt effectively..." rows={3} disabled={isSubmitting || isRedirecting} /><p className="text-sm text-muted-foreground mt-1">Guidance for using the prompt.</p></div></div>
                <div className="grid grid-cols-4 items-start gap-4"><Label htmlFor="example-input" className="text-right pt-2">Example Input</Label><div className="col-span-3"><Textarea id="example-input" value={exampleInput} onChange={e => setExampleInput(e.target.value)} placeholder="Example of input for the prompt..." rows={3} disabled={isSubmitting || isRedirecting} /><p className="text-sm text-muted-foreground mt-1">Show what input works well.</p></div></div>
                <div className="grid grid-cols-4 items-start gap-4"><Label htmlFor="example-output" className="text-right pt-2">Example Output</Label><div className="col-span-3"><Textarea id="example-output" value={exampleOutput} onChange={e => setExampleOutput(e.target.value)} placeholder="Example of output from the prompt..." rows={3} disabled={isSubmitting || isRedirecting} /><p className="text-sm text-muted-foreground mt-1">Show typical output.</p></div></div>
              </>
            )}
          </CardContent>
          <CardFooter className="flex justify-between items-center pt-6">
            <Button type="button" variant="destructive" onClick={() => setIsDeleteDialogOpen(true)} disabled={isSubmitting || isRedirecting || isDeleting} className="flex items-center"><Trash2 className="mr-2 h-4 w-4" /> Delete Prompt</Button>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting || isRedirecting}>Cancel</Button>
              <Button type="submit" disabled={isSubmitting || isRedirecting} className="bg-accent-green hover:bg-accent-green/90">
                {isSubmitting ? (<><Loader2 className="mr-2 h-4 w-4 animate-spin" />Updating...</>) : 
                 isRedirecting ? (<><CheckCircle className="mr-2 h-4 w-4" />Updated!</>) : 
                 'Update Prompt'}
              </Button>
            </div>
          </CardFooter>
        </form>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your prompt
              and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete} 
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90" // Destructive variant
            >
              {isDeleting ? (<><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Deleting...</>) : "Yes, delete prompt"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

```
## Explanation of Changes for Edit Prompt Page (`/app/prompt/edit-v2/[shortId]/page.tsx`)

### 1. Visceral Design (Look & Feel)
*   **Cleaner Layout**: The form is structured within a `Card` component, providing a consistent and contained look.
*   **Improved Spacing & Typography**: Standard shadcn/ui components (`Label`, `Input`, `Textarea`, `Button`) ensure consistent typography and spacing, aligning with the existing design system.
*   **Visual Hierarchy**: Labels are clearly associated with their inputs. Required fields are marked with a red asterisk. Action buttons (Update, Cancel, Delete) are clearly delineated.
*   **Progressive Disclosure**: Advanced options (Description, Instructions, Examples) are initially hidden within a collapsible section, making the primary form less daunting. The toggle button clearly indicates its purpose.
*   **Image Upload**: The `DragDropImageUpload` component provides a modern and visually appealing way to handle image uploads, with clear preview and remove options.
*   **Loading/Submitting States**: Buttons show loaders and change text to provide clear visual feedback during asynchronous operations.

### 2. Behavioural Design (Usability & Functionality)
*   **Streamlined Flow**:
    *   Essential fields (Title, Prompt Text, Category, Tool, Tags, Visibility) are presented first.
    *   Advanced fields are optional and grouped under a "Show Advanced Options" toggle, reducing initial cognitive load.
*   **Clear Guidance**:
    *   Placeholders are used for most fields.
    *   Helper text (e.g., for username format, character limits, image constraints) is provided below relevant inputs.
*   **Intuitive Controls**:
    *   `Combobox` is used for selecting Category and Tool, allowing search within options.
    *   `TagInput` provides an intuitive way to add and remove tags.
    *   `DragDropImageUpload` simplifies image handling with drag & drop and click-to-upload.
    *   `Switch` component is used for the public/private visibility toggle.
*   **Error Handling**:
    *   Client-side validation is implemented for key fields (e.g., title length, tag count).
    *   Error messages are displayed directly below the relevant field or via toasts for API errors.
*   **Delete Functionality**:
    *   A "Delete Prompt" button is clearly visible.
    *   An `AlertDialog` is used for confirmation before deletion, preventing accidental data loss.
*   **Feedback**:
    *   Toasts are used for success and error messages for update and delete operations.
    *   Loading states on buttons provide immediate feedback during API calls.
    *   Redirect to the updated prompt page or homepage after successful operations reinforces completion.
*   **Data Pre-fill**: The form is pre-filled with the existing prompt's data, making it easy for users to see what they are editing.
*   **Image Management**:
    *   Displays the current image.
    *   Allows uploading a new image (which replaces the old one).
    *   Allows removing the current image (sets `imageUrl` to null).

### 3. Reflective Design (Meaning & Connection)
*   **Clear Purpose**: The page title ("Edit Your Prompt") and description ("Refine your prompt's details. Your contributions help the community!") frame the task positively.
*   **User Control**: The ability to easily edit all aspects of their prompt, change visibility, and delete it gives users a sense of ownership and control.
*   **Positive Reinforcement**: Success toasts ("Prompt updated!", "Prompt deleted!") provide positive feedback.
*   **Contextual Back Button**: A "Back" button allows users to easily navigate away without losing context.

### Technical Implementation Details
*   **State Management**: Uses `useState` for all form fields, loading states, and error messages.
*   **Data Fetching**:
    *   `useEffect` fetches the prompt data using `getPromptByShortId` when the component mounts or `shortId` changes.
    *   Owner verification is done after fetching the prompt.
    *   Data for selectors (categories, tools, tags, AI models) is imported from static data files or fetched as needed (AI models).
*   **API Calls**:
    *   `updatePrompt` is called on form submission. The payload includes logic to handle new image uploads (`imageFile`) or image removal (`removeCurrentImage: true`).
    *   `deletePrompt` is called after delete confirmation.
*   **Routing**: Uses `useRouter` for navigation (e.g., after successful update/delete, or if user is not owner/logged out).
*   **Component Re-use**: Leverages existing shadcn/ui components and custom components like `DragDropImageUpload`, `Combobox`, `TagInput`.
*   **Slug Generation**: Uses `createTitleSlug` for generating the title part of the URL, and constructs the full URL for redirection after a successful update. The `updatePrompt` API service also handles slug regeneration internally.
*   **Code Editor**: The `CodeEditor` component is used for `promptText`, `exampleInput`, and `exampleOutput` to provide a better editing experience for code-like content, with Markdown styling.

### Styling
*   Uses Tailwind CSS and existing global styles.
*   Form elements are styled according to `shadcn/ui` conventions.
*   The layout uses a `Card` component for a consistent look and feel.
*   Grid layout (`grid-cols-4`) is used for aligning labels and inputs, similar to the submit form.

This revised edit page should provide a more user-friendly, efficient, and reassuring experience for users managing their prompts.