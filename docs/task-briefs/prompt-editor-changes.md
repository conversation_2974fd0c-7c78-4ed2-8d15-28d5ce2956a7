Okay, switching to CodeMirror 6 (likely via a React wrapper like `@uiw/react-codemirror` for easier integration) is a solid choice for robust Markdown and custom syntax highlighting. It's much more powerful and stable for these tasks than manipulating `contentEditable` divs directly.

Here's a plan and the code modifications for your frontend developer:

**Plan Overview:**

1.  **Remove `@uiw/react-textarea-code-editor`:** Uninstall this package.
2.  **Install CodeMirror 6 Packages:** Add `@uiw/react-codemirror` and necessary CodeMirror 6 modules (for Markdown, themes, and custom highlighting).
3.  **Create a Reusable `MarkdownPromptEditor` Component:** This will encapsulate the CodeMirror 6 setup.
4.  **Implement `[[variable]]` Highlighting:** Use CodeMirror's extension system (Decorations or a ViewPlugin) to style `[[...]]` patterns.
5.  **Integrate `MarkdownPromptEditor`:** Replace the old editor in:
    *   `app/prompt/submit/page.tsx`
    *   `app/prompt/edit/[shortId]/page.tsx`
    *   `app/prompt/remix/[shortId]/page.tsx`
6.  **Add CSS:** For the variable highlighting.

**Briefing for Your Frontend Developer:**

"We're standardising our rich text and code editing experience by replacing `@uiw/react-textarea-code-editor` and the custom `contentEditable` editor on the submit page with **CodeMirror 6**, using the `@uiw/react-codemirror` wrapper.

**Key Goals:**

1.  **Consistent Editor:** Use the same powerful editor component across submit, edit, and remix pages for prompt text, instructions, and examples.
2.  **Reliable Markdown Highlighting:** Leverage CodeMirror's mature Markdown mode.
3.  **Stable `[[Variable]]` Highlighting:** Implement custom syntax highlighting for `[[variables]]` using CodeMirror's extension system. This will be much more robust than the previous `innerHTML` manipulation, fixing cursor jumps, focus issues, and lag.
4.  **Performance & Customisability:** CodeMirror 6 is modular, allowing us to include only what's needed and customise its appearance and behaviour.

**Action Items:**

1.  **Update `package.json`:**
    *   Remove `@uiw/react-textarea-code-editor`.
    *   Add the following (or latest compatible versions):
        ```json
        "dependencies": {
          // ... other dependencies
          "@uiw/react-codemirror": "^4.23.0", // Or latest
          "@codemirror/lang-markdown": "^6.2.5",
          "@codemirror/language-data": "^6.1.0", // For Markdown's sub-languages like JS, Python in fenced blocks
          "@codemirror/view": "^6.28.2",
          "@codemirror/state": "^6.4.1",
          // Choose a theme, e.g., one dark or github dark
          "@uiw/codemirror-theme-github": "^4.23.0", 
          // ...
        }
        ```
    *   Run `pnpm install`.

2.  **Create `components/ui/markdown-prompt-editor.tsx`:**
    *   This new component will wrap `@uiw/react-codemirror` and include the logic for Markdown support and our custom variable highlighting.

3.  **Update Page Components:**
    *   Replace the existing editor implementations in `app/prompt/submit/page.tsx`, `app/prompt/edit/[shortId]/page.tsx`, and `app/prompt/remix/[shortId]/page.tsx` with the new `<MarkdownPromptEditor />`.

4.  **Add CSS for Variable Highlighting:**
    *   Add styles to `app/globals.css` (or a relevant CSS module) for the `cm-variable-highlight` class we'll define in the editor component.

---

**Code Implementation Details:**

**1. New Component: `components/ui/markdown-prompt-editor.tsx`**
This will be the core of our new editor.

```tsx
// File: /Users/<USER>/Documents/Code/PromptHQ/components/ui/markdown-prompt-editor.tsx
"use client";

import React from "react";
import CodeMirror from "@uiw/react-codemirror";
import { markdown, markdownLanguage } from "@codemirror/lang-markdown";
import { languages } from "@codemirror/language-data"; // For fenced code blocks in Markdown
import { Extension } from "@codemirror/state";
import { ViewPlugin, Decoration, DecorationSet, ViewUpdate, EditorView } from "@codemirror/view";
import { githubDark } from '@uiw/codemirror-theme-github'; // Example theme

interface MarkdownPromptEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  minHeight?: string;
  className?: string;
}

// Variable Highlighting Logic for CodeMirror 6
// This plugin finds text like [[variable]] and applies a CSS class.
const variableHighlightMatcher = /\[\[(.*?)\]\]/g;

const variableHighlightPlugin = ViewPlugin.fromClass(
  class {
    decorations: DecorationSet;

    constructor(view: EditorView) {
      this.decorations = this.getDecorations(view);
    }

    update(update: ViewUpdate) {
      if (update.docChanged || update.viewportChanged) {
        this.decorations = this.getDecorations(update.view);
      }
    }

    getDecorations(view: EditorView): DecorationSet {
      const widgets: Range<Decoration>[] = [];
      for (const { from, to } of view.visibleRanges) {
        const text = view.state.doc.sliceString(from, to);
        let match;
        while ((match = variableHighlightMatcher.exec(text))) {
          const start = from + match.index;
          const end = start + match[0].length;
          widgets.push(
            Decoration.mark({
              class: "cm-variable-highlight", // CSS class for styling
            }).range(start, end)
          );
        }
      }
      return Decoration.set(widgets);
    }
  },
  {
    decorations: (v) => v.decorations,
  }
);

const MarkdownPromptEditor: React.FC<MarkdownPromptEditorProps> = ({
  value,
  onChange,
  placeholder,
  disabled = false,
  minHeight = "200px",
  className = "",
}) => {
  const extensions: Extension[] = [
    markdown({ base: markdownLanguage, codeLanguages: languages }),
    variableHighlightPlugin, // Add our custom plugin
    EditorView.lineWrapping, // Enable line wrapping
  ];

  return (
    <CodeMirror
      value={value}
      onChange={onChange}
      placeholder={placeholder || "Enter your prompt text here. Use [[variables]] for placeholders..."}
      editable={!disabled}
      theme={githubDark} // Using GitHub Dark theme as an example
      extensions={extensions}
      minHeight={minHeight}
      className={`text-sm rounded-md border border-input bg-background ${className}`} // Base styling
      style={{
        // Ensure font consistency if needed, though theme might handle it
        fontFamily: 'ui-monospace, SFMono-Regular, SF Mono, Consolas, Liberation Mono, Menlo, monospace',
      }}
    />
  );
};

export default MarkdownPromptEditor;
```

**2. CSS for Variable Highlighting**

Add this to `/Users/<USER>/Documents/Code/PromptHQ/app/globals.css` (or a relevant imported CSS file):

```css
/* /Users/<USER>/Documents/Code/PromptHQ/app/globals.css */

/* ... existing styles ... */

@layer utilities {
  .cm-variable-highlight {
    @apply bg-yellow-200 dark:bg-yellow-700/60 px-0.5 rounded-sm text-yellow-900 dark:text-yellow-100 font-medium;
    /* Slightly less padding than before as it's within the editor's flow */
  }
}
```

**3. Update `app/prompt/submit/page.tsx`**

Replace the custom `PromptTextEditor` component and its usage with the new `MarkdownPromptEditor`.

```tsx
// File: /Users/<USER>/Documents/Code/PromptHQ/app/prompt/submit/page.tsx
"use client"

import type React from "react"
import { useState, useEffect, useMemo, useCallback, useRef } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
// Remove Textarea if no longer used elsewhere, or keep if needed
// import { Textarea } from "@/components/ui/textarea" 
import { Badge } from "@/components/ui/badge"
import { X, Upload, Loader2, CheckCircle, PlusCircle, Lock, Globe, Brain, ArrowLeft } from "lucide-react"
import { allTools } from "@/lib/data/tools"
import { allTags } from "@/lib/data/tags"
import { allCategories } from "@/lib/data/categories"
import { 
  getAIModelsForTool, 
  createPrompt, 
  getUserCollections, 
  addPromptToCollection, 
  createCollection 
} from "@/lib/api-services"
import type { AIModel as BaseAIModel, Collection, CreatePromptData } from "@/lib/types"
import { createTitleSlug } from "@/lib/utils/url-helpers"
import { createBrowserClient } from "@supabase/ssr"
import { toast } from "@/components/ui/use-toast"
import { Combobox, type ComboboxOption } from "@/components/ui/combobox"
import { TagInput, type TagOption } from "@/components/ui/tag-input"
import CreateCollectionDialog from "@/components/create-collection-dialog"
import { Switch } from "@/components/ui/switch"
import { DragDropImageUpload } from "@/components/ui/drag-drop-image-upload"

// Import the new editor
import MarkdownPromptEditor from "@/components/ui/markdown-prompt-editor"; 

// Extended AIModel interface with displayName property
interface AIModel extends BaseAIModel {
  displayName?: string;
}

// REMOVE the old PromptTextEditor component entirely from this file

export default function SubmitPromptPage() {
  const router = useRouter()
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
  
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  // Initial sample text for the prompt editor to demonstrate highlighting
  const [promptText, setPromptText] = useState("This is a sample prompt with a [[placeholder_variable]] and another one [[like_this_one]]. Users can also paste `inline code` or text with **markdown** like styles.")
  const [instructions, setInstructions] = useState("")
  const [exampleInput, setExampleInput] = useState("")
  const [exampleOutput, setExampleOutput] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("")
  const [selectedTool, setSelectedTool] = useState<string>("") 
  const [selectedAIModel, setSelectedAIModel] = useState<string>("") 
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isPublic, setIsPublic] = useState(true)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false) 
  const [userEnteredAiModel, setUserEnteredAiModel] = useState<string>("")
  
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [collections, setCollections] = useState<Collection[]>([])
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null)
  const [isLoadingCollections, setIsLoadingCollections] = useState(false)
  const [isCreateCollectionDialogOpen, setIsCreateCollectionDialogOpen] = useState(false)
  const [myPromptsCollectionId, setMyPromptsCollectionId] = useState<string | null>(null)
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]) 
  const [isLoadingModels, setIsLoadingModels] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [nlpSuggestions, setNlpSuggestions] = useState<{
    category?: string;
    tool?: string;
    tags?: string[];
    confidence?: {
      category?: 'high' | 'medium' | 'low';
      tool?: 'high' | 'medium' | 'low';
      tags?: 'high' | 'medium' | 'low';
    };
  }>({})
  const [hasAcceptedSuggestions, setHasAcceptedSuggestions] = useState({
    category: false,
    tool: false,
    tags: false
  })
  const [lastAnalyzedContent, setLastAnalyzedContent] = useState("")

  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true)
      try {
        const { data: { session } } = await supabase.auth.getSession()
        if (!session?.user) {
          router.push('/sign-in?redirect=/prompt/submit')
          return
        }
        setUser(session.user)
      } catch (error) {
        console.error('Error checking auth:', error)
        router.push('/sign-in?redirect=/prompt/submit')
      } finally {
        setIsCheckingAuth(false)
      }
    }
    checkAuth()
  }, [router, supabase])
  
  useEffect(() => {
    const fetchModels = async () => {
      if (!selectedTool) {
        setAvailableModels([])
        return
      }
      setIsLoadingModels(true)
      try {
        const selectedToolObj = allTools.find(tool => String(tool.id) === selectedTool)
        if (!selectedToolObj) {
          setAvailableModels([])
          return
        }
        const models = await getAIModelsForTool(selectedToolObj.slug)
        const formattedModels = models.map(model => ({
          ...model,
          displayName: model.provider ? `${model.name} (${model.provider})` : model.name
        })).sort((a, b) => a.name.localeCompare(b.name))
        setAvailableModels(formattedModels)
      } catch (error) {
        console.error('Error fetching AI models:', error)
        setAvailableModels([])
      } finally {
        setIsLoadingModels(false)
      }
    }
    fetchModels()
  }, [selectedTool])

  useEffect(() => {
    const fetchCollections = async () => {
      if (!user) return
      setIsLoadingCollections(true)
      try {
        const userCollections = await getUserCollections(user.id, { includePrivate: true })
        setCollections(userCollections)
        const myPromptsCollection = userCollections.find(
          collection => collection.name === "My Prompts" && collection.isDefault
        )
        if (myPromptsCollection) {
          setMyPromptsCollectionId(myPromptsCollection.id)
        }
      } catch (error) {
        console.error('Error fetching collections:', error)
      } finally {
        setIsLoadingCollections(false)
      }
    }
    fetchCollections()
  }, [user])

  const analyzePromptContent = useCallback(
    async (title: string, description: string, promptText: string) => {
      if (hasAcceptedSuggestions.category || hasAcceptedSuggestions.tool || hasAcceptedSuggestions.tags) {
        return;
      }
      const combinedLength = title.length + description.length + promptText.length;
      if (combinedLength < 50) {
        return;
      }
      const currentContent = `${title}|${description}|${promptText}`;
      if (currentContent === lastAnalyzedContent) {
        return;
      }
      setLastAnalyzedContent(currentContent);
      setIsAnalyzing(true);
      try {
        console.log('NLP analysis would run here for:', { title, description, promptText });
        setNlpSuggestions({});
      } catch (error) {
        console.error('Error analyzing prompt content:', error);
      } finally {
        setIsAnalyzing(false);
      }
    },
    [hasAcceptedSuggestions, lastAnalyzedContent]
  );

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      analyzePromptContent(title, description, promptText);
    }, 1000); 
    return () => clearTimeout(timeoutId);
  }, [title, description, promptText, analyzePromptContent]);

  const acceptSuggestion = (type: 'category' | 'tool' | 'tags') => {
    if (type === 'category' && nlpSuggestions.category) {
      setSelectedCategory(nlpSuggestions.category);
      setHasAcceptedSuggestions(prev => ({ ...prev, category: true }));
    } else if (type === 'tool' && nlpSuggestions.tool) {
      setSelectedTool(nlpSuggestions.tool);
      setHasAcceptedSuggestions(prev => ({ ...prev, tool: true }));
    } else if (type === 'tags' && nlpSuggestions.tags) {
      setSelectedTags(prev => [...new Set([...prev, ...nlpSuggestions.tags!])]);
      setHasAcceptedSuggestions(prev => ({ ...prev, tags: true }));
    }
  };

  const acceptIndividualTag = (tagId: string) => {
    setSelectedTags(prev => [...new Set([...prev, tagId])]);
  };

  const handleCreateCollection = async (name: string, description: string | null, imageFile: File | null, isPublic: boolean) => {
    try {
      const newCollection = await createCollection(user.id, {
        name,
        description,
        imageFile,
        is_public: isPublic
      })
      setCollections(prev => [newCollection, ...prev])
      setSelectedCollection(newCollection.id)
      toast({
        title: "Collection created!",
        description: `"${name}" has been created and selected.`,
      })
      setIsCreateCollectionDialogOpen(false)
    } catch (error) {
      console.error('Error creating collection:', error)
      toast({
        title: "Error creating collection",
        description: error instanceof Error ? error.message : "An unexpected error occurred.",
        variant: "destructive",
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!user) {
      toast({ title: "Authentication required", description: "Please sign in to submit a prompt.", variant: "destructive" })
      return
    }
    if (!title.trim() || !promptText.trim() || !selectedCategory || !selectedTool) {
      toast({ title: "Missing required fields", description: "Please fill in all required fields (title, prompt text, category, and AI tool).", variant: "destructive" })
      return
    }
    if (selectedTags.length === 0) {
      toast({ title: "Tags required", description: "Please select at least one tag for your prompt.", variant: "destructive" })
      return
    }
    setIsSubmitting(true)
    try {
      let imageUrl: string | undefined = undefined
      if (imageFile) {
        try {
          const fileName = `${user.id}/${Date.now()}-${imageFile.name}`
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('prompt-images')
            .upload(fileName, imageFile, { cacheControl: '3600', upsert: false })
          if (uploadError) {
            console.error('Error uploading image:', uploadError)
            toast({ title: "Image upload failed", description: "Failed to upload image. Continuing without image.", variant: "destructive" })
          } else {
            const publicUrlResult = supabase.storage.from('prompt-images').getPublicUrl(fileName)
            imageUrl = publicUrlResult.data.publicUrl
          }
        } catch (error) {
          console.error('Error in image upload process:', error)
          toast({ title: "Image upload failed", description: "Failed to upload image. Continuing without image.", variant: "destructive" })
        }
      }
      const promptData: CreatePromptData = {
        userId: user.id, title: title.trim(), description: description.trim(), promptText: promptText.trim(),
        instructions: instructions.trim() || undefined, exampleInput: exampleInput.trim() || undefined,
        exampleOutputText: exampleOutput.trim() || undefined, categoryId: parseInt(selectedCategory),
        toolId: parseInt(selectedTool), tagIds: selectedTags.map(id => parseInt(id)), isPublic,
        aiModelId: selectedAIModel ? parseInt(selectedAIModel) : undefined,
        userEnteredAiModel: userEnteredAiModel.trim() || undefined, imageUrl: imageUrl,
      }
      const { promptId, shortId, error } = await createPrompt(promptData)
      if (error || !promptId || !shortId) {
        throw new Error(error?.message || "Failed to create prompt")
      }
      console.log('Prompt created successfully:', { promptId, shortId })
      const collectionId = selectedCollection || myPromptsCollectionId;
      if (collectionId && promptId) {
        try {
          await addPromptToCollection(user.id, promptId, collectionId)
          console.log('Prompt added to collection successfully')
        } catch (collectionError) {
          console.error('Error adding prompt to collection:', collectionError)
        }
      }
      toast({ title: "Prompt submitted successfully!", description: "Your prompt has been created and is now live."})
      setIsRedirecting(true)
      setIsSubmitting(false)
      const selectedCategoryObj = allCategories.find(cat => String(cat.id) === selectedCategory)
      const categorySlug = selectedCategoryObj?.slug
      const titleSlugValue = createTitleSlug(title) // Renamed to avoid conflict
      if (categorySlug && shortId) {
        router.push(`/prompt/${categorySlug}/${titleSlugValue}-${shortId}`) // Use titleSlugValue
      } else {
        router.push('/explore')
      }
    } catch (error) {
      console.error('Error submitting prompt:', error)
      toast({ title: "Error submitting prompt", description: error instanceof Error ? error.message : "An unexpected error occurred. Please try again.", variant: "destructive"})
    } finally {
      setIsSubmitting(false)
    }
  }

  const categoryOptions: ComboboxOption[] = useMemo(() => 
    allCategories.map(category => ({ value: String(category.id), label: category.name })), []
  )
  const toolOptions: ComboboxOption[] = useMemo(() => 
    allTools.map(tool => ({ value: String(tool.id), label: tool.name })), []
  )
  const modelOptions: ComboboxOption[] = useMemo(() => 
    availableModels.map(model => ({ value: String(model.id), label: model.displayName || model.name })), [availableModels]
  )
  const tagOptions: TagOption[] = useMemo(() => 
    allTags.map(tag => ({ value: String(tag.id), label: tag.name })), []
  )
  const collectionOptions: ComboboxOption[] = useMemo(() => 
    collections.map(collection => ({ value: collection.id, label: collection.name })), [collections]
  )

  const renderCollectionOption = (option: ComboboxOption) => {
    const collection = collections.find(c => c.id === option.value);
    return (
      <div className="flex items-center">
        {collection?.isPublic ? <Globe className="h-3 w-3 mr-2 text-muted-foreground" /> : <Lock className="h-3 w-3 mr-2 text-muted-foreground" />}
        {option.label}
      </div>
    );
  };
  const renderCollectionValue = (option: ComboboxOption) => {
    const collection = collections.find(c => c.id === option.value);
    return (
      <div className="flex items-center">
        {collection?.isPublic ? <Globe className="h-3 w-3 mr-2 text-muted-foreground" /> : <Lock className="h-3 w-3 mr-2 text-muted-foreground" />}
        {option.label}
      </div>
    );
  };

  if (isCheckingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }
  if (!user) return null

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="mx-auto max-w-3xl">
        <CardHeader>
          <Button variant="ghost" size="sm" onClick={() => router.back()} className="mb-2 self-start pl-0 text-muted-foreground">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
          <CardTitle className="text-2xl">Submit Your Prompt</CardTitle>
          <CardDescription>
            Share your AI prompt with the community and help others discover new possibilities
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {/* Title Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Title <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Give your prompt a clear, descriptive title..."
                  required
                  disabled={isSubmitting || isRedirecting}
                />
              </div>
            </div>

            {/* Description Field - Now Optional */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <div className="col-span-3">
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe what your prompt does and how it helps users..."
                  className="min-h-[100px]"
                  disabled={isSubmitting || isRedirecting}
                />
                <p className="text-xs text-muted-foreground mt-1">Optional - helps others understand your prompt better</p>
              </div>
            </div>

            {/* Prompt Text Field - NOW USING MarkdownPromptEditor */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="prompt-text" className="text-right">
                Prompt Text <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-2">
                <MarkdownPromptEditor
                  value={promptText}
                  onChange={(value) => setPromptText(value)} // Updated onChange signature
                  disabled={isSubmitting || isRedirecting}
                  placeholder="Enter your prompt text here. Use [[variables]] for placeholders..."
                  minHeight="250px" // Example of setting minHeight
                />
                <p className="text-xs text-muted-foreground">
                  Use Markdown for formatting. Variables like <span className="cm-variable-highlight">[[variable_name]]</span> will be highlighted.
                </p>
              </div>
            </div>

            {/* Image Upload Field */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">Preview Image</Label>
              <div className="col-span-3">
                <DragDropImageUpload
                  imageFile={imageFile}
                  imagePreview={imagePreview}
                  onImageChange={(file, preview) => {
                    setImageFile(file)
                    setImagePreview(preview)
                  }}
                  disabled={isSubmitting || isRedirecting}
                />
                <p className="text-sm text-muted-foreground mt-1">Add an image to make your prompt more appealing.</p>
              </div>
            </div>

            {/* Category Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                Category <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                {nlpSuggestions.category && !hasAcceptedSuggestions.category && (
                  <Button
                    type="button" variant="outline" size="sm"
                    onClick={() => acceptSuggestion('category')}
                    className="mb-2 h-6 px-2 text-xs bg-accent-green/10 border-accent-green/20 text-accent-green hover:bg-accent-green/20"
                  > <Brain className="h-3 w-3 mr-1" /> Use AI suggestion </Button>
                )}
                <Combobox
                  options={categoryOptions} value={selectedCategory} onChange={setSelectedCategory}
                  placeholder="Select a category..." emptyText="No categories found."
                  disabled={isSubmitting || isRedirecting}
                />
                {nlpSuggestions.category && !hasAcceptedSuggestions.category && (
                  <p className="text-xs text-accent-green mt-1">
                    💡 AI suggests: {allCategories.find(cat => cat.id.toString() === nlpSuggestions.category)?.name}
                    {nlpSuggestions.confidence?.category && ` (${nlpSuggestions.confidence.category} confidence)`}
                  </p>
                )}
              </div>
            </div>

            {/* AI Tool Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">
                AI Tool <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                {nlpSuggestions.tool && !hasAcceptedSuggestions.tool && (
                  <Button
                    type="button" variant="outline" size="sm"
                    onClick={() => acceptSuggestion('tool')}
                    className="mb-2 h-6 px-2 text-xs bg-accent-green/10 border-accent-green/20 text-accent-green hover:bg-accent-green/20"
                  > <Brain className="h-3 w-3 mr-1" /> Use AI suggestion </Button>
                )}
                <Combobox
                  options={toolOptions} value={selectedTool} onChange={setSelectedTool}
                  placeholder="Select an AI tool..." emptyText="No AI tools found."
                  disabled={isSubmitting || isRedirecting}
                />
                {nlpSuggestions.tool && !hasAcceptedSuggestions.tool && (
                  <p className="text-xs text-accent-green mt-1">
                    💡 AI suggests: {allTools.find(tool => tool.id.toString() === nlpSuggestions.tool)?.name}
                    {nlpSuggestions.confidence?.tool && ` (${nlpSuggestions.confidence.tool} confidence)`}
                  </p>
                )}
              </div>
            </div>

            {/* AI Model Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">AI Model</Label>
              <div className="col-span-3">
                {selectedTool ? (
                  <div className="space-y-2">
                    <Combobox
                      options={modelOptions} value={selectedAIModel} onChange={setSelectedAIModel}
                      placeholder={isLoadingModels ? "Loading models..." : "Select an AI model..."}
                      emptyText="No AI models found."
                      disabled={isLoadingModels || isSubmitting || isRedirecting}
                    />
                    {isLoadingModels && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" /> Loading available models...
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">Or enter manually:</span>
                      <Input
                        placeholder="e.g., GPT-4, Claude-3, Custom Model..."
                        value={userEnteredAiModel} onChange={(e) => setUserEnteredAiModel(e.target.value)}
                        disabled={isSubmitting || isRedirecting} className="flex-1"
                      />
                    </div>
                  </div>
                ) : ( <div className="text-sm text-muted-foreground italic"> Select an AI tool first to see available models </div> )}
              </div>
            </div>

            {/* Tags Selection */}
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right">
                Tags <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                {nlpSuggestions.tags && nlpSuggestions.tags.length > 0 && !hasAcceptedSuggestions.tags && (
                  <Button
                    type="button" variant="outline" size="sm"
                    onClick={() => acceptSuggestion('tags')}
                    className="mb-2 h-6 px-2 text-xs bg-accent-green/10 border-accent-green/20 text-accent-green hover:bg-accent-green/20"
                  > <Brain className="h-3 w-3 mr-1" /> Use AI suggestions </Button>
                )}
                <TagInput
                  options={tagOptions} selectedTags={selectedTags} onTagsChange={setSelectedTags}
                  placeholder="Search and select tags..." maxTags={10}
                  className={isSubmitting || isRedirecting ? 'opacity-50 pointer-events-none' : ''}
                />
                {nlpSuggestions.tags && nlpSuggestions.tags.length > 0 && !hasAcceptedSuggestions.tags && (
                  <div className="space-y-1 mt-2">
                    <p className="text-xs text-accent-green">
                      💡 AI suggests these tags:
                      {nlpSuggestions.confidence?.tags && ` (${nlpSuggestions.confidence.tags} confidence)`}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {nlpSuggestions.tags.map(tagId => {
                        const tag = allTags.find(t => t.id.toString() === tagId);
                        if (!tag) return null;
                        return (
                          <Button
                            key={tagId} type="button" variant="outline" size="sm"
                            onClick={() => acceptIndividualTag(tagId)}
                            className="h-6 px-2 text-xs bg-accent-green/10 border-accent-green/20 text-accent-green hover:bg-accent-green/20"
                            disabled={selectedTags.includes(tagId)}
                          >
                            {selectedTags.includes(tagId) ? <CheckCircle className="h-3 w-3 mr-1" /> : <PlusCircle className="h-3 w-3 mr-1" />}
                            {tag.name}
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Visibility Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Visibility</Label>
              <div className="col-span-3 flex items-center space-x-3">
                <Switch id="isPublic" checked={isPublic} onCheckedChange={setIsPublic} disabled={isSubmitting || isRedirecting} />
                <div className="flex items-center space-x-2">
                  {isPublic ? <Globe className="h-4 w-4 text-muted-foreground" /> : <Lock className="h-4 w-4 text-muted-foreground" />}
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">{isPublic ? 'Public' : 'Private'}</span>
                    <p className="text-sm text-muted-foreground">{isPublic ? 'Visible to everyone' : 'Only visible to you'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Collection Field */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right flex items-center justify-between">
                Collection
                <Button
                  type="button" variant="outline" size="sm"
                  onClick={() => setIsCreateCollectionDialogOpen(true)}
                  className="h-7 px-2 text-xs ml-2" disabled={isSubmitting || isRedirecting}
                > <PlusCircle className="h-3 w-3 mr-1" /> New </Button>
              </Label>
              <div className="col-span-3">
                <Combobox
                  options={collectionOptions} value={selectedCollection || ""}
                  onChange={(value) => setSelectedCollection(value || null)}
                  placeholder={isLoadingCollections ? "Loading collections..." : "Select a collection (optional)..."}
                  emptyText="No collections found."
                  disabled={isLoadingCollections || isSubmitting || isRedirecting}
                  renderOption={renderCollectionOption} renderValue={renderCollectionValue}
                />
                <p className="text-xs text-muted-foreground mt-1">Optional - prompts are saved to "My Prompts" collection by default.</p>
                {isLoadingCollections && (
                  <div className="flex items-center text-sm text-muted-foreground mt-1">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" /> Loading your collections...
                  </div>
                )}
              </div>
            </div>

            {/* Advanced Options Toggle */}
            <div className="grid grid-cols-4 items-center gap-4 border-t pt-6">
              <div></div>
              <div className="col-span-3">
                <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border">
                  <div className="flex flex-col">
                    <h3 className="text-sm font-medium">Advanced Options</h3>
                    <p className="text-xs text-muted-foreground">Add instructions and examples</p>
                  </div>
                  <Button
                    type="button" variant="outline" onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                    disabled={isSubmitting || isRedirecting} className="ml-4"
                  > {showAdvancedOptions ? 'Hide' : 'Show'} Advanced </Button>
                </div>
              </div>
            </div>

            {/* Advanced Options */}
            {showAdvancedOptions && (
              <>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="instructions" className="text-right pt-2">Instructions</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="instructions" value={instructions} onChange={(e) => setInstructions(e.target.value)}
                      placeholder="Provide step-by-step instructions on how to use this prompt effectively..."
                      className="min-h-[80px]" disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Guidance for using the prompt.</p>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="example-input" className="text-right pt-2">Example Input</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="example-input" value={exampleInput} onChange={(e) => setExampleInput(e.target.value)}
                      placeholder="Show an example of what input users should provide..."
                      className="min-h-[80px]" disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Show what input works well.</p>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="example-output" className="text-right pt-2">Example Output</Label>
                  <div className="col-span-3">
                    <Textarea
                      id="example-output" value={exampleOutput} onChange={(e) => setExampleOutput(e.target.value)}
                      placeholder="Show what kind of output users can expect..."
                      className="min-h-[80px]" disabled={isSubmitting || isRedirecting}
                    />
                    <p className="text-sm text-muted-foreground mt-1">Show typical output.</p>
                  </div>
                </div>
              </>
            )}
          </CardContent>
          <CardFooter className="flex justify-between items-center pt-6">
            <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting || isRedirecting}>
              Cancel
            </Button>
            <Button
              type="submit" disabled={isSubmitting || isRedirecting}
              className="bg-accent-green hover:bg-accent-green/90"
            >
              {isSubmitting ? ( <><Loader2 className="h-4 w-4 mr-2 animate-spin" /> Creating Prompt...</>
              ) : isRedirecting ? ( <><CheckCircle className="h-4 w-4 mr-2" /> Redirecting...</>
              ) : ( <><Upload className="h-4 w-4 mr-2" /> Submit Prompt</>
              )}
            </Button>
          </CardFooter>
        </form>
        <CreateCollectionDialog
          isOpen={isCreateCollectionDialogOpen}
          onClose={() => setIsCreateCollectionDialogOpen(false)}
          onCreate={handleCreateCollection}
        />
      </Card>
    </div>
  )
}

```

**4. Update Other Pages Using the Editor (Edit/Remix):**

You'll need to perform similar replacements in:

*   `/Users/<USER>/Documents/Code/PromptHQ/app/prompt/edit/[shortId]/page.tsx`
*   `/Users/<USER>/Documents/Code/PromptHQ/app/prompt/remix/[shortId]/page.tsx`

Replace their `@uiw/react-textarea-code-editor` instances with `<MarkdownPromptEditor />`, passing the appropriate `value` and `onChange` props. You'll also need to remove the old `CodeEditor` dynamic import from those files.

**Example of replacement in `edit/.../page.tsx` (simplified):**

```tsx
// app/prompt/edit/[shortId]/page.tsx
// ... other imports
import MarkdownPromptEditor from "@/components/ui/markdown-prompt-editor"; // Import the new editor

export default function EditPromptPage() {
  // ... existing state for promptText, setPromptText ...

  // Remove the old CodeEditor dynamic import:
  // const CodeEditor = dynamic(...)

  return (
    // ... JSX ...
    // Replace this:
    /*
    <CodeEditor
      id="prompt-text"
      value={promptText}
      language="markdown"
      // ... other props
      onChange={(e) => setPromptText(e.target.value)}
    />
    */
    // With this:
    <MarkdownPromptEditor
      value={promptText}
      onChange={setPromptText} // CodeMirror's onChange typically just returns the new value
      disabled={isSubmitting}
      placeholder="Enter the full text of your AI prompt here..."
    />
    // ... rest of JSX ...
  );
}
```

And similarly for the `remix` page and any other fields (like `instructions`, `exampleInput`, `exampleOutput`) that should use this new editor.

This approach provides a consistent, robust, and customizable editing experience across your platform.