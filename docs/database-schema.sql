--
-- PostgreSQL database dump
--

-- Dumped from database version 15.8
-- Dumped by pg_dump version 15.13 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: auth; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA auth;


--
-- Name: extensions; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA extensions;


--
-- Name: graphql; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA graphql;


--
-- Name: graphql_public; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA graphql_public;


--
-- Name: pgbouncer; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA pgbouncer;


--
-- Name: realtime; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA realtime;


--
-- Name: storage; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA storage;


--
-- Name: vault; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA vault;


--
-- Name: pg_graphql; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_graphql WITH SCHEMA graphql;


--
-- Name: EXTENSION pg_graphql; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_graphql IS 'pg_graphql: GraphQL support';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: pg_trgm; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_trgm WITH SCHEMA public;


--
-- Name: EXTENSION pg_trgm; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_trgm IS 'text similarity measurement and index searching based on trigrams';


--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: pgjwt; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgjwt WITH SCHEMA extensions;


--
-- Name: EXTENSION pgjwt; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pgjwt IS 'JSON Web Token API for Postgresql';


--
-- Name: supabase_vault; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS supabase_vault WITH SCHEMA vault;


--
-- Name: EXTENSION supabase_vault; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION supabase_vault IS 'Supabase Vault Extension';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: aal_level; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.aal_level AS ENUM (
    'aal1',
    'aal2',
    'aal3'
);


--
-- Name: code_challenge_method; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.code_challenge_method AS ENUM (
    's256',
    'plain'
);


--
-- Name: factor_status; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.factor_status AS ENUM (
    'unverified',
    'verified'
);


--
-- Name: factor_type; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.factor_type AS ENUM (
    'totp',
    'webauthn',
    'phone'
);


--
-- Name: one_time_token_type; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.one_time_token_type AS ENUM (
    'confirmation_token',
    'reauthentication_token',
    'recovery_token',
    'email_change_token_new',
    'email_change_token_current',
    'phone_change_token'
);


--
-- Name: action; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.action AS ENUM (
    'INSERT',
    'UPDATE',
    'DELETE',
    'TRUNCATE',
    'ERROR'
);


--
-- Name: equality_op; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.equality_op AS ENUM (
    'eq',
    'neq',
    'lt',
    'lte',
    'gt',
    'gte',
    'in'
);


--
-- Name: user_defined_filter; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.user_defined_filter AS (
	column_name text,
	op realtime.equality_op,
	value text
);


--
-- Name: wal_column; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.wal_column AS (
	name text,
	type_name text,
	type_oid oid,
	value jsonb,
	is_pkey boolean,
	is_selectable boolean
);


--
-- Name: wal_rls; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.wal_rls AS (
	wal jsonb,
	is_rls_enabled boolean,
	subscription_ids uuid[],
	errors text[]
);


--
-- Name: email(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.email() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.email', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'email')
  )::text
$$;


--
-- Name: FUNCTION email(); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.email() IS 'Deprecated. Use auth.jwt() -> ''email'' instead.';


--
-- Name: jwt(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.jwt() RETURNS jsonb
    LANGUAGE sql STABLE
    AS $$
  select 
    coalesce(
        nullif(current_setting('request.jwt.claim', true), ''),
        nullif(current_setting('request.jwt.claims', true), '')
    )::jsonb
$$;


--
-- Name: role(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.role() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.role', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'role')
  )::text
$$;


--
-- Name: FUNCTION role(); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.role() IS 'Deprecated. Use auth.jwt() -> ''role'' instead.';


--
-- Name: uid(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.uid() RETURNS uuid
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'sub')
  )::uuid
$$;


--
-- Name: FUNCTION uid(); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.uid() IS 'Deprecated. Use auth.jwt() -> ''sub'' instead.';


--
-- Name: grant_pg_cron_access(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.grant_pg_cron_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_cron'
  )
  THEN
    grant usage on schema cron to postgres with grant option;

    alter default privileges in schema cron grant all on tables to postgres with grant option;
    alter default privileges in schema cron grant all on functions to postgres with grant option;
    alter default privileges in schema cron grant all on sequences to postgres with grant option;

    alter default privileges for user supabase_admin in schema cron grant all
        on sequences to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on tables to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on functions to postgres with grant option;

    grant all privileges on all tables in schema cron to postgres with grant option;
    revoke all on table cron.job from postgres;
    grant select on table cron.job to postgres with grant option;
  END IF;
END;
$$;


--
-- Name: FUNCTION grant_pg_cron_access(); Type: COMMENT; Schema: extensions; Owner: -
--

COMMENT ON FUNCTION extensions.grant_pg_cron_access() IS 'Grants access to pg_cron';


--
-- Name: grant_pg_graphql_access(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.grant_pg_graphql_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
DECLARE
    func_is_graphql_resolve bool;
BEGIN
    func_is_graphql_resolve = (
        SELECT n.proname = 'resolve'
        FROM pg_event_trigger_ddl_commands() AS ev
        LEFT JOIN pg_catalog.pg_proc AS n
        ON ev.objid = n.oid
    );

    IF func_is_graphql_resolve
    THEN
        -- Update public wrapper to pass all arguments through to the pg_graphql resolve func
        DROP FUNCTION IF EXISTS graphql_public.graphql;
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language sql
        as $$
            select graphql.resolve(
                query := query,
                variables := coalesce(variables, '{}'),
                "operationName" := "operationName",
                extensions := extensions
            );
        $$;

        -- This hook executes when `graphql.resolve` is created. That is not necessarily the last
        -- function in the extension so we need to grant permissions on existing entities AND
        -- update default permissions to any others that are created after `graphql.resolve`
        grant usage on schema graphql to postgres, anon, authenticated, service_role;
        grant select on all tables in schema graphql to postgres, anon, authenticated, service_role;
        grant execute on all functions in schema graphql to postgres, anon, authenticated, service_role;
        grant all on all sequences in schema graphql to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on tables to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on functions to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on sequences to postgres, anon, authenticated, service_role;

        -- Allow postgres role to allow granting usage on graphql and graphql_public schemas to custom roles
        grant usage on schema graphql_public to postgres with grant option;
        grant usage on schema graphql to postgres with grant option;
    END IF;

END;
$_$;


--
-- Name: FUNCTION grant_pg_graphql_access(); Type: COMMENT; Schema: extensions; Owner: -
--

COMMENT ON FUNCTION extensions.grant_pg_graphql_access() IS 'Grants access to pg_graphql';


--
-- Name: grant_pg_net_access(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.grant_pg_net_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_net'
  )
  THEN
    IF NOT EXISTS (
      SELECT 1
      FROM pg_roles
      WHERE rolname = 'supabase_functions_admin'
    )
    THEN
      CREATE USER supabase_functions_admin NOINHERIT CREATEROLE LOGIN NOREPLICATION;
    END IF;

    GRANT USAGE ON SCHEMA net TO supabase_functions_admin, postgres, anon, authenticated, service_role;

    IF EXISTS (
      SELECT FROM pg_extension
      WHERE extname = 'pg_net'
      -- all versions in use on existing projects as of 2025-02-20
      -- version 0.12.0 onwards don't need these applied
      AND extversion IN ('0.2', '0.6', '0.7', '0.7.1', '0.8', '0.10.0', '0.11.0')
    ) THEN
      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;

      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;

      REVOKE ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;
      REVOKE ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;

      GRANT EXECUTE ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
      GRANT EXECUTE ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
    END IF;
  END IF;
END;
$$;


--
-- Name: FUNCTION grant_pg_net_access(); Type: COMMENT; Schema: extensions; Owner: -
--

COMMENT ON FUNCTION extensions.grant_pg_net_access() IS 'Grants access to pg_net';


--
-- Name: pgrst_ddl_watch(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.pgrst_ddl_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  cmd record;
BEGIN
  FOR cmd IN SELECT * FROM pg_event_trigger_ddl_commands()
  LOOP
    IF cmd.command_tag IN (
      'CREATE SCHEMA', 'ALTER SCHEMA'
    , 'CREATE TABLE', 'CREATE TABLE AS', 'SELECT INTO', 'ALTER TABLE'
    , 'CREATE FOREIGN TABLE', 'ALTER FOREIGN TABLE'
    , 'CREATE VIEW', 'ALTER VIEW'
    , 'CREATE MATERIALIZED VIEW', 'ALTER MATERIALIZED VIEW'
    , 'CREATE FUNCTION', 'ALTER FUNCTION'
    , 'CREATE TRIGGER'
    , 'CREATE TYPE', 'ALTER TYPE'
    , 'CREATE RULE'
    , 'COMMENT'
    )
    -- don't notify in case of CREATE TEMP table or other objects created on pg_temp
    AND cmd.schema_name is distinct from 'pg_temp'
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


--
-- Name: pgrst_drop_watch(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.pgrst_drop_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  obj record;
BEGIN
  FOR obj IN SELECT * FROM pg_event_trigger_dropped_objects()
  LOOP
    IF obj.object_type IN (
      'schema'
    , 'table'
    , 'foreign table'
    , 'view'
    , 'materialized view'
    , 'function'
    , 'trigger'
    , 'type'
    , 'rule'
    )
    AND obj.is_temporary IS false -- no pg_temp objects
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


--
-- Name: set_graphql_placeholder(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.set_graphql_placeholder() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
    DECLARE
    graphql_is_dropped bool;
    BEGIN
    graphql_is_dropped = (
        SELECT ev.schema_name = 'graphql_public'
        FROM pg_event_trigger_dropped_objects() AS ev
        WHERE ev.schema_name = 'graphql_public'
    );

    IF graphql_is_dropped
    THEN
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language plpgsql
        as $$
            DECLARE
                server_version float;
            BEGIN
                server_version = (SELECT (SPLIT_PART((select version()), ' ', 2))::float);

                IF server_version >= 14 THEN
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql extension is not enabled.'
                            )
                        )
                    );
                ELSE
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql is only available on projects running Postgres 14 onwards.'
                            )
                        )
                    );
                END IF;
            END;
        $$;
    END IF;

    END;
$_$;


--
-- Name: FUNCTION set_graphql_placeholder(); Type: COMMENT; Schema: extensions; Owner: -
--

COMMENT ON FUNCTION extensions.set_graphql_placeholder() IS 'Reintroduces placeholder function for graphql_public.graphql';


--
-- Name: get_auth(text); Type: FUNCTION; Schema: pgbouncer; Owner: -
--

CREATE FUNCTION pgbouncer.get_auth(p_usename text) RETURNS TABLE(username text, password text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
  BEGIN
      RAISE DEBUG 'PgBouncer auth request: %', p_usename;

      RETURN QUERY
      SELECT
          rolname::text,
          CASE WHEN rolvaliduntil < now()
              THEN null
              ELSE rolpassword::text
          END
      FROM pg_authid
      WHERE rolname=$1 and rolcanlogin;
  END;
  $_$;


--
-- Name: add_comment(uuid, uuid, text, uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.add_comment(p_user_id uuid, p_prompt_id uuid, p_text text, p_parent_comment_id uuid DEFAULT NULL::uuid) RETURNS uuid
    LANGUAGE plpgsql
    AS $$
DECLARE
  new_comment_id UUID;
BEGIN
  -- Check if the prompt exists and is public
  IF NOT EXISTS (SELECT 1 FROM prompts WHERE id = p_prompt_id AND is_public = TRUE) THEN
    RETURN NULL;
  END IF;
  
  -- If parent_comment_id is provided, check if it exists and belongs to the same prompt
  IF p_parent_comment_id IS NOT NULL THEN
    IF NOT EXISTS (SELECT 1 FROM comments WHERE id = p_parent_comment_id AND prompt_id = p_prompt_id) THEN
      RETURN NULL;
    END IF;
  END IF;
  
  -- Insert the comment
  INSERT INTO comments (
    id,
    user_id,
    prompt_id,
    parent_comment_id,
    text,
    created_at,
    updated_at
  ) VALUES (
    uuid_generate_v4(),
    p_user_id,
    p_prompt_id,
    p_parent_comment_id,
    p_text,
    NOW(),
    NOW()
  ) RETURNING id INTO new_comment_id;
  
  RETURN new_comment_id;
END;
$$;


--
-- Name: add_prompt(uuid, integer, integer, text, text, text, text, text, text, text, text, boolean, uuid, integer[], integer, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.add_prompt(p_user_id uuid, p_category_id integer, p_tool_id integer, p_title text, p_description text, p_prompt_text text, p_instructions text DEFAULT NULL::text, p_example_input text DEFAULT NULL::text, p_example_output_text text DEFAULT NULL::text, p_example_output_image_url text DEFAULT NULL::text, p_image_url text DEFAULT NULL::text, p_is_public boolean DEFAULT true, p_original_prompt_id uuid DEFAULT NULL::uuid, p_tag_ids integer[] DEFAULT '{}'::integer[], p_ai_model_id integer DEFAULT NULL::integer, p_user_entered_ai_model text DEFAULT NULL::text) RETURNS TABLE(created_prompt_id uuid, created_short_id text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  v_prompt_id UUID;
  v_short_id TEXT;
  v_slug TEXT;
BEGIN
  -- Use the existing generate_short_id function (6 characters)
  v_short_id := generate_short_id(6);
  
  -- Generate a slug from the title
  v_slug := lower(regexp_replace(p_title, '[^a-zA-Z0-9]', '-', 'g'));
  
  -- Insert the prompt
  INSERT INTO public.prompts(
    user_id,
    category_id,
    tool_id,
    title,
    description,
    prompt_text,
    instructions,
    example_input,
    example_output_text,
    example_output_image_url,
    image_url,
    is_public,
    original_prompt_id,
    ai_model_id,
    user_entered_ai_model,
    short_id,
    slug
  ) VALUES (
    p_user_id,
    p_category_id,
    p_tool_id,
    p_title,
    p_description,
    p_prompt_text,
    p_instructions,
    p_example_input,
    p_example_output_text,
    p_example_output_image_url,
    p_image_url,
    p_is_public,
    p_original_prompt_id,
    p_ai_model_id,
    p_user_entered_ai_model,
    v_short_id,
    v_slug
  )
  RETURNING id, short_id INTO v_prompt_id, v_short_id;
  
  -- Insert tags if provided
  IF array_length(p_tag_ids, 1) > 0 THEN
    INSERT INTO public.prompt_tags (prompt_id, tag_id)
    SELECT v_prompt_id, tag_id
    FROM unnest(p_tag_ids) AS tag_id;
  END IF;
  
  -- Return the created prompt ID and short ID
  RETURN QUERY SELECT v_prompt_id, v_short_id;
END;
$$;


--
-- Name: add_prompt_to_collection(uuid, uuid, uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.add_prompt_to_collection(p_user_id uuid, p_prompt_id uuid, p_collection_id uuid DEFAULT NULL::uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
  default_collection_id UUID;
  target_collection_id UUID;
BEGIN
  -- Check if the prompt exists and is public
  IF NOT EXISTS (SELECT 1 FROM prompts WHERE id = p_prompt_id AND is_public = TRUE) THEN
    RETURN FALSE;
  END IF;
  
  -- If no collection_id is provided, use the user's default collection
  IF p_collection_id IS NULL THEN
    SELECT id INTO default_collection_id
    FROM collections
    WHERE user_id = p_user_id AND is_default = TRUE;
    
    IF default_collection_id IS NULL THEN
      -- Create a default collection if it doesn't exist
      INSERT INTO collections (
        id,
        user_id,
        name,
        description,
        icon,
        color,
        is_public,
        is_default,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        p_user_id,
        'Saved Prompts',
        'Your default collection for saved prompts',
        '📌',
        'blue',
        FALSE,
        TRUE,
        NOW(),
        NOW()
      ) RETURNING id INTO default_collection_id;
    END IF;
    
    target_collection_id := default_collection_id;
  ELSE
    -- Check if the user owns the specified collection
    IF NOT EXISTS (SELECT 1 FROM collections WHERE id = p_collection_id AND user_id = p_user_id) THEN
      RETURN FALSE;
    END IF;
    
    target_collection_id := p_collection_id;
  END IF;
  
  -- Check if the prompt is already in the collection
  IF EXISTS (SELECT 1 FROM collection_prompts WHERE collection_id = target_collection_id AND prompt_id = p_prompt_id) THEN
    RETURN TRUE; -- Already in the collection, consider it a success
  END IF;
  
  -- Add the prompt to the collection
  INSERT INTO collection_prompts (collection_id, prompt_id, added_at)
  VALUES (target_collection_id, p_prompt_id, NOW());
  
  RETURN TRUE;
END;
$$;


--
-- Name: add_prompt_to_multiple_collections(uuid, uuid, uuid[]); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.add_prompt_to_multiple_collections(p_user_id uuid, p_prompt_id uuid, p_collection_ids uuid[]) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    collection_id_to_add UUID;
    current_collection RECORD;
    prompt_author_id UUID;
BEGIN
    -- Get the author of the prompt
    SELECT user_id INTO prompt_author_id FROM prompts WHERE id = p_prompt_id;

    IF NOT FOUND THEN
        RAISE WARNING 'Prompt with ID % not found.', p_prompt_id;
        RETURN FALSE;
    END IF;

    FOREACH collection_id_to_add IN ARRAY p_collection_ids
    LOOP
        -- Check if the collection exists and belongs to the user
        SELECT * INTO current_collection FROM collections 
        WHERE id = collection_id_to_add AND user_id = p_user_id;

        IF NOT FOUND THEN
            RAISE WARNING 'Collection with ID % not found or does not belong to user %.', collection_id_to_add, p_user_id;
            CONTINUE; -- Skip this collection and try the next one
        END IF;
        
        -- Add the prompt to this collection, do nothing if already exists
        INSERT INTO collection_prompts (collection_id, prompt_id)
        VALUES (collection_id_to_add, p_prompt_id)
        ON CONFLICT (collection_id, prompt_id) DO NOTHING;
    END LOOP;

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error in add_prompt_to_multiple_collections: %', SQLERRM;
        RETURN FALSE;
END;
$$;


--
-- Name: calculate_trending_score(integer, integer, integer, integer, integer, timestamp with time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.calculate_trending_score(upvotes integer, downvotes integer, views integer, comments integer, remixes integer, created_timestamp timestamp with time zone) RETURNS double precision
    LANGUAGE plpgsql
    AS $$
DECLARE
  age_hours FLOAT;
  vote_score FLOAT;
  gravity FLOAT := 1.8; -- Higher values make score decay faster
BEGIN
  -- Calculate age in hours
  age_hours := EXTRACT(EPOCH FROM (NOW() - created_timestamp)) / 3600;
  
  -- Calculate vote score (similar to Reddit's algorithm)
  vote_score := upvotes - downvotes;
  
  -- Calculate trending score with Wilson score confidence and time decay
  -- Also factor in views, comments, and remixes
  RETURN (vote_score + (comments * 2) + (remixes * 3) + (views * 0.1)) / 
         POWER((age_hours + 2), gravity);
END;
$$;


--
-- Name: calculate_trending_score(bigint, bigint, integer, bigint, bigint, timestamp with time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.calculate_trending_score(upvotes bigint, downvotes bigint, views integer, comments bigint, remixes bigint, created_timestamp timestamp with time zone) RETURNS double precision
    LANGUAGE plpgsql
    AS $$
DECLARE
  age_hours FLOAT;
  vote_score FLOAT;
  gravity FLOAT := 1.8; -- Higher values make score decay faster
BEGIN
  -- Calculate age in hours
  age_hours := EXTRACT(EPOCH FROM (NOW() - created_timestamp)) / 3600;
  
  -- Calculate vote score (similar to Reddit's algorithm)
  vote_score := upvotes - downvotes;
  
  -- Calculate trending score with Wilson score confidence and time decay
  -- Also factor in views, comments, and remixes
  RETURN (vote_score + (comments * 2) + (remixes * 3) + (views * 0.1)) / 
         POWER((age_hours + 2), gravity);
END;
$$;


--
-- Name: check_default_collection(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.check_default_collection() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF NEW.is_default = TRUE THEN
    UPDATE collections
    SET is_default = FALSE
    WHERE user_id = NEW.user_id AND id != NEW.id AND is_default = TRUE;
  END IF;
  RETURN NEW;
END;
$$;


--
-- Name: configure_supabase_url(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.configure_supabase_url(url text) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    EXECUTE format('ALTER DATABASE %I SET app.supabase_url = %L', current_database(), url);
    RETURN 'Supabase URL configured successfully: ' || url;
END;
$$;


--
-- Name: create_collection(uuid, text, text, text, text, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_collection(p_user_id uuid, p_name text, p_description text DEFAULT NULL::text, p_icon text DEFAULT '📚'::text, p_color text DEFAULT 'blue'::text, p_is_public boolean DEFAULT false) RETURNS uuid
    LANGUAGE plpgsql
    AS $$
DECLARE
  new_collection_id UUID;
BEGIN
  -- Insert the collection
  INSERT INTO collections (
    id,
    user_id,
    name,
    description,
    icon,
    color,
    is_public,
    is_default,
    created_at,
    updated_at
  ) VALUES (
    uuid_generate_v4(),
    p_user_id,
    p_name,
    p_description,
    p_icon,
    p_color,
    p_is_public,
    FALSE,
    NOW(),
    NOW()
  ) RETURNING id INTO new_collection_id;
  
  RETURN new_collection_id;
END;
$$;


--
-- Name: create_comment_reply_notification(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_comment_reply_notification() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  prompt_author_id UUID;
  parent_comment_author_id UUID;
  prompt_details_rec RECORD; -- This is fine here as it will be populated by SELECT INTO
  notification_type TEXT;
  recipient_id UUID;
  -- fetched_prompt_title TEXT; -- No longer needed if get_prompt_details_for_notification returns title
BEGIN
  -- Get details of the prompt using the existing function
  SELECT 
    gpdn.ret_short_id,
    gpdn.ret_title,
    gpdn.ret_category_slug,
    gpdn.ret_tool_slug,
    gpdn.ret_primary_tag_slug,
    gpdn.ret_title_slug
  INTO prompt_details_rec -- SELECT INTO implicitly defines the structure of prompt_details_rec
  FROM public.get_prompt_details_for_notification(NEW.prompt_id) gpdn;

  -- If prompt_details_rec is NULL or key fields are missing (prompt not found by helper), exit.
  IF prompt_details_rec IS NULL OR prompt_details_rec.ret_short_id IS NULL THEN
    RETURN NEW; -- Safely exit if prompt details couldn't be fetched
  END IF;

  -- Fetch the prompt's author_id directly
  SELECT p.user_id INTO prompt_author_id
  FROM public.prompts p
  WHERE p.id = NEW.prompt_id;
  
  -- If the new comment is a top-level comment (not a reply)
  IF NEW.parent_comment_id IS NULL THEN
    -- Notify the prompt author if they are not the one commenting
    IF prompt_author_id IS NOT NULL AND prompt_author_id <> NEW.user_id THEN
      recipient_id := prompt_author_id;
      notification_type := 'comment_prompt';

      INSERT INTO public.notifications (
        recipient_user_id,
        actor_user_id,
        type,
        entity_id,
        entity_type,
        entity_title, -- Use the title returned by the helper function
        prompt_short_id,
        prompt_category_slug,
        prompt_tool_slug,
        prompt_primary_tag_slug,
        prompt_title_slug
      )
      VALUES (
        recipient_id,
        NEW.user_id,
        notification_type,
        NEW.prompt_id,
        'prompt',
        prompt_details_rec.ret_title, -- Title for display in notification
        prompt_details_rec.ret_short_id,
        COALESCE(prompt_details_rec.ret_category_slug, 'uncategorized'),
        COALESCE(prompt_details_rec.ret_tool_slug, 'general'),
        COALESCE(prompt_details_rec.ret_primary_tag_slug, 'misc'),
        COALESCE(prompt_details_rec.ret_title_slug, public.slugify(prompt_details_rec.ret_title)) -- Fallback if title_slug was null
      );
    END IF;
  ELSE
    -- The new comment is a reply, notify the author of the parent comment
    SELECT user_id INTO parent_comment_author_id
    FROM public.comments
    WHERE id = NEW.parent_comment_id;

    -- Notify the parent comment author if they are not the one replying
    IF parent_comment_author_id IS NOT NULL AND parent_comment_author_id <> NEW.user_id THEN
      recipient_id := parent_comment_author_id;
      notification_type := 'reply_comment';

      INSERT INTO public.notifications (
        recipient_user_id,
        actor_user_id,
        type,
        entity_id,
        entity_type,
        entity_title, -- Use the title returned by the helper function
        prompt_short_id,
        prompt_category_slug,
        prompt_tool_slug,
        prompt_primary_tag_slug,
        prompt_title_slug
      )
      VALUES (
        recipient_id,
        NEW.user_id,
        notification_type,
        NEW.prompt_id,
        'prompt',
        prompt_details_rec.ret_title, -- Title for display in notification
        prompt_details_rec.ret_short_id,
        COALESCE(prompt_details_rec.ret_category_slug, 'uncategorized'),
        COALESCE(prompt_details_rec.ret_tool_slug, 'general'),
        COALESCE(prompt_details_rec.ret_primary_tag_slug, 'misc'),
        COALESCE(prompt_details_rec.ret_title_slug, public.slugify(prompt_details_rec.ret_title)) -- Fallback
      );
    END IF;
  END IF;

  RETURN NEW;
END;
$$;


--
-- Name: create_custom_collection(uuid, text, text, text, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_custom_collection(p_user_id uuid, p_name text, p_description text DEFAULT NULL::text, p_icon_url text DEFAULT NULL::text, p_is_public boolean DEFAULT false) RETURNS uuid
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  new_collection_id UUID;
  collection_slug TEXT;
  base_slug TEXT;
  counter INT := 1;
BEGIN
  -- Name validation: Check for NULL, empty, or too short names
  IF p_name IS NULL OR TRIM(p_name) = '' OR LENGTH(TRIM(p_name)) < 3 THEN
    RAISE EXCEPTION 'Collection name must be at least 3 characters.';
  END IF;
  
  -- Generate a base slug from the name
  base_slug := LOWER(REGEXP_REPLACE(TRIM(p_name), '[^a-zA-Z0-9]+', '-', 'g'));
  -- Remove leading and trailing hyphens
  base_slug := TRIM(BOTH '-' FROM base_slug);
  
  -- Initialize slug to the base slug
  collection_slug := base_slug;
  
  -- Handle potential duplicate slugs by appending a counter
  WHILE EXISTS (
    SELECT 1 FROM collections 
    WHERE user_id = p_user_id AND slug = collection_slug
  ) LOOP
    counter := counter + 1;
    collection_slug := base_slug || '-' || counter::TEXT;
  END LOOP;
  
  -- Insert the new collection
  BEGIN
    INSERT INTO public.collections (
      user_id, 
      name, 
      slug,
      description, 
      icon, 
      is_public, 
      is_default, 
      default_type
    )
    VALUES (
      p_user_id, 
      p_name, 
      collection_slug,
      p_description, 
      p_icon_url, 
      p_is_public, 
      FALSE, 
      NULL
    )
    RETURNING id INTO new_collection_id;
    
    RETURN new_collection_id;
  EXCEPTION
    WHEN unique_violation THEN
      -- This shouldn't happen with our slug generation logic, but just in case
      RAISE EXCEPTION 'A collection with this name already exists.';
    WHEN OTHERS THEN
      RAISE EXCEPTION 'Error creating collection: %', SQLERRM;
  END;
END;
$$;


--
-- Name: create_default_collection_for_new_user(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_default_collection_for_new_user() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Create a default collection for the new user
  INSERT INTO collections (
    id,
    user_id,
    name,
    description,
    icon,
    color,
    is_public,
    is_default,
    created_at,
    updated_at
  ) VALUES (
    uuid_generate_v4(),
    NEW.id,
    'My Prompts',
    'Your default collection for saved prompts',
    '📌',
    'blue',
    false,
    true,
    NOW(),
    NOW()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error (you can customize this)
    RAISE NOTICE 'Error creating default collection: %', SQLERRM;
    -- Still return NEW to allow the profile creation to succeed
    RETURN NEW;
END;
$$;


--
-- Name: create_default_collections_for_new_user(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_default_collections_for_new_user() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  new_user_id UUID;
BEGIN
  new_user_id := NEW.id; -- Assuming NEW.id is the user_id from the profiles table

  -- Create "Saved Prompts" collection
  INSERT INTO public.collections (user_id, name, description, icon, is_public, is_default, default_type)
  VALUES (
    new_user_id,
    'Saved Prompts',
    'A collection of your saved prompts from other users.',
    '/images/collection-Saved-Prompts.png', -- Fixed image path
    FALSE, -- Always private
    TRUE,
    'saved_prompts'
  );

  -- Create "My Prompts" collection
  INSERT INTO public.collections (user_id, name, description, icon, is_public, is_default, default_type)
  VALUES (
    new_user_id,
    'My Prompts',
    'Your own submitted prompts live here.',
    '/images/collection-my-prompt.png', -- Fixed image path
    FALSE, -- Always private
    TRUE,
    'my_prompts'
  );

  RETURN NEW;
END;
$$;


--
-- Name: create_like_notification(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.create_like_notification() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  prompt_author_id UUID;
  prompt_title_text TEXT;
  prompt_details RECORD;
  notification_link TEXT;
BEGIN
  -- Only create notification for upvotes (vote_type = 1)
  IF NEW.vote_type = 1 THEN
    -- Get the author of the prompt
    SELECT user_id, title INTO prompt_author_id, prompt_title_text
    FROM public.prompts
    WHERE id = NEW.prompt_id;

    -- Don't notify if user likes their own prompt
    IF prompt_author_id IS NOT NULL AND prompt_author_id <> NEW.user_id THEN
      -- Get prompt details for link construction
      SELECT * INTO prompt_details FROM get_prompt_details_for_notification(NEW.prompt_id);

      -- Construct the link (adapt generatePromptUrl logic here or store parts)
      notification_link := '/prompt/' || COALESCE(prompt_details.category_slug, 'uncategorized') ||
                           '/' || COALESCE(prompt_details.tool_slug, 'general') ||
                           '/' || COALESCE(prompt_details.primary_tag_slug, 'misc') ||
                           '/' || COALESCE(prompt_details.title_slug, 'prompt') ||
                           '/' || COALESCE(prompt_details.short_id, NEW.prompt_id::text);

      INSERT INTO public.notifications (
        recipient_user_id,
        actor_user_id,
        type,
        entity_id,
        entity_type,
        entity_title,
        link,
        prompt_short_id,
        prompt_category_slug,
        prompt_tool_slug,
        prompt_primary_tag_slug,
        prompt_title_slug
      )
      VALUES (
        prompt_author_id,
        NEW.user_id,
        'like_prompt',
        NEW.prompt_id,
        'prompt',
        prompt_title_text,
        notification_link,
        prompt_details.short_id,
        prompt_details.category_slug,
        prompt_details.tool_slug,
        prompt_details.primary_tag_slug,
        prompt_details.title_slug
      );
    END IF;
  END IF;
  RETURN NEW;
END;
$$;


--
-- Name: delete_comment(uuid, uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.delete_comment(p_user_id uuid, p_comment_id uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
  -- Check if the user is the owner of the comment
  IF NOT EXISTS (SELECT 1 FROM comments WHERE id = p_comment_id AND user_id = p_user_id) THEN
    RETURN FALSE;
  END IF;
  
  -- Delete the comment
  DELETE FROM comments
  WHERE id = p_comment_id;
  
  RETURN TRUE;
END;
$$;


--
-- Name: delete_current_profile_picture(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.delete_current_profile_picture() RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
    current_avatar_url TEXT;
    file_path TEXT;
    deleted_count INTEGER;
BEGIN
    SELECT avatar_url INTO current_avatar_url 
    FROM public.profiles 
    WHERE id = auth.uid();
    
    IF current_avatar_url IS NULL THEN
        RETURN FALSE;
    END IF;
    
    file_path := SUBSTRING(current_avatar_url FROM '.*/storage/v1/object/public/profile-pictures/(.*)$');
    
    DELETE FROM storage.objects 
    WHERE bucket_id = 'profile-pictures' 
    AND name = file_path
    AND (storage.foldername(name))[1] = auth.uid()::text;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count > 0 THEN
        UPDATE public.profiles 
        SET 
            avatar_url = NULL,
            updated_at = NOW()
        WHERE id = auth.uid();
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$_$;


--
-- Name: enforce_collection_content_rules(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.enforce_collection_content_rules() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  collection_info RECORD;
  prompt_owner_id UUID;
BEGIN
  -- Get collection details
  SELECT c.default_type, c.user_id INTO collection_info
  FROM public.collections c
  WHERE c.id = NEW.collection_id;

  -- Get prompt owner
  SELECT p.user_id INTO prompt_owner_id
  FROM public.prompts p
  WHERE p.id = NEW.prompt_id;

  IF collection_info.default_type = 'my_prompts' AND prompt_owner_id != collection_info.user_id THEN
    RAISE EXCEPTION 'Violated content rule: Cannot add another user''s prompt to "My Prompts" collection.';
  END IF;

  IF collection_info.default_type = 'saved_prompts' AND prompt_owner_id = collection_info.user_id THEN
    RAISE EXCEPTION 'Violated content rule: Cannot add your own prompt to "Saved Prompts" collection.';
  END IF;

  RETURN NEW;
END;
$$;


--
-- Name: generate_collection_search_vector(text, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.generate_collection_search_vector(p_name text, p_description text) RETURNS tsvector
    LANGUAGE plpgsql IMMUTABLE
    AS $$
    BEGIN
      RETURN (
        setweight(to_tsvector('english', coalesce(p_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(p_description, '')), 'B')
      );
    END;
    $$;


--
-- Name: generate_prompt_search_vector(text, text, text, text, text, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.generate_prompt_search_vector(p_title text, p_description text, p_prompt_text text, p_instructions text, p_example_input text, p_example_output_text text) RETURNS tsvector
    LANGUAGE plpgsql IMMUTABLE
    AS $$
BEGIN
  RETURN setweight(to_tsvector('english', COALESCE(p_title, '')), 'A') ||
         setweight(to_tsvector('english', COALESCE(p_description, '')), 'B') ||
         setweight(to_tsvector('english', COALESCE(p_prompt_text, '')), 'C') ||
         setweight(to_tsvector('english', COALESCE(p_instructions, '')), 'D') ||
         setweight(to_tsvector('english', COALESCE(p_example_input, '')), 'D') ||
         setweight(to_tsvector('english', COALESCE(p_example_output_text, '')), 'D');
END;
$$;


--
-- Name: generate_prompt_slug(text, text, text, text, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.generate_prompt_slug(p_title text, p_category_slug text, p_tool_slug text, p_tag_slug text, p_short_id text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
  title_slug TEXT;
BEGIN
  -- Convert title to lowercase and replace spaces with hyphens
  title_slug := lower(regexp_replace(p_title, '[^a-zA-Z0-9\s]', '', 'g'));
  title_slug := regexp_replace(title_slug, '\s+', '-', 'g');
  
  -- Truncate title_slug to 30 characters
  IF length(title_slug) > 30 THEN
    title_slug := substr(title_slug, 1, 30);
    -- Remove trailing hyphens if any
    title_slug := rtrim(title_slug, '-');
  END IF;
  
  -- Construct the full slug
  RETURN 'prompt/' || p_category_slug || '/' || p_tool_slug || '/' || 
         COALESCE(p_tag_slug, 'untagged') || '/' || title_slug || '/' || p_short_id;
END;
$$;


--
-- Name: generate_short_id(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.generate_short_id(length integer DEFAULT 8) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
  chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  result TEXT := '';
  i INTEGER := 0;
BEGIN
  FOR i IN 1..length LOOP
    result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
  END LOOP;
  RETURN result;
END;
$$;


--
-- Name: get_profile_picture_upload_path(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_profile_picture_upload_path(file_extension text DEFAULT 'jpg'::text) RETURNS text
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    RETURN auth.uid()::text || '/profile_' || EXTRACT(EPOCH FROM NOW())::bigint || '.' || file_extension;
END;
$$;


--
-- Name: get_prompt_details_for_notification(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_prompt_details_for_notification(p_prompt_id uuid) RETURNS TABLE(ret_short_id text, ret_title text, ret_category_slug text, ret_tool_slug text, ret_primary_tag_slug text, ret_title_slug text)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.short_id,
    p.title,
    cat.slug,
    tool.slug,
    tag.slug,
    public.slugify(p.title) -- Assumes public.slugify(text) SQL function exists
  FROM public.prompts p
  LEFT JOIN public.categories cat ON p.category_id = cat.id
  LEFT JOIN public.tools tool ON p.tool_id = tool.id
  LEFT JOIN public.tags tag ON p.primary_tag_id = tag.id
  WHERE p.id = p_prompt_id
  LIMIT 1;
END;
$$;


--
-- Name: get_prompt_with_stats(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_prompt_with_stats(p_short_id text) RETURNS TABLE(id uuid, short_id text, title text, description text, image_url text, created_at timestamp with time zone, updated_at timestamp with time zone, is_public boolean, view_count integer, user_id uuid, category_id integer, tool_id integer, primary_tag_id integer, rating bigint, comment_count bigint, trending_score double precision, category_name text, category_slug text, tool_name text, tool_slug text, author_username text, author_avatar_url text, primary_tag_slug text)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.short_id,
    p.title,
    p.description,
    p.image_url,
    p.created_at,
    p.updated_at,
    p.is_public,
    p.view_count,
    p.user_id,
    p.category_id,
    p.tool_id,
    p.primary_tag_id,
    COALESCE(stats.rating, 0::bigint) as rating,
    COALESCE(stats.comment_count, 0::bigint) as comment_count,
    COALESCE(trend.trending_score, 0::double precision) as trending_score,
    cat.name as category_name,
    cat.slug as category_slug,
    tool.name as tool_name,
    tool.slug as tool_slug,
    prof.username as author_username,
    prof.avatar_url as author_avatar_url,
    ptag.slug as primary_tag_slug
  FROM prompts p
  LEFT JOIN categories cat ON p.category_id = cat.id
  LEFT JOIN tools tool ON p.tool_id = tool.id
  LEFT JOIN profiles prof ON p.user_id = prof.id
  LEFT JOIN tags ptag ON p.primary_tag_id = ptag.id
  LEFT JOIN prompt_statistics stats ON p.id = stats.id
  LEFT JOIN trending_prompts trend ON p.id = trend.id
  WHERE p.short_id = p_short_id;
END;
$$;


--
-- Name: get_prompt_with_stats_cached(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_prompt_with_stats_cached(p_short_id text) RETURNS TABLE(id uuid, short_id text, title text, description text, image_url text, created_at timestamp with time zone, updated_at timestamp with time zone, is_public boolean, view_count integer, user_id uuid, category_id integer, tool_id integer, primary_tag_id integer, rating bigint, comment_count bigint, trending_score double precision, category_name text, category_slug text, tool_name text, tool_slug text, author_username text, author_avatar_url text, primary_tag_slug text, prompt_text text, instructions text, example_input text, example_output_text text, example_output_image_url text, prompt_slug text, original_prompt_id uuid, ai_model_id integer, user_entered_ai_model text, ai_model_provider text, ai_model_name text, ai_model_slug text, ai_model_deprecated boolean, ai_model_type text, tags jsonb)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.id,
    p.short_id,
    p.title,
    p.description,
    p.image_url,
    p.created_at,
    p.updated_at,
    p.is_public,
    p.view_count,
    p.user_id, -- author_id
    p.category_id,
    p.tool_id,
    p.primary_tag_id,
    COALESCE(cached_stats.rating, 0::bigint) as rating,
    COALESCE(cached_stats.comment_count, 0::bigint) as comment_count,
    COALESCE(cached_trend.trending_score, 0::double precision) as trending_score,
    cat.name as category_name,
    cat.slug as category_slug,
    tool.name as tool_name,
    tool.slug as tool_slug,
    prof.username as author_username,
    prof.avatar_url as author_avatar_url,
    ptag.slug as primary_tag_slug,
    -- Select detailed fields directly from prompts table 'p'
    p.prompt_text,
    p.instructions,
    p.example_input,
    p.example_output_text,
    p.example_output_image_url,
    p.slug as prompt_slug,
    p.original_prompt_id,
    p.ai_model_id,
    p.user_entered_ai_model,
    -- Select AI model details from 'am'
    am.provider as ai_model_provider,
    am.tool_name as ai_model_name, -- Note: ai_models.tool_name is the specific model name
    am.slug as ai_model_slug,
    am.deprecated as ai_model_deprecated,
    am.type as ai_model_type,
    -- Aggregate tags for the prompt
    (SELECT jsonb_agg(jsonb_build_object('id', t.id, 'name', t.name, 'slug', t.slug) ORDER BY t.name)
     FROM public.prompt_tags pt_join
     JOIN public.tags t ON pt_join.tag_id = t.id
     WHERE pt_join.prompt_id = p.id) AS tags
  FROM public.prompts p
  LEFT JOIN public.categories cat ON p.category_id = cat.id
  LEFT JOIN public.tools tool ON p.tool_id = tool.id
  LEFT JOIN public.profiles prof ON p.user_id = prof.id
  LEFT JOIN public.tags ptag ON p.primary_tag_id = ptag.id
  LEFT JOIN public.ai_models am ON p.ai_model_id = am.id
  LEFT JOIN public.mv_prompt_statistics cached_stats ON p.id = cached_stats.id
  LEFT JOIN public.mv_trending_prompts cached_trend ON p.id = cached_trend.id
  WHERE p.short_id = p_short_id;
END;
$$;


--
-- Name: get_prompts_with_saved_status(uuid, integer, integer, text[], text[], text[], text[], text, uuid, text, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_prompts_with_saved_status(p_user_id uuid DEFAULT NULL::uuid, p_limit integer DEFAULT 20, p_offset integer DEFAULT 0, p_category_slugs text[] DEFAULT NULL::text[], p_tool_slugs text[] DEFAULT NULL::text[], p_tag_slugs text[] DEFAULT NULL::text[], p_ai_model_slugs text[] DEFAULT NULL::text[], p_search_query text DEFAULT NULL::text, p_author_id uuid DEFAULT NULL::uuid, p_sort_by text DEFAULT 'created_at'::text, p_sort_order text DEFAULT 'desc'::text) RETURNS TABLE(id uuid, short_id text, title text, description text, image_url text, created_at timestamp with time zone, updated_at timestamp with time zone, is_public boolean, view_count integer, primary_tag_id integer, category_id integer, tool_id integer, author_id uuid, search_vector tsvector, tag_slugs_array text[], category_name text, category_slug text, tool_name text, tool_slug text, author_username text, author_avatar_url text, primary_tag_slug text, tags jsonb, rating bigint, comment_count bigint, trending_score double precision, ai_model_id integer, ai_model_provider text, ai_model_name text, ai_model_slug text, ai_model_deprecated boolean, is_saved_by_user boolean)
    LANGUAGE plpgsql
    AS $$
DECLARE
  search_ts_query tsquery;
  exact_username_match boolean := false;
BEGIN
  -- Check if search query exactly matches a username (case-insensitive)
  IF p_search_query IS NOT NULL THEN
    SELECT EXISTS(
      SELECT 1 FROM profiles 
      WHERE lower(username) = lower(p_search_query)
    ) INTO exact_username_match;
    
    -- Prepare the tsquery for full-text search
    search_ts_query := websearch_to_tsquery('english', p_search_query);
  END IF;

  RETURN QUERY
  WITH user_saved_prompts AS (
    SELECT DISTINCT cp.prompt_id
    FROM collection_prompts cp
    JOIN collections c ON cp.collection_id = c.id
    WHERE c.user_id = p_user_id 
      AND p_user_id IS NOT NULL
  ),
  search_results AS (
    SELECT 
      pcd.id,
      pcd.short_id,
      pcd.title,
      pcd.description,
      pcd.image_url,
      pcd.created_at,
      pcd.updated_at,
      pcd.is_public,
      pcd.view_count,
      pcd.primary_tag_id,
      pcd.category_id,
      pcd.tool_id,
      pcd.author_id,
      pcd.search_vector,
      pcd.tag_slugs_array,
      pcd.category_name,
      pcd.category_slug,
      pcd.tool_name,
      pcd.tool_slug,
      pcd.author_username,
      pcd.author_avatar_url,
      pcd.primary_tag_slug,
      pcd.tags,
      pcd.rating,
      pcd.comment_count,
      pcd.trending_score,
      pcd.ai_model_id,
      pcd.ai_model_provider,
      pcd.ai_model_name,
      pcd.ai_model_slug,
      pcd.ai_model_deprecated,
      CASE WHEN usp.prompt_id IS NOT NULL THEN true ELSE false END AS is_saved_by_user,
      -- Sophisticated relevance scoring
      CASE 
        WHEN p_search_query IS NULL THEN 0
        WHEN exact_username_match AND lower(pcd.author_username) = lower(p_search_query) THEN 100 -- High boost for exact username match
        WHEN pcd.author_username ILIKE p_search_query || '%' THEN 30 -- Username starts with query
        WHEN pcd.author_username ILIKE '%' || p_search_query || '%' THEN 20 -- Username contains query
        ELSE 0
      END +
      CASE 
        WHEN p_search_query IS NULL THEN 0
        WHEN pcd.search_vector @@ search_ts_query THEN ts_rank_cd(pcd.search_vector, search_ts_query) * 100
        WHEN pcd.title ILIKE '%' || p_search_query || '%' THEN 15 -- Title contains query
        WHEN pcd.description ILIKE '%' || p_search_query || '%' THEN 5 -- Description contains query
        ELSE 0
      END AS relevance_score
    FROM prompt_card_details pcd
    LEFT JOIN user_saved_prompts usp ON pcd.id = usp.prompt_id
    WHERE pcd.is_public = true
      AND (p_category_slugs IS NULL OR pcd.category_slug = ANY(p_category_slugs))
      AND (p_tool_slugs IS NULL OR pcd.tool_slug = ANY(p_tool_slugs))
      AND (p_tag_slugs IS NULL OR pcd.tag_slugs_array && p_tag_slugs)
      AND (p_ai_model_slugs IS NULL OR pcd.ai_model_slug = ANY(p_ai_model_slugs))
      AND (p_author_id IS NULL OR pcd.author_id = p_author_id)
      AND (
        p_search_query IS NULL OR 
        pcd.search_vector @@ search_ts_query OR 
        pcd.title ILIKE '%' || p_search_query || '%' OR
        pcd.description ILIKE '%' || p_search_query || '%' OR
        pcd.author_username ILIKE '%' || p_search_query || '%'
      )
  )
  SELECT 
    sr.id,
    sr.short_id,
    sr.title,
    sr.description,
    sr.image_url,
    sr.created_at,
    sr.updated_at,
    sr.is_public,
    sr.view_count,
    sr.primary_tag_id,
    sr.category_id,
    sr.tool_id,
    sr.author_id,
    sr.search_vector,
    sr.tag_slugs_array,
    sr.category_name,
    sr.category_slug,
    sr.tool_name,
    sr.tool_slug,
    sr.author_username,
    sr.author_avatar_url,
    sr.primary_tag_slug,
    sr.tags,
    sr.rating,
    sr.comment_count,
    sr.trending_score,
    sr.ai_model_id,
    sr.ai_model_provider,
    sr.ai_model_name,
    sr.ai_model_slug,
    sr.ai_model_deprecated,
    sr.is_saved_by_user
  FROM search_results sr
  ORDER BY 
    -- If there's a search query, order by relevance first
    CASE 
      WHEN p_search_query IS NOT NULL THEN sr.relevance_score
      ELSE 0
    END DESC,
    -- Then apply the requested sorting
    CASE 
      WHEN p_sort_by = 'created_at' AND p_sort_order = 'desc' THEN sr.created_at
    END DESC,
    CASE 
      WHEN p_sort_by = 'created_at' AND p_sort_order = 'asc' THEN sr.created_at
    END ASC,
    CASE 
      WHEN p_sort_by = 'rating' AND p_sort_order = 'desc' THEN sr.rating
    END DESC,
    CASE 
      WHEN p_sort_by = 'rating' AND p_sort_order = 'asc' THEN sr.rating
    END ASC,
    CASE 
      WHEN p_sort_by = 'trending_score' AND p_sort_order = 'desc' THEN sr.trending_score
    END DESC,
    CASE 
      WHEN p_sort_by = 'trending_score' AND p_sort_order = 'asc' THEN sr.trending_score
    END ASC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$;


--
-- Name: FUNCTION get_prompts_with_saved_status(p_user_id uuid, p_limit integer, p_offset integer, p_category_slugs text[], p_tool_slugs text[], p_tag_slugs text[], p_ai_model_slugs text[], p_search_query text, p_author_id uuid, p_sort_by text, p_sort_order text); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.get_prompts_with_saved_status(p_user_id uuid, p_limit integer, p_offset integer, p_category_slugs text[], p_tool_slugs text[], p_tag_slugs text[], p_ai_model_slugs text[], p_search_query text, p_author_id uuid, p_sort_by text, p_sort_order text) IS 'Enhanced prompt search with username matching and dynamic relevance scoring';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: ai_models; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ai_models (
    id integer NOT NULL,
    provider text NOT NULL,
    tool_name text NOT NULL,
    type text NOT NULL,
    deprecated boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now(),
    created_at timestamp without time zone DEFAULT now(),
    tool text,
    tool_id integer,
    slug text
);


--
-- Name: COLUMN ai_models.tool; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.ai_models.tool IS 'category of the main tool';


--
-- Name: categories; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.categories (
    id integer NOT NULL,
    name text NOT NULL,
    slug text NOT NULL,
    description text,
    image_path text
);


--
-- Name: comments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.comments (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    prompt_id uuid NOT NULL,
    parent_comment_id uuid,
    text text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.profiles (
    id uuid NOT NULL,
    username text NOT NULL,
    bio text,
    avatar_url text,
    website_url text,
    github_url text,
    x_url text,
    youtube_url text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    is_username_customized boolean DEFAULT false,
    total_likes_received integer DEFAULT 0,
    public_prompts_count integer DEFAULT 0,
    public_collections_count integer DEFAULT 0,
    email_on_comment boolean DEFAULT true,
    email_on_reply boolean DEFAULT true,
    email_on_like boolean DEFAULT true,
    email_product_updates boolean DEFAULT true
);


--
-- Name: prompt_votes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.prompt_votes (
    user_id uuid NOT NULL,
    prompt_id uuid NOT NULL,
    vote_type smallint NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT prompt_votes_vote_type_check CHECK ((vote_type = ANY (ARRAY['-1'::integer, 1])))
);


--
-- Name: prompts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.prompts (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    short_id text NOT NULL,
    user_id uuid NOT NULL,
    category_id integer NOT NULL,
    tool_id integer NOT NULL,
    title text NOT NULL,
    description text NOT NULL,
    prompt_text text NOT NULL,
    instructions text,
    example_input text,
    example_output_text text,
    example_output_image_url text,
    image_url text,
    is_public boolean DEFAULT true NOT NULL,
    original_prompt_id uuid,
    view_count integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_by_user_id uuid,
    search_vector tsvector,
    primary_tag_id integer,
    tag_slugs text[],
    slug text,
    ai_model_id integer,
    user_entered_ai_model text
);


--
-- Name: prompt_statistics; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.prompt_statistics AS
 WITH votecounts AS (
         SELECT prompt_votes.prompt_id,
            sum(prompt_votes.vote_type) AS rating,
            count(
                CASE
                    WHEN (prompt_votes.vote_type = 1) THEN 1
                    ELSE NULL::integer
                END) AS upvotes,
            count(
                CASE
                    WHEN (prompt_votes.vote_type = '-1'::integer) THEN 1
                    ELSE NULL::integer
                END) AS downvotes
           FROM public.prompt_votes
          GROUP BY prompt_votes.prompt_id
        ), commentcounts AS (
         SELECT comments.prompt_id,
            count(*) AS comment_count
           FROM public.comments
          GROUP BY comments.prompt_id
        ), remixcounts AS (
         SELECT prompts.original_prompt_id AS prompt_id,
            count(*) AS remix_count
           FROM public.prompts
          WHERE (prompts.original_prompt_id IS NOT NULL)
          GROUP BY prompts.original_prompt_id
        )
 SELECT p.id,
    p.short_id,
    p.user_id,
    p.category_id,
    p.tool_id,
    p.title,
    p.description,
    p.prompt_text,
    p.instructions,
    p.example_input,
    p.example_output_text,
    p.example_output_image_url,
    p.image_url,
    p.is_public,
    p.original_prompt_id,
    p.view_count,
    p.created_at,
    p.updated_at,
    p.updated_by_user_id,
    p.search_vector,
    p.primary_tag_id,
    p.tag_slugs,
    COALESCE(vc.rating, (0)::bigint) AS rating,
    COALESCE(vc.upvotes, (0)::bigint) AS upvotes,
    COALESCE(vc.downvotes, (0)::bigint) AS downvotes,
    COALESCE(cc.comment_count, (0)::bigint) AS comment_count,
    COALESCE(rc.remix_count, (0)::bigint) AS remix_count
   FROM (((public.prompts p
     LEFT JOIN votecounts vc ON ((p.id = vc.prompt_id)))
     LEFT JOIN commentcounts cc ON ((p.id = cc.prompt_id)))
     LEFT JOIN remixcounts rc ON ((p.id = rc.prompt_id)));


--
-- Name: VIEW prompt_statistics; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.prompt_statistics IS 'Aggregates rating (sum of votes), upvote count, downvote count, comment count, and remix count for each prompt.';


--
-- Name: prompt_tags; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.prompt_tags (
    prompt_id uuid NOT NULL,
    tag_id integer NOT NULL
);


--
-- Name: tags; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tags (
    id integer NOT NULL,
    name text NOT NULL,
    slug text NOT NULL
);


--
-- Name: tools; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tools (
    id integer NOT NULL,
    name text NOT NULL,
    slug text NOT NULL,
    description text,
    website text,
    icon text
);


--
-- Name: trending_prompts; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.trending_prompts AS
 WITH votestats AS (
         SELECT prompt_votes.prompt_id,
            count(
                CASE
                    WHEN (prompt_votes.vote_type = 1) THEN 1
                    ELSE NULL::integer
                END) AS upvotes,
            count(
                CASE
                    WHEN (prompt_votes.vote_type = '-1'::integer) THEN 1
                    ELSE NULL::integer
                END) AS downvotes
           FROM public.prompt_votes
          GROUP BY prompt_votes.prompt_id
        ), commentcounts AS (
         SELECT comments.prompt_id,
            count(*) AS comment_count
           FROM public.comments
          GROUP BY comments.prompt_id
        ), remixcounts AS (
         SELECT prompts.original_prompt_id AS prompt_id,
            count(*) AS remix_count
           FROM public.prompts
          WHERE (prompts.original_prompt_id IS NOT NULL)
          GROUP BY prompts.original_prompt_id
        )
 SELECT p.id,
    p.title,
    p.user_id,
    p.created_at,
    p.view_count,
    p.is_public,
    COALESCE(vs.upvotes, (0)::bigint) AS upvotes,
    COALESCE(vs.downvotes, (0)::bigint) AS downvotes,
    COALESCE(cc.comment_count, (0)::bigint) AS comment_count,
    COALESCE(rc.remix_count, (0)::bigint) AS remix_count,
    ((((((((COALESCE(vs.upvotes, (0)::bigint) - COALESCE(vs.downvotes, (0)::bigint)))::numeric * 0.7) + ((COALESCE(cc.comment_count, (0)::bigint))::numeric * 0.2)) + ((COALESCE(rc.remix_count, (0)::bigint))::numeric * 0.3)) + ((p.view_count)::numeric * 0.1)) - (GREATEST(0.0, (EXTRACT(epoch FROM (now() - p.created_at)) / (((60 * 60))::numeric * 24.0))) * 0.3)))::double precision AS trending_score
   FROM (((public.prompts p
     LEFT JOIN votestats vs ON ((p.id = vs.prompt_id)))
     LEFT JOIN commentcounts cc ON ((p.id = cc.prompt_id)))
     LEFT JOIN remixcounts rc ON ((p.id = rc.prompt_id)))
  WHERE (p.is_public = true);


--
-- Name: VIEW trending_prompts; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.trending_prompts IS 'Calculates statistics and a trending score for prompts based on weighted votes, comments, remixes, views, and a recency penalty.';


--
-- Name: prompt_card_details; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.prompt_card_details AS
 WITH aggregatedtagsfordisplay AS (
         SELECT pt.prompt_id,
            jsonb_agg(jsonb_build_object('id', t.id, 'name', t.name, 'slug', t.slug) ORDER BY t.name) FILTER (WHERE (t.id IS NOT NULL)) AS tags_jsonb
           FROM (public.prompt_tags pt
             JOIN public.tags t ON ((pt.tag_id = t.id)))
          GROUP BY pt.prompt_id
        )
 SELECT p.id,
    p.short_id,
    p.title,
    p.description,
    p.image_url,
    p.created_at,
    p.updated_at,
    p.is_public,
    p.view_count,
    p.primary_tag_id,
    p.category_id,
    p.tool_id,
    p.user_id AS author_id,
    p.search_vector,
    COALESCE(p.tag_slugs, '{}'::text[]) AS tag_slugs_array,
    cat.name AS category_name,
    cat.slug AS category_slug,
    tool.name AS tool_name,
    tool.slug AS tool_slug,
    prof.username AS author_username,
    prof.avatar_url AS author_avatar_url,
    ptag.slug AS primary_tag_slug,
    COALESCE(atd.tags_jsonb, '[]'::jsonb) AS tags,
    COALESCE(stats.rating, (0)::bigint) AS rating,
    COALESCE(stats.comment_count, (0)::bigint) AS comment_count,
    COALESCE(stats.remix_count, (0)::bigint) AS remix_count,
    COALESCE(trend.trending_score, (0)::double precision) AS trending_score,
    p.ai_model_id,
    am.provider AS ai_model_provider,
    am.tool_name AS ai_model_name,
    am.slug AS ai_model_slug,
    am.deprecated AS ai_model_deprecated
   FROM ((((((((public.prompts p
     LEFT JOIN public.categories cat ON ((p.category_id = cat.id)))
     LEFT JOIN public.tools tool ON ((p.tool_id = tool.id)))
     LEFT JOIN public.profiles prof ON ((p.user_id = prof.id)))
     LEFT JOIN public.tags ptag ON ((p.primary_tag_id = ptag.id)))
     LEFT JOIN aggregatedtagsfordisplay atd ON ((p.id = atd.prompt_id)))
     LEFT JOIN public.prompt_statistics stats ON ((p.id = stats.id)))
     LEFT JOIN public.trending_prompts trend ON ((p.id = trend.id)))
     LEFT JOIN public.ai_models am ON ((p.ai_model_id = am.id)));


--
-- Name: get_related_prompts(text, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_related_prompts(p_source_prompt_short_id text, p_limit integer DEFAULT 6) RETURNS SETOF public.prompt_card_details
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_source_category_slug TEXT;
    v_source_tool_slug TEXT;
    v_source_tags_array TEXT[];
BEGIN
    -- 1. Fetch details of the source prompt
    SELECT
        pcd.category_slug,
        pcd.tool_slug,
        pcd.tag_slugs_array
    INTO
        v_source_category_slug,
        v_source_tool_slug,
        v_source_tags_array
    FROM prompt_card_details pcd
    WHERE pcd.short_id = p_source_prompt_short_id;

    IF NOT FOUND THEN
        RETURN;
    END IF;

    -- 2. Query and score candidate prompts
    RETURN QUERY
    WITH scored_candidates AS (
        SELECT
            candidate.*, -- Selects all columns from prompt_card_details for the CTE
            (
                CASE
                    WHEN candidate.category_slug = v_source_category_slug THEN 3.0
                    ELSE 0.0
                END +
                CASE
                    WHEN candidate.tool_slug = v_source_tool_slug THEN 2.0
                    ELSE 0.0
                END +
                (
                    0.5 * COALESCE(
                        array_length(
                            ARRAY(
                                SELECT unnest(COALESCE(candidate.tag_slugs_array, '{}'::TEXT[]))
                                INTERSECT
                                SELECT unnest(COALESCE(v_source_tags_array, '{}'::TEXT[]))
                            ), 1
                        ), 0
                    )
                )
            ) AS relevance_score
        FROM
            prompt_card_details AS candidate
        WHERE
            candidate.short_id != p_source_prompt_short_id
            AND candidate.is_public = TRUE
    )
    -- Final SELECT with columns in the exact order of prompt_card_details view
    SELECT
        sc.id,                    -- 1: uuid
        sc.short_id,              -- 2: text
        sc.title,                 -- 3: text
        sc.description,           -- 4: text
        sc.image_url,             -- 5: text
        sc.created_at,            -- 6: timestamp with time zone
        sc.updated_at,            -- 7: timestamp with time zone
        sc.is_public,             -- 8: boolean
        sc.view_count,            -- 9: integer
        sc.primary_tag_id,        -- 10: integer
        sc.category_id,           -- 11: integer
        sc.tool_id,               -- 12: integer
        sc.author_id,             -- 13: uuid
        sc.search_vector,         -- 14: tsvector
        sc.tag_slugs_array,       -- 15: text[]
        sc.category_name,         -- 16: text
        sc.category_slug,         -- 17: text
        sc.tool_name,             -- 18: text
        sc.tool_slug,             -- 19: text
        sc.author_username,       -- 20: text
        sc.author_avatar_url,     -- 21: text
        sc.primary_tag_slug,      -- 22: text
        sc.tags,                  -- 23: jsonb
        sc.rating,                -- 24: bigint
        sc.comment_count,         -- 25: bigint
        sc.remix_count,           -- 26: bigint
        sc.trending_score,        -- 27: double precision
        sc.ai_model_id,           -- 28: integer
        sc.ai_model_provider,     -- 29: text
        sc.ai_model_name,         -- 30: text
        sc.ai_model_slug,         -- 31: text
        sc.ai_model_deprecated    -- 32: boolean
    FROM
        scored_candidates sc
    ORDER BY
        sc.relevance_score DESC,
        sc.rating DESC,
        sc.created_at DESC
    LIMIT
        p_limit;

END;
$$;


--
-- Name: get_vote_type(uuid, uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_vote_type(user_id_param uuid, prompt_id_param uuid) RETURNS smallint
    LANGUAGE sql STABLE
    AS $$
  SELECT vote_type FROM public.prompt_votes
  WHERE user_id = user_id_param AND prompt_id = prompt_id_param;
$$;


--
-- Name: handle_new_user(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.handle_new_user() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Create a new profile for the user
  INSERT INTO public.profiles (
    id,
    username,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(md5(NEW.email::text), 1, 8)),
    NOW(),
    NOW()
  );
  
  RETURN NEW;
END;
$$;


--
-- Name: handle_profile_picture_deletion(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.handle_profile_picture_deletion() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    user_id_from_path TEXT;
BEGIN
    IF OLD.bucket_id != 'profile-pictures' THEN
        RETURN OLD;
    END IF;
    
    user_id_from_path := (storage.foldername(OLD.name))[1];
    
    UPDATE public.profiles 
    SET 
        avatar_url = NULL,
        updated_at = NOW()
    WHERE id = user_id_from_path::uuid
    AND avatar_url LIKE '%' || OLD.name;
    
    RETURN OLD;
END;
$$;


--
-- Name: handle_profile_picture_upload(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.handle_profile_picture_upload() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
DECLARE
    user_id_from_path TEXT;
    old_avatar_url TEXT;
    new_avatar_url TEXT;
    supabase_url TEXT;
    old_file_path TEXT;
BEGIN
    IF NEW.bucket_id != 'profile-pictures' THEN
        RETURN NEW;
    END IF;
    
    user_id_from_path := (storage.foldername(NEW.name))[1];
    
    -- Get Supabase URL from app settings (must be configured via configure_supabase_url function)
    supabase_url := current_setting('app.supabase_url', true);

    -- Ensure URL is configured
    IF supabase_url IS NULL OR supabase_url = '' THEN
        RAISE EXCEPTION 'Supabase URL not configured. Run: SELECT public.configure_supabase_url(''https://your-project.supabase.co'');';
    END IF;
    
    new_avatar_url := supabase_url || '/storage/v1/object/public/profile-pictures/' || NEW.name;
    
    SELECT avatar_url INTO old_avatar_url 
    FROM public.profiles 
    WHERE id = user_id_from_path::uuid;
    
    UPDATE public.profiles 
    SET 
        avatar_url = new_avatar_url,
        updated_at = NOW()
    WHERE id = user_id_from_path::uuid;
    
    IF old_avatar_url IS NOT NULL 
       AND old_avatar_url != new_avatar_url 
       AND old_avatar_url LIKE '%/storage/v1/object/public/profile-pictures/%' THEN
        
        old_file_path := SUBSTRING(old_avatar_url FROM '.*/storage/v1/object/public/profile-pictures/(.*)$');
        
        BEGIN
            DELETE FROM storage.objects 
            WHERE bucket_id = 'profile-pictures' 
            AND name = old_file_path;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Failed to delete old profile picture: %', SQLERRM;
        END;
    END IF;
    
    RETURN NEW;
END;
$_$;


--
-- Name: increment_prompt_unique_view_count(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.increment_prompt_unique_view_count() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    -- Increment the view_count in the prompts table
    UPDATE public.prompts 
    SET view_count = view_count + 1 
    WHERE id = NEW.prompt_id;
    
    RETURN NEW;
END;
$$;


--
-- Name: increment_prompt_view_count(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.increment_prompt_view_count(prompt_id uuid) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  UPDATE prompts
  SET view_count = view_count + 1
  WHERE id = prompt_id;
END;
$$;


--
-- Name: process_search_query(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.process_search_query(query text) RETURNS text
    LANGUAGE plpgsql IMMUTABLE
    AS $$
DECLARE
  processed_query TEXT;
BEGIN
  -- Convert the query to lowercase
  processed_query := lower(query);
  
  -- Replace special characters with spaces
  processed_query := regexp_replace(processed_query, '[^a-z0-9\s]', ' ', 'g');
  
  -- Replace multiple spaces with a single space
  processed_query := regexp_replace(processed_query, '\s+', ' ', 'g');
  
  -- Trim leading and trailing spaces
  processed_query := trim(processed_query);
  
  -- Replace spaces with & for AND operations in tsquery
  processed_query := regexp_replace(processed_query, '\s+', ' & ', 'g');
  
  -- Add :* to each word for prefix matching
  processed_query := regexp_replace(processed_query, '([a-z0-9]+)', '\1:*', 'g');
  
  RETURN processed_query;
END;
$$;


--
-- Name: random_timestamp(timestamp with time zone, timestamp with time zone); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.random_timestamp(start_date timestamp with time zone, end_date timestamp with time zone) RETURNS timestamp with time zone
    LANGUAGE plpgsql
    AS $$
DECLARE
  safe_start_date TIMESTAMPTZ;
BEGIN
  -- Ensure end_date is valid, default to NOW() if null
  IF end_date IS NULL THEN
    end_date := NOW();
  END IF;

  -- Ensure start_date is valid and before end_date
  IF start_date IS NULL OR start_date >= end_date THEN
     -- Use GREATEST to ensure the fallback start date isn't before epoch if end_date is very early
     safe_start_date := GREATEST(end_date - INTERVAL '1 minute', '1970-01-01'::timestamptz);
     -- Ensure start is strictly before end after adjustments
     IF safe_start_date >= end_date THEN
       safe_start_date := end_date - INTERVAL '1 second';
     END IF;
  ELSE
     safe_start_date := start_date;
  END IF;

  -- Final check for edge case where interval might still be zero or negative
  IF end_date <= safe_start_date THEN
    -- Return end_date or handle as error, returning end_date is safer for bulk operations
    RETURN end_date;
  END IF;

  -- Calculate and return the random timestamp
  RETURN safe_start_date + random() * (end_date - safe_start_date);
END;
$$;


--
-- Name: record_prompt_view(uuid, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.record_prompt_view(p_prompt_id uuid, p_viewer_hash text DEFAULT NULL::text) RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    -- Simply increment the view_count in the prompts table
    -- This will count every view, ensuring the counter goes up
    UPDATE public.prompts 
    SET view_count = view_count + 1 
    WHERE id = p_prompt_id AND is_public = true;
    
    -- Log the update for debugging (optional)
    RAISE NOTICE 'Updated view_count for prompt_id: %', p_prompt_id;
END;
$$;


--
-- Name: refresh_prompt_caches(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_prompt_caches() RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_trending_prompts;
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_prompt_statistics;
  
  -- Log the refresh
  RAISE NOTICE 'Prompt caches refreshed at %', NOW();
END;
$$;


--
-- Name: remove_prompt_from_collection(uuid, uuid, uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.remove_prompt_from_collection(p_user_id uuid, p_prompt_id uuid, p_collection_id uuid) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    target_collection_owner_id UUID;
BEGIN
    -- Verify the user owns the collection they are removing from
    SELECT user_id INTO target_collection_owner_id
    FROM public.collections
    WHERE id = p_collection_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Collection % not found.', p_collection_id;
        RETURN FALSE;
    END IF;

    IF target_collection_owner_id != p_user_id THEN
        RAISE EXCEPTION 'User % does not own collection %.', p_user_id, p_collection_id;
        RETURN FALSE;
    END IF;

    DELETE FROM public.collection_prompts
    WHERE collection_id = p_collection_id AND prompt_id = p_prompt_id AND user_id = p_user_id;

    IF NOT FOUND THEN
        -- Prompt was not in the collection, or RLS prevented delete.
        -- Consider this a success or failure based on desired behavior.
        -- For now, let's say it's fine if it wasn't there.
        RETURN TRUE;
    END IF;

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error in remove_prompt_from_collection: %', SQLERRM;
        RETURN FALSE;
END;
$$;


--
-- Name: search_prompts(text, integer, integer, integer, integer, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.search_prompts(search_query text, category_id_filter integer DEFAULT NULL::integer, tool_id_filter integer DEFAULT NULL::integer, tag_id_filter integer DEFAULT NULL::integer, limit_count integer DEFAULT 20, offset_count integer DEFAULT 0) RETURNS TABLE(id uuid, short_id text, title text, slug text, description text, user_id uuid, category_id integer, tool_id integer, image_url text, created_at timestamp with time zone, rating bigint, comment_count bigint, remix_count bigint, view_count integer, rank double precision)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.id,
    p.short_id,
    p.title,
    p.slug,
    p.description,
    p.user_id,
    p.category_id,
    p.tool_id,
    p.image_url,
    p.created_at,
    COALESCE(SUM(pv.vote_type), 0) AS rating,
    COUNT(DISTINCT c.id) AS comment_count,
    COUNT(DISTINCT rp.id) AS remix_count,
    p.view_count,
    ts_rank(p.search_vector, to_tsquery('english', search_query)) AS rank
  FROM
    prompts p
  LEFT JOIN prompt_votes pv ON p.id = pv.prompt_id
  LEFT JOIN comments c ON p.id = c.prompt_id
  LEFT JOIN prompts rp ON p.id = rp.original_prompt_id
  LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
  WHERE
    p.is_public = true
    AND p.search_vector @@ to_tsquery('english', search_query)
    AND (category_id_filter IS NULL OR p.category_id = category_id_filter)
    AND (tool_id_filter IS NULL OR p.tool_id = tool_id_filter)
    AND (tag_id_filter IS NULL OR pt.tag_id = tag_id_filter)
  GROUP BY
    p.id
  ORDER BY
    rank DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$;


--
-- Name: search_prompts_improved(text, integer, integer, integer[], integer, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.search_prompts_improved(search_query text, category_id_filter integer DEFAULT NULL::integer, tool_id_filter integer DEFAULT NULL::integer, tag_ids_filter integer[] DEFAULT NULL::integer[], limit_count integer DEFAULT 20, offset_count integer DEFAULT 0) RETURNS TABLE(id uuid, short_id text, title text, slug text, description text, user_id uuid, category_id integer, tool_id integer, image_url text, created_at timestamp with time zone, rating bigint, comment_count bigint, remix_count bigint, view_count integer, rank double precision)
    LANGUAGE plpgsql
    AS $$
DECLARE
  processed_query TEXT;
  tsquery_obj tsquery;
BEGIN
  -- Process the search query
  processed_query := process_search_query(search_query);
  
  -- Convert to tsquery
  tsquery_obj := to_tsquery('english', processed_query);
  
  RETURN QUERY
  WITH prompt_stats AS (
    SELECT
      p.id,
      p.short_id,
      p.title,
      p.slug,
      p.description,
      p.user_id,
      p.category_id,
      p.tool_id,
      p.image_url,
      p.created_at,
      p.view_count,
      p.search_vector,
      COALESCE(SUM(pv.vote_type), 0) AS rating,
      COUNT(DISTINCT c.id) AS comment_count,
      COUNT(DISTINCT rp.id) AS remix_count
    FROM
      prompts p
    LEFT JOIN prompt_votes pv ON p.id = pv.prompt_id
    LEFT JOIN comments c ON p.id = c.prompt_id
    LEFT JOIN prompts rp ON p.id = rp.original_prompt_id
    WHERE
      p.is_public = true
      AND (category_id_filter IS NULL OR p.category_id = category_id_filter)
      AND (tool_id_filter IS NULL OR p.tool_id = tool_id_filter)
    GROUP BY
      p.id
  ),
  filtered_prompts AS (
    SELECT
      ps.*,
      ts_rank(ps.search_vector, tsquery_obj) AS rank
    FROM
      prompt_stats ps
    WHERE
      ps.search_vector @@ tsquery_obj
      AND (
        tag_ids_filter IS NULL
        OR EXISTS (
          SELECT 1
          FROM prompt_tags pt
          WHERE pt.prompt_id = ps.id
          AND pt.tag_id = ANY(tag_ids_filter)
        )
      )
  )
  SELECT
    fp.id,
    fp.short_id,
    fp.title,
    fp.slug,
    fp.description,
    fp.user_id,
    fp.category_id,
    fp.tool_id,
    fp.image_url,
    fp.created_at,
    fp.rating,
    fp.comment_count,
    fp.remix_count,
    fp.view_count,
    fp.rank
  FROM
    filtered_prompts fp
  ORDER BY
    fp.rank DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$;


--
-- Name: set_primary_tag_on_first_insert(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.set_primary_tag_on_first_insert() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Check if the primary_tag_id for the associated prompt is currently NULL
    IF (SELECT primary_tag_id FROM prompts WHERE id = NEW.prompt_id) IS NULL THEN
        -- If it's NULL, update it with the tag_id that was just inserted
        UPDATE prompts
        SET primary_tag_id = NEW.tag_id
        WHERE id = NEW.prompt_id;
    END IF;

    -- Return the newly inserted row (standard practice for AFTER triggers)
    RETURN NEW;
END;
$$;


--
-- Name: set_prompt_slug(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.set_prompt_slug() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  generated_slug TEXT;
  base_slug TEXT;
  existing_slug_count INTEGER;
  suffix_num INTEGER := 1;
BEGIN
  -- Only generate/set slug on INSERT. For UPDATE, we keep the original (Option A policy).
  IF (TG_OP = 'INSERT') THEN
    -- Use NEW.title to generate the slug.
    -- The frontend could also pass a NEW.slug if it pre-calculates one,
    -- but for consistency, always recalculating from title on insert is safer.
    IF NEW.title IS NULL OR NEW.title = '' THEN
      NEW.slug := 'untitled-' || substr(md5(random()::text), 1, 6); -- Fallback for empty title
    ELSE
      base_slug := public.slugify(NEW.title);
      generated_slug := base_slug;

      -- Check for uniqueness (slug + short_id will make the full URL path unique).
      -- However, making just the title_slug somewhat unique is good practice
      -- to avoid too many prompts having `/my-awesome-prompt/...`
      -- This basic uniqueness check for `slug` itself is optional if `short_id` ensures overall URL path uniqueness.
      -- For truly unique slugs, you'd typically check against existing slugs.
      -- Given `short_id` handles final uniqueness, we might not need aggressive suffixing here.
      -- A simple check:
      SELECT count(*) INTO existing_slug_count FROM public.prompts WHERE slug = generated_slug;
      WHILE existing_slug_count > 0 LOOP
        generated_slug := base_slug || '-' || suffix_num;
        suffix_num := suffix_num + 1;
        SELECT count(*) INTO existing_slug_count FROM public.prompts WHERE slug = generated_slug;
        IF suffix_num > 20 THEN -- safety break
            generated_slug := base_slug || '-' || substr(md5(random()::text),1,4); -- random suffix
            EXIT;
        END IF;
      END LOOP;
      --
      -- Simpler approach for now since short_id ensures path uniqueness:
      -- If many prompts have the same title, they'll have the same `slug`, but different `short_id`.
      NEW.slug := base_slug;
    END IF;
  END IF;

  -- If TG_OP = 'UPDATE', we are intentionally NOT changing NEW.slug to maintain URL stability,
  -- unless specific logic for "significant title change + intent to update slug" is added.
  -- For example, the application could explicitly set NEW.slug during an update if a change is desired.
  -- IF (TG_OP = 'UPDATE' AND NEW.title IS DISTINCT FROM OLD.title AND NEW.slug = OLD.slug) THEN
      -- Potentially re-slugify if title changed AND slug wasn't manually set in the UPDATE statement.
      -- This is where you'd implement Option B if chosen.
      -- NEW.slug := public.slugify(NEW.title); -- This would change slug on every title edit.
  -- END IF;

  RETURN NEW;
END;
$$;


--
-- Name: slugify(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.slugify(input_text text) RETURNS text
    LANGUAGE plpgsql IMMUTABLE STRICT
    AS $_$
DECLARE
  slug TEXT;
  -- Add more filler words as needed, or consider a separate table for them
  filler_words TEXT[] := ARRAY[
    'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'of', 'by', 'as',
    'is', 'am', 'are', 'was', 'were', 'be', 'being', 'been', 'it', 'its', 'i', 'you', 'he', 'she',
    'they', 'we', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'our', 'their',
    'this', 'that', 'these', 'those', 'how', 'what', 'when', 'where', 'why',
    'do', 'does', 'did', 'will', 'shall', 'should', 'can', 'could', 'may', 'might', 'must',
    'about', 'above', 'across', 'after', 'against', 'along', 'among', 'around', 'before',
    'behind', 'below', 'beneath', 'beside', 'between', 'beyond', 'down', 'during',
    'except', 'inside', 'into', 'like', 'near', 'onto', 'out', 'outside', 'over', 'past',
    'since', 'through', 'throughout', 'toward', 'under', 'underneath', 'until', 'unto',
    'up', 'upon', 'without', 'from', 'for', 'how', 'get', 'create', 'make', 'using', 'about',
    'with', 'your', 'that', 'new'
  ];
  word TEXT;
  result_words TEXT[] := '{}';
BEGIN
  IF input_text IS NULL OR input_text = '' THEN
    RETURN 'untitled'; -- Or an empty string if preferred, but 'untitled' is safer for URLs
  END IF;

  -- 1. Convert to lowercase and replace common separators with space
  slug := lower(input_text);
  slug := regexp_replace(slug, '[_.:]+', ' ', 'g');

  -- 2. Remove apostrophes and other non-alphanumeric characters (except hyphens and spaces)
  slug := regexp_replace(slug, E'[^a-z0-9\\s-]', '', 'g');

  -- 3. Split into words and filter out filler words
  FOREACH word IN ARRAY string_to_array(trim(slug), ' ')
  LOOP
    IF word <> '' AND NOT (word = ANY(filler_words)) THEN
      result_words := array_append(result_words, word);
    END IF;
  END LOOP;

  IF array_length(result_words, 1) IS NULL OR array_length(result_words, 1) = 0 THEN
    -- If all words were fillers, try to use a few words from the original slug before filtering
    slug := lower(input_text);
    slug := regexp_replace(slug, E'[^a-z0-9\\s-]', '', 'g');
    result_words := (string_to_array(trim(slug), ' '))[1:3]; -- Take first 3 words as fallback
    IF array_length(result_words, 1) IS NULL OR array_length(result_words, 1) = 0 THEN
        RETURN 'untitled-' || substr(md5(random()::text), 1, 4); -- Ultimate fallback
    END IF;
  END IF;

  -- 4. Join significant words with hyphens
  slug := array_to_string(result_words, '-');

  -- 5. Replace multiple hyphens with a single hyphen
  slug := regexp_replace(slug, '-{2,}', '-', 'g');

  -- 6. Remove leading/trailing hyphens
  slug := regexp_replace(slug, '^-+|-+$', '', 'g');
  
  -- 7. Truncate to a reasonable length (e.g., 70 chars)
  slug := substr(slug, 1, 70);
  -- Ensure it doesn't end with a hyphen after truncation
  slug := regexp_replace(slug, '-+$', '', 'g');

  IF slug = '' THEN
    RETURN 'untitled-' || substr(md5(random()::text), 1, 4);
  END IF;

  RETURN slug;
END;
$_$;


--
-- Name: sync_prompt_tag_slugs(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.sync_prompt_tag_slugs() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  v_prompt_id UUID;
  v_tag_slugs TEXT[];
BEGIN
  -- Determine the prompt_id affected by the trigger event
  IF (TG_OP = 'DELETE') THEN
    v_prompt_id := OLD.prompt_id;
  ELSE -- INSERT or UPDATE
    v_prompt_id := NEW.prompt_id;
  END IF;

  -- Recalculate the tag slugs array for the affected prompt_id
  SELECT COALESCE(ARRAY_AGG(t.slug ORDER BY t.slug), '{}'::TEXT[])
  INTO v_tag_slugs
  FROM public.prompt_tags pt
  JOIN public.tags t ON pt.tag_id = t.id
  WHERE pt.prompt_id = v_prompt_id;

  -- Update the prompts table
  UPDATE public.prompts
  SET tag_slugs = v_tag_slugs,
      updated_at = NOW() -- Also update the timestamp
  WHERE id = v_prompt_id;

  -- Return the appropriate record for the trigger type
  IF (TG_OP = 'DELETE') THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;

EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error in sync_prompt_tag_slugs for prompt_id %: %', v_prompt_id, SQLERRM;
        RAISE; -- Re-raise the original error
END;
$$;


--
-- Name: universal_search_typeahead(text, uuid, integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.universal_search_typeahead(p_search_query text, p_current_user_id uuid DEFAULT NULL::uuid, p_limit integer DEFAULT 7) RETURNS TABLE(id uuid, title text, item_type text, short_id text, slug text, user_id uuid, username text, avatar_url text, item_icon text, rank real)
    LANGUAGE plpgsql STABLE
    AS $$
DECLARE
  processed_ts_query tsquery;
BEGIN
  -- Use websearch_to_tsquery for more flexible search term parsing
  processed_ts_query := websearch_to_tsquery('english', p_search_query);

  RETURN QUERY
  WITH search_results AS (
    -- Prompts (highest priority for exact/partial title matches)
    SELECT
      p.id,
      p.title,
      'prompt' AS item_type,
      p.short_id,
      p.slug,
      p.user_id,
      prof.username,
      prof.avatar_url,
      t.name AS item_icon, -- Tool name for prompts
      (CASE 
        WHEN p.title ILIKE p_search_query THEN 3.0 -- Exact title match
        WHEN p.title ILIKE p_search_query || '%' THEN 2.8 -- Title starts with query
        WHEN p.title ILIKE '%' || p_search_query || '%' THEN 2.5 -- Title contains query
        ELSE ts_rank_cd(p.search_vector, processed_ts_query) + 2.0 -- FTS match with boost
      END)::REAL AS rank_score
    FROM public.prompts p
    JOIN public.profiles prof ON p.user_id = prof.id
    LEFT JOIN public.tools t ON p.tool_id = t.id
    WHERE p.is_public = TRUE 
      AND (p.search_vector @@ processed_ts_query OR p.title ILIKE '%' || p_search_query || '%')

    UNION ALL

    -- Collections (medium priority for exact/partial name matches)
    SELECT
      c.id,
      c.name AS title,
      'collection' AS item_type,
      NULL AS short_id,
      NULL AS slug, -- Collections currently do not have slugs
      c.user_id,
      prof.username,
      prof.avatar_url,
      c.icon AS item_icon, -- Collection icon
      (CASE 
        WHEN c.name ILIKE p_search_query THEN 2.0 -- Exact name match
        WHEN c.name ILIKE p_search_query || '%' THEN 1.8 -- Name starts with query
        WHEN c.name ILIKE '%' || p_search_query || '%' THEN 1.5 -- Name contains query
        ELSE ts_rank_cd(c.search_vector, processed_ts_query) + 1.0 -- FTS match with boost
      END)::REAL AS rank_score
    FROM public.collections c
    JOIN public.profiles prof ON c.user_id = prof.id
    WHERE c.is_public = TRUE 
      AND c.prompt_count > 0 -- Only include collections with prompts
      AND (c.search_vector @@ processed_ts_query OR c.name ILIKE '%' || p_search_query || '%')

    UNION ALL

    -- Profiles/Users (lower priority for username matches)
    SELECT
      prof.id,
      prof.username AS title,
      'profile' AS item_type,
      NULL AS short_id,
      prof.username AS slug, -- Use username as slug for URL construction
      prof.id AS user_id, -- Profile's own ID
      prof.username,
      prof.avatar_url,
      NULL AS item_icon, -- No specific icon for profiles
      (CASE 
        WHEN prof.username ILIKE p_search_query THEN 1.0 -- Exact username match
        WHEN prof.username ILIKE p_search_query || '%' THEN 0.8 -- Username starts with query
        WHEN prof.username ILIKE '%' || p_search_query || '%' THEN 0.5 -- Username contains query
        ELSE 0.1 -- Fallback minimal score
      END)::REAL AS rank_score
    FROM public.profiles prof
    WHERE prof.username ILIKE '%' || p_search_query || '%'
      AND prof.username IS NOT NULL
      AND prof.username != ''
  )
  SELECT sr.*
  FROM search_results sr
  WHERE sr.rank_score > 0
  ORDER BY sr.rank_score DESC, sr.title ASC
  LIMIT p_limit;

END;
$$;


--
-- Name: FUNCTION universal_search_typeahead(p_search_query text, p_current_user_id uuid, p_limit integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.universal_search_typeahead(p_search_query text, p_current_user_id uuid, p_limit integer) IS 'Enhanced universal search with sophisticated ranking for prompts, collections, and user profiles';


--
-- Name: update_collection(uuid, uuid, text, text, text, text, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_collection(p_user_id uuid, p_collection_id uuid, p_name text, p_description text DEFAULT NULL::text, p_icon text DEFAULT NULL::text, p_color text DEFAULT NULL::text, p_is_public boolean DEFAULT NULL::boolean) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
  -- Check if the user is the owner of the collection
  IF NOT EXISTS (SELECT 1 FROM collections WHERE id = p_collection_id AND user_id = p_user_id) THEN
    RETURN FALSE;
  END IF;
  
  -- Update the collection
  UPDATE collections
  SET
    name = COALESCE(p_name, name),
    description = COALESCE(p_description, description),
    icon = COALESCE(p_icon, icon),
    color = COALESCE(p_color, color),
    is_public = COALESCE(p_is_public, is_public),
    updated_at = NOW()
  WHERE id = p_collection_id;
  
  RETURN TRUE;
END;
$$;


--
-- Name: update_collection_prompt_count(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_collection_prompt_count() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    UPDATE public.collections SET prompt_count = prompt_count + 1 WHERE id = NEW.collection_id;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.collections SET prompt_count = prompt_count - 1 WHERE id = OLD.collection_id;
  END IF;
  RETURN NULL;
END;
$$;


--
-- Name: update_collection_search_vector(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_collection_search_vector() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    BEGIN
      IF (TG_OP = 'INSERT') THEN
        NEW.search_vector := public.generate_collection_search_vector(NEW.name, NEW.description);
      ELSIF (TG_OP = 'UPDATE') THEN
        IF (NEW.name IS DISTINCT FROM OLD.name) OR (NEW.description IS DISTINCT FROM OLD.description) THEN
          NEW.search_vector := public.generate_collection_search_vector(NEW.name, NEW.description);
        END IF;
      END IF;
      RETURN NEW;
    END;
    $$;


--
-- Name: update_comment(uuid, uuid, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_comment(p_user_id uuid, p_comment_id uuid, p_text text) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
  -- Check if the user is the owner of the comment
  IF NOT EXISTS (SELECT 1 FROM comments WHERE id = p_comment_id AND user_id = p_user_id) THEN
    RETURN FALSE;
  END IF;
  
  -- Update the comment
  UPDATE comments
  SET
    text = p_text,
    updated_at = NOW()
  WHERE id = p_comment_id;
  
  RETURN TRUE;
END;
$$;


--
-- Name: update_profile_likes_count(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_profile_likes_count() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
  prompt_author_id UUID;
BEGIN
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    SELECT user_id INTO prompt_author_id FROM public.prompts WHERE id = NEW.prompt_id;
    IF NEW.vote_type = 1 THEN -- Upvote
      UPDATE public.profiles SET total_likes_received = total_likes_received + 1 WHERE id = prompt_author_id;
    ELSIF NEW.vote_type = -1 AND TG_OP = 'INSERT' THEN -- Downvote (on initial insert)
      UPDATE public.profiles SET total_likes_received = total_likes_received - 1 WHERE id = prompt_author_id;
    ELSIF TG_OP = 'UPDATE' AND OLD.vote_type = 1 AND NEW.vote_type = -1 THEN -- Changed from upvote to downvote
       UPDATE public.profiles SET total_likes_received = total_likes_received - 2 WHERE id = prompt_author_id;
    ELSIF TG_OP = 'UPDATE' AND OLD.vote_type = -1 AND NEW.vote_type = 1 THEN -- Changed from downvote to upvote
       UPDATE public.profiles SET total_likes_received = total_likes_received + 2 WHERE id = prompt_author_id;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    SELECT user_id INTO prompt_author_id FROM public.prompts WHERE id = OLD.prompt_id;
    IF OLD.vote_type = 1 THEN
      UPDATE public.profiles SET total_likes_received = total_likes_received - 1 WHERE id = prompt_author_id;
    ELSIF OLD.vote_type = -1 THEN
      UPDATE public.profiles SET total_likes_received = total_likes_received + 1 WHERE id = prompt_author_id;
    END IF;
  END IF;
  RETURN NULL; -- Result is ignored since this is an AFTER trigger
END;
$$;


--
-- Name: update_profile_public_collections_count(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_profile_public_collections_count() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    IF NEW.is_public THEN
      UPDATE public.profiles SET public_collections_count = public_collections_count + 1 WHERE id = NEW.user_id;
    END IF;
  ELSIF TG_OP = 'UPDATE' THEN
    IF OLD.is_public <> NEW.is_public THEN
      IF NEW.is_public THEN
        UPDATE public.profiles SET public_collections_count = public_collections_count + 1 WHERE id = NEW.user_id;
      ELSE
        UPDATE public.profiles SET public_collections_count = public_collections_count - 1 WHERE id = NEW.user_id;
      END IF;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    IF OLD.is_public THEN
      UPDATE public.profiles SET public_collections_count = public_collections_count - 1 WHERE id = OLD.user_id;
    END IF;
  END IF;
  RETURN NULL;
END;
$$;


--
-- Name: update_profile_public_prompts_count(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_profile_public_prompts_count() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    IF NEW.is_public THEN
      UPDATE public.profiles SET public_prompts_count = public_prompts_count + 1 WHERE id = NEW.user_id;
    END IF;
  ELSIF TG_OP = 'UPDATE' THEN
    IF OLD.is_public <> NEW.is_public THEN
      IF NEW.is_public THEN
        UPDATE public.profiles SET public_prompts_count = public_prompts_count + 1 WHERE id = NEW.user_id;
      ELSE
        UPDATE public.profiles SET public_prompts_count = public_prompts_count - 1 WHERE id = NEW.user_id;
      END IF;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    IF OLD.is_public THEN
      UPDATE public.profiles SET public_prompts_count = public_prompts_count - 1 WHERE id = OLD.user_id;
    END IF;
  END IF;
  RETURN NULL;
END;
$$;


--
-- Name: update_prompt(uuid, uuid, integer, integer, text, text, text, text, text, text, text, text, boolean, integer[]); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_prompt(p_prompt_id uuid, p_user_id uuid, p_category_id integer, p_tool_id integer, p_title text, p_description text, p_prompt_text text, p_instructions text DEFAULT NULL::text, p_example_input text DEFAULT NULL::text, p_example_output_text text DEFAULT NULL::text, p_example_output_image_url text DEFAULT NULL::text, p_image_url text DEFAULT NULL::text, p_is_public boolean DEFAULT true, p_tag_ids integer[] DEFAULT NULL::integer[]) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
  new_slug TEXT;
  category_slug TEXT;
  tool_slug TEXT;
  first_tag_slug TEXT := 'untagged';
  short_id TEXT;
  i INTEGER;
BEGIN
  -- Check if the user is the owner of the prompt
  IF NOT EXISTS (SELECT 1 FROM prompts WHERE id = p_prompt_id AND user_id = p_user_id) THEN
    RETURN FALSE;
  END IF;
  
  -- Get the short_id
  SELECT short_id INTO short_id FROM prompts WHERE id = p_prompt_id;
  
  -- Get the category slug
  SELECT slug INTO category_slug FROM categories WHERE id = p_category_id;
  
  -- Get the tool slug
  SELECT slug INTO tool_slug FROM tools WHERE id = p_tool_id;
  
  -- Get the first tag slug if tag_ids is provided
  IF p_tag_ids IS NOT NULL AND array_length(p_tag_ids, 1) > 0 THEN
    SELECT slug INTO first_tag_slug FROM tags WHERE id = p_tag_ids[1];
  END IF;
  
  -- Generate the new slug
  new_slug := generate_prompt_slug(
    p_title,
    category_slug,
    tool_slug,
    first_tag_slug,
    short_id
  );
  
  -- Update the prompt
  UPDATE prompts
  SET
    category_id = p_category_id,
    tool_id = p_tool_id,
    title = p_title,
    slug = new_slug,
    description = p_description,
    prompt_text = p_prompt_text,
    instructions = p_instructions,
    example_input = p_example_input,
    example_output_text = p_example_output_text,
    example_output_image_url = p_example_output_image_url,
    image_url = p_image_url,
    is_public = p_is_public,
    updated_at = NOW(),
    updated_by_user_id = p_user_id
  WHERE id = p_prompt_id;
  
  -- Delete existing tags
  DELETE FROM prompt_tags WHERE prompt_id = p_prompt_id;
  
  -- Add new tags if provided
  IF p_tag_ids IS NOT NULL AND array_length(p_tag_ids, 1) > 0 THEN
    FOR i IN 1..array_length(p_tag_ids, 1) LOOP
      INSERT INTO prompt_tags (prompt_id, tag_id)
      VALUES (p_prompt_id, p_tag_ids[i]);
    END LOOP;
  END IF;
  
  RETURN TRUE;
END;
$$;


--
-- Name: update_prompt_collections(uuid, uuid, uuid[], uuid[]); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_prompt_collections(p_user_id uuid, p_prompt_id uuid, p_add_collection_ids uuid[], p_remove_collection_ids uuid[]) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    collection_id_to_add UUID;
    collection_id_to_remove UUID;
    current_collection RECORD;
BEGIN
    -- First handle additions
    IF p_add_collection_ids IS NOT NULL AND array_length(p_add_collection_ids, 1) > 0 THEN
        FOREACH collection_id_to_add IN ARRAY p_add_collection_ids
        LOOP
            -- Check if the collection exists and belongs to the user
            SELECT * INTO current_collection FROM collections 
            WHERE id = collection_id_to_add AND user_id = p_user_id;

            IF NOT FOUND THEN
                RAISE WARNING 'Collection with ID % not found or does not belong to user %.', collection_id_to_add, p_user_id;
                CONTINUE; -- Skip this collection and try the next one
            END IF;
            
            -- Add the prompt to this collection, do nothing if already exists
            INSERT INTO collection_prompts (collection_id, prompt_id)
            VALUES (collection_id_to_add, p_prompt_id)
            ON CONFLICT (collection_id, prompt_id) DO NOTHING;
        END LOOP;
    END IF;

    -- Then handle removals
    IF p_remove_collection_ids IS NOT NULL AND array_length(p_remove_collection_ids, 1) > 0 THEN
        FOREACH collection_id_to_remove IN ARRAY p_remove_collection_ids
        LOOP
            -- Check if the collection exists and belongs to the user
            SELECT * INTO current_collection FROM collections 
            WHERE id = collection_id_to_remove AND user_id = p_user_id;

            IF NOT FOUND THEN
                RAISE WARNING 'Collection with ID % not found or does not belong to user %.', collection_id_to_remove, p_user_id;
                CONTINUE; -- Skip this collection and try the next one
            END IF;
            
            -- Remove the prompt from this collection
            DELETE FROM collection_prompts 
            WHERE collection_id = collection_id_to_remove 
            AND prompt_id = p_prompt_id;
        END LOOP;
    END IF;

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error in update_prompt_collections: %', SQLERRM;
        RETURN FALSE;
END;
$$;


--
-- Name: update_prompt_search_vector(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_prompt_search_vector() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  NEW.search_vector := generate_prompt_search_vector(
    NEW.title,
    NEW.description,
    NEW.prompt_text,
    NEW.instructions,
    NEW.example_input,
    NEW.example_output_text
  );
  RETURN NEW;
END;
$$;


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;


--
-- Name: update_user_collection(uuid, uuid, text, text, text, boolean); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_user_collection(p_user_id uuid, p_collection_id uuid, p_name text DEFAULT NULL::text, p_description text DEFAULT NULL::text, p_icon_url text DEFAULT NULL::text, p_is_public boolean DEFAULT NULL::boolean) RETURNS TABLE(id uuid, user_id uuid, name text, description text, icon text, is_public boolean, is_default boolean, default_type text, created_at timestamp with time zone, updated_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_is_default boolean;
    v_default_type text;
    v_owner_id uuid;
    v_debug_info text;
BEGIN
    -- Debug info
    v_debug_info := 'Parameters: p_user_id=' || p_user_id || ', p_collection_id=' || p_collection_id;
    IF p_icon_url IS NOT NULL THEN
        v_debug_info := v_debug_info || ', p_icon_url=' || p_icon_url;
    END IF;
    RAISE NOTICE '%', v_debug_info;
    
    -- Check if collection exists and get owner_id and default status
    SELECT c.is_default, c.default_type, c.user_id INTO v_is_default, v_default_type, v_owner_id 
    FROM collections c
    WHERE c.id = p_collection_id;
    
    RAISE NOTICE 'Collection found: is_default=%, default_type=%, owner_id=%', 
        v_is_default, v_default_type, v_owner_id;
    
    -- Verify ownership
    IF v_owner_id IS NULL THEN
        RAISE EXCEPTION 'Collection not found';
    END IF;
    
    IF v_owner_id != p_user_id THEN
        RAISE EXCEPTION 'You do not have permission to update this collection';
    END IF;
    
    -- Check if it's a default collection
    IF v_is_default = true THEN
        RAISE NOTICE 'Updating default collection (type: %)', v_default_type;
        
        -- For default collections, only allow updating the icon
        IF p_icon_url IS NOT NULL THEN
            RAISE NOTICE 'Updating icon for default collection to: %', p_icon_url;
            
            UPDATE collections c
            SET 
                icon = p_icon_url,
                updated_at = NOW()
            WHERE c.id = p_collection_id AND c.user_id = p_user_id;
            
            RETURN QUERY SELECT
                c.id, c.user_id, c.name, c.description, c.icon, c.is_public, c.is_default, c.default_type, c.created_at, c.updated_at
            FROM collections c
            WHERE c.id = p_collection_id;
            
            RAISE NOTICE 'Default collection icon updated successfully';
        ELSE
            -- If no icon update is requested, check if other fields are being updated
            IF p_name IS NOT NULL OR p_description IS NOT NULL OR p_is_public IS NOT NULL THEN
                RAISE EXCEPTION 'Default collections cannot be edited except for their icon';
            ELSE
                -- No changes requested, just return the current collection
                RETURN QUERY SELECT 
                    c.id, c.user_id, c.name, c.description, c.icon, c.is_public, c.is_default, c.default_type, c.created_at, c.updated_at 
                FROM collections c 
                WHERE c.id = p_collection_id;
            END IF;
        END IF;
    ELSE
        -- For custom collections, allow updating all fields
        RAISE NOTICE 'Updating custom collection with fields: name=%, description=%, icon=%, is_public=%', 
            p_name, p_description, p_icon_url, p_is_public;
        
        UPDATE collections c
        SET 
            name = COALESCE(p_name, c.name),
            description = COALESCE(p_description, c.description),
            icon = CASE WHEN p_icon_url IS NOT NULL THEN p_icon_url ELSE c.icon END,
            is_public = COALESCE(p_is_public, c.is_public),
            updated_at = NOW()
        WHERE c.id = p_collection_id AND c.user_id = p_user_id;
        
        RETURN QUERY SELECT
            c.id, c.user_id, c.name, c.description, c.icon, c.is_public, c.is_default, c.default_type, c.created_at, c.updated_at
        FROM collections c
        WHERE c.id = p_collection_id;
        
        RAISE NOTICE 'Custom collection updated successfully';
    END IF;
    
    -- Since we've moved the RETURN QUERY statements after each UPDATE,
    -- we don't need this check anymore. The function will always return
    -- the updated collection data or throw an exception if something went wrong.
END;
$$;


--
-- Name: FUNCTION update_user_collection(p_user_id uuid, p_collection_id uuid, p_name text, p_description text, p_icon_url text, p_is_public boolean); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.update_user_collection(p_user_id uuid, p_collection_id uuid, p_name text, p_description text, p_icon_url text, p_is_public boolean) IS 'Updates a user collection with proper validation and image handling';


--
-- Name: vote_on_prompt(uuid, uuid, smallint); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.vote_on_prompt(p_user_id uuid, p_prompt_id uuid, p_vote_type smallint) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
  prompt_is_public boolean; -- To store the is_public status
  prompt_exists boolean;    -- To store if the prompt exists
BEGIN
  -- RAISE WARNING '[vote_on_prompt_v7.1] Called with UserID: %, PromptID: %, VoteType: %', p_user_id, p_prompt_id, p_vote_type;

  -- Check 1: Vote type validity
  IF p_vote_type NOT IN (-1, 0, 1) THEN
    -- RAISE WARNING '[vote_on_prompt_v7.1] Invalid vote_type: %', p_vote_type;
    RETURN FALSE;
  END IF;

  -- Check 2: Prompt existence
  SELECT EXISTS (SELECT 1 FROM public.prompts p WHERE p.id = p_prompt_id)
  INTO prompt_exists;

  IF NOT prompt_exists THEN
    -- RAISE WARNING '[vote_on_prompt_v7.1] Prompt ID % not found.', p_prompt_id;
    RETURN FALSE;
  END IF;

  -- Check 3: Prompt public status (only if it exists)
  SELECT p.is_public INTO prompt_is_public FROM public.prompts p WHERE p.id = p_prompt_id;
  
  IF NOT prompt_is_public THEN -- This handles FALSE and NULL correctly (NULL IS NOT TRUE)
    -- RAISE WARNING '[vote_on_prompt_v7.1] Prompt ID % is not public. is_public: %', p_prompt_id, prompt_is_public;
    RETURN FALSE;
  END IF;
  
  -- Perform DML
  BEGIN
    IF p_vote_type = 0 THEN
      -- RAISE WARNING '[vote_on_prompt_v7.1] Attempting DELETE for UserID: %, PromptID: %', p_user_id, p_prompt_id;
      DELETE FROM public.prompt_votes pv
      WHERE pv.user_id = p_user_id AND pv.prompt_id = p_prompt_id;
    ELSE
      -- RAISE WARNING '[vote_on_prompt_v7.1] Attempting UPSERT for UserID: %, PromptID: %, VoteType: %', p_user_id, p_prompt_id, p_vote_type;
      INSERT INTO public.prompt_votes (user_id, prompt_id, vote_type, created_at)
      VALUES (p_user_id, p_prompt_id, p_vote_type, NOW())
      ON CONFLICT (user_id, prompt_id) 
      DO UPDATE SET 
        vote_type = EXCLUDED.vote_type;
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      RAISE WARNING '[vote_on_prompt_v7.1] DML EXCEPTION: SQLSTATE: %, SQLERRM: %', SQLSTATE, SQLERRM;
      RETURN FALSE; -- DML failed
  END;
  
  -- RAISE WARNING '[vote_on_prompt_v7.1] DML Succeeded.';
  RETURN TRUE; -- If DML was successful

EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING '[vote_on_prompt_v7.1] OUTER EXCEPTION: SQLSTATE: %, SQLERRM: %', SQLSTATE, SQLERRM;
    RETURN FALSE;
END;
$$;


--
-- Name: vote_on_prompt_smallint(uuid, uuid, smallint); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.vote_on_prompt_smallint(p_user_id uuid, p_prompt_id uuid, p_vote_type smallint) RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
  v_old_vote SMALLINT;
BEGIN
  -- Check if user has already voted on this prompt
  SELECT vote_type INTO v_old_vote
  FROM prompt_votes
  WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
  
  IF v_old_vote IS NULL THEN
    -- No previous vote, insert new vote if not removing (p_vote_type != 0)
    IF p_vote_type != 0 THEN
      INSERT INTO prompt_votes (user_id, prompt_id, vote_type)
      VALUES (p_user_id, p_prompt_id, p_vote_type);
      
      -- Update prompt statistics
      UPDATE prompt_statistics
      SET 
        rating = rating + p_vote_type,
        upvotes = CASE WHEN p_vote_type = 1 THEN upvotes + 1 ELSE upvotes END,
        downvotes = CASE WHEN p_vote_type = -1 THEN downvotes + 1 ELSE downvotes END
      WHERE id = p_prompt_id;
    END IF;
  ELSE
    -- Previous vote exists
    IF p_vote_type = 0 THEN
      -- Remove vote
      DELETE FROM prompt_votes
      WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
      
      -- Update prompt statistics
      UPDATE prompt_statistics
      SET 
        rating = rating - v_old_vote,
        upvotes = CASE WHEN v_old_vote = 1 THEN upvotes - 1 ELSE upvotes END,
        downvotes = CASE WHEN v_old_vote = -1 THEN downvotes - 1 ELSE downvotes END
      WHERE id = p_prompt_id;
    ELSE
      -- Change vote
      UPDATE prompt_votes
      SET vote_type = p_vote_type
      WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
      
      -- Update prompt statistics
      UPDATE prompt_statistics
      SET 
        rating = rating - v_old_vote + p_vote_type,
        upvotes = 
          CASE 
            WHEN v_old_vote = 1 AND p_vote_type = -1 THEN upvotes - 1
            WHEN v_old_vote = -1 AND p_vote_type = 1 THEN upvotes + 1
            WHEN v_old_vote != 1 AND p_vote_type = 1 THEN upvotes + 1
            ELSE upvotes
          END,
        downvotes = 
          CASE 
            WHEN v_old_vote = -1 AND p_vote_type = 1 THEN downvotes - 1
            WHEN v_old_vote = 1 AND p_vote_type = -1 THEN downvotes + 1
            WHEN v_old_vote != -1 AND p_vote_type = -1 THEN downvotes + 1
            ELSE downvotes
          END
      WHERE id = p_prompt_id;
    END IF;
  END IF;
END;
$$;


--
-- Name: apply_rls(jsonb, integer); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer DEFAULT (1024 * 1024)) RETURNS SETOF realtime.wal_rls
    LANGUAGE plpgsql
    AS $$
declare
-- Regclass of the table e.g. public.notes
entity_ regclass = (quote_ident(wal ->> 'schema') || '.' || quote_ident(wal ->> 'table'))::regclass;

-- I, U, D, T: insert, update ...
action realtime.action = (
    case wal ->> 'action'
        when 'I' then 'INSERT'
        when 'U' then 'UPDATE'
        when 'D' then 'DELETE'
        else 'ERROR'
    end
);

-- Is row level security enabled for the table
is_rls_enabled bool = relrowsecurity from pg_class where oid = entity_;

subscriptions realtime.subscription[] = array_agg(subs)
    from
        realtime.subscription subs
    where
        subs.entity = entity_;

-- Subscription vars
roles regrole[] = array_agg(distinct us.claims_role::text)
    from
        unnest(subscriptions) us;

working_role regrole;
claimed_role regrole;
claims jsonb;

subscription_id uuid;
subscription_has_access bool;
visible_to_subscription_ids uuid[] = '{}';

-- structured info for wal's columns
columns realtime.wal_column[];
-- previous identity values for update/delete
old_columns realtime.wal_column[];

error_record_exceeds_max_size boolean = octet_length(wal::text) > max_record_bytes;

-- Primary jsonb output for record
output jsonb;

begin
perform set_config('role', null, true);

columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'columns') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

old_columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'identity') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

for working_role in select * from unnest(roles) loop

    -- Update `is_selectable` for columns and old_columns
    columns =
        array_agg(
            (
                c.name,
                c.type_name,
                c.type_oid,
                c.value,
                c.is_pkey,
                pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
            )::realtime.wal_column
        )
        from
            unnest(columns) c;

    old_columns =
            array_agg(
                (
                    c.name,
                    c.type_name,
                    c.type_oid,
                    c.value,
                    c.is_pkey,
                    pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
                )::realtime.wal_column
            )
            from
                unnest(old_columns) c;

    if action <> 'DELETE' and count(1) = 0 from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            -- subscriptions is already filtered by entity
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 400: Bad Request, no primary key']
        )::realtime.wal_rls;

    -- The claims role does not have SELECT permission to the primary key of entity
    elsif action <> 'DELETE' and sum(c.is_selectable::int) <> count(1) from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 401: Unauthorized']
        )::realtime.wal_rls;

    else
        output = jsonb_build_object(
            'schema', wal ->> 'schema',
            'table', wal ->> 'table',
            'type', action,
            'commit_timestamp', to_char(
                ((wal ->> 'timestamp')::timestamptz at time zone 'utc'),
                'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'
            ),
            'columns', (
                select
                    jsonb_agg(
                        jsonb_build_object(
                            'name', pa.attname,
                            'type', pt.typname
                        )
                        order by pa.attnum asc
                    )
                from
                    pg_attribute pa
                    join pg_type pt
                        on pa.atttypid = pt.oid
                where
                    attrelid = entity_
                    and attnum > 0
                    and pg_catalog.has_column_privilege(working_role, entity_, pa.attname, 'SELECT')
            )
        )
        -- Add "record" key for insert and update
        || case
            when action in ('INSERT', 'UPDATE') then
                jsonb_build_object(
                    'record',
                    (
                        select
                            jsonb_object_agg(
                                -- if unchanged toast, get column name and value from old record
                                coalesce((c).name, (oc).name),
                                case
                                    when (c).name is null then (oc).value
                                    else (c).value
                                end
                            )
                        from
                            unnest(columns) c
                            full outer join unnest(old_columns) oc
                                on (c).name = (oc).name
                        where
                            coalesce((c).is_selectable, (oc).is_selectable)
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                    )
                )
            else '{}'::jsonb
        end
        -- Add "old_record" key for update and delete
        || case
            when action = 'UPDATE' then
                jsonb_build_object(
                        'old_record',
                        (
                            select jsonb_object_agg((c).name, (c).value)
                            from unnest(old_columns) c
                            where
                                (c).is_selectable
                                and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                        )
                    )
            when action = 'DELETE' then
                jsonb_build_object(
                    'old_record',
                    (
                        select jsonb_object_agg((c).name, (c).value)
                        from unnest(old_columns) c
                        where
                            (c).is_selectable
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                            and ( not is_rls_enabled or (c).is_pkey ) -- if RLS enabled, we can't secure deletes so filter to pkey
                    )
                )
            else '{}'::jsonb
        end;

        -- Create the prepared statement
        if is_rls_enabled and action <> 'DELETE' then
            if (select 1 from pg_prepared_statements where name = 'walrus_rls_stmt' limit 1) > 0 then
                deallocate walrus_rls_stmt;
            end if;
            execute realtime.build_prepared_statement_sql('walrus_rls_stmt', entity_, columns);
        end if;

        visible_to_subscription_ids = '{}';

        for subscription_id, claims in (
                select
                    subs.subscription_id,
                    subs.claims
                from
                    unnest(subscriptions) subs
                where
                    subs.entity = entity_
                    and subs.claims_role = working_role
                    and (
                        realtime.is_visible_through_filters(columns, subs.filters)
                        or (
                          action = 'DELETE'
                          and realtime.is_visible_through_filters(old_columns, subs.filters)
                        )
                    )
        ) loop

            if not is_rls_enabled or action = 'DELETE' then
                visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
            else
                -- Check if RLS allows the role to see the record
                perform
                    -- Trim leading and trailing quotes from working_role because set_config
                    -- doesn't recognize the role as valid if they are included
                    set_config('role', trim(both '"' from working_role::text), true),
                    set_config('request.jwt.claims', claims::text, true);

                execute 'execute walrus_rls_stmt' into subscription_has_access;

                if subscription_has_access then
                    visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
                end if;
            end if;
        end loop;

        perform set_config('role', null, true);

        return next (
            output,
            is_rls_enabled,
            visible_to_subscription_ids,
            case
                when error_record_exceeds_max_size then array['Error 413: Payload Too Large']
                else '{}'
            end
        )::realtime.wal_rls;

    end if;
end loop;

perform set_config('role', null, true);
end;
$$;


--
-- Name: broadcast_changes(text, text, text, text, text, record, record, text); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text DEFAULT 'ROW'::text) RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    -- Declare a variable to hold the JSONB representation of the row
    row_data jsonb := '{}'::jsonb;
BEGIN
    IF level = 'STATEMENT' THEN
        RAISE EXCEPTION 'function can only be triggered for each row, not for each statement';
    END IF;
    -- Check the operation type and handle accordingly
    IF operation = 'INSERT' OR operation = 'UPDATE' OR operation = 'DELETE' THEN
        row_data := jsonb_build_object('old_record', OLD, 'record', NEW, 'operation', operation, 'table', table_name, 'schema', table_schema);
        PERFORM realtime.send (row_data, event_name, topic_name);
    ELSE
        RAISE EXCEPTION 'Unexpected operation type: %', operation;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to process the row: %', SQLERRM;
END;

$$;


--
-- Name: build_prepared_statement_sql(text, regclass, realtime.wal_column[]); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) RETURNS text
    LANGUAGE sql
    AS $$
      /*
      Builds a sql string that, if executed, creates a prepared statement to
      tests retrive a row from *entity* by its primary key columns.
      Example
          select realtime.build_prepared_statement_sql('public.notes', '{"id"}'::text[], '{"bigint"}'::text[])
      */
          select
      'prepare ' || prepared_statement_name || ' as
          select
              exists(
                  select
                      1
                  from
                      ' || entity || '
                  where
                      ' || string_agg(quote_ident(pkc.name) || '=' || quote_nullable(pkc.value #>> '{}') , ' and ') || '
              )'
          from
              unnest(columns) pkc
          where
              pkc.is_pkey
          group by
              entity
      $$;


--
-- Name: cast(text, regtype); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime."cast"(val text, type_ regtype) RETURNS jsonb
    LANGUAGE plpgsql IMMUTABLE
    AS $$
    declare
      res jsonb;
    begin
      execute format('select to_jsonb(%L::'|| type_::text || ')', val)  into res;
      return res;
    end
    $$;


--
-- Name: check_equality_op(realtime.equality_op, regtype, text, text); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
      /*
      Casts *val_1* and *val_2* as type *type_* and check the *op* condition for truthiness
      */
      declare
          op_symbol text = (
              case
                  when op = 'eq' then '='
                  when op = 'neq' then '!='
                  when op = 'lt' then '<'
                  when op = 'lte' then '<='
                  when op = 'gt' then '>'
                  when op = 'gte' then '>='
                  when op = 'in' then '= any'
                  else 'UNKNOWN OP'
              end
          );
          res boolean;
      begin
          execute format(
              'select %L::'|| type_::text || ' ' || op_symbol
              || ' ( %L::'
              || (
                  case
                      when op = 'in' then type_::text || '[]'
                      else type_::text end
              )
              || ')', val_1, val_2) into res;
          return res;
      end;
      $$;


--
-- Name: is_visible_through_filters(realtime.wal_column[], realtime.user_defined_filter[]); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) RETURNS boolean
    LANGUAGE sql IMMUTABLE
    AS $_$
    /*
    Should the record be visible (true) or filtered out (false) after *filters* are applied
    */
        select
            -- Default to allowed when no filters present
            $2 is null -- no filters. this should not happen because subscriptions has a default
            or array_length($2, 1) is null -- array length of an empty array is null
            or bool_and(
                coalesce(
                    realtime.check_equality_op(
                        op:=f.op,
                        type_:=coalesce(
                            col.type_oid::regtype, -- null when wal2json version <= 2.4
                            col.type_name::regtype
                        ),
                        -- cast jsonb to text
                        val_1:=col.value #>> '{}',
                        val_2:=f.value
                    ),
                    false -- if null, filter does not match
                )
            )
        from
            unnest(filters) f
            join unnest(columns) col
                on f.column_name = col.name;
    $_$;


--
-- Name: list_changes(name, name, integer, integer); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) RETURNS SETOF realtime.wal_rls
    LANGUAGE sql
    SET log_min_messages TO 'fatal'
    AS $$
      with pub as (
        select
          concat_ws(
            ',',
            case when bool_or(pubinsert) then 'insert' else null end,
            case when bool_or(pubupdate) then 'update' else null end,
            case when bool_or(pubdelete) then 'delete' else null end
          ) as w2j_actions,
          coalesce(
            string_agg(
              realtime.quote_wal2json(format('%I.%I', schemaname, tablename)::regclass),
              ','
            ) filter (where ppt.tablename is not null and ppt.tablename not like '% %'),
            ''
          ) w2j_add_tables
        from
          pg_publication pp
          left join pg_publication_tables ppt
            on pp.pubname = ppt.pubname
        where
          pp.pubname = publication
        group by
          pp.pubname
        limit 1
      ),
      w2j as (
        select
          x.*, pub.w2j_add_tables
        from
          pub,
          pg_logical_slot_get_changes(
            slot_name, null, max_changes,
            'include-pk', 'true',
            'include-transaction', 'false',
            'include-timestamp', 'true',
            'include-type-oids', 'true',
            'format-version', '2',
            'actions', pub.w2j_actions,
            'add-tables', pub.w2j_add_tables
          ) x
      )
      select
        xyz.wal,
        xyz.is_rls_enabled,
        xyz.subscription_ids,
        xyz.errors
      from
        w2j,
        realtime.apply_rls(
          wal := w2j.data::jsonb,
          max_record_bytes := max_record_bytes
        ) xyz(wal, is_rls_enabled, subscription_ids, errors)
      where
        w2j.w2j_add_tables <> ''
        and xyz.subscription_ids[1] is not null
    $$;


--
-- Name: quote_wal2json(regclass); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.quote_wal2json(entity regclass) RETURNS text
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
      select
        (
          select string_agg('' || ch,'')
          from unnest(string_to_array(nsp.nspname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
        )
        || '.'
        || (
          select string_agg('' || ch,'')
          from unnest(string_to_array(pc.relname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
          )
      from
        pg_class pc
        join pg_namespace nsp
          on pc.relnamespace = nsp.oid
      where
        pc.oid = entity
    $$;


--
-- Name: send(jsonb, text, text, boolean); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean DEFAULT true) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  BEGIN
    -- Set the topic configuration
    EXECUTE format('SET LOCAL realtime.topic TO %L', topic);

    -- Attempt to insert the message
    INSERT INTO realtime.messages (payload, event, topic, private, extension)
    VALUES (payload, event, topic, private, 'broadcast');
  EXCEPTION
    WHEN OTHERS THEN
      -- Capture and notify the error
      PERFORM pg_notify(
          'realtime:system',
          jsonb_build_object(
              'error', SQLERRM,
              'function', 'realtime.send',
              'event', event,
              'topic', topic,
              'private', private
          )::text
      );
  END;
END;
$$;


--
-- Name: subscription_check_filters(); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.subscription_check_filters() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    /*
    Validates that the user defined filters for a subscription:
    - refer to valid columns that the claimed role may access
    - values are coercable to the correct column type
    */
    declare
        col_names text[] = coalesce(
                array_agg(c.column_name order by c.ordinal_position),
                '{}'::text[]
            )
            from
                information_schema.columns c
            where
                format('%I.%I', c.table_schema, c.table_name)::regclass = new.entity
                and pg_catalog.has_column_privilege(
                    (new.claims ->> 'role'),
                    format('%I.%I', c.table_schema, c.table_name)::regclass,
                    c.column_name,
                    'SELECT'
                );
        filter realtime.user_defined_filter;
        col_type regtype;

        in_val jsonb;
    begin
        for filter in select * from unnest(new.filters) loop
            -- Filtered column is valid
            if not filter.column_name = any(col_names) then
                raise exception 'invalid column for filter %', filter.column_name;
            end if;

            -- Type is sanitized and safe for string interpolation
            col_type = (
                select atttypid::regtype
                from pg_catalog.pg_attribute
                where attrelid = new.entity
                      and attname = filter.column_name
            );
            if col_type is null then
                raise exception 'failed to lookup type for column %', filter.column_name;
            end if;

            -- Set maximum number of entries for in filter
            if filter.op = 'in'::realtime.equality_op then
                in_val = realtime.cast(filter.value, (col_type::text || '[]')::regtype);
                if coalesce(jsonb_array_length(in_val), 0) > 100 then
                    raise exception 'too many values for `in` filter. Maximum 100';
                end if;
            else
                -- raises an exception if value is not coercable to type
                perform realtime.cast(filter.value, col_type);
            end if;

        end loop;

        -- Apply consistent order to filters so the unique constraint on
        -- (subscription_id, entity, filters) can't be tricked by a different filter order
        new.filters = coalesce(
            array_agg(f order by f.column_name, f.op, f.value),
            '{}'
        ) from unnest(new.filters) f;

        return new;
    end;
    $$;


--
-- Name: to_regrole(text); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.to_regrole(role_name text) RETURNS regrole
    LANGUAGE sql IMMUTABLE
    AS $$ select role_name::regrole $$;


--
-- Name: topic(); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.topic() RETURNS text
    LANGUAGE sql STABLE
    AS $$
select nullif(current_setting('realtime.topic', true), '')::text;
$$;


--
-- Name: can_insert_object(text, text, uuid, jsonb); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  INSERT INTO "storage"."objects" ("bucket_id", "name", "owner", "metadata") VALUES (bucketid, name, owner, metadata);
  -- hack to rollback the successful insert
  RAISE sqlstate 'PT200' using
  message = 'ROLLBACK',
  detail = 'rollback successful insert';
END
$$;


--
-- Name: extension(text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.extension(name text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
_filename text;
BEGIN
	select string_to_array(name, '/') into _parts;
	select _parts[array_length(_parts,1)] into _filename;
	-- @todo return the last part instead of 2
	return reverse(split_part(reverse(_filename), '.', 1));
END
$$;


--
-- Name: filename(text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.filename(name text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[array_length(_parts,1)];
END
$$;


--
-- Name: foldername(text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.foldername(name text) RETURNS text[]
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[1:array_length(_parts,1)-1];
END
$$;


--
-- Name: get_size_by_bucket(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.get_size_by_bucket() RETURNS TABLE(size bigint, bucket_id text)
    LANGUAGE plpgsql
    AS $$
BEGIN
    return query
        select sum((metadata->>'size')::int) as size, obj.bucket_id
        from "storage".objects as obj
        group by obj.bucket_id;
END
$$;


--
-- Name: list_multipart_uploads_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.list_multipart_uploads_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, next_key_token text DEFAULT ''::text, next_upload_token text DEFAULT ''::text) RETURNS TABLE(key text, id text, created_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(key COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                        substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1)))
                    ELSE
                        key
                END AS key, id, created_at
            FROM
                storage.s3_multipart_uploads
            WHERE
                bucket_id = $5 AND
                key ILIKE $1 || ''%'' AND
                CASE
                    WHEN $4 != '''' AND $6 = '''' THEN
                        CASE
                            WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                                substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                key COLLATE "C" > $4
                            END
                    ELSE
                        true
                END AND
                CASE
                    WHEN $6 != '''' THEN
                        id COLLATE "C" > $6
                    ELSE
                        true
                    END
            ORDER BY
                key COLLATE "C" ASC, created_at ASC) as e order by key COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_key_token, bucket_id, next_upload_token;
END;
$_$;


--
-- Name: list_objects_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.list_objects_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, start_after text DEFAULT ''::text, next_token text DEFAULT ''::text) RETURNS TABLE(name text, id uuid, metadata jsonb, updated_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(name COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                        substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1)))
                    ELSE
                        name
                END AS name, id, metadata, updated_at
            FROM
                storage.objects
            WHERE
                bucket_id = $5 AND
                name ILIKE $1 || ''%'' AND
                CASE
                    WHEN $6 != '''' THEN
                    name COLLATE "C" > $6
                ELSE true END
                AND CASE
                    WHEN $4 != '''' THEN
                        CASE
                            WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                                substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                name COLLATE "C" > $4
                            END
                    ELSE
                        true
                END
            ORDER BY
                name COLLATE "C" ASC) as e order by name COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_token, bucket_id, start_after;
END;
$_$;


--
-- Name: operation(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.operation() RETURNS text
    LANGUAGE plpgsql STABLE
    AS $$
BEGIN
    RETURN current_setting('storage.operation', true);
END;
$$;


--
-- Name: search(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.search(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
declare
  v_order_by text;
  v_sort_order text;
begin
  case
    when sortcolumn = 'name' then
      v_order_by = 'name';
    when sortcolumn = 'updated_at' then
      v_order_by = 'updated_at';
    when sortcolumn = 'created_at' then
      v_order_by = 'created_at';
    when sortcolumn = 'last_accessed_at' then
      v_order_by = 'last_accessed_at';
    else
      v_order_by = 'name';
  end case;

  case
    when sortorder = 'asc' then
      v_sort_order = 'asc';
    when sortorder = 'desc' then
      v_sort_order = 'desc';
    else
      v_sort_order = 'asc';
  end case;

  v_order_by = v_order_by || ' ' || v_sort_order;

  return query execute
    'with folders as (
       select path_tokens[$1] as folder
       from storage.objects
         where objects.name ilike $2 || $3 || ''%''
           and bucket_id = $4
           and array_length(objects.path_tokens, 1) <> $1
       group by folder
       order by folder ' || v_sort_order || '
     )
     (select folder as "name",
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[$1] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where objects.name ilike $2 || $3 || ''%''
       and bucket_id = $4
       and array_length(objects.path_tokens, 1) = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$_$;


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW; 
END;
$$;


--
-- Name: audit_log_entries; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.audit_log_entries (
    instance_id uuid,
    id uuid NOT NULL,
    payload json,
    created_at timestamp with time zone,
    ip_address character varying(64) DEFAULT ''::character varying NOT NULL
);


--
-- Name: TABLE audit_log_entries; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.audit_log_entries IS 'Auth: Audit trail for user actions.';


--
-- Name: flow_state; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.flow_state (
    id uuid NOT NULL,
    user_id uuid,
    auth_code text NOT NULL,
    code_challenge_method auth.code_challenge_method NOT NULL,
    code_challenge text NOT NULL,
    provider_type text NOT NULL,
    provider_access_token text,
    provider_refresh_token text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    authentication_method text NOT NULL,
    auth_code_issued_at timestamp with time zone
);


--
-- Name: TABLE flow_state; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.flow_state IS 'stores metadata for pkce logins';


--
-- Name: identities; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.identities (
    provider_id text NOT NULL,
    user_id uuid NOT NULL,
    identity_data jsonb NOT NULL,
    provider text NOT NULL,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    email text GENERATED ALWAYS AS (lower((identity_data ->> 'email'::text))) STORED,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: TABLE identities; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.identities IS 'Auth: Stores identities associated to a user.';


--
-- Name: COLUMN identities.email; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.identities.email IS 'Auth: Email is a generated column that references the optional email property in the identity_data';


--
-- Name: instances; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.instances (
    id uuid NOT NULL,
    uuid uuid,
    raw_base_config text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


--
-- Name: TABLE instances; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.instances IS 'Auth: Manages users across multiple sites.';


--
-- Name: mfa_amr_claims; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.mfa_amr_claims (
    session_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    authentication_method text NOT NULL,
    id uuid NOT NULL
);


--
-- Name: TABLE mfa_amr_claims; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.mfa_amr_claims IS 'auth: stores authenticator method reference claims for multi factor authentication';


--
-- Name: mfa_challenges; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.mfa_challenges (
    id uuid NOT NULL,
    factor_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    verified_at timestamp with time zone,
    ip_address inet NOT NULL,
    otp_code text,
    web_authn_session_data jsonb
);


--
-- Name: TABLE mfa_challenges; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.mfa_challenges IS 'auth: stores metadata about challenge requests made';


--
-- Name: mfa_factors; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.mfa_factors (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    friendly_name text,
    factor_type auth.factor_type NOT NULL,
    status auth.factor_status NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    secret text,
    phone text,
    last_challenged_at timestamp with time zone,
    web_authn_credential jsonb,
    web_authn_aaguid uuid
);


--
-- Name: TABLE mfa_factors; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.mfa_factors IS 'auth: stores metadata about factors';


--
-- Name: one_time_tokens; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.one_time_tokens (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    token_type auth.one_time_token_type NOT NULL,
    token_hash text NOT NULL,
    relates_to text NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    CONSTRAINT one_time_tokens_token_hash_check CHECK ((char_length(token_hash) > 0))
);


--
-- Name: refresh_tokens; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.refresh_tokens (
    instance_id uuid,
    id bigint NOT NULL,
    token character varying(255),
    user_id character varying(255),
    revoked boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    parent character varying(255),
    session_id uuid
);


--
-- Name: TABLE refresh_tokens; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.refresh_tokens IS 'Auth: Store of tokens used to refresh JWT tokens once they expire.';


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE; Schema: auth; Owner: -
--

CREATE SEQUENCE auth.refresh_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: auth; Owner: -
--

ALTER SEQUENCE auth.refresh_tokens_id_seq OWNED BY auth.refresh_tokens.id;


--
-- Name: saml_providers; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.saml_providers (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    entity_id text NOT NULL,
    metadata_xml text NOT NULL,
    metadata_url text,
    attribute_mapping jsonb,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    name_id_format text,
    CONSTRAINT "entity_id not empty" CHECK ((char_length(entity_id) > 0)),
    CONSTRAINT "metadata_url not empty" CHECK (((metadata_url = NULL::text) OR (char_length(metadata_url) > 0))),
    CONSTRAINT "metadata_xml not empty" CHECK ((char_length(metadata_xml) > 0))
);


--
-- Name: TABLE saml_providers; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.saml_providers IS 'Auth: Manages SAML Identity Provider connections.';


--
-- Name: saml_relay_states; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.saml_relay_states (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    request_id text NOT NULL,
    for_email text,
    redirect_to text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    flow_state_id uuid,
    CONSTRAINT "request_id not empty" CHECK ((char_length(request_id) > 0))
);


--
-- Name: TABLE saml_relay_states; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.saml_relay_states IS 'Auth: Contains SAML Relay State information for each Service Provider initiated login.';


--
-- Name: schema_migrations; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.schema_migrations (
    version character varying(255) NOT NULL
);


--
-- Name: TABLE schema_migrations; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.schema_migrations IS 'Auth: Manages updates to the auth system.';


--
-- Name: sessions; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.sessions (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    factor_id uuid,
    aal auth.aal_level,
    not_after timestamp with time zone,
    refreshed_at timestamp without time zone,
    user_agent text,
    ip inet,
    tag text
);


--
-- Name: TABLE sessions; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.sessions IS 'Auth: Stores session data associated to a user.';


--
-- Name: COLUMN sessions.not_after; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.sessions.not_after IS 'Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired.';


--
-- Name: sso_domains; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.sso_domains (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    domain text NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "domain not empty" CHECK ((char_length(domain) > 0))
);


--
-- Name: TABLE sso_domains; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.sso_domains IS 'Auth: Manages SSO email address domain mapping to an SSO Identity Provider.';


--
-- Name: sso_providers; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.sso_providers (
    id uuid NOT NULL,
    resource_id text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "resource_id not empty" CHECK (((resource_id = NULL::text) OR (char_length(resource_id) > 0)))
);


--
-- Name: TABLE sso_providers; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.sso_providers IS 'Auth: Manages SSO identity provider information; see saml_providers for SAML.';


--
-- Name: COLUMN sso_providers.resource_id; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.sso_providers.resource_id IS 'Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code.';


--
-- Name: users; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.users (
    instance_id uuid,
    id uuid NOT NULL,
    aud character varying(255),
    role character varying(255),
    email character varying(255),
    encrypted_password character varying(255),
    email_confirmed_at timestamp with time zone,
    invited_at timestamp with time zone,
    confirmation_token character varying(255),
    confirmation_sent_at timestamp with time zone,
    recovery_token character varying(255),
    recovery_sent_at timestamp with time zone,
    email_change_token_new character varying(255),
    email_change character varying(255),
    email_change_sent_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    phone text DEFAULT NULL::character varying,
    phone_confirmed_at timestamp with time zone,
    phone_change text DEFAULT ''::character varying,
    phone_change_token character varying(255) DEFAULT ''::character varying,
    phone_change_sent_at timestamp with time zone,
    confirmed_at timestamp with time zone GENERATED ALWAYS AS (LEAST(email_confirmed_at, phone_confirmed_at)) STORED,
    email_change_token_current character varying(255) DEFAULT ''::character varying,
    email_change_confirm_status smallint DEFAULT 0,
    banned_until timestamp with time zone,
    reauthentication_token character varying(255) DEFAULT ''::character varying,
    reauthentication_sent_at timestamp with time zone,
    is_sso_user boolean DEFAULT false NOT NULL,
    deleted_at timestamp with time zone,
    is_anonymous boolean DEFAULT false NOT NULL,
    CONSTRAINT users_email_change_confirm_status_check CHECK (((email_change_confirm_status >= 0) AND (email_change_confirm_status <= 2)))
);


--
-- Name: TABLE users; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.users IS 'Auth: Stores user login data within a secure schema.';


--
-- Name: COLUMN users.is_sso_user; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.users.is_sso_user IS 'Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails.';


--
-- Name: ai_models_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.ai_models_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: ai_models_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.ai_models_id_seq OWNED BY public.ai_models.id;


--
-- Name: categories_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.categories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: categories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.categories_id_seq OWNED BY public.categories.id;


--
-- Name: collection_prompts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.collection_prompts (
    collection_id uuid NOT NULL,
    prompt_id uuid NOT NULL,
    added_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: collections; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.collections (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    name text NOT NULL,
    description text,
    icon text,
    is_public boolean DEFAULT false NOT NULL,
    is_default boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    view_count integer DEFAULT 0,
    prompt_count integer DEFAULT 0,
    slug text,
    default_type text,
    search_vector tsvector,
    CONSTRAINT chk_default_type CHECK (((is_default = true) OR (default_type IS NULL))),
    CONSTRAINT chk_valid_default_type CHECK (((default_type = ANY (ARRAY['saved_prompts'::text, 'my_prompts'::text])) OR (default_type IS NULL)))
);


--
-- Name: comment_votes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.comment_votes (
    user_id uuid NOT NULL,
    comment_id uuid NOT NULL,
    vote_type smallint DEFAULT 1 NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT comment_votes_vote_type_check CHECK ((vote_type = 1))
);


--
-- Name: comment_display_details; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.comment_display_details AS
 WITH likecounts AS (
         SELECT comment_votes.comment_id,
            count(*) AS like_count
           FROM public.comment_votes
          GROUP BY comment_votes.comment_id
        )
 SELECT c.id,
    c.prompt_id,
    c.user_id,
    c.parent_comment_id,
    c.text,
    c.created_at,
    c.updated_at,
    p.username AS author_username,
    p.avatar_url AS author_avatar_url,
    COALESCE(lc.like_count, (0)::bigint) AS like_count
   FROM ((public.comments c
     LEFT JOIN public.profiles p ON ((c.user_id = p.id)))
     LEFT JOIN likecounts lc ON ((c.id = lc.comment_id)));


--
-- Name: VIEW comment_display_details; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.comment_display_details IS 'Provides comment data joined with author details (profile) and total like count.';


--
-- Name: followed_collections; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.followed_collections (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    follower_id uuid NOT NULL,
    collection_id uuid NOT NULL,
    followed_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: TABLE followed_collections; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.followed_collections IS 'Tracks which collections a user follows';


--
-- Name: mv_prompt_statistics; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_prompt_statistics AS
 SELECT prompt_statistics.id,
    prompt_statistics.short_id,
    prompt_statistics.user_id,
    prompt_statistics.category_id,
    prompt_statistics.tool_id,
    prompt_statistics.title,
    prompt_statistics.description,
    prompt_statistics.prompt_text,
    prompt_statistics.instructions,
    prompt_statistics.example_input,
    prompt_statistics.example_output_text,
    prompt_statistics.example_output_image_url,
    prompt_statistics.image_url,
    prompt_statistics.is_public,
    prompt_statistics.original_prompt_id,
    prompt_statistics.view_count,
    prompt_statistics.created_at,
    prompt_statistics.updated_at,
    prompt_statistics.updated_by_user_id,
    prompt_statistics.search_vector,
    prompt_statistics.primary_tag_id,
    prompt_statistics.tag_slugs,
    prompt_statistics.rating,
    prompt_statistics.upvotes,
    prompt_statistics.downvotes,
    prompt_statistics.comment_count,
    prompt_statistics.remix_count
   FROM public.prompt_statistics
  WITH NO DATA;


--
-- Name: mv_trending_prompts; Type: MATERIALIZED VIEW; Schema: public; Owner: -
--

CREATE MATERIALIZED VIEW public.mv_trending_prompts AS
 SELECT trending_prompts.id,
    trending_prompts.title,
    trending_prompts.user_id,
    trending_prompts.created_at,
    trending_prompts.view_count,
    trending_prompts.is_public,
    trending_prompts.upvotes,
    trending_prompts.downvotes,
    trending_prompts.comment_count,
    trending_prompts.remix_count,
    trending_prompts.trending_score
   FROM public.trending_prompts
  WITH NO DATA;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notifications (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    recipient_user_id uuid NOT NULL,
    actor_user_id uuid,
    type text NOT NULL,
    entity_id uuid,
    entity_type text,
    entity_title text,
    link text,
    is_read boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    prompt_short_id text,
    prompt_category_slug text,
    prompt_tool_slug text,
    prompt_primary_tag_slug text,
    prompt_title_slug text
);


--
-- Name: prompt_card_details_with_saved; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.prompt_card_details_with_saved AS
 WITH aggregatedtagsfordisplay AS (
         SELECT pt.prompt_id,
            jsonb_agg(jsonb_build_object('id', t.id, 'name', t.name, 'slug', t.slug) ORDER BY t.name) FILTER (WHERE (t.id IS NOT NULL)) AS tags_jsonb
           FROM (public.prompt_tags pt
             JOIN public.tags t ON ((pt.tag_id = t.id)))
          GROUP BY pt.prompt_id
        ), user_saved_prompts AS (
         SELECT cp.prompt_id,
            cp.collection_id,
            c.user_id,
            cp.added_at AS saved_at
           FROM (public.collection_prompts cp
             JOIN public.collections c ON ((cp.collection_id = c.id)))
          WHERE (c.default_type = 'saved_prompts'::text)
        )
 SELECT p.id,
    p.short_id,
    p.title,
    p.description,
    p.image_url,
    p.created_at,
    p.updated_at,
    p.is_public,
    p.view_count,
    p.primary_tag_id,
    p.category_id,
    p.tool_id,
    p.user_id AS author_id,
    p.search_vector,
    COALESCE(p.tag_slugs, '{}'::text[]) AS tag_slugs_array,
    cat.name AS category_name,
    cat.slug AS category_slug,
    tool.name AS tool_name,
    tool.slug AS tool_slug,
    prof.username AS author_username,
    prof.avatar_url AS author_avatar_url,
    ptag.slug AS primary_tag_slug,
    COALESCE(atd.tags_jsonb, '[]'::jsonb) AS tags,
    COALESCE(stats.rating, (0)::bigint) AS rating,
    COALESCE(stats.comment_count, (0)::bigint) AS comment_count,
    COALESCE(trend.trending_score, (0)::double precision) AS trending_score,
    p.ai_model_id,
    am.provider AS ai_model_provider,
    am.tool_name AS ai_model_name,
    am.slug AS ai_model_slug,
    am.deprecated AS ai_model_deprecated,
    usp.user_id AS saved_by_user_id,
    usp.saved_at,
        CASE
            WHEN (usp.user_id IS NOT NULL) THEN true
            ELSE false
        END AS is_saved_by_user
   FROM (((((((((public.prompts p
     LEFT JOIN public.categories cat ON ((p.category_id = cat.id)))
     LEFT JOIN public.tools tool ON ((p.tool_id = tool.id)))
     LEFT JOIN public.profiles prof ON ((p.user_id = prof.id)))
     LEFT JOIN public.tags ptag ON ((p.primary_tag_id = ptag.id)))
     LEFT JOIN aggregatedtagsfordisplay atd ON ((p.id = atd.prompt_id)))
     LEFT JOIN public.prompt_statistics stats ON ((p.id = stats.id)))
     LEFT JOIN public.trending_prompts trend ON ((p.id = trend.id)))
     LEFT JOIN public.ai_models am ON ((p.ai_model_id = am.id)))
     LEFT JOIN user_saved_prompts usp ON ((p.id = usp.prompt_id)));


--
-- Name: saved_prompts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.saved_prompts (
    user_id uuid NOT NULL,
    prompt_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: tags_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.tags_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: tags_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.tags_id_seq OWNED BY public.tags.id;


--
-- Name: tools_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.tools_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: tools_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.tools_id_seq OWNED BY public.tools.id;


--
-- Name: view_tool_models; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.view_tool_models AS
 SELECT t.id AS tool_id,
    t.name AS tool_name,
    t.slug AS tool_slug,
    am.id AS ai_model_id,
    am.provider AS ai_model_provider,
    am.tool_name AS ai_model_name,
    am.slug AS ai_model_slug,
    am.type AS ai_model_type,
    am.deprecated AS ai_model_deprecated
   FROM (public.tools t
     JOIN public.ai_models am ON ((t.id = am.tool_id)));


--
-- Name: VIEW view_tool_models; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON VIEW public.view_tool_models IS 'Lists AI models associated with each tool, including their details and deprecated status.';


--
-- Name: messages; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.messages (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
)
PARTITION BY RANGE (inserted_at);


--
-- Name: messages_2025_05_28; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.messages_2025_05_28 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: messages_2025_05_29; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.messages_2025_05_29 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: messages_2025_05_30; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.messages_2025_05_30 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: messages_2025_05_31; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.messages_2025_05_31 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: messages_2025_06_01; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.messages_2025_06_01 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: messages_2025_06_02; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.messages_2025_06_02 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: messages_2025_06_03; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.messages_2025_06_03 (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: schema_migrations; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


--
-- Name: subscription; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.subscription (
    id bigint NOT NULL,
    subscription_id uuid NOT NULL,
    entity regclass NOT NULL,
    filters realtime.user_defined_filter[] DEFAULT '{}'::realtime.user_defined_filter[] NOT NULL,
    claims jsonb NOT NULL,
    claims_role regrole GENERATED ALWAYS AS (realtime.to_regrole((claims ->> 'role'::text))) STORED NOT NULL,
    created_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


--
-- Name: subscription_id_seq; Type: SEQUENCE; Schema: realtime; Owner: -
--

ALTER TABLE realtime.subscription ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME realtime.subscription_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: buckets; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.buckets (
    id text NOT NULL,
    name text NOT NULL,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    public boolean DEFAULT false,
    avif_autodetection boolean DEFAULT false,
    file_size_limit bigint,
    allowed_mime_types text[],
    owner_id text
);


--
-- Name: COLUMN buckets.owner; Type: COMMENT; Schema: storage; Owner: -
--

COMMENT ON COLUMN storage.buckets.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: migrations; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.migrations (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    hash character varying(40) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: objects; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.objects (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    bucket_id text,
    name text,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    last_accessed_at timestamp with time zone DEFAULT now(),
    metadata jsonb,
    path_tokens text[] GENERATED ALWAYS AS (string_to_array(name, '/'::text)) STORED,
    version text,
    owner_id text,
    user_metadata jsonb
);


--
-- Name: COLUMN objects.owner; Type: COMMENT; Schema: storage; Owner: -
--

COMMENT ON COLUMN storage.objects.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: s3_multipart_uploads; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.s3_multipart_uploads (
    id text NOT NULL,
    in_progress_size bigint DEFAULT 0 NOT NULL,
    upload_signature text NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    version text NOT NULL,
    owner_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    user_metadata jsonb
);


--
-- Name: s3_multipart_uploads_parts; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.s3_multipart_uploads_parts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    upload_id text NOT NULL,
    size bigint DEFAULT 0 NOT NULL,
    part_number integer NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    etag text NOT NULL,
    owner_id text,
    version text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: messages_2025_05_28; Type: TABLE ATTACH; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_05_28 FOR VALUES FROM ('2025-05-28 00:00:00') TO ('2025-05-29 00:00:00');


--
-- Name: messages_2025_05_29; Type: TABLE ATTACH; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_05_29 FOR VALUES FROM ('2025-05-29 00:00:00') TO ('2025-05-30 00:00:00');


--
-- Name: messages_2025_05_30; Type: TABLE ATTACH; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_05_30 FOR VALUES FROM ('2025-05-30 00:00:00') TO ('2025-05-31 00:00:00');


--
-- Name: messages_2025_05_31; Type: TABLE ATTACH; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_05_31 FOR VALUES FROM ('2025-05-31 00:00:00') TO ('2025-06-01 00:00:00');


--
-- Name: messages_2025_06_01; Type: TABLE ATTACH; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_01 FOR VALUES FROM ('2025-06-01 00:00:00') TO ('2025-06-02 00:00:00');


--
-- Name: messages_2025_06_02; Type: TABLE ATTACH; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_02 FOR VALUES FROM ('2025-06-02 00:00:00') TO ('2025-06-03 00:00:00');


--
-- Name: messages_2025_06_03; Type: TABLE ATTACH; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages ATTACH PARTITION realtime.messages_2025_06_03 FOR VALUES FROM ('2025-06-03 00:00:00') TO ('2025-06-04 00:00:00');


--
-- Name: refresh_tokens id; Type: DEFAULT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens ALTER COLUMN id SET DEFAULT nextval('auth.refresh_tokens_id_seq'::regclass);


--
-- Name: ai_models id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_models ALTER COLUMN id SET DEFAULT nextval('public.ai_models_id_seq'::regclass);


--
-- Name: categories id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.categories ALTER COLUMN id SET DEFAULT nextval('public.categories_id_seq'::regclass);


--
-- Name: tags id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tags ALTER COLUMN id SET DEFAULT nextval('public.tags_id_seq'::regclass);


--
-- Name: tools id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tools ALTER COLUMN id SET DEFAULT nextval('public.tools_id_seq'::regclass);


--
-- Name: mfa_amr_claims amr_id_pk; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT amr_id_pk PRIMARY KEY (id);


--
-- Name: audit_log_entries audit_log_entries_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.audit_log_entries
    ADD CONSTRAINT audit_log_entries_pkey PRIMARY KEY (id);


--
-- Name: flow_state flow_state_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.flow_state
    ADD CONSTRAINT flow_state_pkey PRIMARY KEY (id);


--
-- Name: identities identities_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_pkey PRIMARY KEY (id);


--
-- Name: identities identities_provider_id_provider_unique; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_provider_id_provider_unique UNIQUE (provider_id, provider);


--
-- Name: instances instances_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.instances
    ADD CONSTRAINT instances_pkey PRIMARY KEY (id);


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_authentication_method_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_authentication_method_pkey UNIQUE (session_id, authentication_method);


--
-- Name: mfa_challenges mfa_challenges_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_pkey PRIMARY KEY (id);


--
-- Name: mfa_factors mfa_factors_last_challenged_at_key; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_last_challenged_at_key UNIQUE (last_challenged_at);


--
-- Name: mfa_factors mfa_factors_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_pkey PRIMARY KEY (id);


--
-- Name: one_time_tokens one_time_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_token_unique; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_token_unique UNIQUE (token);


--
-- Name: saml_providers saml_providers_entity_id_key; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_entity_id_key UNIQUE (entity_id);


--
-- Name: saml_providers saml_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_pkey PRIMARY KEY (id);


--
-- Name: saml_relay_states saml_relay_states_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: sso_domains sso_domains_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_pkey PRIMARY KEY (id);


--
-- Name: sso_providers sso_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sso_providers
    ADD CONSTRAINT sso_providers_pkey PRIMARY KEY (id);


--
-- Name: users users_phone_key; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_phone_key UNIQUE (phone);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: ai_models ai_models_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_models
    ADD CONSTRAINT ai_models_pkey PRIMARY KEY (id);


--
-- Name: ai_models ai_models_slug_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_models
    ADD CONSTRAINT ai_models_slug_key UNIQUE (slug);


--
-- Name: categories categories_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_name_key UNIQUE (name);


--
-- Name: categories categories_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_pkey PRIMARY KEY (id);


--
-- Name: categories categories_slug_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_slug_key UNIQUE (slug);


--
-- Name: collection_prompts collection_prompts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.collection_prompts
    ADD CONSTRAINT collection_prompts_pkey PRIMARY KEY (collection_id, prompt_id);


--
-- Name: collections collections_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.collections
    ADD CONSTRAINT collections_pkey PRIMARY KEY (id);


--
-- Name: collections collections_user_id_slug_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.collections
    ADD CONSTRAINT collections_user_id_slug_key UNIQUE (user_id, slug);


--
-- Name: comment_votes comment_votes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.comment_votes
    ADD CONSTRAINT comment_votes_pkey PRIMARY KEY (user_id, comment_id);


--
-- Name: comments comments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_pkey PRIMARY KEY (id);


--
-- Name: followed_collections followed_collections_follower_id_collection_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.followed_collections
    ADD CONSTRAINT followed_collections_follower_id_collection_id_key UNIQUE (follower_id, collection_id);


--
-- Name: followed_collections followed_collections_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.followed_collections
    ADD CONSTRAINT followed_collections_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: profiles profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_pkey PRIMARY KEY (id);


--
-- Name: profiles profiles_username_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_username_key UNIQUE (username);


--
-- Name: prompt_tags prompt_tags_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_tags
    ADD CONSTRAINT prompt_tags_pkey PRIMARY KEY (prompt_id, tag_id);


--
-- Name: prompt_votes prompt_votes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_votes
    ADD CONSTRAINT prompt_votes_pkey PRIMARY KEY (user_id, prompt_id);


--
-- Name: prompts prompts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompts
    ADD CONSTRAINT prompts_pkey PRIMARY KEY (id);


--
-- Name: prompts prompts_short_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompts
    ADD CONSTRAINT prompts_short_id_key UNIQUE (short_id);


--
-- Name: saved_prompts saved_prompts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.saved_prompts
    ADD CONSTRAINT saved_prompts_pkey PRIMARY KEY (user_id, prompt_id);


--
-- Name: tags tags_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tags
    ADD CONSTRAINT tags_name_key UNIQUE (name);


--
-- Name: tags tags_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tags
    ADD CONSTRAINT tags_pkey PRIMARY KEY (id);


--
-- Name: tags tags_slug_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tags
    ADD CONSTRAINT tags_slug_key UNIQUE (slug);


--
-- Name: tools tools_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tools
    ADD CONSTRAINT tools_name_key UNIQUE (name);


--
-- Name: tools tools_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tools
    ADD CONSTRAINT tools_pkey PRIMARY KEY (id);


--
-- Name: tools tools_slug_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tools
    ADD CONSTRAINT tools_slug_key UNIQUE (slug);


--
-- Name: collections unique_default_type_per_user; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.collections
    ADD CONSTRAINT unique_default_type_per_user UNIQUE (user_id, default_type);


--
-- Name: tools uq_tools_name; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tools
    ADD CONSTRAINT uq_tools_name UNIQUE (name);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_05_28 messages_2025_05_28_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages_2025_05_28
    ADD CONSTRAINT messages_2025_05_28_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_05_29 messages_2025_05_29_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages_2025_05_29
    ADD CONSTRAINT messages_2025_05_29_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_05_30 messages_2025_05_30_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages_2025_05_30
    ADD CONSTRAINT messages_2025_05_30_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_05_31 messages_2025_05_31_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages_2025_05_31
    ADD CONSTRAINT messages_2025_05_31_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_01 messages_2025_06_01_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages_2025_06_01
    ADD CONSTRAINT messages_2025_06_01_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_02 messages_2025_06_02_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages_2025_06_02
    ADD CONSTRAINT messages_2025_06_02_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: messages_2025_06_03 messages_2025_06_03_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages_2025_06_03
    ADD CONSTRAINT messages_2025_06_03_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: subscription pk_subscription; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.subscription
    ADD CONSTRAINT pk_subscription PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: buckets buckets_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.buckets
    ADD CONSTRAINT buckets_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_name_key; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_name_key UNIQUE (name);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);


--
-- Name: objects objects_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT objects_pkey PRIMARY KEY (id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_pkey PRIMARY KEY (id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_pkey PRIMARY KEY (id);


--
-- Name: audit_logs_instance_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id);


--
-- Name: confirmation_token_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX confirmation_token_idx ON auth.users USING btree (confirmation_token) WHERE ((confirmation_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_current_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX email_change_token_current_idx ON auth.users USING btree (email_change_token_current) WHERE ((email_change_token_current)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_new_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX email_change_token_new_idx ON auth.users USING btree (email_change_token_new) WHERE ((email_change_token_new)::text !~ '^[0-9 ]*$'::text);


--
-- Name: factor_id_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at);


--
-- Name: flow_state_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC);


--
-- Name: identities_email_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX identities_email_idx ON auth.identities USING btree (email text_pattern_ops);


--
-- Name: INDEX identities_email_idx; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON INDEX auth.identities_email_idx IS 'Auth: Ensures indexed queries on the email column';


--
-- Name: identities_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX identities_user_id_idx ON auth.identities USING btree (user_id);


--
-- Name: idx_auth_code; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX idx_auth_code ON auth.flow_state USING btree (auth_code);


--
-- Name: idx_user_id_auth_method; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method);


--
-- Name: mfa_challenge_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC);


--
-- Name: mfa_factors_user_friendly_name_unique; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text);


--
-- Name: mfa_factors_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX mfa_factors_user_id_idx ON auth.mfa_factors USING btree (user_id);


--
-- Name: one_time_tokens_relates_to_hash_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX one_time_tokens_relates_to_hash_idx ON auth.one_time_tokens USING hash (relates_to);


--
-- Name: one_time_tokens_token_hash_hash_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX one_time_tokens_token_hash_hash_idx ON auth.one_time_tokens USING hash (token_hash);


--
-- Name: one_time_tokens_user_id_token_type_key; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX one_time_tokens_user_id_token_type_key ON auth.one_time_tokens USING btree (user_id, token_type);


--
-- Name: reauthentication_token_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX reauthentication_token_idx ON auth.users USING btree (reauthentication_token) WHERE ((reauthentication_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: recovery_token_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX recovery_token_idx ON auth.users USING btree (recovery_token) WHERE ((recovery_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: refresh_tokens_instance_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id);


--
-- Name: refresh_tokens_instance_id_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id);


--
-- Name: refresh_tokens_parent_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent);


--
-- Name: refresh_tokens_session_id_revoked_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked);


--
-- Name: refresh_tokens_updated_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_updated_at_idx ON auth.refresh_tokens USING btree (updated_at DESC);


--
-- Name: saml_providers_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_providers_sso_provider_id_idx ON auth.saml_providers USING btree (sso_provider_id);


--
-- Name: saml_relay_states_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_relay_states_created_at_idx ON auth.saml_relay_states USING btree (created_at DESC);


--
-- Name: saml_relay_states_for_email_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email);


--
-- Name: saml_relay_states_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id);


--
-- Name: sessions_not_after_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX sessions_not_after_idx ON auth.sessions USING btree (not_after DESC);


--
-- Name: sessions_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX sessions_user_id_idx ON auth.sessions USING btree (user_id);


--
-- Name: sso_domains_domain_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain));


--
-- Name: sso_domains_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id);


--
-- Name: sso_providers_resource_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX sso_providers_resource_id_idx ON auth.sso_providers USING btree (lower(resource_id));


--
-- Name: unique_phone_factor_per_user; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX unique_phone_factor_per_user ON auth.mfa_factors USING btree (user_id, phone);


--
-- Name: user_id_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at);


--
-- Name: users_email_partial_key; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX users_email_partial_key ON auth.users USING btree (email) WHERE (is_sso_user = false);


--
-- Name: INDEX users_email_partial_key; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON INDEX auth.users_email_partial_key IS 'Auth: A partial unique index that applies only when is_sso_user is false';


--
-- Name: users_instance_id_email_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text));


--
-- Name: users_instance_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX users_instance_id_idx ON auth.users USING btree (instance_id);


--
-- Name: users_is_anonymous_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX users_is_anonymous_idx ON auth.users USING btree (is_anonymous);


--
-- Name: idx_categories_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_categories_slug ON public.categories USING btree (slug);


--
-- Name: idx_collection_prompts_added_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_collection_prompts_added_time ON public.collection_prompts USING btree (collection_id, added_at DESC);


--
-- Name: idx_collection_prompts_collection_prompt; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_collection_prompts_collection_prompt ON public.collection_prompts USING btree (collection_id, prompt_id);


--
-- Name: idx_collection_prompts_prompt_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_collection_prompts_prompt_id ON public.collection_prompts USING btree (prompt_id);


--
-- Name: idx_collections_is_public; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_collections_is_public ON public.collections USING btree (is_public);


--
-- Name: idx_collections_user_default_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_collections_user_default_type ON public.collections USING btree (user_id, default_type);


--
-- Name: idx_collections_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_collections_user_id ON public.collections USING btree (user_id);


--
-- Name: idx_collections_user_id_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_collections_user_id_slug ON public.collections USING btree (user_id, slug);


--
-- Name: idx_comment_votes_comment_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_comment_votes_comment_id ON public.comment_votes USING btree (comment_id);


--
-- Name: idx_comments_parent_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_comments_parent_id ON public.comments USING btree (parent_comment_id);


--
-- Name: idx_comments_prompt_created_top_level; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_comments_prompt_created_top_level ON public.comments USING btree (prompt_id, created_at DESC) WHERE (parent_comment_id IS NULL);


--
-- Name: idx_comments_prompt_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_comments_prompt_id ON public.comments USING btree (prompt_id);


--
-- Name: idx_comments_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_comments_user_id ON public.comments USING btree (user_id);


--
-- Name: idx_followed_collections_collection; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_followed_collections_collection ON public.followed_collections USING btree (collection_id);


--
-- Name: idx_followed_collections_composite; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_followed_collections_composite ON public.followed_collections USING btree (follower_id, collection_id);


--
-- Name: idx_followed_collections_follower; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_followed_collections_follower ON public.followed_collections USING btree (follower_id);


--
-- Name: idx_mv_prompt_statistics_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mv_prompt_statistics_id ON public.mv_prompt_statistics USING btree (id);


--
-- Name: idx_mv_trending_prompts_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_mv_trending_prompts_id ON public.mv_trending_prompts USING btree (id);


--
-- Name: idx_notifications_recipient_user_id_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_notifications_recipient_user_id_created_at ON public.notifications USING btree (recipient_user_id, created_at DESC);


--
-- Name: idx_notifications_recipient_user_id_is_read; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_notifications_recipient_user_id_is_read ON public.notifications USING btree (recipient_user_id, is_read);


--
-- Name: idx_profiles_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profiles_id ON public.profiles USING btree (id);


--
-- Name: idx_profiles_username_lower; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profiles_username_lower ON public.profiles USING btree (lower(username) text_pattern_ops);


--
-- Name: INDEX idx_profiles_username_lower; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON INDEX public.idx_profiles_username_lower IS 'B-tree index for efficient exact username matches and prefix searches';


--
-- Name: idx_profiles_username_trgm; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_profiles_username_trgm ON public.profiles USING gin (username public.gin_trgm_ops);


--
-- Name: INDEX idx_profiles_username_trgm; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON INDEX public.idx_profiles_username_trgm IS 'GIN index for efficient username substring searches using pg_trgm';


--
-- Name: idx_prompt_tags_prompt_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_tags_prompt_id ON public.prompt_tags USING btree (prompt_id);


--
-- Name: idx_prompt_tags_tag_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_tags_tag_id ON public.prompt_tags USING btree (tag_id);


--
-- Name: idx_prompt_votes_prompt_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_votes_prompt_id ON public.prompt_votes USING btree (prompt_id);


--
-- Name: idx_prompt_votes_user_prompt; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_votes_user_prompt ON public.prompt_votes USING btree (user_id, prompt_id);


--
-- Name: idx_prompt_votes_user_prompt_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_votes_user_prompt_type ON public.prompt_votes USING btree (user_id, prompt_id) INCLUDE (vote_type);


--
-- Name: idx_prompts_ai_model_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_ai_model_id ON public.prompts USING btree (ai_model_id);


--
-- Name: idx_prompts_category_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_category_created ON public.prompts USING btree (category_id, created_at DESC);


--
-- Name: idx_prompts_category_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_category_created_at ON public.prompts USING btree (category_id, created_at DESC);


--
-- Name: idx_prompts_category_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_category_id ON public.prompts USING btree (category_id);


--
-- Name: idx_prompts_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_created_at ON public.prompts USING btree (created_at);


--
-- Name: idx_prompts_is_public; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_is_public ON public.prompts USING btree (is_public);


--
-- Name: idx_prompts_original_prompt_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_original_prompt_id ON public.prompts USING btree (original_prompt_id);


--
-- Name: idx_prompts_primary_tag_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_primary_tag_id ON public.prompts USING btree (primary_tag_id);


--
-- Name: idx_prompts_public_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_public_created ON public.prompts USING btree (is_public, created_at DESC);


--
-- Name: idx_prompts_search_combo; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_search_combo ON public.prompts USING btree (is_public, category_id, tool_id, created_at DESC) WHERE (is_public = true);


--
-- Name: idx_prompts_search_vector; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_search_vector ON public.prompts USING gin (search_vector);


--
-- Name: idx_prompts_short_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_short_id ON public.prompts USING btree (short_id);


--
-- Name: idx_prompts_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_slug ON public.prompts USING btree (slug);


--
-- Name: idx_prompts_tag_slugs_gin; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_tag_slugs_gin ON public.prompts USING gin (tag_slugs);


--
-- Name: idx_prompts_tool_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_tool_created_at ON public.prompts USING btree (tool_id, created_at DESC);


--
-- Name: idx_prompts_tool_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_tool_id ON public.prompts USING btree (tool_id);


--
-- Name: idx_prompts_user_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_user_created_at ON public.prompts USING btree (user_id, created_at DESC);


--
-- Name: idx_prompts_user_entered_ai_model; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_user_entered_ai_model ON public.prompts USING btree (user_entered_ai_model);


--
-- Name: idx_prompts_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompts_user_id ON public.prompts USING btree (user_id);


--
-- Name: idx_provider; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_provider ON public.ai_models USING btree (provider);


--
-- Name: idx_saved_prompts_prompt_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_saved_prompts_prompt_id ON public.saved_prompts USING btree (prompt_id);


--
-- Name: idx_saved_prompts_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_saved_prompts_user_id ON public.saved_prompts USING btree (user_id);


--
-- Name: idx_tags_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_tags_slug ON public.tags USING btree (slug);


--
-- Name: idx_tool_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_tool_name ON public.ai_models USING btree (tool_name);


--
-- Name: idx_tools_slug; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_tools_slug ON public.tools USING btree (slug);


--
-- Name: prompts_search_vector_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX prompts_search_vector_idx ON public.prompts USING gin (search_vector);


--
-- Name: ix_realtime_subscription_entity; Type: INDEX; Schema: realtime; Owner: -
--

CREATE INDEX ix_realtime_subscription_entity ON realtime.subscription USING btree (entity);


--
-- Name: subscription_subscription_id_entity_filters_key; Type: INDEX; Schema: realtime; Owner: -
--

CREATE UNIQUE INDEX subscription_subscription_id_entity_filters_key ON realtime.subscription USING btree (subscription_id, entity, filters);


--
-- Name: bname; Type: INDEX; Schema: storage; Owner: -
--

CREATE UNIQUE INDEX bname ON storage.buckets USING btree (name);


--
-- Name: bucketid_objname; Type: INDEX; Schema: storage; Owner: -
--

CREATE UNIQUE INDEX bucketid_objname ON storage.objects USING btree (bucket_id, name);


--
-- Name: idx_multipart_uploads_list; Type: INDEX; Schema: storage; Owner: -
--

CREATE INDEX idx_multipart_uploads_list ON storage.s3_multipart_uploads USING btree (bucket_id, key, created_at);


--
-- Name: idx_objects_bucket_id_name; Type: INDEX; Schema: storage; Owner: -
--

CREATE INDEX idx_objects_bucket_id_name ON storage.objects USING btree (bucket_id, name COLLATE "C");


--
-- Name: name_prefix_search; Type: INDEX; Schema: storage; Owner: -
--

CREATE INDEX name_prefix_search ON storage.objects USING btree (name text_pattern_ops);


--
-- Name: messages_2025_05_28_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: -
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_05_28_pkey;


--
-- Name: messages_2025_05_29_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: -
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_05_29_pkey;


--
-- Name: messages_2025_05_30_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: -
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_05_30_pkey;


--
-- Name: messages_2025_05_31_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: -
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_05_31_pkey;


--
-- Name: messages_2025_06_01_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: -
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_01_pkey;


--
-- Name: messages_2025_06_02_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: -
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_02_pkey;


--
-- Name: messages_2025_06_03_pkey; Type: INDEX ATTACH; Schema: realtime; Owner: -
--

ALTER INDEX realtime.messages_pkey ATTACH PARTITION realtime.messages_2025_06_03_pkey;


--
-- Name: users on_auth_user_created; Type: TRIGGER; Schema: auth; Owner: -
--

CREATE TRIGGER on_auth_user_created AFTER INSERT ON auth.users FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();


--
-- Name: collection_prompts before_insert_collection_prompt_check_rules; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER before_insert_collection_prompt_check_rules BEFORE INSERT ON public.collection_prompts FOR EACH ROW EXECUTE FUNCTION public.enforce_collection_content_rules();


--
-- Name: profiles create_default_collection_after_profile_insert; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER create_default_collection_after_profile_insert AFTER INSERT ON public.profiles FOR EACH ROW EXECUTE FUNCTION public.create_default_collection_for_new_user();


--
-- Name: collection_prompts on_collection_prompt_change; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER on_collection_prompt_change AFTER INSERT OR DELETE ON public.collection_prompts FOR EACH ROW EXECUTE FUNCTION public.update_collection_prompt_count();


--
-- Name: collections on_collection_public_status_change; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER on_collection_public_status_change AFTER INSERT OR DELETE OR UPDATE OF is_public ON public.collections FOR EACH ROW EXECUTE FUNCTION public.update_profile_public_collections_count();


--
-- Name: profiles on_new_profile_create_default_collections; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER on_new_profile_create_default_collections AFTER INSERT ON public.profiles FOR EACH ROW EXECUTE FUNCTION public.create_default_collections_for_new_user();


--
-- Name: prompt_votes on_new_prompt_like; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER on_new_prompt_like AFTER INSERT ON public.prompt_votes FOR EACH ROW EXECUTE FUNCTION public.create_like_notification();

ALTER TABLE public.prompt_votes DISABLE TRIGGER on_new_prompt_like;


--
-- Name: prompts on_prompt_public_status_change; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER on_prompt_public_status_change AFTER INSERT OR DELETE OR UPDATE OF is_public ON public.prompts FOR EACH ROW EXECUTE FUNCTION public.update_profile_public_prompts_count();


--
-- Name: prompt_votes on_prompt_vote_change; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER on_prompt_vote_change AFTER INSERT OR DELETE OR UPDATE ON public.prompt_votes FOR EACH ROW EXECUTE FUNCTION public.update_profile_likes_count();


--
-- Name: comments trigger_after_comment_insert_send_notification; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_after_comment_insert_send_notification AFTER INSERT ON public.comments FOR EACH ROW EXECUTE FUNCTION public.create_comment_reply_notification();


--
-- Name: prompt_tags trigger_set_primary_tag; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_set_primary_tag AFTER INSERT ON public.prompt_tags FOR EACH ROW EXECUTE FUNCTION public.set_primary_tag_on_first_insert();


--
-- Name: TRIGGER trigger_set_primary_tag ON prompt_tags; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TRIGGER trigger_set_primary_tag ON public.prompt_tags IS 'Sets the prompts.primary_tag_id to the tag_id of the first tag inserted for that prompt in prompt_tags.';


--
-- Name: prompts trigger_set_prompt_slug_on_insert; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_set_prompt_slug_on_insert BEFORE INSERT ON public.prompts FOR EACH ROW EXECUTE FUNCTION public.set_prompt_slug();


--
-- Name: prompt_tags trigger_sync_prompt_tag_slugs; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER trigger_sync_prompt_tag_slugs AFTER INSERT OR DELETE OR UPDATE ON public.prompt_tags FOR EACH ROW EXECUTE FUNCTION public.sync_prompt_tag_slugs();


--
-- Name: collections update_collection_search_vector_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_collection_search_vector_trigger BEFORE INSERT OR UPDATE ON public.collections FOR EACH ROW EXECUTE FUNCTION public.update_collection_search_vector();


--
-- Name: collections update_collections_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_collections_updated_at BEFORE UPDATE ON public.collections FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: comments update_comments_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON public.comments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: profiles update_profiles_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: prompts update_prompt_search_vector_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_prompt_search_vector_trigger BEFORE INSERT OR UPDATE ON public.prompts FOR EACH ROW EXECUTE FUNCTION public.update_prompt_search_vector();


--
-- Name: prompts update_prompts_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_prompts_updated_at BEFORE UPDATE ON public.prompts FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: subscription tr_check_filters; Type: TRIGGER; Schema: realtime; Owner: -
--

CREATE TRIGGER tr_check_filters BEFORE INSERT OR UPDATE ON realtime.subscription FOR EACH ROW EXECUTE FUNCTION realtime.subscription_check_filters();


--
-- Name: objects on_profile_picture_deletion; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER on_profile_picture_deletion AFTER DELETE ON storage.objects FOR EACH ROW EXECUTE FUNCTION public.handle_profile_picture_deletion();


--
-- Name: objects on_profile_picture_upload; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER on_profile_picture_upload AFTER INSERT ON storage.objects FOR EACH ROW EXECUTE FUNCTION public.handle_profile_picture_upload();


--
-- Name: objects update_objects_updated_at; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER update_objects_updated_at BEFORE UPDATE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.update_updated_at_column();


--
-- Name: identities identities_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: mfa_challenges mfa_challenges_auth_factor_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_auth_factor_id_fkey FOREIGN KEY (factor_id) REFERENCES auth.mfa_factors(id) ON DELETE CASCADE;


--
-- Name: mfa_factors mfa_factors_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: one_time_tokens one_time_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: refresh_tokens refresh_tokens_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: saml_providers saml_providers_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_flow_state_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_flow_state_id_fkey FOREIGN KEY (flow_state_id) REFERENCES auth.flow_state(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: sso_domains sso_domains_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: collection_prompts collection_prompts_collection_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.collection_prompts
    ADD CONSTRAINT collection_prompts_collection_id_fkey FOREIGN KEY (collection_id) REFERENCES public.collections(id) ON DELETE CASCADE;


--
-- Name: collection_prompts collection_prompts_prompt_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.collection_prompts
    ADD CONSTRAINT collection_prompts_prompt_id_fkey FOREIGN KEY (prompt_id) REFERENCES public.prompts(id) ON DELETE CASCADE;


--
-- Name: collections collections_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.collections
    ADD CONSTRAINT collections_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: comment_votes comment_votes_comment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.comment_votes
    ADD CONSTRAINT comment_votes_comment_id_fkey FOREIGN KEY (comment_id) REFERENCES public.comments(id) ON DELETE CASCADE;


--
-- Name: comment_votes comment_votes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.comment_votes
    ADD CONSTRAINT comment_votes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: comments comments_parent_comment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_parent_comment_id_fkey FOREIGN KEY (parent_comment_id) REFERENCES public.comments(id) ON DELETE CASCADE;


--
-- Name: comments comments_prompt_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_prompt_id_fkey FOREIGN KEY (prompt_id) REFERENCES public.prompts(id) ON DELETE CASCADE;


--
-- Name: comments comments_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE SET NULL;


--
-- Name: ai_models fk_ai_models_tool_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ai_models
    ADD CONSTRAINT fk_ai_models_tool_id FOREIGN KEY (tool_id) REFERENCES public.tools(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: prompts fk_prompts_ai_model; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompts
    ADD CONSTRAINT fk_prompts_ai_model FOREIGN KEY (ai_model_id) REFERENCES public.ai_models(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: prompts fk_prompts_primary_tag; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompts
    ADD CONSTRAINT fk_prompts_primary_tag FOREIGN KEY (primary_tag_id) REFERENCES public.tags(id) ON DELETE SET NULL;


--
-- Name: followed_collections followed_collections_collection_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.followed_collections
    ADD CONSTRAINT followed_collections_collection_id_fkey FOREIGN KEY (collection_id) REFERENCES public.collections(id) ON DELETE CASCADE;


--
-- Name: followed_collections followed_collections_follower_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.followed_collections
    ADD CONSTRAINT followed_collections_follower_id_fkey FOREIGN KEY (follower_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: notifications notifications_actor_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_actor_user_id_fkey FOREIGN KEY (actor_user_id) REFERENCES public.profiles(id) ON DELETE SET NULL;


--
-- Name: notifications notifications_recipient_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_recipient_user_id_fkey FOREIGN KEY (recipient_user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: profiles profiles_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: prompt_tags prompt_tags_prompt_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_tags
    ADD CONSTRAINT prompt_tags_prompt_id_fkey FOREIGN KEY (prompt_id) REFERENCES public.prompts(id) ON DELETE CASCADE;


--
-- Name: prompt_tags prompt_tags_tag_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_tags
    ADD CONSTRAINT prompt_tags_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES public.tags(id) ON DELETE RESTRICT;


--
-- Name: prompt_votes prompt_votes_prompt_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_votes
    ADD CONSTRAINT prompt_votes_prompt_id_fkey FOREIGN KEY (prompt_id) REFERENCES public.prompts(id) ON DELETE CASCADE;


--
-- Name: prompt_votes prompt_votes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_votes
    ADD CONSTRAINT prompt_votes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: prompts prompts_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompts
    ADD CONSTRAINT prompts_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id) ON DELETE RESTRICT;


--
-- Name: prompts prompts_original_prompt_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompts
    ADD CONSTRAINT prompts_original_prompt_id_fkey FOREIGN KEY (original_prompt_id) REFERENCES public.prompts(id) ON DELETE SET NULL;


--
-- Name: prompts prompts_tool_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompts
    ADD CONSTRAINT prompts_tool_id_fkey FOREIGN KEY (tool_id) REFERENCES public.tools(id) ON DELETE RESTRICT;


--
-- Name: prompts prompts_updated_by_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompts
    ADD CONSTRAINT prompts_updated_by_user_id_fkey FOREIGN KEY (updated_by_user_id) REFERENCES public.profiles(id) ON DELETE SET NULL;


--
-- Name: prompts prompts_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompts
    ADD CONSTRAINT prompts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE SET NULL;


--
-- Name: saved_prompts saved_prompts_prompt_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.saved_prompts
    ADD CONSTRAINT saved_prompts_prompt_id_fkey FOREIGN KEY (prompt_id) REFERENCES public.prompts(id) ON DELETE CASCADE;


--
-- Name: saved_prompts saved_prompts_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.saved_prompts
    ADD CONSTRAINT saved_prompts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;


--
-- Name: objects objects_bucketId_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT "objects_bucketId_fkey" FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_upload_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_upload_id_fkey FOREIGN KEY (upload_id) REFERENCES storage.s3_multipart_uploads(id) ON DELETE CASCADE;


--
-- Name: audit_log_entries; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.audit_log_entries ENABLE ROW LEVEL SECURITY;

--
-- Name: flow_state; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.flow_state ENABLE ROW LEVEL SECURITY;

--
-- Name: identities; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.identities ENABLE ROW LEVEL SECURITY;

--
-- Name: instances; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.instances ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_amr_claims; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.mfa_amr_claims ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_challenges; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.mfa_challenges ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_factors; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.mfa_factors ENABLE ROW LEVEL SECURITY;

--
-- Name: one_time_tokens; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.one_time_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: refresh_tokens; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.refresh_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_providers; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.saml_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_relay_states; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.saml_relay_states ENABLE ROW LEVEL SECURITY;

--
-- Name: schema_migrations; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.schema_migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: sessions; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.sessions ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_domains; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.sso_domains ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_providers; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.sso_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: users; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

--
-- Name: categories Anyone can view categories; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view categories" ON public.categories FOR SELECT USING (true);


--
-- Name: collection_prompts Anyone can view collection_prompts for public collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view collection_prompts for public collections" ON public.collection_prompts FOR SELECT USING ((EXISTS ( SELECT 1
   FROM public.collections
  WHERE ((collections.id = collection_prompts.collection_id) AND (collections.is_public = true)))));


--
-- Name: comment_votes Anyone can view comment votes; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view comment votes" ON public.comment_votes FOR SELECT USING (true);


--
-- Name: comments Anyone can view comments on public prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view comments on public prompts" ON public.comments FOR SELECT USING ((EXISTS ( SELECT 1
   FROM public.prompts
  WHERE ((prompts.id = comments.prompt_id) AND (prompts.is_public = true)))));


--
-- Name: prompt_tags Anyone can view prompt tags for public prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view prompt tags for public prompts" ON public.prompt_tags FOR SELECT USING ((EXISTS ( SELECT 1
   FROM public.prompts
  WHERE ((prompts.id = prompt_tags.prompt_id) AND (prompts.is_public = true)))));


--
-- Name: collections Anyone can view public collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view public collections" ON public.collections FOR SELECT USING ((is_public = true));


--
-- Name: tags Anyone can view tags; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view tags" ON public.tags FOR SELECT USING (true);


--
-- Name: tools Anyone can view tools; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view tools" ON public.tools FOR SELECT USING (true);


--
-- Name: prompt_votes Anyone can view votes for public prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view votes for public prompts" ON public.prompt_votes FOR SELECT USING ((EXISTS ( SELECT 1
   FROM public.prompts
  WHERE ((prompts.id = prompt_votes.prompt_id) AND (prompts.is_public = true)))));


--
-- Name: collections Private collections are only viewable by their owners; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Private collections are only viewable by their owners" ON public.collections FOR SELECT USING (((auth.uid() = user_id) AND (is_public = false)));


--
-- Name: prompts Private prompts are only viewable by their owners; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Private prompts are only viewable by their owners" ON public.prompts FOR SELECT USING (((auth.uid() = user_id) AND (is_public = false)));


--
-- Name: collections Public collections are viewable by everyone; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Public collections are viewable by everyone" ON public.collections FOR SELECT USING ((is_public = true));


--
-- Name: profiles Public profiles are viewable by everyone; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles FOR SELECT USING (true);


--
-- Name: prompts Public prompts are viewable by everyone; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Public prompts are viewable by everyone" ON public.prompts FOR SELECT USING ((is_public = true));


--
-- Name: collections Users can create their own collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can create their own collections" ON public.collections FOR INSERT WITH CHECK ((user_id = auth.uid()));


--
-- Name: collection_prompts Users can delete prompts from their own collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete prompts from their own collections" ON public.collection_prompts FOR DELETE USING ((EXISTS ( SELECT 1
   FROM public.collections
  WHERE ((collections.id = collection_prompts.collection_id) AND (collections.user_id = auth.uid())))));


--
-- Name: prompt_tags Users can delete tags from their own prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete tags from their own prompts" ON public.prompt_tags FOR DELETE USING ((EXISTS ( SELECT 1
   FROM public.prompts
  WHERE ((prompts.id = prompt_tags.prompt_id) AND (prompts.user_id = auth.uid())))));


--
-- Name: collections Users can delete their own collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete their own collections" ON public.collections FOR DELETE USING ((auth.uid() = user_id));


--
-- Name: comment_votes Users can delete their own comment votes; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete their own comment votes" ON public.comment_votes FOR DELETE USING ((auth.uid() = user_id));


--
-- Name: comments Users can delete their own comments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete their own comments" ON public.comments FOR DELETE USING ((auth.uid() = user_id));


--
-- Name: collections Users can delete their own non-default collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete their own non-default collections" ON public.collections FOR DELETE USING (((user_id = auth.uid()) AND ((is_default = false) OR (is_default IS NULL))));


--
-- Name: prompts Users can delete their own prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete their own prompts" ON public.prompts FOR DELETE USING ((auth.uid() = user_id));


--
-- Name: prompt_votes Users can delete their own votes; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can delete their own votes" ON public.prompt_votes FOR DELETE USING ((auth.uid() = user_id));


--
-- Name: followed_collections Users can follow collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can follow collections" ON public.followed_collections FOR INSERT WITH CHECK ((auth.uid() = follower_id));


--
-- Name: collection_prompts Users can insert prompts into their own collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert prompts into their own collections" ON public.collection_prompts FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM public.collections
  WHERE ((collections.id = collection_prompts.collection_id) AND (collections.user_id = auth.uid())))));


--
-- Name: prompt_tags Users can insert tags for their own prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert tags for their own prompts" ON public.prompt_tags FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM public.prompts
  WHERE ((prompts.id = prompt_tags.prompt_id) AND (prompts.user_id = auth.uid())))));


--
-- Name: collections Users can insert their own collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert their own collections" ON public.collections FOR INSERT WITH CHECK ((auth.uid() = user_id));


--
-- Name: comment_votes Users can insert their own comment votes; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert their own comment votes" ON public.comment_votes FOR INSERT WITH CHECK ((auth.uid() = user_id));


--
-- Name: comments Users can insert their own comments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert their own comments" ON public.comments FOR INSERT WITH CHECK ((auth.uid() = user_id));


--
-- Name: prompts Users can insert their own prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert their own prompts" ON public.prompts FOR INSERT WITH CHECK ((auth.uid() = user_id));


--
-- Name: prompt_votes Users can insert their own votes; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert their own votes" ON public.prompt_votes FOR INSERT WITH CHECK ((auth.uid() = user_id));


--
-- Name: notifications Users can manage their own notifications; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can manage their own notifications" ON public.notifications USING ((auth.uid() = recipient_user_id)) WITH CHECK ((auth.uid() = recipient_user_id));


--
-- Name: saved_prompts Users can manage their own saved prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can manage their own saved prompts" ON public.saved_prompts USING ((auth.uid() = user_id)) WITH CHECK ((auth.uid() = user_id));


--
-- Name: followed_collections Users can unfollow collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can unfollow collections" ON public.followed_collections FOR DELETE USING ((auth.uid() = follower_id));


--
-- Name: collections Users can update their own collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can update their own collections" ON public.collections FOR UPDATE USING ((auth.uid() = user_id));


--
-- Name: comments Users can update their own comments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can update their own comments" ON public.comments FOR UPDATE USING ((auth.uid() = user_id));


--
-- Name: collections Users can update their own non-default collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can update their own non-default collections" ON public.collections FOR UPDATE USING (((user_id = auth.uid()) AND ((is_default = false) OR (is_default IS NULL))));


--
-- Name: profiles Users can update their own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can update their own profile" ON public.profiles FOR UPDATE USING ((auth.uid() = id));


--
-- Name: prompts Users can update their own prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can update their own prompts" ON public.prompts FOR UPDATE USING ((auth.uid() = user_id));


--
-- Name: prompt_votes Users can update their own votes; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can update their own votes" ON public.prompt_votes FOR UPDATE USING ((auth.uid() = user_id));


--
-- Name: collection_prompts Users can view collection_prompts for their own private collect; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view collection_prompts for their own private collect" ON public.collection_prompts FOR SELECT USING ((EXISTS ( SELECT 1
   FROM public.collections
  WHERE ((collections.id = collection_prompts.collection_id) AND (collections.is_public = false) AND (collections.user_id = auth.uid())))));


--
-- Name: prompt_tags Users can view tags for their own private prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view tags for their own private prompts" ON public.prompt_tags FOR SELECT USING ((EXISTS ( SELECT 1
   FROM public.prompts
  WHERE ((prompts.id = prompt_tags.prompt_id) AND (prompts.is_public = false) AND (prompts.user_id = auth.uid())))));


--
-- Name: collections Users can view their own collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view their own collections" ON public.collections FOR SELECT USING ((user_id = auth.uid()));


--
-- Name: followed_collections Users can view their own followed collections; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view their own followed collections" ON public.followed_collections FOR SELECT USING ((auth.uid() = follower_id));


--
-- Name: notifications Users can view their own notifications; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view their own notifications" ON public.notifications FOR SELECT USING ((auth.uid() = recipient_user_id));


--
-- Name: saved_prompts Users can view their own saved prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view their own saved prompts" ON public.saved_prompts FOR SELECT USING ((auth.uid() = user_id));


--
-- Name: categories; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

--
-- Name: collection_prompts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.collection_prompts ENABLE ROW LEVEL SECURITY;

--
-- Name: collections; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;

--
-- Name: comment_votes; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.comment_votes ENABLE ROW LEVEL SECURITY;

--
-- Name: comments; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

--
-- Name: notifications; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

--
-- Name: profiles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: prompt_tags; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.prompt_tags ENABLE ROW LEVEL SECURITY;

--
-- Name: prompt_votes; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.prompt_votes ENABLE ROW LEVEL SECURITY;

--
-- Name: prompts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.prompts ENABLE ROW LEVEL SECURITY;

--
-- Name: saved_prompts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.saved_prompts ENABLE ROW LEVEL SECURITY;

--
-- Name: tags; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;

--
-- Name: tools; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.tools ENABLE ROW LEVEL SECURITY;

--
-- Name: messages; Type: ROW SECURITY; Schema: realtime; Owner: -
--

ALTER TABLE realtime.messages ENABLE ROW LEVEL SECURITY;

--
-- Name: objects Allow authenticated delete on own collection images; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Allow authenticated delete on own collection images" ON storage.objects FOR DELETE TO authenticated USING (((bucket_id = 'collection-images'::text) AND (auth.uid() IS NOT NULL) AND ((storage.foldername(name))[1] = (auth.uid())::text)));


--
-- Name: objects Allow authenticated insert on collection images; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Allow authenticated insert on collection images" ON storage.objects FOR INSERT TO authenticated WITH CHECK (((bucket_id = 'collection-images'::text) AND (auth.uid() IS NOT NULL) AND ((storage.foldername(name))[1] = (auth.uid())::text)));


--
-- Name: objects Allow authenticated update on own collection images; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Allow authenticated update on own collection images" ON storage.objects FOR UPDATE TO authenticated USING (((bucket_id = 'collection-images'::text) AND (auth.uid() IS NOT NULL) AND ((storage.foldername(name))[1] = (auth.uid())::text))) WITH CHECK (((bucket_id = 'collection-images'::text) AND (auth.uid() IS NOT NULL) AND ((storage.foldername(name))[1] = (auth.uid())::text)));


--
-- Name: objects Allow public read access on collection images; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Allow public read access on collection images" ON storage.objects FOR SELECT USING ((bucket_id = 'collection-images'::text));


--
-- Name: objects Anyone can view images; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Anyone can view images" ON storage.objects FOR SELECT USING ((bucket_id = 'prompt-images'::text));


--
-- Name: objects Public profile pictures are viewable by everyone; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Public profile pictures are viewable by everyone" ON storage.objects FOR SELECT USING ((bucket_id = 'profile-pictures'::text));


--
-- Name: objects Users can delete their own images; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Users can delete their own images" ON storage.objects FOR DELETE USING (((bucket_id = 'prompt-images'::text) AND ((auth.uid())::text = (storage.foldername(name))[1])));


--
-- Name: objects Users can delete their own profile picture; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Users can delete their own profile picture" ON storage.objects FOR DELETE USING (((bucket_id = 'profile-pictures'::text) AND (auth.role() = 'authenticated'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text)));


--
-- Name: objects Users can update their own images; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Users can update their own images" ON storage.objects FOR UPDATE USING (((bucket_id = 'prompt-images'::text) AND ((auth.uid())::text = (storage.foldername(name))[1])));


--
-- Name: objects Users can update their own profile picture; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Users can update their own profile picture" ON storage.objects FOR UPDATE USING (((bucket_id = 'profile-pictures'::text) AND (auth.role() = 'authenticated'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text)));


--
-- Name: objects Users can upload their own images; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Users can upload their own images" ON storage.objects FOR INSERT WITH CHECK (((bucket_id = 'prompt-images'::text) AND ((auth.uid())::text = (storage.foldername(name))[1])));


--
-- Name: objects Users can upload their own profile picture; Type: POLICY; Schema: storage; Owner: -
--

CREATE POLICY "Users can upload their own profile picture" ON storage.objects FOR INSERT WITH CHECK (((bucket_id = 'profile-pictures'::text) AND (auth.role() = 'authenticated'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text)));


--
-- Name: buckets; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.buckets ENABLE ROW LEVEL SECURITY;

--
-- Name: migrations; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: objects; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.s3_multipart_uploads ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads_parts; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.s3_multipart_uploads_parts ENABLE ROW LEVEL SECURITY;

--
-- Name: supabase_realtime; Type: PUBLICATION; Schema: -; Owner: -
--

CREATE PUBLICATION supabase_realtime WITH (publish = 'insert, update, delete, truncate');


--
-- Name: supabase_realtime_messages_publication; Type: PUBLICATION; Schema: -; Owner: -
--

CREATE PUBLICATION supabase_realtime_messages_publication WITH (publish = 'insert, update, delete, truncate');


--
-- Name: supabase_realtime_messages_publication messages; Type: PUBLICATION TABLE; Schema: realtime; Owner: -
--

ALTER PUBLICATION supabase_realtime_messages_publication ADD TABLE ONLY realtime.messages;


--
-- Name: issue_graphql_placeholder; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_graphql_placeholder ON sql_drop
         WHEN TAG IN ('DROP EXTENSION')
   EXECUTE FUNCTION extensions.set_graphql_placeholder();


--
-- Name: issue_pg_cron_access; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_pg_cron_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_cron_access();


--
-- Name: issue_pg_graphql_access; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_pg_graphql_access ON ddl_command_end
         WHEN TAG IN ('CREATE FUNCTION')
   EXECUTE FUNCTION extensions.grant_pg_graphql_access();


--
-- Name: issue_pg_net_access; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_pg_net_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_net_access();


--
-- Name: pgrst_ddl_watch; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER pgrst_ddl_watch ON ddl_command_end
   EXECUTE FUNCTION extensions.pgrst_ddl_watch();


--
-- Name: pgrst_drop_watch; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER pgrst_drop_watch ON sql_drop
   EXECUTE FUNCTION extensions.pgrst_drop_watch();


--
-- PostgreSQL database dump complete
--

