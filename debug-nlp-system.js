// NLP System Diagnostic Script
// Run this in browser console on /prompt/submit page to diagnose issues

(function() {
  'use strict';
  
  console.log('🔧 NLP System Diagnostic Starting...');
  
  // Track all console activity
  const originalLog = console.log;
  const originalError = console.error;
  const originalWarn = console.warn;
  
  let logs = [];
  let errors = [];
  let nlpLogs = [];
  
  // Intercept all console output
  console.log = function(...args) {
    logs.push({ type: 'log', args, timestamp: Date.now() });
    if (args[0] && typeof args[0] === 'string' && args[0].includes('[NLP]')) {
      nlpLogs.push({ args, timestamp: Date.now() });
    }
    originalLog.apply(console, args);
  };
  
  console.error = function(...args) {
    errors.push({ type: 'error', args, timestamp: Date.now() });
    originalError.apply(console, args);
  };
  
  console.warn = function(...args) {
    errors.push({ type: 'warn', args, timestamp: Date.now() });
    originalWarn.apply(console, args);
  };
  
  // Diagnostic functions
  function checkDOMElements() {
    console.log('\n🔍 DOM Element Check:');
    
    const elements = {
      title: document.getElementById('title'),
      description: document.getElementById('description'), 
      promptTextArea: document.querySelector('[data-color-mode="dark"]'),
      instructions: document.getElementById('instructions'),
      exampleInput: document.getElementById('example-input'),
      exampleOutput: document.getElementById('example-output')
    };
    
    Object.entries(elements).forEach(([name, element]) => {
      if (element) {
        console.log(`  ✅ ${name}: Found (${element.tagName}, value length: ${element.value?.length || 0})`);
      } else {
        console.log(`  ❌ ${name}: Not found`);
      }
    });
    
    return elements;
  }
  
  function checkNLPModules() {
    console.log('\n📦 NLP Module Check:');
    
    // Check if analyzePromptContent exists
    if (typeof window.analyzePromptContent === 'function') {
      console.log('  ✅ analyzePromptContent function exists');
    } else {
      console.log('  ❌ analyzePromptContent function not found');
    }
    
    // Check for any NLP-related globals
    const nlpGlobals = Object.keys(window).filter(key => 
      key.toLowerCase().includes('nlp') || 
      key.toLowerCase().includes('analyze') ||
      key.toLowerCase().includes('keyword') ||
      key.toLowerCase().includes('category')
    );
    
    console.log(`  📋 NLP-related globals: [${nlpGlobals.join(', ')}]`);
  }
  
  function checkEventListeners() {
    console.log('\n🎧 Event Listener Check:');
    
    const elements = checkDOMElements();
    
    // Try to trigger events manually
    Object.entries(elements).forEach(([name, element]) => {
      if (element) {
        const events = ['input', 'change', 'keyup'];
        events.forEach(eventType => {
          try {
            element.dispatchEvent(new Event(eventType, { bubbles: true }));
            console.log(`  ✅ ${name}: ${eventType} event dispatched`);
          } catch (error) {
            console.log(`  ❌ ${name}: ${eventType} event failed - ${error.message}`);
          }
        });
      }
    });
  }
  
  function testFormInput() {
    console.log('\n📝 Form Input Test:');
    
    const promptTextArea = document.querySelector('[data-color-mode="dark"]');
    if (promptTextArea) {
      const testText = 'Create a Python script to analyze data and generate visualizations for machine learning models.';
      
      console.log(`  📄 Setting test content: "${testText}"`);
      promptTextArea.value = testText;
      
      // Trigger multiple events
      ['input', 'change', 'keyup', 'focus', 'blur'].forEach(eventType => {
        promptTextArea.dispatchEvent(new Event(eventType, { bubbles: true }));
      });
      
      console.log('  ⏳ Waiting 10 seconds for NLP analysis...');
      
      setTimeout(() => {
        console.log('\n📊 NLP Analysis Results:');
        console.log(`  🗂️  Total logs captured: ${logs.length}`);
        console.log(`  🔴 Errors captured: ${errors.length}`);
        console.log(`  🧠 NLP logs captured: ${nlpLogs.length}`);
        
        if (nlpLogs.length > 0) {
          console.log('  ✅ NLP logs found:');
          nlpLogs.forEach((log, i) => {
            console.log(`    ${i + 1}. ${log.args.join(' ')}`);
          });
        } else {
          console.log('  ❌ No NLP logs found');
        }
        
        if (errors.length > 0) {
          console.log('  🚨 Errors found:');
          errors.forEach((error, i) => {
            console.log(`    ${i + 1}. [${error.type}] ${error.args.join(' ')}`);
          });
        }
        
        // Restore original console
        console.log = originalLog;
        console.error = originalError;
        console.warn = originalWarn;
        
        generateDiagnosticReport();
        
      }, 10000);
      
    } else {
      console.log('  ❌ Prompt textarea not found');
    }
  }
  
  function generateDiagnosticReport() {
    console.log('\n🎯 DIAGNOSTIC REPORT');
    console.log('===================');
    
    const report = {
      timestamp: new Date().toISOString(),
      domElements: checkDOMElements(),
      totalLogs: logs.length,
      nlpLogs: nlpLogs.length,
      errors: errors.length,
      hasAnalyzeFunction: typeof window.analyzePromptContent === 'function',
      pageURL: window.location.href,
      userAgent: navigator.userAgent
    };
    
    console.log('📋 Summary:');
    console.log(`  Page: ${report.pageURL}`);
    console.log(`  Analyze Function: ${report.hasAnalyzeFunction ? '✅ Found' : '❌ Missing'}`);
    console.log(`  DOM Elements: ${Object.values(report.domElements).filter(Boolean).length}/6 found`);
    console.log(`  Total Logs: ${report.totalLogs}`);
    console.log(`  NLP Logs: ${report.nlpLogs}`);
    console.log(`  Errors: ${report.errors}`);
    
    if (report.nlpLogs === 0) {
      console.log('\n🚨 ISSUE IDENTIFIED:');
      console.log('   NLP system is not producing any output');
      console.log('   Possible causes:');
      console.log('   1. NLP modules not loaded');
      console.log('   2. Event listeners not attached');
      console.log('   3. Minimum character/time thresholds not met');
      console.log('   4. JavaScript errors preventing execution');
      console.log('   5. Form inputs not properly detected');
    }
    
    // Export for analysis
    window.nlpDiagnosticReport = report;
    console.log('\n💾 Full report saved to: window.nlpDiagnosticReport');
  }
  
  // Start diagnostics
  console.log('🚀 Running diagnostics...');
  checkDOMElements();
  checkNLPModules();
  checkEventListeners();
  
  // Wait a moment then test form input
  setTimeout(() => {
    testFormInput();
  }, 2000);
  
})(); 