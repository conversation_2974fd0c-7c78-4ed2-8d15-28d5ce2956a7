// Automated NLP Test Suite for Submit Page
// Comprehensive testing of category, tool, and tag suggestions
// Run this in browser console on /prompt/submit page

(function() {
  'use strict';
  
  console.log('🧪 Starting Automated NLP Test Suite...');
  
  // Test cases covering all categories, tools, and various scenarios
  const testCases = [
    // CREATIVE WRITING TESTS
    {
      id: 'CW-001',
      name: 'Fantasy Story - Highly Specific',
      title: 'Epic Dragon Quest Generator',
      description: 'Generate compelling fantasy adventures with dragons, magic systems, and heroic quests across multiple realms.',
      promptText: 'Create an epic fantasy story featuring a young dragon rider who must save their kingdom from an ancient evil. Include detailed descriptions of the magic system, character development arcs, and world-building elements that make the story immersive.',
      expected: {
        category: 'creative-writing',
        tool: 'chatgpt', 
        tags: ['fantasy', 'creative', 'story', 'character']
      }
    },
    {
      id: 'CW-002',
      name: 'Creative Writing - Ambiguous',
      title: 'Story Helper',
      description: null,
      promptText: 'Help me write something creative and interesting.',
      expected: {
        category: 'creative-writing',
        tool: 'chatgpt',
        tags: ['creative', 'story']
      }
    },
    {
      id: 'CW-003',
      name: 'Fiction Writing - Medium Specificity',
      title: 'Character Development Assistant',
      description: 'Create compelling fictional characters with depth and believable motivations.',
      promptText: 'Design a complex protagonist for a Victorian-era mystery novel. Include their background, personality flaws, motivations, and how they evolve throughout the story.',
      expected: {
        category: 'fiction',
        tool: 'chatgpt',
        tags: ['character', 'fiction', 'creative']
      }
    },

    // CODE GENERATION TESTS
    {
      id: 'CG-001',
      name: 'Python Algorithm - Very Specific',
      title: 'Advanced Sorting Algorithm Implementation',
      description: 'Generate optimized Python code for complex data structures and algorithms with performance analysis.',
      promptText: 'Create a Python implementation of merge sort that includes time complexity analysis, memory optimization, and comparison with other sorting algorithms. Add detailed comments and examples.',
      expected: {
        category: 'code-generation',
        tool: 'chatgpt',
        tags: ['python', 'algorithm', 'optimization']
      }
    },
    {
      id: 'CG-002',
      name: 'JavaScript - Ambiguous Request',
      title: 'Fix my code',
      description: null,
      promptText: 'My JavaScript function isn\'t working properly. Help debug.',
      expected: {
        category: 'code-generation',
        tool: 'chatgpt',
        tags: ['javascript', 'debugging']
      }
    },
    {
      id: 'CG-003',
      name: 'React Component - Medium Specificity',
      title: 'Interactive Dashboard Component',
      description: 'Build reusable React components for data visualization dashboards.',
      promptText: 'Create a React component that displays real-time analytics data with interactive charts, filters, and responsive design.',
      expected: {
        category: 'code-generation',
        tool: 'chatgpt',
        tags: ['react', 'javascript', 'frontend']
      }
    },

    // IMAGE GENERATION TESTS
    {
      id: 'IG-001',
      name: 'Midjourney - Explicit Pattern',
      title: 'Cyberpunk Portrait Generator',
      description: 'Generate stunning cyberpunk character portraits with neon aesthetics.',
      promptText: 'portrait of a cyberpunk hacker, neon lighting, futuristic cityscape background, high detail, cinematic lighting --ar 16:9 --v 6',
      expected: {
        category: 'image-generation',
        tool: 'midjourney',
        tags: ['cyberpunk', 'portrait', 'futuristic']
      }
    },
    {
      id: 'IG-002',
      name: 'DALL-E Style - Artistic Description',
      title: 'Surreal Landscape Creator',
      description: 'Create dreamlike landscapes with impossible architecture and floating elements.',
      promptText: 'A photorealistic surreal landscape with floating islands, impossible waterfalls flowing upward, and crystalline structures defying gravity, golden hour lighting.',
      expected: {
        category: 'image-generation',
        tool: 'dall-e',
        tags: ['photorealistic', 'landscape', 'surreal']
      }
    },
    {
      id: 'IG-003',
      name: 'Stable Diffusion - Technical Prompt',
      title: 'Professional Headshot Generator',
      description: null,
      promptText: 'professional business headshot, corporate lighting, neutral background, high resolution, detailed facial features, confident expression',
      expected: {
        category: 'image-generation',
        tool: 'stable-diffusion',
        tags: ['professional', 'portrait', 'business']
      }
    },

    // MARKETING TESTS
    {
      id: 'MK-001',
      name: 'Marketing Campaign - Comprehensive',
      title: 'Complete Marketing Campaign Generator',
      description: 'Create full-scale marketing campaigns with multiple touchpoints and audience targeting.',
      promptText: 'Develop a comprehensive marketing campaign for a sustainable fashion brand targeting Gen Z consumers. Include social media strategy, content calendar, influencer partnerships, and conversion funnel.',
      expected: {
        category: 'marketing',
        tool: 'jasper-ai',
        tags: ['campaign', 'social-media', 'copywriting']
      }
    },
    {
      id: 'MK-002',
      name: 'Simple Ad Copy',
      title: 'Ad Copy',
      description: null,
      promptText: 'Write compelling ad copy for our new product.',
      expected: {
        category: 'marketing',
        tool: 'chatgpt',
        tags: ['ad-copy', 'persuasive']
      }
    },

    // BUSINESS TESTS
    {
      id: 'BZ-001',
      name: 'Business Strategy - Detailed',
      title: 'Strategic Business Plan Assistant',
      description: 'Comprehensive business strategy development with market analysis and growth projections.',
      promptText: 'Create a detailed business plan for a SaaS startup in the project management space. Include market analysis, competitive landscape, revenue projections, and go-to-market strategy with specific metrics and timelines.',
      expected: {
        category: 'business',
        tool: 'claude',
        tags: ['strategy', 'analysis', 'professional']
      }
    },

    // RESEARCH TESTS
    {
      id: 'RS-001',
      name: 'Academic Research - Complex',
      title: 'Literature Review Generator',
      description: 'Comprehensive academic research assistance with citation analysis.',
      promptText: 'Conduct a systematic literature review on the impact of artificial intelligence on employment patterns in the manufacturing sector. Include methodology, key findings, gaps in research, and recommendations for future studies with proper academic citations.',
      expected: {
        category: 'research',
        tool: 'perplexity-ai',
        tags: ['academic', 'literature', 'analysis']
      }
    },
    {
      id: 'RS-002',
      name: 'Simple Research Query',
      title: 'Quick Research Help',
      description: null,
      promptText: 'Find recent statistics about renewable energy adoption.',
      expected: {
        category: 'research',
        tool: 'perplexity-ai',
        tags: ['research', 'information']
      }
    },

    // AUDIO TESTS
    {
      id: 'AU-001',
      name: 'Music Generation - Suno Style',
      title: 'Epic Orchestral Soundtrack',
      description: 'Create cinematic orchestral music with emotional depth and dynamic arrangements.',
      promptText: 'Compose an epic orchestral piece for a fantasy movie finale. Include soaring melodies, dramatic crescendos, and heroic themes. Style: cinematic, orchestral, emotional',
      expected: {
        category: 'audio',
        tool: 'suno',
        tags: ['music', 'orchestral', 'epic']
      }
    },
    {
      id: 'AU-002',
      name: 'Voice Synthesis - ElevenLabs',
      title: 'Professional Narration Voice',
      description: null,
      promptText: 'Generate a professional voice narration for corporate training video with clear pronunciation and authoritative tone.',
      expected: {
        category: 'audio',
        tool: 'elevenlabs',
        tags: ['voice', 'narration', 'professional']
      }
    },

    // VIDEO TESTS
    {
      id: 'VD-001',
      name: 'Runway Video Generation',
      title: 'Motion Graphics Sequence',
      description: 'Create dynamic video content with smooth transitions and professional quality.',
      promptText: 'Generate a motion graphics sequence showing data flowing through a futuristic interface with smooth animations and cinematic camera movements.',
      expected: {
        category: 'video',
        tool: 'runway',
        tags: ['motion', 'cinematic', 'futuristic']
      }
    },

    // EMAIL TESTS
    {
      id: 'EM-001',
      name: 'Email Campaign - Professional',
      title: 'Customer Onboarding Email Series',
      description: 'Multi-part email sequence for new user engagement and retention.',
      promptText: 'Create a 5-part onboarding email series for a productivity app. Include welcome message, feature tutorials, success stories, engagement tips, and feedback collection with professional tone and clear CTAs.',
      expected: {
        category: 'email',
        tool: 'chatgpt',
        tags: ['email-marketing', 'onboarding', 'professional']
      }
    },

    // EDGE CASES
    {
      id: 'EC-001',
      name: 'Very Short Prompt',
      title: 'Help',
      description: null,
      promptText: 'I need help.',
      expected: {
        category: 'other',
        tool: 'chatgpt',
        tags: ['help']
      }
    },
    {
      id: 'EC-002',
      name: 'Mixed Categories',
      title: 'Multi-Purpose AI Assistant',
      description: 'Combines creative writing, code generation, and marketing in one complex request.',
      promptText: 'Create a Python script that generates marketing copy for creative writing prompts, then write sample fantasy stories using those prompts.',
      expected: {
        category: 'code-generation', // Should prioritize the primary technical task
        tool: 'chatgpt',
        tags: ['python', 'creative', 'marketing']
      }
    },
    {
      id: 'EC-003',
      name: 'Claude-Specific Pattern',
      title: 'Deep Analysis Request',
      description: 'Requires step-by-step reasoning and comprehensive analysis.',
      promptText: 'Analyze the philosophical implications of artificial consciousness from multiple perspectives. Break down the argument step by step, consider counterarguments, and provide a nuanced conclusion based on current research and ethical frameworks.',
      expected: {
        category: 'research',
        tool: 'claude',
        tags: ['analysis', 'detailed', 'research']
      }
    },
    {
      id: 'EC-004',
      name: 'Technical Documentation',
      title: 'API Documentation Generator',
      description: null,
      promptText: 'Generate comprehensive API documentation for a REST API including endpoints, parameters, response examples, and error codes with clear formatting.',
      expected: {
        category: 'code-generation',
        tool: 'chatgpt',
        tags: ['api', 'documentation', 'technical']
      }
    },
    {
      id: 'EC-005',
      name: 'Long Complex Prompt',
      title: 'Enterprise Software Architecture Design',
      description: 'Comprehensive system design for large-scale enterprise applications with microservices, security, and scalability considerations.',
      promptText: 'Design a complete enterprise software architecture for a global e-commerce platform handling millions of users. Include microservices design patterns, database architecture, caching strategies, security implementations, load balancing, disaster recovery, monitoring solutions, CI/CD pipelines, and scalability considerations. Provide detailed diagrams, technology stack recommendations, and implementation timelines with specific performance metrics and cost optimization strategies.',
      expected: {
        category: 'code-generation',
        tool: 'claude',
        tags: ['architecture', 'enterprise', 'scalability']
      }
    }
  ];

  // Test execution function
  async function runTest(testCase) {
    console.log(`\n🧪 Running Test ${testCase.id}: ${testCase.name}`);
    console.log(`📝 Title: "${testCase.title}"`);
    console.log(`📄 Description: ${testCase.description || '(none)'}`);
    console.log(`💬 Prompt Length: ${testCase.promptText.length} chars`);
    
    const input = {
      title: testCase.title,
      description: testCase.description,
      promptLength: testCase.promptText.length,
      hasInstructions: false, // Not used in current tests
      hasExamples: false // Not used in current tests
    };
    
    try {
      console.log('⏳ Waiting for NLP analysis...');
      
      // Use the testing-specific function to bypass suggestion acceptance checks
      const result = await window.analyzePromptContentForTesting(
        testCase.title, 
        testCase.description || '', 
        testCase.promptText
      );
      
      // Evaluate results
      const success = {
        category: result.category === testCase.expected.category,
        tool: result.tool === testCase.expected.tool,
        tags: testCase.expected.tags.some(tag => result.tags.includes(tag)) // At least one expected tag found
      };
      
      console.log('📊 Results:');
      console.log(`  Category: ${result.category} ${success.category ? '✅' : '❌'} (expected: ${testCase.expected.category})`);
      console.log(`  Tool: ${result.tool} ${success.tool ? '✅' : '❌'} (expected: ${testCase.expected.tool})`);
      console.log(`  Tags: ${JSON.stringify(result.tags)} ${success.tags ? '✅' : '❌'} (expected: ${JSON.stringify(testCase.expected.tags)})`);
      console.log(`  Confidence: Category=${result.confidence.category}, Tool=${result.confidence.tool}, Tags=${result.confidence.tags}`);
      
      return {
        testId: testCase.id,
        testName: testCase.name,
        input,
        expected: testCase.expected,
        actual: result,
        success,
        debug: null
      };
    } catch (error) {
      console.error(`❌ Test ${testCase.id} failed:`, error);
      return {
        testId: testCase.id,
        testName: testCase.name,
        input,
        expected: testCase.expected,
        actual: { category: null, tool: null, tags: [], confidence: {} },
        success: { category: false, tool: false, tags: false },
        debug: error.message
      };
    }
  }

  // Main test runner
  async function runAllTests() {
    console.log('🚀 Starting Automated NLP Test Suite');
    console.log(`📋 Running ${testCases.length} test cases...`);
    
    const startTime = Date.now();
    const results = [];
    
    for (const testCase of testCases) {
      const result = await runTest(testCase);
      results.push(result);
      
      // Small delay between tests to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    // Generate summary report
    generateSummaryReport(results, duration);
    
    // Store results globally for export
    window.nlpTestResults = results;
    
    return results;
  }

  // Generate comprehensive summary report
  function generateSummaryReport(results, duration) {
    console.log('\n🎯 TEST SUITE SUMMARY REPORT');
    console.log(' ================================');
    console.log(` 📊 Total Tests: ${results.length}`);
    console.log(` ⏱️  Duration: ${duration}s`);
    console.log(' ');
    
    // Success rates
    const categorySuccesses = results.filter(r => r.success.category).length;
    const toolSuccesses = results.filter(r => r.success.tool).length;
    const tagSuccesses = results.filter(r => r.success.tags).length;
    
    console.log('📈 Success Rates:');
    console.log(`   Category: ${categorySuccesses}/${results.length} (${Math.round(categorySuccesses/results.length*100)}%)`);
    console.log(`   Tool: ${toolSuccesses}/${results.length} (${Math.round(toolSuccesses/results.length*100)}%)`);
    console.log(`   Tags: ${tagSuccesses}/${results.length} (${Math.round(tagSuccesses/results.length*100)}%)`);
    console.log(' ');
    
    // Category performance breakdown
    const categoryBreakdown = {};
    const toolBreakdown = {};
    
    results.forEach(result => {
      const expectedCategory = result.expected.category;
      const expectedTool = result.expected.tool;
      
      if (!categoryBreakdown[expectedCategory]) {
        categoryBreakdown[expectedCategory] = { total: 0, success: 0 };
      }
      categoryBreakdown[expectedCategory].total++;
      if (result.success.category) categoryBreakdown[expectedCategory].success++;
      
      if (!toolBreakdown[expectedTool]) {
        toolBreakdown[expectedTool] = { total: 0, success: 0 };
      }
      toolBreakdown[expectedTool].total++;
      if (result.success.tool) toolBreakdown[expectedTool].success++;
    });
    
    console.log('🏷️  Category Performance:');
    Object.entries(categoryBreakdown).forEach(([category, stats]) => {
      const percentage = Math.round(stats.success / stats.total * 100);
      console.log(`   ${category}: ${stats.success}/${stats.total} (${percentage}%)`);
    });
    console.log(' ');
    
    console.log('🔧 Tool Performance:');
    Object.entries(toolBreakdown).forEach(([tool, stats]) => {
      const percentage = Math.round(stats.success / stats.total * 100);
      console.log(`   ${tool}: ${stats.success}/${stats.total} (${percentage}%)`);
    });
    console.log(' ');
    
    // Show failed tests
    const failedTests = results.filter(r => !r.success.category || !r.success.tool || !r.success.tags);
    if (failedTests.length > 0) {
      console.log(`❌ Failed Tests (${failedTests.length}):`);
      failedTests.forEach(test => {
        console.log(`   ${test.testId}: ${test.testName}`);
        if (!test.success.category) console.log(`     Category: got "${test.actual.category}" expected "${test.expected.category}"`);
        if (!test.success.tool) console.log(`     Tool: got "${test.actual.tool}" expected "${test.expected.tool}"`);
        if (!test.success.tags) console.log(`     Tags: got ${JSON.stringify(test.actual.tags)} expected ${JSON.stringify(test.expected.tags)}`);
      });
    }
    
    // Confidence analysis
    const confidenceLevels = { category: {}, tool: {}, tags: {} };
    results.forEach(result => {
      ['category', 'tool', 'tags'].forEach(type => {
        const conf = result.actual.confidence[type];
        if (conf) {
          if (!confidenceLevels[type][conf]) confidenceLevels[type][conf] = 0;
          confidenceLevels[type][conf]++;
        }
      });
    });
    
    console.log('\n🎯 Average Confidence:');
    ['category', 'tool', 'tags'].forEach(type => {
      const levels = confidenceLevels[type];
      const summary = Object.entries(levels).map(([level, count]) => `${level.charAt(0).toUpperCase() + level.slice(1)}=${count}`).join(', ');
      console.log(`   ${type}: ${summary || 'No data'}`);
    });
    
    console.log('\n💾 Results saved to window.nlpTestResults');
    console.log('📥 Export JSON: copy(JSON.stringify(window.nlpTestResults, null, 2))');
  }

  // Export test suite to global scope
  window.nlpTestSuite = {
    testCases,
    runTest,
    runAllTests,
    generateSummaryReport
  };
  
  console.log('📋 NLP Test Suite loaded!');
  console.log('🎯 Quick start: window.nlpTestSuite.runAllTests()');
  console.log(`📊 Available: ${testCases.length} test cases`);

})(); 