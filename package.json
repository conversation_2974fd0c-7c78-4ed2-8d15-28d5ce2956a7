{"name": "PromptHQ", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.12.3", "engines": {"node": "20.x"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "import-ai-models": "tsx scripts/import-ai-models.ts"}, "dependencies": {"@2toad/profanity": "3.1.1", "@codemirror/commands": "6.8.1", "@codemirror/lang-markdown": "^6.2.5", "@codemirror/language": "6.11.1", "@codemirror/language-data": "^6.1.0", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.28.2", "@hookform/resolvers": "^3.9.1", "@lezer/highlight": "1.2.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "2.49.8", "@uiw/codemirror-themes": "^4.23.0", "@uiw/react-codemirror": "^4.23.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "compromise": "14.14.4", "csv-parse": "5.6.0", "date-fns": "latest", "embla-carousel-react": "8.5.1", "fasttext.wasm": "1.0.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "minisearch": "7.1.2", "next": "15.2.4", "next-themes": "^0.4.4", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "retext-english": "5.0.0", "retext-keywords": "8.0.2", "slugify": "1.6.6", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "unified": "11.0.5", "vaul": "latest", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "16.5.0", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8", "tailwindcss": "^3.4.17", "ts-node": "10.9.2", "tsx": "4.19.4", "typescript": "~5.3.0"}}