-- Profile Pictures Storage Bucket Setup
-- This script creates a storage bucket for user profile pictures with proper security policies

-- 1. Create the profile-pictures storage bucket (skip if already exists)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'profile-pictures',
    'profile-pictures', 
    true,  -- Public bucket so profile pictures can be viewed without auth
    1048576,  -- 1MB file size limit (1024 * 1024 bytes)
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']  -- Only allow image files
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- 2. Create RLS policies for the profile-pictures bucket (drop and recreate to avoid conflicts)

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Public profile pictures are viewable by everyone" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own profile picture" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own profile picture" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own profile picture" ON storage.objects;

-- Policy: Users can view all profile pictures (since bucket is public, this is mainly for consistency)
CREATE POLICY "Public profile pictures are viewable by everyone" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'profile-pictures');

-- Policy: Users can only upload to their own folder
CREATE POLICY "Users can upload their own profile picture" 
ON storage.objects FOR INSERT 
WITH CHECK (
    bucket_id = 'profile-pictures' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Policy: Users can only update their own profile pictures
CREATE POLICY "Users can update their own profile picture" 
ON storage.objects FOR UPDATE 
USING (
    bucket_id = 'profile-pictures' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Policy: Users can only delete their own profile pictures
CREATE POLICY "Users can delete their own profile picture" 
ON storage.objects FOR DELETE 
USING (
    bucket_id = 'profile-pictures' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
);

-- 3. Function to handle profile picture updates
CREATE OR REPLACE FUNCTION public.handle_profile_picture_upload()
RETURNS TRIGGER AS $$
DECLARE
    user_id_from_path TEXT;
    old_avatar_url TEXT;
    new_avatar_url TEXT;
    supabase_url TEXT;
    old_file_path TEXT;
BEGIN
    -- Only process profile-pictures bucket
    IF NEW.bucket_id != 'profile-pictures' THEN
        RETURN NEW;
    END IF;
    
    -- Extract user ID from the file path (first folder in path)
    user_id_from_path := (storage.foldername(NEW.name))[1];
    
    -- Get Supabase URL from app settings (must be configured via configure_supabase_url function)
    supabase_url := current_setting('app.supabase_url', true);

    -- Ensure URL is configured
    IF supabase_url IS NULL OR supabase_url = '' THEN
        RAISE EXCEPTION 'Supabase URL not configured. Run: SELECT public.configure_supabase_url(''https://your-project.supabase.co'');';
    END IF;
    
    -- Construct the new avatar URL
    new_avatar_url := supabase_url || '/storage/v1/object/public/profile-pictures/' || NEW.name;
    
    -- Get the current avatar URL to delete old file if it exists
    SELECT avatar_url INTO old_avatar_url 
    FROM public.profiles 
    WHERE id = user_id_from_path::uuid;
    
    -- Update the user's avatar_url in profiles table
    UPDATE public.profiles 
    SET 
        avatar_url = new_avatar_url,
        updated_at = NOW()
    WHERE id = user_id_from_path::uuid;
    
    -- Delete old profile picture if it exists and is different from new one
    IF old_avatar_url IS NOT NULL 
       AND old_avatar_url != new_avatar_url 
       AND old_avatar_url LIKE '%/storage/v1/object/public/profile-pictures/%' THEN
        
        -- Extract the file path from the old URL
        old_file_path := SUBSTRING(old_avatar_url FROM '.*/storage/v1/object/public/profile-pictures/(.*)$');
        
        -- Delete the old file from storage
        BEGIN
            DELETE FROM storage.objects 
            WHERE bucket_id = 'profile-pictures' 
            AND name = old_file_path;
        EXCEPTION WHEN OTHERS THEN
            -- Log error but don't fail the upload
            RAISE WARNING 'Failed to delete old profile picture: %', SQLERRM;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Function to handle profile picture deletion
CREATE OR REPLACE FUNCTION public.handle_profile_picture_deletion()
RETURNS TRIGGER AS $$
DECLARE
    user_id_from_path TEXT;
BEGIN
    -- Only process profile-pictures bucket
    IF OLD.bucket_id != 'profile-pictures' THEN
        RETURN OLD;
    END IF;
    
    -- Extract user ID from the file path
    user_id_from_path := (storage.foldername(OLD.name))[1];
    
    -- Clear the avatar_url in profiles table if it matches the deleted file
    UPDATE public.profiles 
    SET 
        avatar_url = NULL,
        updated_at = NOW()
    WHERE id = user_id_from_path::uuid
    AND avatar_url LIKE '%' || OLD.name;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create triggers for automatic profile picture management (drop and recreate to avoid conflicts)
DROP TRIGGER IF EXISTS on_profile_picture_upload ON storage.objects;
DROP TRIGGER IF EXISTS on_profile_picture_deletion ON storage.objects;

CREATE TRIGGER on_profile_picture_upload
    AFTER INSERT ON storage.objects
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_profile_picture_upload();

CREATE TRIGGER on_profile_picture_deletion
    AFTER DELETE ON storage.objects
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_profile_picture_deletion();

-- 6. Helper function to generate profile picture upload path
CREATE OR REPLACE FUNCTION public.get_profile_picture_upload_path(file_extension TEXT DEFAULT 'jpg')
RETURNS TEXT AS $$
BEGIN
    -- Generate path: user_id/profile_timestamp.extension
    RETURN auth.uid()::text || '/profile_' || EXTRACT(EPOCH FROM NOW())::bigint || '.' || file_extension;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Function to delete user's current profile picture
CREATE OR REPLACE FUNCTION public.delete_current_profile_picture()
RETURNS BOOLEAN AS $$
DECLARE
    current_avatar_url TEXT;
    file_path TEXT;
    deleted_count INTEGER;
BEGIN
    -- Get current user's avatar URL
    SELECT avatar_url INTO current_avatar_url 
    FROM public.profiles 
    WHERE id = auth.uid();
    
    -- Return false if no avatar exists
    IF current_avatar_url IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Extract file path from URL
    file_path := SUBSTRING(current_avatar_url FROM '.*/storage/v1/object/public/profile-pictures/(.*)$');
    
    -- Delete the file from storage
    DELETE FROM storage.objects 
    WHERE bucket_id = 'profile-pictures' 
    AND name = file_path
    AND (storage.foldername(name))[1] = auth.uid()::text;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Clear avatar_url in profiles table
    IF deleted_count > 0 THEN
        UPDATE public.profiles 
        SET 
            avatar_url = NULL,
            updated_at = NOW()
        WHERE id = auth.uid();
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Grant necessary permissions
GRANT USAGE ON SCHEMA storage TO authenticated;
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;

-- 9. Configuration function to set your Supabase URL (optional)
CREATE OR REPLACE FUNCTION public.configure_supabase_url(url TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Set the Supabase URL as a database setting
    EXECUTE format('ALTER DATABASE %I SET app.supabase_url = %L', current_database(), url);
    RETURN 'Supabase URL configured successfully: ' || url;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Usage Examples:
-- 
-- 1. To upload a profile picture from your application:
--    Use the path returned by: SELECT public.get_profile_picture_upload_path('jpg');
--    Upload to: /profile-pictures/{user_id}/profile_{timestamp}.jpg
--
-- 2. To delete current profile picture:
--    SELECT public.delete_current_profile_picture();
--
-- 3. File naming convention:
--    {user_id}/profile_{unix_timestamp}.{extension}
--    Example: 123e4567-e89b-12d3-a456-426614174000/profile_1703123456.jpg
--
-- 4. Optional: Configure custom Supabase URL (if different from hardcoded):
--    SELECT public.configure_supabase_url('https://your-custom-url.supabase.co'); 