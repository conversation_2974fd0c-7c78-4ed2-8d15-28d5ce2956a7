-- Debug the get_prompts_unified function by checking dependencies

-- Check if all required tables exist
SELECT 'Tables check' as check_name;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('prompts', 'categories', 'tools', 'profiles', 'tags', 'prompt_tags', 'collections', 'collection_prompts', 'prompt_statistics', 'trending_prompts', 'ai_models');

-- Check if prompts table has required columns
SELECT 'Prompts columns check' as check_name;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'prompts'
AND column_name IN ('id', 'short_id', 'title', 'description', 'image_url', 'created_at', 'updated_at', 'is_public', 'view_count', 'primary_tag_id', 'category_id', 'tool_id', 'user_id', 'search_vector', 'ai_model_id');

-- Check if prompt_statistics table exists and has required columns
SELECT 'Prompt statistics check' as check_name;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'prompt_statistics'
AND column_name IN ('id', 'rating', 'comment_count', 'remix_count');

-- Check if trending_prompts table exists
SELECT 'Trending prompts check' as check_name;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'trending_prompts'
AND column_name IN ('id', 'trending_score');

-- Simple test of basic prompt query
SELECT 'Basic prompts query' as check_name;
SELECT COUNT(*) as prompt_count 
FROM prompts 
WHERE is_public = true;

-- Test a simple version of the function logic
SELECT 'Simple function test' as check_name;
SELECT p.id, p.title, p.short_id
FROM prompts p
WHERE p.is_public = true
LIMIT 3;
