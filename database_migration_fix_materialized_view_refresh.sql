-- Migration: Fix materialized view refresh issue
-- This file fixes the concurrent refresh error for mv_trending_prompts and mv_prompt_statistics

-- Step 1: Create a fixed version of the refresh function that doesn't use CONCURRENTLY
CREATE OR REPLACE FUNCTION public.refresh_prompt_caches_fixed() RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Refresh materialized views without CONCURRENTLY to avoid unique index requirement
  REFRESH MATERIALIZED VIEW mv_trending_prompts;
  REFRESH MATERIALIZED VIEW mv_prompt_statistics;
  
  -- Log the refresh
  RAISE NOTICE 'Prompt caches refreshed at % (non-concurrent)', NOW();
END;
$$;

-- Step 2: Create a generic function to refresh individual materialized views
CREATE OR REPLACE FUNCTION public.refresh_materialized_view(view_name text) RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Dynamically refresh the specified materialized view
  EXECUTE format('REFRESH MATERIALIZED VIEW %I', view_name);
  
  -- Log the refresh
  RAISE NOTICE 'Materialized view % refreshed at %', view_name, NOW();
END;
$$;

-- Step 3: Update the original function to use non-concurrent refresh
CREATE OR REPLACE FUNCTION public.refresh_prompt_caches() RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Use non-concurrent refresh to avoid the unique index requirement
  REFRESH MATERIALIZED VIEW mv_trending_prompts;
  REFRESH MATERIALIZED VIEW mv_prompt_statistics;
  
  -- Log the refresh
  RAISE NOTICE 'Prompt caches refreshed at % (updated to non-concurrent)', NOW();
END;
$$;

-- Step 4: Add unique indexes to support concurrent refresh in the future (optional)
-- Only uncomment these if you want to enable concurrent refresh later
-- Note: This will make the initial index creation slower but allow concurrent refresh

-- CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_mv_trending_prompts_id_unique 
-- ON public.mv_trending_prompts (id);

-- CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_mv_prompt_statistics_id_unique 
-- ON public.mv_prompt_statistics (id);

-- Step 5: Grant execute permissions
GRANT EXECUTE ON FUNCTION public.refresh_prompt_caches_fixed() TO service_role;
GRANT EXECUTE ON FUNCTION public.refresh_materialized_view(text) TO service_role;
GRANT EXECUTE ON FUNCTION public.refresh_prompt_caches() TO service_role;

-- Step 6: Add comments
COMMENT ON FUNCTION public.refresh_prompt_caches_fixed() IS 'Fixed version of refresh_prompt_caches that uses non-concurrent refresh to avoid unique index requirement';
COMMENT ON FUNCTION public.refresh_materialized_view(text) IS 'Generic function to refresh individual materialized views';
COMMENT ON FUNCTION public.refresh_prompt_caches() IS 'Updated to use non-concurrent refresh to fix Vercel deployment errors';

-- Step 7: Test the functions (you can run these manually to verify)
-- SELECT public.refresh_prompt_caches_fixed();
-- SELECT public.refresh_materialized_view('mv_trending_prompts');
-- SELECT public.refresh_materialized_view('mv_prompt_statistics'); 