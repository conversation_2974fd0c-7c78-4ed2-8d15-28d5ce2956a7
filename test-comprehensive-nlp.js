// test-comprehensive-nlp.js
// Comprehensive test suite for NLP optimizations

const { analysePrompt } = require('./lib/nlp/index.ts');

async function runComprehensiveTests() {
  console.log('🧠 Comprehensive NLP Optimization Test Suite...\n');

  const testCases = [
    // Original failing cases from brief
    {
      name: 'CAT-IG-001: Midjourney Syntax Detection',
      details: {
        title: 'Epic Fantasy Art',
        promptText: '/imagine prompt: A majestic dragon soaring over ancient castles, cinematic lighting, photorealistic --ar 16:9 --v 6'
      },
      expected: { category: 'image-generation', tool: 'midjourney' },
      priority: 'high'
    },
    {
      name: 'PERF-VS-001: Marketing Slogan Detection',
      details: {
        title: 'Brand Slogan',
        promptText: 'Create a catchy slogan for our new eco-friendly water bottle brand'
      },
      expected: { category: 'marketing', tool: 'chatgpt' },
      priority: 'high'
    },
    {
      name: 'USP-GEN-001: Vague Image Request',
      details: {
        title: 'Cool Robot',
        promptText: 'make picture cool robot'
      },
      expected: { category: 'image-generation', tool: 'dall-e' },
      priority: 'high'
    },
    {
      name: 'CAT-CW-002: Creative Writing Tool Inference',
      details: {
        title: 'Poem Creation',
        promptText: 'Write a beautiful poem about the changing seasons and the passage of time'
      },
      expected: { category: 'creative-writing', tool: 'chatgpt' },
      priority: 'high'
    },
    {
      name: 'CAT-CG-001: Code Generation Tool Inference',
      details: {
        title: 'Python FastAPI',
        promptText: 'Create a Python FastAPI application with user authentication and database integration'
      },
      expected: { category: 'code-generation', tool: 'chatgpt' },
      priority: 'high'
    },
    {
      name: 'CSC-008: Short Prompt Performance',
      details: {
        title: 'List',
        promptText: 'Grocery List'
      },
      expected: { category: 'personal', tool: 'chatgpt' },
      priority: 'high',
      maxTime: 50 // Should be very fast
    },

    // Additional edge cases
    {
      name: 'Edge Case: Very Short Input',
      details: {
        title: 'Help',
        promptText: 'Help me'
      },
      expected: { category: 'other', tool: 'chatgpt' },
      priority: 'medium',
      maxTime: 30
    },
    {
      name: 'Edge Case: Code with Multiple Languages',
      details: {
        title: 'Multi-language Code',
        promptText: 'Create a full-stack application with React frontend, Node.js backend, and Python data processing'
      },
      expected: { category: 'code-generation', tool: 'chatgpt' },
      priority: 'medium'
    },
    {
      name: 'Edge Case: Mixed Category Signals',
      details: {
        title: 'Creative Code',
        promptText: 'Write a creative story about a programmer who discovers their code can generate art'
      },
      expected: { category: 'creative-writing', tool: 'chatgpt' },
      priority: 'medium'
    },
    {
      name: 'Edge Case: Stable Diffusion Syntax',
      details: {
        title: 'SD Art',
        promptText: 'beautiful landscape, (masterpiece:1.2), [detailed:0.8], negative prompt: blurry, low quality'
      },
      expected: { category: 'image-generation', tool: 'stable-diffusion' },
      priority: 'medium'
    },
    {
      name: 'Edge Case: Business Strategy',
      details: {
        title: 'Business Plan',
        promptText: 'Develop a comprehensive business strategy for a tech startup focusing on AI solutions'
      },
      expected: { category: 'business', tool: 'chatgpt' },
      priority: 'medium'
    },
    {
      name: 'Edge Case: Music Generation',
      details: {
        title: 'Song Creation',
        promptText: '[Verse] Walking down the street [Chorus] This is our time to shine [Bridge] Never looking back'
      },
      expected: { category: 'audio', tool: 'suno' },
      priority: 'medium'
    }
  ];

  let totalPassed = 0;
  let highPriorityPassed = 0;
  let totalHighPriority = testCases.filter(t => t.priority === 'high').length;
  let performanceIssues = 0;

  console.log(`Running ${testCases.length} test cases...\n`);

  for (const testCase of testCases) {
    console.log(`📝 Testing: ${testCase.name}`);
    console.log(`   Input: "${testCase.details.promptText}"`);
    console.log(`   Priority: ${testCase.priority.toUpperCase()}`);
    
    const startTime = Date.now();
    
    try {
      const result = await analysePrompt(testCase.details, { 
        includeDebug: false, 
        enableMLFallback: false 
      });
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Check results
      const categoryMatch = result.category === testCase.expected.category;
      const toolMatch = result.tool === testCase.expected.tool;
      const isPass = categoryMatch && toolMatch;
      
      // Performance check
      const maxTime = testCase.maxTime || 100;
      const performanceOk = executionTime <= maxTime;
      
      if (!performanceOk) {
        performanceIssues++;
      }

      console.log(`   Results (${executionTime}ms):`);
      console.log(`     Category: ${result.category || 'None'} ${categoryMatch ? '✅' : '❌'}`);
      console.log(`     Tool: ${result.tool || 'None'} ${toolMatch ? '✅' : '❌'}`);
      console.log(`     Performance: ${performanceOk ? '✅' : '⚠️'} (${executionTime}ms, max: ${maxTime}ms)`);
      console.log(`     Confidence: Cat=${result.confidence.category}, Tool=${result.confidence.tool}`);

      if (isPass) {
        totalPassed++;
        if (testCase.priority === 'high') {
          highPriorityPassed++;
        }
        console.log(`   ✅ PASS`);
      } else {
        console.log(`   ❌ FAIL: Expected cat=${testCase.expected.category}, tool=${testCase.expected.tool}`);
      }

    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
    }

    console.log('');
  }

  // Final report
  const overallPassRate = (totalPassed / testCases.length * 100).toFixed(1);
  const highPriorityPassRate = (highPriorityPassed / totalHighPriority * 100).toFixed(1);

  console.log('🎉 COMPREHENSIVE TEST RESULTS');
  console.log('================================');
  console.log(`Overall Pass Rate: ${totalPassed}/${testCases.length} (${overallPassRate}%)`);
  console.log(`High Priority Pass Rate: ${highPriorityPassed}/${totalHighPriority} (${highPriorityPassRate}%)`);
  console.log(`Performance Issues: ${performanceIssues} tests exceeded time limits`);
  
  console.log('\n📊 Assessment:');
  if (overallPassRate >= 80) {
    console.log('✅ EXCELLENT: System performing at production level');
  } else if (overallPassRate >= 60) {
    console.log('✅ GOOD: Target pass rate achieved, ready for deployment');
  } else {
    console.log('⚠️  NEEDS WORK: Below target pass rate');
  }

  if (highPriorityPassRate >= 90) {
    console.log('✅ CRITICAL CASES: All high-priority cases passing');
  } else {
    console.log('⚠️  CRITICAL CASES: Some high-priority failures need attention');
  }

  if (performanceIssues === 0) {
    console.log('✅ PERFORMANCE: All tests within time limits');
  } else {
    console.log(`⚠️  PERFORMANCE: ${performanceIssues} tests need optimization`);
  }
}

// Run comprehensive tests
runComprehensiveTests().catch(console.error); 