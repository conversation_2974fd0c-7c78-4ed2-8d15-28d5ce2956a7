-- Update the default collection name from "Saved Prompts" to "My Prompts"
-- This updates the function that creates default collections for new users

CREATE OR REPLACE FUNCTION public.create_default_collection_for_new_user() RET<PERSON>NS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
  -- Create a default collection for the new user
  INSERT INTO collections (
    id,
    user_id,
    name,
    description,
    icon,
    color,
    is_public,
    is_default,
    created_at,
    updated_at
  ) VALUES (
    uuid_generate_v4(),
    NEW.id,
    'My Prompts',
    'Your default collection for saved prompts',
    '📌',
    'blue',
    false,
    true,
    NOW(),
    NOW()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error (you can customize this)
    RAISE NOTICE 'Error creating default collection: %', SQLERRM;
    -- Still return NEW to allow the profile creation to succeed
    RETURN NEW;
END;
$$;

-- Also update any existing default collections that have the old name
UPDATE collections
SET name = 'My Prompts'
WHERE name = 'Saved Prompts' AND is_default = true;
