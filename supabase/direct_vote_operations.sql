-- Create a function to handle voting operations directly
CREATE OR REPLACE FUNCTION public.direct_vote_insert(
  p_user_id UUID,
  p_prompt_id UUID,
  p_vote_type SMALLINT
) RETURNS VOID AS $$
BEGIN
  INSERT INTO prompt_votes (user_id, prompt_id, vote_type)
  VALUES (p_user_id, p_prompt_id, p_vote_type);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to handle vote updates
CREATE OR REPLACE FUNCTION public.direct_vote_update(
  p_user_id UUID,
  p_prompt_id UUID,
  p_vote_type SMALLINT
) RETURNS VOID AS $$
BEGIN
  UPDATE prompt_votes
  SET vote_type = p_vote_type
  WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to handle vote deletion
CREATE OR REPLACE FUNCTION public.direct_vote_delete(
  p_user_id UUID,
  p_prompt_id UUID
) RETURNS VOID AS $$
BEGIN
  DELETE FROM prompt_votes
  WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to update statistics after a vote change
CREATE OR REPLACE FUNCTION public.update_prompt_statistics(
  p_prompt_id UUID,
  p_old_vote SMALLINT,
  p_new_vote SMALLINT
) RETURNS VOID AS $$
BEGIN
  -- Handle different vote change scenarios
  IF p_old_vote IS NULL AND p_new_vote = 1 THEN
    -- New upvote
    UPDATE prompt_statistics
    SET rating = rating + 1, upvotes = upvotes + 1
    WHERE id = p_prompt_id;
  ELSIF p_old_vote IS NULL AND p_new_vote = -1 THEN
    -- New downvote
    UPDATE prompt_statistics
    SET rating = rating - 1, downvotes = downvotes + 1
    WHERE id = p_prompt_id;
  ELSIF p_old_vote = 1 AND p_new_vote IS NULL THEN
    -- Remove upvote
    UPDATE prompt_statistics
    SET rating = rating - 1, upvotes = upvotes - 1
    WHERE id = p_prompt_id;
  ELSIF p_old_vote = -1 AND p_new_vote IS NULL THEN
    -- Remove downvote
    UPDATE prompt_statistics
    SET rating = rating + 1, downvotes = downvotes - 1
    WHERE id = p_prompt_id;
  ELSIF p_old_vote = 1 AND p_new_vote = -1 THEN
    -- Change from upvote to downvote
    UPDATE prompt_statistics
    SET rating = rating - 2, upvotes = upvotes - 1, downvotes = downvotes + 1
    WHERE id = p_prompt_id;
  ELSIF p_old_vote = -1 AND p_new_vote = 1 THEN
    -- Change from downvote to upvote
    UPDATE prompt_statistics
    SET rating = rating + 2, upvotes = upvotes + 1, downvotes = downvotes - 1
    WHERE id = p_prompt_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
