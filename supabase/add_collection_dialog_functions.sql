-- Function to add a prompt to multiple collections
CREATE OR REPLACE FUNCTION add_prompt_to_multiple_collections(
    p_user_id UUID,
    p_prompt_id UUID,
    p_collection_ids UUID[]
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    collection_id_to_add UUID;
    current_collection RECORD;
    prompt_author_id UUID;
BEGIN
    -- Get the author of the prompt
    SELECT user_id INTO prompt_author_id FROM prompts WHERE id = p_prompt_id;

    IF NOT FOUND THEN
        RAISE WARNING 'Prompt with ID % not found.', p_prompt_id;
        RETURN FALSE;
    END IF;

    FOREACH collection_id_to_add IN ARRAY p_collection_ids
    LOOP
        -- Check if the collection exists and belongs to the user
        SELECT * INTO current_collection FROM collections 
        WHERE id = collection_id_to_add AND user_id = p_user_id;

        IF NOT FOUND THEN
            RAISE WARNING 'Collection with ID % not found or does not belong to user %.', collection_id_to_add, p_user_id;
            CONTINUE; -- Skip this collection and try the next one
        END IF;
        
        -- Add the prompt to this collection, do nothing if already exists
        INSERT INTO collection_prompts (collection_id, prompt_id)
        VALUES (collection_id_to_add, p_prompt_id)
        ON CONFLICT (collection_id, prompt_id) DO NOTHING;
    END LOOP;

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error in add_prompt_to_multiple_collections: %', SQLERRM;
        RETURN FALSE;
END;
$$;

-- Enhanced function to create a custom collection with proper validation and slug generation
CREATE OR REPLACE FUNCTION create_custom_collection(
  p_user_id UUID,
  p_name TEXT,
  p_description TEXT DEFAULT NULL,
  p_icon_url TEXT DEFAULT NULL,
  p_is_public BOOLEAN DEFAULT FALSE
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_collection_id UUID;
  collection_slug TEXT;
  base_slug TEXT;
  counter INT := 1;
BEGIN
  -- Name validation: Check for NULL, empty, or too short names
  IF p_name IS NULL OR TRIM(p_name) = '' OR LENGTH(TRIM(p_name)) < 3 THEN
    RAISE EXCEPTION 'Collection name must be at least 3 characters.';
  END IF;
  
  -- Generate a base slug from the name
  base_slug := LOWER(REGEXP_REPLACE(TRIM(p_name), '[^a-zA-Z0-9]+', '-', 'g'));
  -- Remove leading and trailing hyphens
  base_slug := TRIM(BOTH '-' FROM base_slug);
  
  -- Initialize slug to the base slug
  collection_slug := base_slug;
  
  -- Handle potential duplicate slugs by appending a counter
  WHILE EXISTS (
    SELECT 1 FROM collections 
    WHERE user_id = p_user_id AND slug = collection_slug
  ) LOOP
    counter := counter + 1;
    collection_slug := base_slug || '-' || counter::TEXT;
  END LOOP;
  
  -- Insert the new collection
  BEGIN
    INSERT INTO public.collections (
      user_id, 
      name, 
      slug,
      description, 
      icon, 
      is_public, 
      is_default, 
      default_type
    )
    VALUES (
      p_user_id, 
      p_name, 
      collection_slug,
      p_description, 
      p_icon_url, 
      p_is_public, 
      FALSE, 
      NULL
    )
    RETURNING id INTO new_collection_id;
    
    RETURN new_collection_id;
  EXCEPTION
    WHEN unique_violation THEN
      -- This shouldn't happen with our slug generation logic, but just in case
      RAISE EXCEPTION 'A collection with this name already exists.';
    WHEN OTHERS THEN
      RAISE EXCEPTION 'Error creating collection: %', SQLERRM;
  END;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION add_prompt_to_multiple_collections(UUID, UUID, UUID[]) TO authenticated;
GRANT EXECUTE ON FUNCTION create_custom_collection(UUID, TEXT, TEXT, TEXT, BOOLEAN) TO authenticated;
