-- Enable appropriate RLS policies for prompt_votes table

-- First, check if R<PERSON> is enabled
SELECT tablename, rowsecurity FROM pg_tables WHERE tablename = 'prompt_votes';

-- Create or replace RLS policy for inserting votes
CREATE OR REPLACE POLICY "Users can insert their own votes"
ON prompt_votes
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Create or replace RLS policy for updating votes
CREATE OR REPLACE POLICY "Users can update their own votes"
ON prompt_votes
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

-- Create or replace RLS policy for deleting votes
CREATE OR REPLACE POLICY "Users can delete their own votes"
ON prompt_votes
FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

-- Create or replace RLS policy for selecting votes
CREATE OR REPLACE POLICY "Users can view their own votes"
ON prompt_votes
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Create or replace RLS policy for service role to manage all votes
CREATE OR REPLACE POLICY "Service role can manage all votes"
ON prompt_votes
TO service_role
USING (true)
WITH CHECK (true);
