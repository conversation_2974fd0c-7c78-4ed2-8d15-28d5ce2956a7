-- Add a unique constraint to the ai_models table
-- This will prevent duplicate entries with the same provider and tool_name

-- First, check if there are any duplicate entries
SELECT provider, tool_name, COUNT(*)
FROM public.ai_models
GROUP BY provider, tool_name
HAVING COUNT(*) > 1;

-- If there are duplicates, you'll need to handle them before adding the constraint
-- For example, you could keep the first entry and delete the rest:
-- DELETE FROM public.ai_models a
-- USING (
--   SELECT id, provider, tool_name,
--     ROW_NUMBER() OVER (PARTITION BY provider, tool_name ORDER BY id) as row_num
--   FROM public.ai_models
-- ) b
-- WHERE a.id = b.id AND b.row_num > 1;

-- Now add the unique constraint
ALTER TABLE public.ai_models
ADD CONSTRAINT ai_models_provider_tool_name_key UNIQUE (provider, tool_name);
