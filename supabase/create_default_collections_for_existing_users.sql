-- Migration script to create default collections for existing users
-- This script creates the two standard default collections ("Saved Prompts" and "My Prompts")
-- for all existing users who don't already have them

-- First, create the function and trigger to ensure future users get default collections
CREATE OR REPLACE FUNCTION public.create_default_collections_for_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id UUID;
BEGIN
  new_user_id := NEW.id; -- Get the user ID from the profiles table

  -- Create "Saved Prompts" collection
  INSERT INTO public.collections (
    user_id, 
    name, 
    description, 
    icon, 
    is_public, 
    is_default, 
    default_type
  )
  VALUES (
    new_user_id,
    'Saved Prompts',
    'A collection of your saved prompts from other users.',
    '/images/collection-Saved-Prompts.png',
    FALSE, -- Always private
    TRUE,  -- Is a default collection
    'saved_prompts'
  );

  -- Create "My Prompts" collection
  INSERT INTO public.collections (
    user_id, 
    name, 
    description, 
    icon, 
    is_public, 
    is_default, 
    default_type
  )
  VALUES (
    new_user_id,
    'My Prompts',
    'Your own submitted prompts live here.',
    '/images/collection-my-prompt.png',
    FALSE, -- Always private
    TRUE,  -- Is a default collection
    'my_prompts'
  );

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error creating default collections: %', SQLERRM;
    RETURN NEW; -- Still allow profile creation to succeed
END;
$$;

-- Ensure the trigger is correctly set up on the 'profiles' table
DROP TRIGGER IF EXISTS on_new_profile_create_default_collections ON public.profiles;
CREATE TRIGGER on_new_profile_create_default_collections
  AFTER INSERT ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.create_default_collections_for_new_user();

-- Now, migrate existing users:
-- This will create default collections for all existing users who don't already have them

-- Add the required columns if they don't exist
ALTER TABLE public.collections
ADD COLUMN IF NOT EXISTS is_default BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS default_type TEXT DEFAULT NULL;

-- Fix existing data to ensure it meets the constraints we want to add
-- Set default_type to NULL for any collections that have is_default = FALSE but default_type is not NULL
UPDATE public.collections
SET default_type = NULL
WHERE is_default = FALSE AND default_type IS NOT NULL;

-- For collections with is_default = TRUE but default_type is NULL,
-- we can either set them to a default value or make them non-default
-- Here we'll make them non-default as it's safer
UPDATE public.collections
SET is_default = FALSE
WHERE is_default = TRUE AND default_type IS NULL;

-- First, identify and address the unique constraint that's causing the issue
-- The 'unique_default_collection_per_user' constraint only allows one default collection per user
ALTER TABLE public.collections
DROP CONSTRAINT IF EXISTS unique_default_collection_per_user;

-- Replace with a new partial unique index that allows multiple default collections per user, but only one per type
DROP INDEX IF EXISTS idx_unique_default_collection_type_per_user;
CREATE UNIQUE INDEX idx_unique_default_collection_type_per_user
ON public.collections (user_id, default_type)
WHERE is_default = TRUE AND default_type IS NOT NULL;

-- Now add constraints to ensure data integrity
ALTER TABLE public.collections
DROP CONSTRAINT IF EXISTS chk_default_type,
ADD CONSTRAINT chk_default_type CHECK (
  (is_default = TRUE AND default_type IS NOT NULL) OR 
  (is_default = FALSE AND default_type IS NULL)
);

ALTER TABLE public.collections
DROP CONSTRAINT IF EXISTS chk_valid_default_type,
ADD CONSTRAINT chk_valid_default_type CHECK (
  default_type IN ('saved_prompts', 'my_prompts') OR default_type IS NULL
);

-- Migration function for existing users
DO $$
DECLARE
  user_record RECORD;
  existing_collection RECORD;
  saved_prompt_exists BOOLEAN;
  my_prompt_exists BOOLEAN;
  potential_saved_collection UUID;
  potential_my_collection UUID;
BEGIN
  -- Process each user in the profiles table
  FOR user_record IN SELECT id FROM public.profiles
  LOOP
    -- Initialize variables
    potential_saved_collection := NULL;
    potential_my_collection := NULL;
    
    -- Check if the user already has explicitly marked default collections
    SELECT EXISTS (
      SELECT 1 FROM public.collections 
      WHERE user_id = user_record.id 
      AND is_default = TRUE 
      AND default_type = 'saved_prompts'
    ) INTO saved_prompt_exists;

    SELECT EXISTS (
      SELECT 1 FROM public.collections 
      WHERE user_id = user_record.id 
      AND is_default = TRUE 
      AND default_type = 'my_prompts'
    ) INTO my_prompt_exists;

    -- If no default 'saved_prompts' collection exists, look for potential candidates to convert
    -- For example, existing collections with names like 'Saved Prompts', 'Saved', etc.
    IF NOT saved_prompt_exists THEN
      SELECT id INTO potential_saved_collection
      FROM public.collections
      WHERE user_id = user_record.id
        AND is_default = FALSE
        AND (
          LOWER(name) LIKE '%saved%' 
          OR LOWER(name) LIKE '%bookmark%'
          OR LOWER(name) LIKE '%favorite%'
        )
      LIMIT 1;
      
      IF potential_saved_collection IS NOT NULL THEN
        -- Update existing collection to be a default collection
        UPDATE public.collections
        SET 
          name = 'Saved Prompts',
          description = 'A collection of your saved prompts from other users.',
          icon = '/images/collection-Saved-Prompts.png',
          is_default = TRUE,
          default_type = 'saved_prompts'
        WHERE id = potential_saved_collection;
        RAISE NOTICE 'Converted existing collection to "Saved Prompts" for user: %', user_record.id;
      ELSE
        -- Create new "Saved Prompts" collection
        INSERT INTO public.collections (
          user_id, 
          name, 
          description, 
          icon, 
          is_public, 
          is_default, 
          default_type
        )
        VALUES (
          user_record.id,
          'Saved Prompts',
          'A collection of your saved prompts from other users.',
          '/images/collection-Saved-Prompts.png',
          FALSE,
          TRUE,
          'saved_prompts'
        );
        RAISE NOTICE 'Created "Saved Prompts" for user: %', user_record.id;
      END IF;
    END IF;

    -- If no default 'my_prompts' collection exists, look for potential candidates to convert
    -- For example, existing collections with names like 'My Prompts', 'My Collection', etc.
    IF NOT my_prompt_exists THEN
      SELECT id INTO potential_my_collection
      FROM public.collections
      WHERE user_id = user_record.id
        AND is_default = FALSE
        AND (
          LOWER(name) LIKE '%my%' 
          OR LOWER(name) LIKE '%own%'
          OR LOWER(name) LIKE '%personal%'
        )
      LIMIT 1;
      
      IF potential_my_collection IS NOT NULL THEN
        -- Update existing collection to be a default collection
        UPDATE public.collections
        SET 
          name = 'My Prompts',
          description = 'Your own submitted prompts live here.',
          icon = '/images/collection-my-prompt.png',
          is_default = TRUE,
          default_type = 'my_prompts'
        WHERE id = potential_my_collection;
        RAISE NOTICE 'Converted existing collection to "My Prompts" for user: %', user_record.id;
      ELSE
        -- Create new "My Prompts" collection
        INSERT INTO public.collections (
          user_id, 
          name, 
          description, 
          icon, 
          is_public, 
          is_default, 
          default_type
        )
        VALUES (
          user_record.id,
          'My Prompts',
          'Your own submitted prompts live here.',
          '/images/collection-my-prompt.png',
          FALSE,
          TRUE,
          'my_prompts'
        );
        RAISE NOTICE 'Created "My Prompts" for user: %', user_record.id;
      END IF;
    END IF;
  END LOOP;
END
$$;
