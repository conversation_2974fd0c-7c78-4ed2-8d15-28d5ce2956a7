-- Function to check if a vote exists
CREATE OR <PERSON><PERSON>LACE FUNCTION check_vote_exists(
  user_id_param UUID,
  prompt_id_param UUID
) RETURNS INTEGER AS $$
DECLARE
  vote_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO vote_count
  FROM prompt_votes
  WHERE user_id = user_id_param AND prompt_id = prompt_id_param;
  
  RETURN vote_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get vote type
CREATE OR REPLACE FUNCTION get_vote_type(
  user_id_param UUID,
  prompt_id_param UUID
) RETURNS SMALLINT AS $$
DECLARE
  vote_type_val SMALLINT;
BEGIN
  SELECT vote_type INTO vote_type_val
  FROM prompt_votes
  WHERE user_id = user_id_param AND prompt_id = prompt_id_param;
  
  RETURN vote_type_val;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to insert a vote
CREATE OR REPLACE FUNCTION insert_vote(
  user_id_param UUID,
  prompt_id_param UUID,
  vote_type_param SMALLINT
) RETURNS VOID AS $$
BEGIN
  INSERT INTO prompt_votes (user_id, prompt_id, vote_type)
  VALUES (user_id_param, prompt_id_param, vote_type_param);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update a vote
CREATE OR REPLACE FUNCTION update_vote(
  user_id_param UUID,
  prompt_id_param UUID,
  vote_type_param SMALLINT
) RETURNS VOID AS $$
BEGIN
  UPDATE prompt_votes
  SET vote_type = vote_type_param
  WHERE user_id = user_id_param AND prompt_id = prompt_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove a vote
CREATE OR REPLACE FUNCTION remove_vote(
  user_id_param UUID,
  prompt_id_param UUID
) RETURNS VOID AS $$
BEGIN
  DELETE FROM prompt_votes
  WHERE user_id = user_id_param AND prompt_id = prompt_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update prompt statistics
CREATE OR REPLACE FUNCTION update_prompt_stats(
  prompt_id_param UUID,
  old_vote_param SMALLINT,
  new_vote_param SMALLINT
) RETURNS VOID AS $$
BEGIN
  -- Handle different vote change scenarios
  IF old_vote_param IS NULL AND new_vote_param = 1 THEN
    -- New upvote
    UPDATE prompt_statistics
    SET rating = rating + 1, upvotes = upvotes + 1
    WHERE id = prompt_id_param;
  ELSIF old_vote_param IS NULL AND new_vote_param = -1 THEN
    -- New downvote
    UPDATE prompt_statistics
    SET rating = rating - 1, downvotes = downvotes + 1
    WHERE id = prompt_id_param;
  ELSIF old_vote_param = 1 AND new_vote_param IS NULL THEN
    -- Remove upvote
    UPDATE prompt_statistics
    SET rating = rating - 1, upvotes = upvotes - 1
    WHERE id = prompt_id_param;
  ELSIF old_vote_param = -1 AND new_vote_param IS NULL THEN
    -- Remove downvote
    UPDATE prompt_statistics
    SET rating = rating + 1, downvotes = downvotes - 1
    WHERE id = prompt_id_param;
  ELSIF old_vote_param = 1 AND new_vote_param = -1 THEN
    -- Change from upvote to downvote
    UPDATE prompt_statistics
    SET rating = rating - 2, upvotes = upvotes - 1, downvotes = downvotes + 1
    WHERE id = prompt_id_param;
  ELSIF old_vote_param = -1 AND new_vote_param = 1 THEN
    -- Change from downvote to upvote
    UPDATE prompt_statistics
    SET rating = rating + 2, upvotes = upvotes + 1, downvotes = downvotes - 1
    WHERE id = prompt_id_param;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
