-- SQL script to import AI models from the CSV data
-- This script should be run in the Supabase SQL editor

-- First, create a temporary table to hold the CSV data
CREATE TEMP TABLE temp_ai_models (
  provider TEXT,
  model_name TEXT
);

-- Insert data from the CSV file
INSERT INTO temp_ai_models (provider, model_name) VALUES
('Adobe', 'Firefly Image Model 4 74'),
('Adobe', 'Firefly Vector Model 74'),
('Adobe', 'Firefly Video Model 74'),
('AI21 Labs', 'Jamba Large 1.6'),
('AI21 Labs', 'Jamba Mini 1.6'),
('Alibaba', 'Qwen 3 76'),
('<PERSON><PERSON><PERSON>', 'Wan2.1-VACE (1.3B)'),
('<PERSON><PERSON><PERSON>', 'Wan2.1-VACE (14B)'),
('Anthropic', 'Claude 3 Haiku'),
('Anthropic', 'Claude 3 Opus'),
('Anthropic', 'Claude 3.5 Haiku'),
('Anthropic', 'Claude 3.5 Sonnet 10'),
('Anthropic', 'Claude 3.7 Sonnet'),
('Cohere', 'Aya'),
('Cohere', 'Command A'),
('Cohere', 'Command R'),
('Cohere', 'Command R+'),
('DeepSeek', 'DeepSeek Coder V2'),
('DeepSeek', 'DeepSeek-Math (RL)'),
('DeepSeek', 'DeepSeek-Prover-V2 (7B)'),
('DeepSeek', 'DeepSeek-Prover-V2 (671B)'),
('DeepSeek', 'DeepSeek-R1'),
('DeepSeek', 'DeepSeek-R1-Distill-Llama-8B'),
('DeepSeek', 'DeepSeek-R1-Distill-Llama-70B'),
('DeepSeek', 'DeepSeek-R1-Distill-Qwen-1.5B'),
('DeepSeek', 'DeepSeek-R1-Distill-Qwen-7B'),
('DeepSeek', 'DeepSeek-R1-Distill-Qwen-14B'),
('DeepSeek', 'DeepSeek-R1-Distill-Qwen-32B'),
('DeepSeek', 'DeepSeek-R1-Zero'),
('DeepSeek', 'DeepSeek-V3 (Chat)'),
('DeepSeek', 'DeepSeek-V3-0324'),
('DeepSeek', 'DeepSeek-VL2 (27B)'),
('DeepSeek', 'DeepSeek-VL2-Small (16B)'),
('DeepSeek', 'DeepSeek-VL2-Tiny (3B)'),
('ElevenLabs', 'Eleven Flash v2.5 (TTS)'),
('ElevenLabs', 'Eleven Multilingual STS v2 (STS)'),
('ElevenLabs', 'Eleven Multilingual v2 (TTS)'),
('ElevenLabs', 'Eleven Turbo v2.5 (TTS)'),
('ElevenLabs', 'Scribe v1 (Speech Recognition)'),
('Genmo AI', 'Mochi 1 78'),
('Google', 'AlphaEvolve'),
('Google', 'CodeGemma (2B)'),
('Google', 'CodeGemma (7B)'),
('Google', 'Gemma (2B)'),
('Google', 'Gemma (7B)'),
('Google', 'Gemma 2 (2B)'),
('Google', 'Gemma 2 (9B)'),
('Google', 'Gemma 2 (27B)'),
('Google', 'Gemma 3 (1B)'),
('Google', 'Gemma 3 (4B)'),
('Google', 'Gemma 3 (12B)'),
('Google', 'Gemma 3 (27B)'),
('Google', 'Gemini 2.0 Flash-Lite'),
('Google', 'Gemini 2.5 Flash'),
('Google', 'Gemini 2.5 Pro'),
('Google', 'Imagen 3 80'),
('Google', 'Lyria'),
('Google', 'Lyria RealTime'),
('Google', 'PaliGemma 2 (3B)'),
('Google', 'PaliGemma 2 (10B)'),
('Google', 'PaliGemma 2 (28B)'),
('Google', 'ShieldGemma 2'),
('Google', 'TxGemma (2B)'),
('Google', 'TxGemma (9B)'),
('Google', 'TxGemma (27B)'),
('Google', 'Veo'),
('Google', 'Veo 2'),
('Haiper.ai', 'Haiper AI Video Generator 82'),
('IBM', 'Granite Code'),
('IBM', 'Granite Embedding Models'),
('IBM', 'Granite for Geospatial Data'),
('IBM', 'Granite for Time Series (TinyTimeMixer)'),
('IBM', 'Granite Guardian'),
('IBM', 'Granite Language Models (Gen 3 series)'),
('IBM', 'Granite Speech Models'),
('IBM', 'Granite Vision Model (2B)'),
('Jasper AI', 'Jasper AI Engine'),
('Jasper AI', 'Jasper AI Image Suite'),
('Jasper AI', 'Jasper Chat'),
('Jidream (Douyin)', 'JiMeng Video 3.0'),
('Kuaishou', 'Kling AI 1.0'),
('Kuaishou', 'Kling AI 1.5'),
('Kuaishou', 'Kling AI 1.6'),
('Kuaishou', 'Kling AI 2.0'),
('Kuaishou & Peking University', 'Pyramidal Flow Matching 84'),
('Leonardo.Ai', 'Leonardo AI (Platform) 86'),
('Lightricks', 'LTXV-13B (powering LTX Studio) 88'),
('Luma Labs', 'Dream Machine'),
('Meshy AI', 'Meshy AI (Text-to-3D)'),
('Meta AI', 'Llama 3.1 90'),
('Meta AI', 'Llama 4 Behemoth (Preview)'),
('Meta AI', 'Llama 4 Maverick'),
('Meta AI', 'Llama 4 Scout'),
('Midjourney', 'Niji 6'),
('Midjourney', 'Version 6'),
('Midjourney', 'Version 6.1'),
('Midjourney', 'Version 7 (V7)'),
('MiniMax', 'Hailuo I2V-01 92'),
('MiniMax', 'Hailuo S2V-01 92'),
('MiniMax', 'Hailuo T2V-01 92'),
('Mistral AI', 'Codestral (25.01)'),
('Mistral AI', 'Ministral 3B'),
('Mistral AI', 'Ministral 8B'),
('Mistral AI', 'Mistral Embed'),
('Mistral AI', 'Mistral Large 2 (24.11)'),
('Mistral AI', 'Mistral Medium 3 (25.05)'),
('Mistral AI', 'Mistral Moderation'),
('Mistral AI', 'Mistral OCR'),
('Mistral AI', 'Mistral Saba'),
('Mistral AI', 'Mistral Small 3.1'),
('Mistral AI', 'Pixtral Large (24.11)'),
('Moonvalley (w/ Asteria)', 'Marey'),
('NVIDIA', 'ACE (Avatar Cloud Engine)'),
('OpenAI', 'DALL-E 3 94'),
('OpenAI', 'GPT Image 1'),
('OpenAI', 'GPT-4.1'),
('OpenAI', 'GPT-4.1 mini'),
('OpenAI', 'GPT-4.1 nano'),
('OpenAI', 'GPT-4.5 (via ChatGPT)'),
('OpenAI', 'GPT-4o'),
('OpenAI', 'o3'),
('OpenAI', 'o4-mini'),
('OpenAI', 'Sora'),
('OpenAI', 'Whisper'),
('Perplexity AI', 'Sonar Large'),
('Pika Labs', 'Pika 2.2'),
('Pixverse', 'Pixverse v4.5'),
('Recraft', 'Recraft V3 96'),
('Rodin (Hyper 3D)', 'Rodin 1.5 (Text-to-3D)'),
('RunwayML', 'Gen-3 Alpha 98'),
('RunwayML', 'Gen-4'),
('RunwayML', 'Gen-4 Turbo'),
('Sand AI', 'MAGI-1 (4.5B planned)'),
('Sand AI', 'MAGI-1 (24B)'),
('Shengshu Technology', 'Vidu Q1'),
('Spline', 'Spline (Text-to-3D)'),
('Stability AI', 'Stable Audio 2.0 100'),
('Stability AI', 'Stable Diffusion 3.5 Large'),
('Stability AI', 'Stable Diffusion 3.5 Medium'),
('Stability AI', 'Stable Diffusion 3.5 Turbo'),
('Stability AI', 'Stable Diffusion XL'),
('Stability AI', 'Stable Diffusion XL Turbo'),
('Suno AI', 'Suno v3.5'),
('Suno AI', 'Suno v4.0'),
('Suno AI', 'Suno v4.5'),
('Synthesia', 'Synthesia (AI Video Generation Platform)'),
('Tabnine', 'Tabnine (AI Code Completion)'),
('Tencent', 'Hunyuan Video 102'),
('Tencent', 'Hunyuan3D (Text-to-3D)'),
('Tsinghua University', 'CogVideoX v1.5 104'),
('Udio', 'Udio Music 106'),
('xAI', 'Grok 2 Image (grok-2-image-1212)'),
('xAI', 'Grok 2 Vision (grok-2-vision-1212)'),
('xAI', 'Grok 3'),
('xAI', 'Grok 3 Fast'),
('xAI', 'Grok 3 Mini'),
('xAI', 'Grok 3 Mini Fast');

-- Helper function to clean a string for a slug
CREATE OR REPLACE FUNCTION clean_for_slug(input_text TEXT) RETURNS TEXT AS $$
BEGIN
  -- Convert to lowercase, replace spaces with hyphens, remove parentheses and other special characters
  RETURN regexp_replace(
    lower(
      replace(
        replace(
          replace(
              replace(input_text, ' ', '-'),
              '(', ''),
          ')', ''),
      '.', '-')),
    '[^a-z0-9\-]', '', 'g');
END;
$$ LANGUAGE plpgsql;

-- First, let's drop any existing unique constraint on the slug column
-- to allow our custom approach
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'ai_models_slug_key' AND conrelid = 'public.ai_models'::regclass
  ) THEN
    ALTER TABLE public.ai_models DROP CONSTRAINT ai_models_slug_key;
  END IF;
EXCEPTION
  WHEN others THEN
    -- Constraint doesn't exist or other error, continue
    RAISE NOTICE 'Could not drop constraint: %', SQLERRM;
END
$$;

-- Helper function to determine model type
CREATE OR REPLACE FUNCTION determine_model_type(model_name TEXT) RETURNS TEXT AS $$
BEGIN
  IF model_name ~* 'image|dall-e|stable diffusion|midjourney|firefly' THEN
    RETURN 'image';
  ELSIF model_name ~* 'video|sora|gen-3|lyria' THEN
    RETURN 'video';
  ELSIF model_name ~* '3d|avatar|rodin' THEN
    RETURN '3d';
  ELSIF model_name ~* 'audio|music|tts|speech|eleven' THEN
    RETURN 'audio';
  ELSE
    RETURN 'text';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Map provider to tool_id (adjust these values based on your actual tools table)
CREATE OR REPLACE FUNCTION get_tool_id_for_provider(provider TEXT) RETURNS INTEGER AS $$
BEGIN
  CASE provider
    WHEN 'OpenAI' THEN RETURN 1;
    WHEN 'Anthropic' THEN RETURN 2;
    WHEN 'Google' THEN RETURN 3;
    WHEN 'Meta AI' THEN RETURN 4;
    WHEN 'Mistral AI' THEN RETURN 5;
    WHEN 'Stability AI' THEN RETURN 6;
    WHEN 'Midjourney' THEN RETURN 7;
    WHEN 'Adobe' THEN RETURN 8;
    WHEN 'RunwayML' THEN RETURN 9;
    WHEN 'xAI' THEN RETURN 10;
    ELSE RETURN 1; -- Default to 1 if unknown
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- Create a temporary table to store generated slugs
CREATE TEMP TABLE temp_model_slugs AS
SELECT 
  t.provider,
  t.model_name,
  clean_for_slug(t.provider || '-' || t.model_name) AS base_slug,
  determine_model_type(t.model_name) AS model_type,
  get_tool_id_for_provider(t.provider) AS tool_id,
  ROW_NUMBER() OVER (PARTITION BY clean_for_slug(t.provider || '-' || t.model_name) ORDER BY t.provider, t.model_name) AS slug_occurrence
FROM temp_ai_models t;

-- Insert data into ai_models table with clean slugs
-- If there are duplicates, append a number to make them unique
INSERT INTO public.ai_models (provider, tool_name, slug, type, deprecated, tool_id)
SELECT 
  provider,
  model_name,
  CASE 
    WHEN slug_occurrence = 1 THEN base_slug -- Use clean slug for first occurrence
    ELSE base_slug || '-' || slug_occurrence::text -- Append number for duplicates
  END AS slug,
  model_type,
  FALSE, -- Not deprecated
  tool_id
FROM temp_model_slugs;

-- Clean up
DROP FUNCTION clean_for_slug;
DROP FUNCTION determine_model_type;
DROP FUNCTION get_tool_id_for_provider;
DROP TABLE temp_ai_models;
DROP TABLE temp_model_slugs;

-- Add back the unique constraint on slug
ALTER TABLE public.ai_models ADD CONSTRAINT ai_models_slug_key UNIQUE (slug);

-- Count how many models were inserted
SELECT COUNT(*) FROM public.ai_models;
