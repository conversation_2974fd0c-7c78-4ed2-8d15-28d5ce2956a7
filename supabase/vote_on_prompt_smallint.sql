-- Create a function to handle voting on prompts with explicit smallint type
CREATE OR REPLACE FUNCTION vote_on_prompt_smallint(
  p_user_id UUID,
  p_prompt_id UUID,
  p_vote_type SMALLINT
) RETURNS VOID AS $$
DECLARE
  v_old_vote SMALLINT;
BEGIN
  -- Check if user has already voted on this prompt
  SELECT vote_type INTO v_old_vote
  FROM prompt_votes
  WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
  
  IF v_old_vote IS NULL THEN
    -- No previous vote, insert new vote if not removing (p_vote_type != 0)
    IF p_vote_type != 0 THEN
      INSERT INTO prompt_votes (user_id, prompt_id, vote_type)
      VALUES (p_user_id, p_prompt_id, p_vote_type);
      
      -- Update prompt statistics
      UPDATE prompt_statistics
      SET 
        rating = rating + p_vote_type,
        upvotes = CASE WHEN p_vote_type = 1 THEN upvotes + 1 ELSE upvotes END,
        downvotes = CASE WHEN p_vote_type = -1 THEN downvotes + 1 ELSE downvotes END
      WHERE id = p_prompt_id;
    END IF;
  ELSE
    -- Previous vote exists
    IF p_vote_type = 0 THEN
      -- Remove vote
      DELETE FROM prompt_votes
      WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
      
      -- Update prompt statistics
      UPDATE prompt_statistics
      SET 
        rating = rating - v_old_vote,
        upvotes = CASE WHEN v_old_vote = 1 THEN upvotes - 1 ELSE upvotes END,
        downvotes = CASE WHEN v_old_vote = -1 THEN downvotes - 1 ELSE downvotes END
      WHERE id = p_prompt_id;
    ELSE
      -- Change vote
      UPDATE prompt_votes
      SET vote_type = p_vote_type
      WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
      
      -- Update prompt statistics
      UPDATE prompt_statistics
      SET 
        rating = rating - v_old_vote + p_vote_type,
        upvotes = 
          CASE 
            WHEN v_old_vote = 1 AND p_vote_type = -1 THEN upvotes - 1
            WHEN v_old_vote = -1 AND p_vote_type = 1 THEN upvotes + 1
            WHEN v_old_vote != 1 AND p_vote_type = 1 THEN upvotes + 1
            ELSE upvotes
          END,
        downvotes = 
          CASE 
            WHEN v_old_vote = -1 AND p_vote_type = 1 THEN downvotes - 1
            WHEN v_old_vote = 1 AND p_vote_type = -1 THEN downvotes + 1
            WHEN v_old_vote != -1 AND p_vote_type = -1 THEN downvotes + 1
            ELSE downvotes
          END
      WHERE id = p_prompt_id;
    END IF;
  END IF;
END;
$$ LANGUAGE plpgsql;
