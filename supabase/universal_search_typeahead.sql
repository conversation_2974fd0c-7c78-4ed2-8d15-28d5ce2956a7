-- Universal search typeahead function for searching prompts, collections, and profiles
-- This function is used by the search bar to provide quick results

CREATE OR REPLACE FUNCTION public.universal_search_typeahead(
  p_search_query TEXT,
  p_current_user_id UUID DEFAULT NULL, -- Optional: for future use if we need user-specific results
  p_limit INTEGER DEFAULT 7 -- Standard limit for typeahead results
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  item_type TEXT, -- 'prompt', 'collection', or 'profile'
  short_id TEXT, -- For prompts
  slug TEXT, -- For prompts, and collections if applicable, username for profiles
  user_id UUID,
  username TEXT,
  avatar_url TEXT,
  item_icon TEXT, -- Prompt icon (tool), Collection icon, or null for profiles
  rank REAL
) AS $$
DECLARE
  processed_ts_query tsquery;
BEGIN
  -- Use websearch_to_tsquery for more flexible search term parsing
  processed_ts_query := websearch_to_tsquery('english', p_search_query);

  RETURN QUERY
  WITH search_results AS (
    -- Prompts (highest priority for exact/partial title matches)
    SELECT
      p.id,
      p.title,
      'prompt' AS item_type,
      p.short_id,
      p.slug,
      p.user_id,
      prof.username,
      prof.avatar_url,
      t.name AS item_icon, -- Tool name for prompts
      CASE 
        WHEN p.title ILIKE p_search_query THEN 3.0 -- Exact title match
        WHEN p.title ILIKE p_search_query || '%' THEN 2.8 -- Title starts with query
        WHEN p.title ILIKE '%' || p_search_query || '%' THEN 2.5 -- Title contains query
        ELSE ts_rank_cd(p.search_vector, processed_ts_query) + 2.0 -- FTS match with boost
      END AS rank_score
    FROM public.prompts p
    JOIN public.profiles prof ON p.user_id = prof.id
    LEFT JOIN public.tools t ON p.tool_id = t.id
    WHERE p.is_public = TRUE 
      AND (p.search_vector @@ processed_ts_query OR p.title ILIKE '%' || p_search_query || '%')

    UNION ALL

    -- Collections (medium priority for exact/partial name matches)
    SELECT
      c.id,
      c.name AS title,
      'collection' AS item_type,
      NULL AS short_id,
      NULL AS slug, -- Collections currently do not have slugs
      c.user_id,
      prof.username,
      prof.avatar_url,
      c.icon AS item_icon, -- Collection icon
      CASE 
        WHEN c.name ILIKE p_search_query THEN 2.0 -- Exact name match
        WHEN c.name ILIKE p_search_query || '%' THEN 1.8 -- Name starts with query
        WHEN c.name ILIKE '%' || p_search_query || '%' THEN 1.5 -- Name contains query
        ELSE ts_rank_cd(c.search_vector, processed_ts_query) + 1.0 -- FTS match with boost
      END AS rank_score
    FROM public.collections c
    JOIN public.profiles prof ON c.user_id = prof.id
    WHERE c.is_public = TRUE 
      AND c.prompt_count > 0 -- Only include collections with prompts
      AND (c.search_vector @@ processed_ts_query OR c.name ILIKE '%' || p_search_query || '%')

    UNION ALL

    -- Profiles/Users (lower priority for username matches)
    SELECT
      prof.id,
      prof.username AS title,
      'profile' AS item_type,
      NULL AS short_id,
      prof.username AS slug, -- Use username as slug for URL construction
      prof.id AS user_id, -- Profile's own ID
      prof.username,
      prof.avatar_url,
      NULL AS item_icon, -- No specific icon for profiles
      CASE 
        WHEN prof.username ILIKE p_search_query THEN 1.0 -- Exact username match
        WHEN prof.username ILIKE p_search_query || '%' THEN 0.8 -- Username starts with query
        WHEN prof.username ILIKE '%' || p_search_query || '%' THEN 0.5 -- Username contains query
        ELSE 0.1 -- Fallback minimal score
      END AS rank_score
    FROM public.profiles prof
    WHERE prof.username ILIKE '%' || p_search_query || '%'
      AND prof.username IS NOT NULL
      AND prof.username != ''
  )
  SELECT sr.*
  FROM search_results sr
  WHERE sr.rank_score > 0
  ORDER BY sr.rank_score DESC, sr.title ASC
  LIMIT p_limit;

END;
$$ LANGUAGE plpgsql STABLE; 