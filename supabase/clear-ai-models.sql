-- <PERSON><PERSON>t to safely clear the ai_models table

-- First, check if there are any prompts referencing ai_models
SELECT COUNT(*) AS prompts_with_ai_models 
FROM public.prompts 
WHERE ai_model_id IS NOT NULL;

-- If the count is greater than 0, you might want to handle those references first
-- For example, you could set ai_model_id to NULL in those prompts:
-- UPDATE public.prompts SET ai_model_id = NULL WHERE ai_model_id IS NOT NULL;

-- Now delete all records from the ai_models table
DELETE FROM public.ai_models;

-- Reset the sequence if needed (if you're using a sequence for the id column)
-- This will make new insertions start from 1 again
ALTER SEQUENCE public.ai_models_id_seq RESTART WITH 1;

-- Verify the table is empty
SELECT COUNT(*) FROM public.ai_models;
