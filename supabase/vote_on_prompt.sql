-- Create a function to handle voting on prompts
-- This function handles all vote operations (insert, update, delete)
-- and returns a boolean to indicate success or failure
CREATE OR REPLACE FUNCTION vote_on_prompt(
  p_user_id UUID,
  p_prompt_id UUID,
  p_vote_type SMALLINT
) RETURNS BOOLEAN AS $$
DECLARE
  v_old_vote SMALLINT;
  v_prompt_status TEXT;
BEGIN
  -- Validate vote type (must be -1, 0, or 1)
  IF p_vote_type NOT IN (-1, 0, 1) THEN
    RAISE NOTICE 'Invalid vote type: %', p_vote_type;
    RETURN FALSE;
  END IF;

  -- Check if the prompt exists and is public
  SELECT status INTO v_prompt_status
  FROM prompts
  WHERE id = p_prompt_id;
  
  IF v_prompt_status IS NULL THEN
    RAISE NOTICE 'Prompt not found: %', p_prompt_id;
    RETURN FALSE;
  END IF;
  
  IF v_prompt_status != 'public' THEN
    RAISE NOTICE 'Cannot vote on non-public prompt: % (status: %)', p_prompt_id, v_prompt_status;
    RETURN FALSE;
  END IF;

  -- Check if user has already voted on this prompt
  SELECT vote_type INTO v_old_vote
  FROM prompt_votes
  WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
  
  BEGIN
    IF v_old_vote IS NULL THEN
      -- No previous vote, insert new vote if not removing (p_vote_type != 0)
      IF p_vote_type != 0 THEN
        INSERT INTO prompt_votes (user_id, prompt_id, vote_type)
        VALUES (p_user_id, p_prompt_id, p_vote_type);
        
        -- Update prompt statistics
        UPDATE prompt_statistics
        SET 
          rating = rating + p_vote_type,
          upvotes = CASE WHEN p_vote_type = 1 THEN upvotes + 1 ELSE upvotes END,
          downvotes = CASE WHEN p_vote_type = -1 THEN downvotes + 1 ELSE downvotes END
        WHERE id = p_prompt_id;
      END IF;
    ELSE
      -- Previous vote exists
      IF p_vote_type = 0 THEN
        -- Remove vote
        DELETE FROM prompt_votes
        WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
        
        -- Update prompt statistics
        UPDATE prompt_statistics
        SET 
          rating = rating - v_old_vote,
          upvotes = CASE WHEN v_old_vote = 1 THEN upvotes - 1 ELSE upvotes END,
          downvotes = CASE WHEN v_old_vote = -1 THEN downvotes - 1 ELSE downvotes END
        WHERE id = p_prompt_id;
      ELSE
        -- Change vote
        UPDATE prompt_votes
        SET vote_type = p_vote_type
        WHERE user_id = p_user_id AND prompt_id = p_prompt_id;
        
        -- Update prompt statistics
        UPDATE prompt_statistics
        SET 
          rating = rating - v_old_vote + p_vote_type,
          upvotes = 
            CASE 
              WHEN v_old_vote = 1 AND p_vote_type = -1 THEN upvotes - 1
              WHEN v_old_vote = -1 AND p_vote_type = 1 THEN upvotes + 1
              WHEN v_old_vote != 1 AND p_vote_type = 1 THEN upvotes + 1
              ELSE upvotes
            END,
          downvotes = 
            CASE 
              WHEN v_old_vote = -1 AND p_vote_type = 1 THEN downvotes - 1
              WHEN v_old_vote = 1 AND p_vote_type = -1 THEN downvotes + 1
              WHEN v_old_vote != -1 AND p_vote_type = -1 THEN downvotes + 1
              ELSE downvotes
            END
        WHERE id = p_prompt_id;
      END IF;
    END IF;
    
    RETURN TRUE;
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE 'Error in vote_on_prompt: %', SQLERRM;
      RETURN FALSE;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
