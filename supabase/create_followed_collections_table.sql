-- Create the followed_collections table
CREATE TABLE IF NOT EXISTS public.followed_collections (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    collection_id UUID NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
    followed_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(follower_id, collection_id)
);

-- Add RLS policies
ALTER TABLE public.followed_collections ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own followed collections
CREATE POLICY "Users can view their own followed collections"
    ON public.followed_collections
    FOR SELECT
    USING (auth.uid() = follower_id);

-- Allow users to follow collections
CREATE POLICY "Users can follow collections"
    ON public.followed_collections
    FOR INSERT
    WITH CHECK (auth.uid() = follower_id);

-- Allow users to unfollow collections
CREATE POLICY "Users can unfollow collections"
    ON public.followed_collections
    FOR DELETE
    USING (auth.uid() = follower_id);

-- Add comment
COMMENT ON TABLE public.followed_collections IS 'Tracks which collections a user follows';
