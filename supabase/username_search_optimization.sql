-- Comprehensive Username Search Implementation
-- This script optimizes the database for efficient username searching
-- while maintaining excellent performance and ranking

-- Step 1: Enable pg_trgm extension for efficient ILIKE searches
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Step 2: Create optimized indexes for username searching
-- GIN index with pg_trgm for efficient "contains" searches
DROP INDEX IF EXISTS idx_profiles_username_trgm;
CREATE INDEX idx_profiles_username_trgm ON public.profiles USING gin (username gin_trgm_ops);

-- Additional B-tree index for exact matches and prefix searches (optional but recommended)
DROP INDEX IF EXISTS idx_profiles_username_lower;
CREATE INDEX idx_profiles_username_lower ON public.profiles (lower(username) text_pattern_ops);

-- Step 3: Ensure prompt_card_details view includes author_username
-- (This should already exist, but verify it's properly indexed)
-- If author_username is frequently searched, consider adding it to search_vector as well

-- Step 4: Update universal_search_typeahead with sophisticated ranking
CREATE OR REPLACE FUNCTION public.universal_search_typeahead(
  p_search_query TEXT,
  p_current_user_id UUID DEFAULT NULL,
  p_limit INTEGER DEFAULT 7
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  item_type TEXT, -- 'prompt', 'collection', or 'profile'
  short_id TEXT,
  slug TEXT,
  user_id UUID,
  username TEXT,
  avatar_url TEXT,
  item_icon TEXT,
  rank REAL
) AS $$
DECLARE
  processed_ts_query tsquery;
BEGIN
  -- Use websearch_to_tsquery for flexible search term parsing
  processed_ts_query := websearch_to_tsquery('english', p_search_query);

  RETURN QUERY
  WITH search_results AS (
    -- Prompts (highest priority with sophisticated ranking)
    SELECT
      p.id,
      p.title,
      'prompt' AS item_type,
      p.short_id,
      p.slug,
      p.user_id,
      prof.username,
      prof.avatar_url,
      t.name AS item_icon,
      CASE 
        WHEN p.title ILIKE p_search_query THEN 3.0 -- Exact title match
        WHEN p.title ILIKE p_search_query || '%' THEN 2.8 -- Title starts with query
        WHEN p.title ILIKE '%' || p_search_query || '%' THEN 2.5 -- Title contains query
        ELSE ts_rank_cd(p.search_vector, processed_ts_query) + 2.0 -- FTS match with boost
      END AS rank_score
    FROM public.prompts p
    JOIN public.profiles prof ON p.user_id = prof.id
    LEFT JOIN public.tools t ON p.tool_id = t.id
    WHERE p.is_public = TRUE 
      AND (p.search_vector @@ processed_ts_query OR p.title ILIKE '%' || p_search_query || '%')

    UNION ALL

    -- Collections (medium priority)
    SELECT
      c.id,
      c.name AS title,
      'collection' AS item_type,
      NULL AS short_id,
      NULL AS slug,
      c.user_id,
      prof.username,
      prof.avatar_url,
      c.icon AS item_icon,
      CASE 
        WHEN c.name ILIKE p_search_query THEN 2.0 -- Exact name match
        WHEN c.name ILIKE p_search_query || '%' THEN 1.8 -- Name starts with query
        WHEN c.name ILIKE '%' || p_search_query || '%' THEN 1.5 -- Name contains query
        ELSE ts_rank_cd(c.search_vector, processed_ts_query) + 1.0 -- FTS match with boost
      END AS rank_score
    FROM public.collections c
    JOIN public.profiles prof ON c.user_id = prof.id
    WHERE c.is_public = TRUE 
      AND c.prompt_count > 0
      AND (c.search_vector @@ processed_ts_query OR c.name ILIKE '%' || p_search_query || '%')

    UNION ALL

    -- Profiles (sophisticated username ranking)
    SELECT
      prof.id,
      prof.username AS title,
      'profile' AS item_type,
      NULL AS short_id,
      prof.username AS slug,
      prof.id AS user_id,
      prof.username,
      prof.avatar_url,
      NULL AS item_icon,
      CASE 
        WHEN lower(prof.username) = lower(p_search_query) THEN 1.0 -- Exact username match
        WHEN prof.username ILIKE p_search_query || '%' THEN 0.8 -- Username starts with query
        WHEN prof.username ILIKE '%' || p_search_query || '%' THEN 0.5 -- Username contains query
        ELSE 0.1 -- Fallback minimal score
      END AS rank_score
    FROM public.profiles prof
    WHERE prof.username ILIKE '%' || p_search_query || '%'
      AND prof.username IS NOT NULL
      AND prof.username != ''
  )
  SELECT sr.*
  FROM search_results sr
  WHERE sr.rank_score > 0
  ORDER BY sr.rank_score DESC, sr.title ASC
  LIMIT p_limit;

END;
$$ LANGUAGE plpgsql STABLE;

-- Step 5: Enhanced get_prompts_with_saved_status with username search
-- This function now includes sophisticated username matching and relevance scoring
CREATE OR REPLACE FUNCTION get_prompts_with_saved_status(
  p_user_id uuid DEFAULT NULL,
  p_limit integer DEFAULT 20,
  p_offset integer DEFAULT 0,
  p_category_slugs text[] DEFAULT NULL,
  p_tool_slugs text[] DEFAULT NULL,
  p_tag_slugs text[] DEFAULT NULL,
  p_ai_model_slugs text[] DEFAULT NULL,
  p_search_query text DEFAULT NULL,
  p_author_id uuid DEFAULT NULL,
  p_sort_by text DEFAULT 'created_at',
  p_sort_order text DEFAULT 'desc'
)
RETURNS TABLE (
  id uuid,
  short_id text,
  title text,
  description text,
  image_url text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  is_public boolean,
  view_count integer,
  primary_tag_id integer,
  category_id integer,
  tool_id integer,
  author_id uuid,
  search_vector tsvector,
  tag_slugs_array text[],
  category_name text,
  category_slug text,
  tool_name text,
  tool_slug text,
  author_username text,
  author_avatar_url text,
  primary_tag_slug text,
  tags jsonb,
  rating bigint,
  comment_count bigint,
  trending_score double precision,
  ai_model_id integer,
  ai_model_provider text,
  ai_model_name text,
  ai_model_slug text,
  ai_model_deprecated boolean,
  is_saved_by_user boolean
) AS $$
DECLARE
  search_ts_query tsquery;
  exact_username_match boolean := false;
BEGIN
  -- Check if search query exactly matches a username (case-insensitive)
  IF p_search_query IS NOT NULL THEN
    SELECT EXISTS(
      SELECT 1 FROM profiles 
      WHERE lower(username) = lower(p_search_query)
    ) INTO exact_username_match;
    
    -- Prepare the tsquery for full-text search
    search_ts_query := websearch_to_tsquery('english', p_search_query);
  END IF;

  RETURN QUERY
  WITH user_saved_prompts AS (
    SELECT DISTINCT cp.prompt_id
    FROM collection_prompts cp
    JOIN collections c ON cp.collection_id = c.id
    WHERE c.user_id = p_user_id 
      AND p_user_id IS NOT NULL
  ),
  search_results AS (
    SELECT 
      pcd.id,
      pcd.short_id,
      pcd.title,
      pcd.description,
      pcd.image_url,
      pcd.created_at,
      pcd.updated_at,
      pcd.is_public,
      pcd.view_count,
      pcd.primary_tag_id,
      pcd.category_id,
      pcd.tool_id,
      pcd.author_id,
      pcd.search_vector,
      pcd.tag_slugs_array,
      pcd.category_name,
      pcd.category_slug,
      pcd.tool_name,
      pcd.tool_slug,
      pcd.author_username,
      pcd.author_avatar_url,
      pcd.primary_tag_slug,
      pcd.tags,
      pcd.rating,
      pcd.comment_count,
      pcd.trending_score,
      pcd.ai_model_id,
      pcd.ai_model_provider,
      pcd.ai_model_name,
      pcd.ai_model_slug,
      pcd.ai_model_deprecated,
      CASE WHEN usp.prompt_id IS NOT NULL THEN true ELSE false END AS is_saved_by_user,
      -- Sophisticated relevance scoring
      CASE 
        WHEN p_search_query IS NULL THEN 0
        WHEN exact_username_match AND lower(pcd.author_username) = lower(p_search_query) THEN 100 -- High boost for exact username match
        WHEN pcd.author_username ILIKE p_search_query || '%' THEN 30 -- Username starts with query
        WHEN pcd.author_username ILIKE '%' || p_search_query || '%' THEN 20 -- Username contains query
        ELSE 0
      END +
      CASE 
        WHEN p_search_query IS NULL THEN 0
        WHEN pcd.search_vector @@ search_ts_query THEN ts_rank_cd(pcd.search_vector, search_ts_query) * 100
        WHEN pcd.title ILIKE '%' || p_search_query || '%' THEN 15 -- Title contains query
        WHEN pcd.description ILIKE '%' || p_search_query || '%' THEN 5 -- Description contains query
        ELSE 0
      END AS relevance_score
    FROM prompt_card_details pcd
    LEFT JOIN user_saved_prompts usp ON pcd.id = usp.prompt_id
    WHERE pcd.is_public = true
      AND (p_category_slugs IS NULL OR pcd.category_slug = ANY(p_category_slugs))
      AND (p_tool_slugs IS NULL OR pcd.tool_slug = ANY(p_tool_slugs))
      AND (p_tag_slugs IS NULL OR pcd.tag_slugs_array && p_tag_slugs)
      AND (p_ai_model_slugs IS NULL OR pcd.ai_model_slug = ANY(p_ai_model_slugs))
      AND (p_author_id IS NULL OR pcd.author_id = p_author_id)
      AND (
        p_search_query IS NULL OR 
        pcd.search_vector @@ search_ts_query OR 
        pcd.title ILIKE '%' || p_search_query || '%' OR
        pcd.description ILIKE '%' || p_search_query || '%' OR
        pcd.author_username ILIKE '%' || p_search_query || '%'
      )
  )
  SELECT 
    sr.id,
    sr.short_id,
    sr.title,
    sr.description,
    sr.image_url,
    sr.created_at,
    sr.updated_at,
    sr.is_public,
    sr.view_count,
    sr.primary_tag_id,
    sr.category_id,
    sr.tool_id,
    sr.author_id,
    sr.search_vector,
    sr.tag_slugs_array,
    sr.category_name,
    sr.category_slug,
    sr.tool_name,
    sr.tool_slug,
    sr.author_username,
    sr.author_avatar_url,
    sr.primary_tag_slug,
    sr.tags,
    sr.rating,
    sr.comment_count,
    sr.trending_score,
    sr.ai_model_id,
    sr.ai_model_provider,
    sr.ai_model_name,
    sr.ai_model_slug,
    sr.ai_model_deprecated,
    sr.is_saved_by_user
  FROM search_results sr
  ORDER BY 
    -- If there's a search query, order by relevance first
    CASE 
      WHEN p_search_query IS NOT NULL THEN sr.relevance_score
      ELSE 0
    END DESC,
    -- Then apply the requested sorting
    CASE 
      WHEN p_sort_by = 'created_at' AND p_sort_order = 'desc' THEN sr.created_at
    END DESC,
    CASE 
      WHEN p_sort_by = 'created_at' AND p_sort_order = 'asc' THEN sr.created_at
    END ASC,
    CASE 
      WHEN p_sort_by = 'rating' AND p_sort_order = 'desc' THEN sr.rating
    END DESC,
    CASE 
      WHEN p_sort_by = 'rating' AND p_sort_order = 'asc' THEN sr.rating
    END ASC,
    CASE 
      WHEN p_sort_by = 'trending_score' AND p_sort_order = 'desc' THEN sr.trending_score
    END DESC,
    CASE 
      WHEN p_sort_by = 'trending_score' AND p_sort_order = 'asc' THEN sr.trending_score
    END ASC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Grant necessary permissions
GRANT EXECUTE ON FUNCTION universal_search_typeahead TO authenticated;
GRANT EXECUTE ON FUNCTION universal_search_typeahead TO anon;
GRANT EXECUTE ON FUNCTION get_prompts_with_saved_status TO authenticated;
GRANT EXECUTE ON FUNCTION get_prompts_with_saved_status TO anon;

-- Step 7: Optional - Update existing prompts to refresh search vectors
-- Uncomment the following line if you want to update all existing prompts
-- This will be slow on large tables, so consider running during off-peak hours
-- UPDATE public.prompts SET updated_at = NOW() WHERE updated_at < NOW();

-- Step 8: Add helpful comments
COMMENT ON FUNCTION universal_search_typeahead IS 'Enhanced universal search with sophisticated ranking for prompts, collections, and user profiles';
COMMENT ON FUNCTION get_prompts_with_saved_status IS 'Enhanced prompt search with username matching and dynamic relevance scoring';
COMMENT ON INDEX idx_profiles_username_trgm IS 'GIN index for efficient username substring searches using pg_trgm';
COMMENT ON INDEX idx_profiles_username_lower IS 'B-tree index for efficient exact username matches and prefix searches'; 