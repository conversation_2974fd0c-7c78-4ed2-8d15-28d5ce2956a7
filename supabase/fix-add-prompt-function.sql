-- Fix the add_prompt function overloading issue
-- This script drops the old version and keeps only the new one with p_user_entered_ai_model parameter

-- First, drop both functions to clean up
DROP FUNCTION IF EXISTS public.add_prompt(
  p_user_id UUID,
  p_category_id INTEGER,
  p_tool_id INTEGER,
  p_title TEXT,
  p_description TEXT,
  p_prompt_text TEXT,
  p_instructions TEXT,
  p_example_input TEXT,
  p_example_output_text TEXT,
  p_example_output_image_url TEXT,
  p_image_url TEXT,
  p_is_public BOOLEAN,
  p_original_prompt_id UUID,
  p_tag_ids INTEGER[],
  p_ai_model_id INTEGER
);

DROP FUNCTION IF EXISTS public.add_prompt(
  p_user_id UUID,
  p_category_id INTEGER,
  p_tool_id INTEGER,
  p_title TEXT,
  p_description TEXT,
  p_prompt_text TEXT,
  p_instructions TEXT,
  p_example_input TEXT,
  p_example_output_text TEXT,
  p_example_output_image_url TEXT,
  p_image_url TEXT,
  p_is_public BOOLEAN,
  p_original_prompt_id UUID,
  p_tag_ids INTEGER[],
  p_ai_model_id INTEGER,
  p_user_entered_ai_model TEXT
);

-- We'll use the existing generate_short_id function instead of creating our own

-- Now recreate the function with the existing generate_short_id function
CREATE OR REPLACE FUNCTION public.add_prompt(
  p_user_id UUID,
  p_category_id INTEGER,
  p_tool_id INTEGER,
  p_title TEXT,
  p_description TEXT,
  p_prompt_text TEXT,
  p_instructions TEXT DEFAULT NULL,
  p_example_input TEXT DEFAULT NULL,
  p_example_output_text TEXT DEFAULT NULL,
  p_example_output_image_url TEXT DEFAULT NULL,
  p_image_url TEXT DEFAULT NULL,
  p_is_public BOOLEAN DEFAULT true,
  p_original_prompt_id UUID DEFAULT NULL,
  p_tag_ids INTEGER[] DEFAULT '{}',
  p_ai_model_id INTEGER DEFAULT NULL,
  p_user_entered_ai_model TEXT DEFAULT NULL
) RETURNS TABLE(created_prompt_id UUID, created_short_id TEXT) AS $$
DECLARE
  v_prompt_id UUID;
  v_short_id TEXT;
  v_slug TEXT;
BEGIN
  -- Use the existing generate_short_id function (6 characters)
  v_short_id := generate_short_id(6);
  
  -- Generate a slug from the title
  v_slug := lower(regexp_replace(p_title, '[^a-zA-Z0-9]', '-', 'g'));
  
  -- Insert the prompt
  INSERT INTO public.prompts(
    user_id,
    category_id,
    tool_id,
    title,
    description,
    prompt_text,
    instructions,
    example_input,
    example_output_text,
    example_output_image_url,
    image_url,
    is_public,
    original_prompt_id,
    ai_model_id,
    user_entered_ai_model,
    short_id,
    slug
  ) VALUES (
    p_user_id,
    p_category_id,
    p_tool_id,
    p_title,
    p_description,
    p_prompt_text,
    p_instructions,
    p_example_input,
    p_example_output_text,
    p_example_output_image_url,
    p_image_url,
    p_is_public,
    p_original_prompt_id,
    p_ai_model_id,
    p_user_entered_ai_model,
    v_short_id,
    v_slug
  )
  RETURNING id, short_id INTO v_prompt_id, v_short_id;
  
  -- Insert tags if provided
  IF array_length(p_tag_ids, 1) > 0 THEN
    INSERT INTO public.prompt_tags (prompt_id, tag_id)
    SELECT v_prompt_id, tag_id
    FROM unnest(p_tag_ids) AS tag_id;
  END IF;
  
  -- Return the created prompt ID and short ID
  RETURN QUERY SELECT v_prompt_id, v_short_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
