-- Schema changes for collection search functionality
-- This sets up the search_vector column and related functions for collections

-- 1. Add search_vector column to collections table (if it doesn't exist)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'collections' 
        AND column_name = 'search_vector'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.collections ADD COLUMN search_vector tsvector;
    END IF;
END $$;

-- 2. Function to generate search_vector for collections
CREATE OR REPLACE FUNCTION public.generate_collection_search_vector(
  p_name TEXT,
  p_description TEXT
)
RETURNS tsvector AS $$
BEGIN
  RETURN (
    setweight(to_tsvector('english', coalesce(p_name, '')), 'A') ||
    setweight(to_tsvector('english', coalesce(p_description, '')), 'B')
  );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 3. Function to update search_vector on collection changes
CREATE OR REPLACE FUNCTION public.update_collection_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  IF (TG_OP = 'INSERT') THEN
    NEW.search_vector := public.generate_collection_search_vector(NEW.name, NEW.description);
  ELSIF (TG_OP = 'UPDATE') THEN
    IF (NEW.name IS DISTINCT FROM OLD.name) OR (NEW.description IS DISTINCT FROM OLD.description) THEN
      NEW.search_vector := public.generate_collection_search_vector(NEW.name, NEW.description);
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. Create trigger to automatically update search_vector
DROP TRIGGER IF EXISTS update_collection_search_vector_trigger ON public.collections;
CREATE TRIGGER update_collection_search_vector_trigger
BEFORE INSERT OR UPDATE ON public.collections
FOR EACH ROW
EXECUTE FUNCTION public.update_collection_search_vector();

-- 5. Backfill search_vector for existing collections
UPDATE public.collections
SET search_vector = public.generate_collection_search_vector(name, description)
WHERE search_vector IS NULL; 