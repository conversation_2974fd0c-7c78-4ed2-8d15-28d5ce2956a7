-- Function to update prompt collections (add and remove in one operation)
CREATE OR REPLACE FUNCTION update_prompt_collections(
    p_user_id UUID,
    p_prompt_id UUID,
    p_add_collection_ids UUID[],
    p_remove_collection_ids UUID[]
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    collection_id_to_add UUID;
    collection_id_to_remove UUID;
    current_collection RECORD;
    prompt_author_id UUID;
BEGIN
    -- Get the author of the prompt (for validation)
    SELECT user_id INTO prompt_author_id FROM prompts WHERE id = p_prompt_id;

    IF NOT FOUND THEN
        RAISE WARNING 'Prompt with ID % not found.', p_prompt_id;
        RETURN FALSE;
    END IF;

    -- Add extensive logging for debugging
    RAISE NOTICE 'Updating collections for prompt %, user %', p_prompt_id, p_user_id;
    
    -- First handle additions
    IF p_add_collection_ids IS NOT NULL AND array_length(p_add_collection_ids, 1) > 0 THEN
        RAISE NOTICE 'Adding prompt to % collections', array_length(p_add_collection_ids, 1);
        
        FOREACH collection_id_to_add IN ARRAY p_add_collection_ids
        LOOP
            -- Check if the collection exists and belongs to the user
            SELECT * INTO current_collection FROM collections 
            WHERE id = collection_id_to_add AND user_id = p_user_id;

            IF NOT FOUND THEN
                RAISE WARNING 'Collection with ID % not found or does not belong to user %.', collection_id_to_add, p_user_id;
                CONTINUE; -- Skip this collection and try the next one
            END IF;
            
            RAISE NOTICE 'Adding prompt % to collection %', p_prompt_id, collection_id_to_add;
            
            -- Add the prompt to this collection, do nothing if already exists
            INSERT INTO collection_prompts (collection_id, prompt_id)
            VALUES (collection_id_to_add, p_prompt_id)
            ON CONFLICT (collection_id, prompt_id) DO NOTHING;
        END LOOP;
    END IF;

    -- Then handle removals
    IF p_remove_collection_ids IS NOT NULL AND array_length(p_remove_collection_ids, 1) > 0 THEN
        RAISE NOTICE 'Removing prompt from % collections', array_length(p_remove_collection_ids, 1);
        
        FOREACH collection_id_to_remove IN ARRAY p_remove_collection_ids
        LOOP
            -- Check if the collection exists and belongs to the user
            SELECT * INTO current_collection FROM collections 
            WHERE id = collection_id_to_remove AND user_id = p_user_id;

            IF NOT FOUND THEN
                RAISE WARNING 'Collection with ID % not found or does not belong to user %.', collection_id_to_remove, p_user_id;
                CONTINUE; -- Skip this collection and try the next one
            END IF;
            
            RAISE NOTICE 'Removing prompt % from collection %', p_prompt_id, collection_id_to_remove;
            
            -- Remove the prompt from this collection
            DELETE FROM collection_prompts 
            WHERE collection_id = collection_id_to_remove 
            AND prompt_id = p_prompt_id;
        END LOOP;
    END IF;

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error in update_prompt_collections: %', SQLERRM;
        RETURN FALSE;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION update_prompt_collections(UUID, UUID, UUID[], UUID[]) TO authenticated;
