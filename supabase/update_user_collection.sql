-- First drop the existing function to avoid return type errors
DROP FUNCTION IF EXISTS public.update_user_collection(uuid, uuid, text, text, text, boolean);

-- Function to update a user collection with proper image handling
CREATE FUNCTION public.update_user_collection(
    p_user_id uuid,
    p_collection_id uuid,
    p_name text DEFAULT NULL,
    p_description text DEFAULT NULL,
    p_icon_url text DEFAULT NULL,
    p_is_public boolean DEFAULT NULL
)
RETURNS TABLE (
    id uuid,
    user_id uuid,
    name text,
    description text,
    icon text,
    is_public boolean,
    is_default boolean,
    default_type text,
    created_at timestamptz,
    updated_at timestamptz
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_is_default boolean;
    v_default_type text;
    v_owner_id uuid;
    v_debug_info text;
BEGIN
    -- Debug info
    v_debug_info := 'Parameters: p_user_id=' || p_user_id || ', p_collection_id=' || p_collection_id;
    IF p_icon_url IS NOT NULL THEN
        v_debug_info := v_debug_info || ', p_icon_url=' || p_icon_url;
    END IF;
    RAISE NOTICE '%', v_debug_info;
    
    -- Check if collection exists and get owner_id and default status
    SELECT c.is_default, c.default_type, c.user_id INTO v_is_default, v_default_type, v_owner_id 
    FROM collections c
    WHERE c.id = p_collection_id;
    
    RAISE NOTICE 'Collection found: is_default=%, default_type=%, owner_id=%', 
        v_is_default, v_default_type, v_owner_id;
    
    -- Verify ownership
    IF v_owner_id IS NULL THEN
        RAISE EXCEPTION 'Collection not found';
    END IF;
    
    IF v_owner_id != p_user_id THEN
        RAISE EXCEPTION 'You do not have permission to update this collection';
    END IF;
    
    -- Check if it's a default collection
    IF v_is_default = true THEN
        RAISE NOTICE 'Updating default collection (type: %)', v_default_type;
        
        -- For default collections, only allow updating the icon
        IF p_icon_url IS NOT NULL THEN
            RAISE NOTICE 'Updating icon for default collection to: %', p_icon_url;
            
            UPDATE collections c
            SET 
                icon = p_icon_url,
                updated_at = NOW()
            WHERE c.id = p_collection_id AND c.user_id = p_user_id;
            
            RETURN QUERY SELECT
                c.id, c.user_id, c.name, c.description, c.icon, c.is_public, c.is_default, c.default_type, c.created_at, c.updated_at
            FROM collections c
            WHERE c.id = p_collection_id;
            
            RAISE NOTICE 'Default collection icon updated successfully';
        ELSE
            -- If no icon update is requested, check if other fields are being updated
            IF p_name IS NOT NULL OR p_description IS NOT NULL OR p_is_public IS NOT NULL THEN
                RAISE EXCEPTION 'Default collections cannot be edited except for their icon';
            ELSE
                -- No changes requested, just return the current collection
                RETURN QUERY SELECT 
                    c.id, c.user_id, c.name, c.description, c.icon, c.is_public, c.is_default, c.default_type, c.created_at, c.updated_at 
                FROM collections c 
                WHERE c.id = p_collection_id;
            END IF;
        END IF;
    ELSE
        -- For custom collections, allow updating all fields
        RAISE NOTICE 'Updating custom collection with fields: name=%, description=%, icon=%, is_public=%', 
            p_name, p_description, p_icon_url, p_is_public;
        
        UPDATE collections c
        SET 
            name = COALESCE(p_name, c.name),
            description = COALESCE(p_description, c.description),
            icon = CASE WHEN p_icon_url IS NOT NULL THEN p_icon_url ELSE c.icon END,
            is_public = COALESCE(p_is_public, c.is_public),
            updated_at = NOW()
        WHERE c.id = p_collection_id AND c.user_id = p_user_id;
        
        RETURN QUERY SELECT
            c.id, c.user_id, c.name, c.description, c.icon, c.is_public, c.is_default, c.default_type, c.created_at, c.updated_at
        FROM collections c
        WHERE c.id = p_collection_id;
        
        RAISE NOTICE 'Custom collection updated successfully';
    END IF;
    
    -- Since we've moved the RETURN QUERY statements after each UPDATE,
    -- we don't need this check anymore. The function will always return
    -- the updated collection data or throw an exception if something went wrong.
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.update_user_collection TO authenticated;

-- Add comment to the function
COMMENT ON FUNCTION public.update_user_collection IS 'Updates a user collection with proper validation and image handling';

