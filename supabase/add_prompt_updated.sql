-- Updated add_prompt function with ai_model_id parameter and modified return type
-- First, drop the existing function to avoid conflicts with parameter changes
DROP FUNCTION IF EXISTS "public"."add_prompt"("uuid", integer, integer, "text", "text", "text", "text", "text", "text", "text", "text", boolean, "uuid", integer[]);

-- Recreate the function with the new parameter and return type
CREATE FUNCTION "public"."add_prompt"(
    "p_user_id" "uuid",
    "p_category_id" integer,
    "p_tool_id" integer,
    "p_title" "text",
    "p_description" "text",
    "p_prompt_text" "text",
    "p_instructions" "text" DEFAULT NULL::"text",
    "p_example_input" "text" DEFAULT NULL::"text",
    "p_example_output_text" "text" DEFAULT NULL::"text",
    "p_example_output_image_url" "text" DEFAULT NULL::"text",
    "p_image_url" "text" DEFAULT NULL::"text",
    "p_is_public" boolean DEFAULT true,
    "p_original_prompt_id" "uuid" DEFAULT NULL::"uuid",
    "p_tag_ids" integer[] DEFAULT NULL::integer[],
    "p_ai_model_id" integer DEFAULT NULL -- New parameter for AI model
)
RETURNS TABLE(created_prompt_id uuid, created_short_id text) -- Modified return type
LANGUAGE "plpgsql"
AS $$
DECLARE
  new_prompt_id_internal UUID; -- Renamed to avoid conflict with column name in RETURN QUERY
  new_short_id_internal TEXT;  -- Renamed
  new_slug TEXT;
  category_slug TEXT;
  tool_slug TEXT;
  first_tag_slug TEXT := 'untagged';
  i INTEGER;
BEGIN
  -- Generate a short ID
  new_short_id_internal := generate_short_id();
  
  -- Get the category slug
  SELECT slug INTO category_slug FROM categories WHERE id = p_category_id;
  
  -- Get the tool slug
  SELECT slug INTO tool_slug FROM tools WHERE id = p_tool_id;
  
  -- Get the first tag slug if tag_ids is provided
  IF p_tag_ids IS NOT NULL AND array_length(p_tag_ids, 1) > 0 THEN
    SELECT slug INTO first_tag_slug FROM tags WHERE id = p_tag_ids[1];
  END IF;
  
  -- Generate the slug
  new_slug := generate_prompt_slug(
    p_title,
    category_slug,
    tool_slug,
    first_tag_slug,
    new_short_id_internal -- Use the renamed variable
  );
  
  -- Insert the prompt
  INSERT INTO prompts (
    id,
    short_id,
    user_id,
    category_id,
    tool_id,
    title,
    slug,
    description,
    prompt_text,
    instructions,
    example_input,
    example_output_text,
    example_output_image_url,
    image_url,
    is_public,
    original_prompt_id,
    view_count,
    created_at,
    updated_at,
    ai_model_id -- Added new field
  ) VALUES (
    uuid_generate_v4(),
    new_short_id_internal, -- Use the renamed variable
    p_user_id,
    p_category_id,
    p_tool_id,
    p_title,
    new_slug,
    p_description,
    p_prompt_text,
    p_instructions,
    p_example_input,
    p_example_output_text,
    p_example_output_image_url,
    p_image_url,
    p_is_public,
    p_original_prompt_id,
    0,
    NOW(),
    NOW(),
    p_ai_model_id -- Pass the new parameter
  ) RETURNING id INTO new_prompt_id_internal; -- Use the renamed variable
  
  -- Add tags if provided
  IF p_tag_ids IS NOT NULL AND array_length(p_tag_ids, 1) > 0 THEN
    FOR i IN 1..array_length(p_tag_ids, 1) LOOP
      INSERT INTO prompt_tags (prompt_id, tag_id)
      VALUES (new_prompt_id_internal, p_tag_ids[i]); -- Use the renamed variable
    END LOOP;
  END IF;
  
  -- Add an upvote from the creator
  INSERT INTO prompt_votes (user_id, prompt_id, vote_type, created_at)
  VALUES (p_user_id, new_prompt_id_internal, 1, NOW()); -- Use the renamed variable
  
  -- Return both the UUID and short_id
  RETURN QUERY SELECT new_prompt_id_internal, new_short_id_internal;
END;
$$;
