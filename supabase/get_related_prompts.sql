-- Function to get related prompts based on category, tool, and tag similarity
-- This implements the algorithm specified in the related-prompts.md brief

CREATE OR REPLACE FUNCTION get_related_prompts(
    p_source_prompt_short_id TEXT,
    p_limit INT DEFAULT 6
)
RETURNS SETOF prompt_card_details
LANGUAGE plpgsql
AS $$
DECLARE
    v_source_category_slug TEXT;
    v_source_tool_slug TEXT;
    v_source_tags_array TEXT[];
BEGIN
    -- 1. Fetch details of the source prompt
    SELECT
        pcd.category_slug,
        pcd.tool_slug,
        pcd.tag_slugs_array
    INTO
        v_source_category_slug,
        v_source_tool_slug,
        v_source_tags_array
    FROM prompt_card_details pcd
    WHERE pcd.short_id = p_source_prompt_short_id;

    IF NOT FOUND THEN
        RETURN;
    END IF;

    -- 2. Query and score candidate prompts
    RETURN QUERY
    WITH scored_candidates AS (
        SELECT
            candidate.*, -- Selects all columns from prompt_card_details for the CTE
            (
                -- Category Score
                CASE
                    WHEN candidate.category_slug = v_source_category_slug THEN 3.0
                    ELSE 0.0
                END +
                -- Tool Score
                CASE
                    WHEN candidate.tool_slug = v_source_tool_slug THEN 2.0
                    ELSE 0.0
                END +
                -- Tags Score (0.5 points per common tag)
                (
                    0.5 * COALESCE(
                        array_length(
                            ARRAY(
                                SELECT unnest(COALESCE(candidate.tag_slugs_array, '{}'::TEXT[]))
                                INTERSECT
                                SELECT unnest(COALESCE(v_source_tags_array, '{}'::TEXT[]))
                            ), 1
                        ), 0
                    )
                )
            ) AS relevance_score
        FROM
            prompt_card_details AS candidate
        WHERE
            candidate.short_id != p_source_prompt_short_id -- Exclude the source prompt itself
            AND candidate.is_public = TRUE -- Only consider public prompts
    )
    -- Final SELECT with columns in the exact order of prompt_card_details view
    SELECT
        sc.id,                    -- 1: uuid
        sc.short_id,              -- 2: text
        sc.title,                 -- 3: text
        sc.description,           -- 4: text
        sc.image_url,             -- 5: text
        sc.created_at,            -- 6: timestamp with time zone
        sc.updated_at,            -- 7: timestamp with time zone
        sc.is_public,             -- 8: boolean
        sc.view_count,            -- 9: integer
        sc.primary_tag_id,        -- 10: integer
        sc.category_id,           -- 11: integer
        sc.tool_id,               -- 12: integer
        sc.author_id,             -- 13: uuid
        sc.search_vector,         -- 14: tsvector
        sc.tag_slugs_array,       -- 15: text[]
        sc.category_name,         -- 16: text
        sc.category_slug,         -- 17: text
        sc.tool_name,             -- 18: text
        sc.tool_slug,             -- 19: text
        sc.author_username,       -- 20: text
        sc.author_avatar_url,     -- 21: text
        sc.primary_tag_slug,      -- 22: text
        sc.tags,                  -- 23: jsonb
        sc.rating,                -- 24: bigint
        sc.comment_count,         -- 25: bigint
        sc.remix_count,           -- 26: bigint
        sc.trending_score,        -- 27: double precision
        sc.ai_model_id,           -- 28: integer
        sc.ai_model_provider,     -- 29: text
        sc.ai_model_name,         -- 30: text
        sc.ai_model_slug,         -- 31: text
        sc.ai_model_deprecated    -- 32: boolean
    FROM
        scored_candidates sc
    ORDER BY
        sc.relevance_score DESC,
        sc.rating DESC,
        sc.created_at DESC
    LIMIT
        p_limit;
END;
$$; 